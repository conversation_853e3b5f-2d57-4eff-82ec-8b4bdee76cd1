# 发票管理系统部署说明

## 🎯 构建信息
- 构建时间: 2025/6/8 00:13:04
- 前端地址: http://fpm.nslemons.com:5173
- 后端地址: http://fpm.nslemons.com:3001
- API地址: fpm.nslemons.com

## 📦 构建文件
- 前端构建文件: frontend/dist/
- 后端构建文件: backend/dist/
- 环境配置文件: 
  - frontend/.env.production
  - backend/.env.production

## 🚀 部署步骤

### 1. 前端部署
```bash
# 将frontend/dist/目录上传到Web服务器
# 配置Nginx或Apache指向dist目录
# 确保支持SPA路由（所有路由都返回index.html）
```

### 2. 后端部署
```bash
# 上传后端代码到服务器
cd backend
npm install --production
npm run build
npm start
```

### 3. 环境配置
- 确保数据库连接正确
- 检查端口是否开放
- 配置CORS允许前端域名

## 🔧 Nginx配置示例
```nginx
server {
    listen 80;
    server_name fpm.nslemons.com;
    
    location / {
        root /path/to/frontend/dist;
        try_files $uri $uri/ /index.html;
    }
    
    location /api {
        proxy_pass http://fpm.nslemons.com:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 📊 验证部署
1. 访问 http://fpm.nslemons.com 检查前端
2. 访问 http://fpm.nslemons.com:3001/api/status 检查后端
3. 测试登录功能

---
生成时间: 2025-06-07T16:13:04.614Z
