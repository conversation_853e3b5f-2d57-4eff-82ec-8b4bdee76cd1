"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const client_1 = require("@prisma/client");
const joi_1 = __importDefault(require("joi"));
const errorHandler_1 = require("../middleware/errorHandler");
const auth_1 = require("../middleware/auth");
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const multer_1 = __importDefault(require("multer"));
const XLSX = __importStar(require("xlsx"));
const router = express_1.default.Router();
const prisma = new client_1.PrismaClient();
// 应用认证中间件到所有路由
router.use(auth_1.authenticate);
// 验证schemas
const createCompanySchema = joi_1.default.object({
    name: joi_1.default.string().min(2).max(100).required().messages({
        'string.min': '公司名称至少2个字符',
        'string.max': '公司名称不能超过100个字符',
        'any.required': '公司名称是必填项',
    }),
    taxId: joi_1.default.string().required().messages({
        'any.required': '税号是必填项',
    }),
    address: joi_1.default.string().max(200).optional().allow('', null),
    phone: joi_1.default.string().max(20).optional().allow('', null),
    email: joi_1.default.string().email().optional().allow('', null).messages({
        'string.email': '请输入有效的邮箱地址',
    }),
    contact: joi_1.default.string().max(50).optional().allow('', null),
    organization: joi_1.default.string().max(100).optional().allow('', null),
    remarks: joi_1.default.string().max(500).optional().allow('', null),
    registrationDate: joi_1.default.date().optional().allow(null),
    lastInvoiceDate: joi_1.default.date().optional().allow(null),
    isActive: joi_1.default.boolean().optional().default(true),
});
const updateCompanySchema = joi_1.default.object({
    name: joi_1.default.string().min(2).max(100).optional().messages({
        'string.min': '公司名称至少2个字符',
        'string.max': '公司名称不能超过100个字符',
    }),
    taxId: joi_1.default.string().optional(),
    address: joi_1.default.string().max(200).optional().allow('', null),
    phone: joi_1.default.string().max(20).optional().allow('', null),
    email: joi_1.default.string().email().optional().allow('', null).messages({
        'string.email': '请输入有效的邮箱地址',
    }),
    contact: joi_1.default.string().max(50).optional().allow('', null),
    organization: joi_1.default.string().max(100).optional().allow('', null),
    remarks: joi_1.default.string().max(500).optional().allow('', null),
    registrationDate: joi_1.default.date().optional().allow(null),
    lastInvoiceDate: joi_1.default.date().optional().allow(null),
    isActive: joi_1.default.boolean().optional(),
}).unknown(false);
const querySchema = joi_1.default.object({
    page: joi_1.default.number().integer().min(1).default(1),
    pageSize: joi_1.default.number().integer().min(1).max(100).default(10),
    search: joi_1.default.string().optional().allow(''),
    organization: joi_1.default.string().optional().allow(''),
    sortBy: joi_1.default.string().valid('name', 'taxId', 'createdAt').default('createdAt'),
    sortOrder: joi_1.default.string().valid('asc', 'desc').default('desc'),
});
// 获取公司列表
router.get('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { error, value } = querySchema.validate(req.query);
    if (error) {
        throw new errorHandler_1.AppError(error.details[0].message, 400);
    }
    const { page, pageSize, search, organization, sortBy, sortOrder } = value;
    const skip = (page - 1) * pageSize;
    // 构建查询条件
    const where = {};
    // 如果是普通用户，只能查看有权限的公司
    if (req.user?.role !== 'ADMIN') {
        const userCompanyIds = await (0, auth_1.getUserCompanyIds)(req.user.id);
        if (userCompanyIds.length === 0) {
            // 用户没有任何公司权限，返回空结果
            return res.json({
                success: true,
                data: {
                    data: [],
                    total: 0,
                    page,
                    pageSize,
                },
            });
        }
        where.id = { in: userCompanyIds };
    }
    // 构建搜索条件
    const searchConditions = [];
    if (search) {
        searchConditions.push({ name: { contains: search } }, { taxId: { contains: search } }, { contact: { contains: search } });
    }
    if (organization) {
        searchConditions.push({ organization: { contains: organization } });
    }
    if (searchConditions.length > 0) {
        where.OR = searchConditions;
    }
    // 获取总数
    const total = await prisma.company.count({ where });
    // 获取数据
    const companies = await prisma.company.findMany({
        where,
        skip,
        take: pageSize,
        orderBy: { [sortBy]: sortOrder },
        include: {
            _count: {
                select: { invoices: true, receivedInvoices: true },
            },
            invoices: {
                select: {
                    invoiceDate: true,
                },
                orderBy: {
                    invoiceDate: 'desc',
                },
                take: 1,
            },
        },
    });
    // 为每个公司添加权限信息和最新发票日期
    const companiesWithPermissions = companies.map(company => {
        const isAdmin = req.user?.role === 'ADMIN';
        const hasPermission = isAdmin || (req.user?.role !== 'ADMIN' && where.id?.in?.includes(company.id));
        return {
            ...company,
            latestInvoiceDate: company.invoices.length > 0 ? company.invoices[0].invoiceDate : null,
            permissions: {
                canEdit: hasPermission,
                canDelete: hasPermission && company._count.invoices === 0 && company._count.receivedInvoices === 0,
            }
        };
    });
    res.json({
        success: true,
        data: {
            data: companiesWithPermissions,
            total,
            page,
            pageSize,
            totalPages: Math.ceil(total / pageSize),
        },
    });
}));
// 下载公司导入模板 - 必须在 /:id 路由之前
router.get('/template', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const templatePath = path_1.default.join(__dirname, '../../templates/company.xlsx');
    if (!fs_1.default.existsSync(templatePath)) {
        // 如果模板文件不存在，创建一个简单的模板
        const templateData = [
            ['公司名称', '纳税人识别号', '地址', '电话', '邮箱', '联系人', '所属用户', '注册日期', '备注'],
            ['示例公司有限公司', '91110000123456789X', '北京市朝阳区示例路123号', '010-12345678', '<EMAIL>', '张三', '李四', '2020-01-01', '这是一个示例公司']
        ];
        const ws = XLSX.utils.aoa_to_sheet(templateData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, '公司信息');
        const buffer = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' });
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', 'attachment; filename="company-template.xlsx"');
        res.send(buffer);
    }
    else {
        // 使用现有模板文件
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', 'attachment; filename="company-template.xlsx"');
        const fileStream = fs_1.default.createReadStream(templatePath);
        fileStream.pipe(res);
    }
}));
// 获取激活的公司列表（用于下拉选择）
router.get('/active', auth_1.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        // 构建查询条件 - 只返回激活的公司
        const where = {
            isActive: true,
        };
        // 如果是普通用户，只能查看有权限的公司
        if (req.user?.role !== 'ADMIN') {
            const userCompanyIds = await (0, auth_1.getUserCompanyIds)(req.user.id);
            if (userCompanyIds.length === 0) {
                return res.json({
                    success: true,
                    data: [],
                });
            }
            where.id = { in: userCompanyIds };
        }
        // 支持搜索
        const { search } = req.query;
        if (search) {
            where.OR = [
                { name: { contains: search, mode: 'insensitive' } },
                { taxId: { contains: search, mode: 'insensitive' } },
            ];
        }
        const companies = await prisma.company.findMany({
            where,
            select: {
                id: true,
                name: true,
                taxId: true,
                organization: true,
            },
            orderBy: { name: 'asc' },
        });
        res.json({
            success: true,
            data: companies,
        });
    }
    catch (error) {
        console.error('获取激活公司列表失败:', error);
        throw new errorHandler_1.AppError('获取激活公司列表失败', 500);
    }
}));
// 获取单个公司
router.get('/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const company = await prisma.company.findFirst({
        where: {
            id,
        },
        include: {
            _count: {
                select: { invoices: true },
            },
            invoices: {
                take: 10,
                orderBy: { createdAt: 'desc' },
                select: {
                    id: true,
                    invoiceNumber: true,
                    invoiceDate: true,
                    totalAmount: true,
                    status: true,
                },
            },
        },
    });
    if (!company) {
        throw new errorHandler_1.AppError('公司不存在', 404);
    }
    res.json({
        success: true,
        data: company,
    });
}));
// 创建公司
router.post('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { error, value } = createCompanySchema.validate(req.body);
    if (error) {
        throw new errorHandler_1.AppError(error.details[0].message, 400);
    }
    // 检查税号是否已存在
    const existingCompany = await prisma.company.findUnique({
        where: { taxId: value.taxId },
    });
    if (existingCompany) {
        throw new errorHandler_1.AppError('税号已存在', 409);
    }
    // 如果没有设置所属用户，自动设置为当前登录用户的姓名
    if (!value.organization && req.user?.name) {
        value.organization = req.user.name;
    }
    const company = await prisma.company.create({
        data: value,
        include: {
            _count: {
                select: { invoices: true },
            },
        },
    });
    res.status(201).json({
        success: true,
        data: company,
        message: '公司创建成功',
    });
}));
// 更新公司
router.put('/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const { error, value } = updateCompanySchema.validate(req.body);
    if (error) {
        throw new errorHandler_1.AppError(error.details[0].message, 400);
    }
    // 检查公司是否存在
    const existingCompany = await prisma.company.findFirst({
        where: {
            id,
        },
    });
    if (!existingCompany) {
        throw new errorHandler_1.AppError('公司不存在', 404);
    }
    // 权限检查：管理员可以编辑所有公司，普通用户只能编辑有权限的公司
    if (req.user?.role !== 'ADMIN') {
        const userCompanyIds = await (0, auth_1.getUserCompanyIds)(req.user.id);
        if (!userCompanyIds.includes(id)) {
            throw new errorHandler_1.AppError('您没有权限编辑此公司', 403);
        }
    }
    // 如果更新税号，检查是否与其他公司重复
    if (value.taxId && value.taxId !== existingCompany.taxId) {
        const duplicateTaxId = await prisma.company.findFirst({
            where: {
                taxId: value.taxId,
                id: { not: id },
            },
        });
        if (duplicateTaxId) {
            throw new errorHandler_1.AppError('税号已存在', 409);
        }
    }
    const company = await prisma.company.update({
        where: { id },
        data: value,
        include: {
            _count: {
                select: { invoices: true },
            },
        },
    });
    res.json({
        success: true,
        data: company,
        message: '公司更新成功',
    });
}));
// 删除公司（软删除）
router.delete('/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const company = await prisma.company.findFirst({
        where: {
            id,
        },
        include: {
            _count: {
                select: { invoices: true, receivedInvoices: true },
            },
        },
    });
    if (!company) {
        throw new errorHandler_1.AppError('公司不存在', 404);
    }
    // 权限检查：管理员可以删除所有公司，普通用户只能删除有权限的公司
    if (req.user?.role !== 'ADMIN') {
        const userCompanyIds = await (0, auth_1.getUserCompanyIds)(req.user.id);
        if (!userCompanyIds.includes(id)) {
            throw new errorHandler_1.AppError('您没有权限删除此公司', 403);
        }
    }
    // 检查是否有关联的开具发票
    if (company._count.invoices > 0) {
        throw new errorHandler_1.AppError(`该公司下还有 ${company._count.invoices} 张开具发票记录，无法删除`, 400);
    }
    // 检查是否有关联的收取发票
    if (company._count.receivedInvoices > 0) {
        throw new errorHandler_1.AppError(`该公司下还有 ${company._count.receivedInvoices} 张收取发票记录，无法删除`, 400);
    }
    // 使用事务确保数据一致性
    await prisma.$transaction(async (tx) => {
        // 1. 删除所有用户对该公司的权限
        await tx.userCompany.deleteMany({
            where: { companyId: id }
        });
        // 2. 硬删除公司（直接从数据库中删除）
        await tx.company.delete({
            where: { id },
        });
    });
    res.json({
        success: true,
        message: '公司删除成功，相关权限已清理',
    });
}));
// 获取公司统计信息
router.get('/:id/stats', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const company = await prisma.company.findFirst({
        where: {
            id,
        },
    });
    if (!company) {
        throw new errorHandler_1.AppError('公司不存在', 404);
    }
    // 获取统计数据
    const stats = await prisma.invoice.groupBy({
        by: ['status'],
        where: { companyId: id },
        _count: { _all: true },
        _sum: { totalAmount: true },
    });
    const totalInvoices = await prisma.invoice.count({
        where: { companyId: id },
    });
    const totalAmount = await prisma.invoice.aggregate({
        where: { companyId: id },
        _sum: { totalAmount: true },
    });
    res.json({
        success: true,
        data: {
            totalInvoices,
            totalAmount: totalAmount._sum.totalAmount || 0,
            statusBreakdown: stats,
        },
    });
}));
// 配置multer用于文件上传
const upload = (0, multer_1.default)({
    storage: multer_1.default.memoryStorage(),
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
    },
    fileFilter: (req, file, cb) => {
        if (file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
            file.mimetype === 'application/vnd.ms-excel') {
            cb(null, true);
        }
        else {
            cb(new Error('只支持Excel文件格式'));
        }
    },
});
// Excel导入公司
router.post('/import', upload.single('file'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.file) {
        throw new errorHandler_1.AppError('请选择要上传的Excel文件', 400);
    }
    try {
        // 读取Excel文件
        const workbook = XLSX.read(req.file.buffer, { type: 'buffer' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        // 将工作表转换为JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        if (jsonData.length < 2) {
            throw new errorHandler_1.AppError('Excel文件格式不正确或没有数据', 400);
        }
        // 获取表头
        const headers = jsonData[0];
        const dataRows = jsonData.slice(1);
        // 映射Excel列到数据库字段
        const fieldMapping = {
            '公司名称': 'name',
            '名称': 'name', // 支持现有模板
            '纳税人识别号': 'taxId',
            '税号': 'taxId', // 支持现有模板
            '地址': 'address',
            '电话': 'phone',
            '联系电话': 'phone', // 支持现有模板
            '邮箱': 'email',
            '联系人': 'contact',
            '所属组织': 'organization', // 兼容旧模板
            '所属用户': 'organization', // 新的字段名
            '注册日期': 'registrationDate',
            '备注': 'remarks'
            // 注意：不映射 isActive 字段，确保始终使用默认的激活状态
        };
        let successCount = 0;
        let failureCount = 0;
        let duplicateCount = 0;
        const errors = [];
        const importedCompanyIds = []; // 记录成功导入的公司ID
        // 处理每一行数据
        for (let i = 0; i < dataRows.length; i++) {
            const row = dataRows[i];
            const rowNumber = i + 2; // Excel行号（从2开始，因为第1行是表头）
            try {
                if (!row || row.length === 0 || !row.some(cell => cell !== null && cell !== undefined && cell !== '')) {
                    continue; // 跳过空行
                }
                // 构建公司数据
                const companyData = {
                    isActive: true,
                };
                // 映射Excel数据到公司字段
                headers.forEach((header, index) => {
                    const field = fieldMapping[header];
                    if (field && row[index] !== null && row[index] !== undefined && row[index] !== '') {
                        const value = row[index].toString().trim();
                        if (value) { // 确保trim后的值不为空
                            // 特殊处理日期字段
                            if (field === 'registrationDate') {
                                try {
                                    // 处理Excel日期格式
                                    let parsedDate = null;
                                    // 如果是数字，可能是Excel的日期序列号
                                    if (!isNaN(Number(value))) {
                                        const excelDate = Number(value);
                                        // Excel日期序列号范围检查（1900-01-01到2100-12-31大约是1到73050）
                                        if (excelDate >= 1 && excelDate <= 73050) {
                                            // Excel日期序列号转换：1900年1月1日为1，但Excel错误地认为1900年是闰年
                                            // 所以需要减去1天来修正
                                            const baseDate = new Date(1900, 0, 1); // 1900年1月1日
                                            parsedDate = new Date(baseDate.getTime() + (excelDate - 1) * 24 * 60 * 60 * 1000);
                                        }
                                    }
                                    // 如果不是序列号或转换失败，尝试直接解析字符串
                                    if (!parsedDate || isNaN(parsedDate.getTime())) {
                                        parsedDate = new Date(value);
                                    }
                                    // 验证日期是否有效且在合理范围内
                                    if (!isNaN(parsedDate.getTime()) &&
                                        parsedDate.getFullYear() >= 1900 &&
                                        parsedDate.getFullYear() <= 2100) {
                                        companyData[field] = parsedDate;
                                    }
                                    else {
                                        console.warn(`第${rowNumber}行注册日期格式不正确或超出范围: ${value}`);
                                    }
                                }
                                catch (error) {
                                    console.warn(`第${rowNumber}行注册日期解析失败: ${value}`, error);
                                }
                            }
                            else {
                                companyData[field] = value;
                            }
                        }
                    }
                });
                // 验证必填字段
                if (!companyData.name || companyData.name.trim() === '') {
                    errors.push({ row: rowNumber, message: '公司名称不能为空' });
                    failureCount++;
                    continue;
                }
                if (!companyData.taxId) {
                    errors.push({ row: rowNumber, message: '纳税人识别号不能为空' });
                    failureCount++;
                    continue;
                }
                // 检查公司是否已存在
                const existingCompany = await prisma.company.findFirst({
                    where: {
                        OR: [
                            { taxId: companyData.taxId },
                            { name: companyData.name }
                        ]
                    },
                });
                if (existingCompany) {
                    duplicateCount++;
                    continue;
                }
                // 确保公司默认为激活状态
                companyData.isActive = true;
                // 如果没有设置所属用户，自动设置为当前登录用户的姓名
                if (!companyData.organization && req.user?.name) {
                    companyData.organization = req.user.name;
                }
                // 创建公司
                const newCompany = await prisma.company.create({
                    data: companyData,
                });
                // 记录新创建的公司ID
                importedCompanyIds.push(newCompany.id);
                successCount++;
            }
            catch (error) {
                console.error(`处理第${rowNumber}行时出错:`, error);
                errors.push({
                    row: rowNumber,
                    message: error instanceof Error ? error.message : '未知错误'
                });
                failureCount++;
            }
        }
        // 为当前登录用户分配新导入公司的权限（包括管理员，确保权限记录完整）
        if (importedCompanyIds.length > 0 && req.user) {
            try {
                // 使用事务确保权限分配的一致性
                await prisma.$transaction(async (tx) => {
                    let assignedCount = 0;
                    for (const companyId of importedCompanyIds) {
                        try {
                            // 检查权限是否已存在
                            const existingPermission = await tx.userCompany.findUnique({
                                where: {
                                    userId_companyId: {
                                        userId: req.user.id,
                                        companyId: companyId
                                    }
                                }
                            });
                            if (!existingPermission) {
                                // 创建新的权限记录
                                await tx.userCompany.create({
                                    data: {
                                        userId: req.user.id,
                                        companyId: companyId
                                    }
                                });
                                assignedCount++;
                            }
                        }
                        catch (permError) {
                            throw permError; // 在事务中抛出错误以回滚
                        }
                    }
                });
            }
            catch (error) {
                // 不影响导入结果，只记录错误
            }
        }
        res.json({
            success: true,
            data: {
                totalCount: dataRows.length,
                successCount,
                failureCount,
                duplicateCount,
                errors: errors.slice(0, 50), // 最多返回50个错误
                assignedToCurrentUser: importedCompanyIds.length, // 分配给当前用户的公司数量
            },
            message: `导入完成：成功${successCount}条，失败${failureCount}条，重复${duplicateCount}条${importedCompanyIds.length > 0 ? `，已为您分配${importedCompanyIds.length}个新公司的权限` : ''}`,
        });
    }
    catch (error) {
        console.error('Excel导入失败:', error);
        throw new errorHandler_1.AppError('Excel文件解析失败，请检查文件格式', 400);
    }
}));
exports.default = router;
//# sourceMappingURL=companies.js.map