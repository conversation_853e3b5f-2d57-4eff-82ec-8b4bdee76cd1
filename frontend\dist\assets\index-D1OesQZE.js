import{r as ss,a as rs,g as ls}from"./react-vendor-BEN4boU2.js";import{a as is,d as He,L as qt,T as as,r as a,C as sn,A as os,b as C,S as we,F as d,I as $,c as Ct,e as Ot,B as f,f as rn,g as Wt,M as ds,D as Ut,h as Se,i as R,j as r,k as ze,s as k,l as st,m as cs,n as hs,o as Pn,p as us,q as xs,t as ms,u as ce,v as ps,w as ys,x as Dt,y as Yt,z as ln,E as h,G as an,H as tt,J as rt,K as Ke,N as on,O as ue,P as _e,Q as It,U as it,V as Vt,W as yt,X as gs,Y as wt,Z as dn,_ as cn,$ as Bn,a0 as hn,a1 as js,a2 as fs,a3 as vs,a4 as un,a5 as at}from"./antd-vendor-trf5ErUE.js";import{a as bs,B as ws,R as Fn,b as Xe,N as xn,u as Ht,c as Mn}from"./vendor-BDMYW6if.js";(function(){const S=document.createElement("link").relList;if(S&&S.supports&&S.supports("modulepreload"))return;for(const N of document.querySelectorAll('link[rel="modulepreload"]'))D(N);new MutationObserver(N=>{for(const z of N)if(z.type==="childList")for(const w of z.addedNodes)w.tagName==="LINK"&&w.rel==="modulepreload"&&D(w)}).observe(document,{childList:!0,subtree:!0});function L(N){const z={};return N.integrity&&(z.integrity=N.integrity),N.referrerPolicy&&(z.referrerPolicy=N.referrerPolicy),N.crossOrigin==="use-credentials"?z.credentials="include":N.crossOrigin==="anonymous"?z.credentials="omit":z.credentials="same-origin",z}function D(N){if(N.ep)return;N.ep=!0;const z=L(N);fetch(N.href,z)}})();var _t={exports:{}},ot={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var mn;function Ss(){if(mn)return ot;mn=1;var c=ss(),S=Symbol.for("react.element"),L=Symbol.for("react.fragment"),D=Object.prototype.hasOwnProperty,N=c.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,z={key:!0,ref:!0,__self:!0,__source:!0};function w(W,V,he){var A,ie={},I=null,je=null;he!==void 0&&(I=""+he),V.key!==void 0&&(I=""+V.key),V.ref!==void 0&&(je=V.ref);for(A in V)D.call(V,A)&&!z.hasOwnProperty(A)&&(ie[A]=V[A]);if(W&&W.defaultProps)for(A in V=W.defaultProps,V)ie[A]===void 0&&(ie[A]=V[A]);return{$$typeof:S,type:W,key:I,ref:je,props:ie,_owner:N.current}}return ot.Fragment=L,ot.jsx=w,ot.jsxs=w,ot}var pn;function Cs(){return pn||(pn=1,_t.exports=Ss()),_t.exports}var e=Cs(),St={},yn;function ks(){if(yn)return St;yn=1;var c=rs();return St.createRoot=c.createRoot,St.hydrateRoot=c.hydrateRoot,St}var Is=ks();const Ds="modulepreload",zs=function(c,S){return new URL(c,S).href},gn={},jn=function(S,L,D){let N=Promise.resolve();if(L&&L.length>0){let w=function(A){return Promise.all(A.map(ie=>Promise.resolve(ie).then(I=>({status:"fulfilled",value:I}),I=>({status:"rejected",reason:I}))))};const W=document.getElementsByTagName("link"),V=document.querySelector("meta[property=csp-nonce]"),he=(V==null?void 0:V.nonce)||(V==null?void 0:V.getAttribute("nonce"));N=w(L.map(A=>{if(A=zs(A,D),A in gn)return;gn[A]=!0;const ie=A.endsWith(".css"),I=ie?'[rel="stylesheet"]':"";if(!!D)for(let u=W.length-1;u>=0;u--){const g=W[u];if(g.href===A&&(!ie||g.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${A}"]${I}`))return;const me=document.createElement("link");if(me.rel=ie?"stylesheet":Ds,ie||(me.as="script"),me.crossOrigin="",me.href=A,he&&me.setAttribute("nonce",he),document.head.appendChild(me),ie)return new Promise((u,g)=>{me.addEventListener("load",u),me.addEventListener("error",()=>g(new Error(`Unable to preload CSS for ${A}`)))})}))}function z(w){const W=new Event("vite:preloadError",{cancelable:!0});if(W.payload=w,window.dispatchEvent(W),!W.defaultPrevented)throw w}return N.then(w=>{for(const W of w||[])W.status==="rejected"&&z(W.reason);return S().catch(z)})};var dt={},Rt={exports:{}},fn;function zt(){return fn||(fn=1,function(c){function S(L){return L&&L.__esModule?L:{default:L}}c.exports=S,c.exports.__esModule=!0,c.exports.default=c.exports}(Rt)),Rt.exports}var ct={},vn;function Ns(){if(vn)return ct;vn=1,Object.defineProperty(ct,"__esModule",{value:!0}),ct.default=void 0;var c={items_per_page:"条/页",jump_to:"跳至",jump_to_confirm:"确定",page:"页",prev_page:"上一页",next_page:"下一页",prev_5:"向前 5 页",next_5:"向后 5 页",prev_3:"向前 3 页",next_3:"向后 3 页",page_size:"页码"};return ct.default=c,ct}var ht={},ut={},xt={},Lt={exports:{}},Pt={exports:{}},Bt={exports:{}},Ft={exports:{}},bn;function En(){return bn||(bn=1,function(c){function S(L){"@babel/helpers - typeof";return c.exports=S=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(D){return typeof D}:function(D){return D&&typeof Symbol=="function"&&D.constructor===Symbol&&D!==Symbol.prototype?"symbol":typeof D},c.exports.__esModule=!0,c.exports.default=c.exports,S(L)}c.exports=S,c.exports.__esModule=!0,c.exports.default=c.exports}(Ft)),Ft.exports}var Mt={exports:{}},wn;function As(){return wn||(wn=1,function(c){var S=En().default;function L(D,N){if(S(D)!="object"||!D)return D;var z=D[Symbol.toPrimitive];if(z!==void 0){var w=z.call(D,N||"default");if(S(w)!="object")return w;throw new TypeError("@@toPrimitive must return a primitive value.")}return(N==="string"?String:Number)(D)}c.exports=L,c.exports.__esModule=!0,c.exports.default=c.exports}(Mt)),Mt.exports}var Sn;function $s(){return Sn||(Sn=1,function(c){var S=En().default,L=As();function D(N){var z=L(N,"string");return S(z)=="symbol"?z:z+""}c.exports=D,c.exports.__esModule=!0,c.exports.default=c.exports}(Bt)),Bt.exports}var Cn;function Ts(){return Cn||(Cn=1,function(c){var S=$s();function L(D,N,z){return(N=S(N))in D?Object.defineProperty(D,N,{value:z,enumerable:!0,configurable:!0,writable:!0}):D[N]=z,D}c.exports=L,c.exports.__esModule=!0,c.exports.default=c.exports}(Pt)),Pt.exports}var kn;function _s(){return kn||(kn=1,function(c){var S=Ts();function L(N,z){var w=Object.keys(N);if(Object.getOwnPropertySymbols){var W=Object.getOwnPropertySymbols(N);z&&(W=W.filter(function(V){return Object.getOwnPropertyDescriptor(N,V).enumerable})),w.push.apply(w,W)}return w}function D(N){for(var z=1;z<arguments.length;z++){var w=arguments[z]!=null?arguments[z]:{};z%2?L(Object(w),!0).forEach(function(W){S(N,W,w[W])}):Object.getOwnPropertyDescriptors?Object.defineProperties(N,Object.getOwnPropertyDescriptors(w)):L(Object(w)).forEach(function(W){Object.defineProperty(N,W,Object.getOwnPropertyDescriptor(w,W))})}return N}c.exports=D,c.exports.__esModule=!0,c.exports.default=c.exports}(Lt)),Lt.exports}var mt={},In;function Rs(){return In||(In=1,Object.defineProperty(mt,"__esModule",{value:!0}),mt.commonLocale=void 0,mt.commonLocale={yearFormat:"YYYY",dayFormat:"D",cellMeridiemFormat:"A",monthBeforeYear:!0}),mt}var Dn;function Ls(){if(Dn)return xt;Dn=1;var c=zt().default;Object.defineProperty(xt,"__esModule",{value:!0}),xt.default=void 0;var S=c(_s()),L=Rs(),D=(0,S.default)((0,S.default)({},L.commonLocale),{},{locale:"zh_CN",today:"今天",now:"此刻",backToToday:"返回今天",ok:"确定",timeSelect:"选择时间",dateSelect:"选择日期",weekSelect:"选择周",clear:"清除",week:"周",month:"月",year:"年",previousMonth:"上个月 (翻页上键)",nextMonth:"下个月 (翻页下键)",monthSelect:"选择月份",yearSelect:"选择年份",decadeSelect:"选择年代",previousYear:"上一年 (Control键加左方向键)",nextYear:"下一年 (Control键加右方向键)",previousDecade:"上一年代",nextDecade:"下一年代",previousCentury:"上一世纪",nextCentury:"下一世纪",yearFormat:"YYYY年",cellDateFormat:"D",monthBeforeYear:!1});return xt.default=D,xt}var pt={},zn;function qn(){if(zn)return pt;zn=1,Object.defineProperty(pt,"__esModule",{value:!0}),pt.default=void 0;const c={placeholder:"请选择时间",rangePlaceholder:["开始时间","结束时间"]};return pt.default=c,pt}var Nn;function On(){if(Nn)return ut;Nn=1;var c=zt().default;Object.defineProperty(ut,"__esModule",{value:!0}),ut.default=void 0;var S=c(Ls()),L=c(qn());const D={lang:Object.assign({placeholder:"请选择日期",yearPlaceholder:"请选择年份",quarterPlaceholder:"请选择季度",monthPlaceholder:"请选择月份",weekPlaceholder:"请选择周",rangePlaceholder:["开始日期","结束日期"],rangeYearPlaceholder:["开始年份","结束年份"],rangeMonthPlaceholder:["开始月份","结束月份"],rangeQuarterPlaceholder:["开始季度","结束季度"],rangeWeekPlaceholder:["开始周","结束周"]},S.default),timePickerLocale:Object.assign({},L.default)};return D.lang.ok="确定",ut.default=D,ut}var An;function Ps(){if(An)return ht;An=1;var c=zt().default;Object.defineProperty(ht,"__esModule",{value:!0}),ht.default=void 0;var S=c(On());return ht.default=S.default,ht}var $n;function Bs(){if($n)return dt;$n=1;var c=zt().default;Object.defineProperty(dt,"__esModule",{value:!0}),dt.default=void 0;var S=c(Ns()),L=c(Ps()),D=c(On()),N=c(qn());const z="${label}不是一个有效的${type}",w={locale:"zh-cn",Pagination:S.default,DatePicker:D.default,TimePicker:N.default,Calendar:L.default,global:{placeholder:"请选择",close:"关闭"},Table:{filterTitle:"筛选",filterConfirm:"确定",filterReset:"重置",filterEmptyText:"无筛选项",filterCheckAll:"全选",filterSearchPlaceholder:"在筛选项中搜索",emptyText:"暂无数据",selectAll:"全选当页",selectInvert:"反选当页",selectNone:"清空所有",selectionAll:"全选所有",sortTitle:"排序",expand:"展开行",collapse:"关闭行",triggerDesc:"点击降序",triggerAsc:"点击升序",cancelSort:"取消排序"},Modal:{okText:"确定",cancelText:"取消",justOkText:"知道了"},Tour:{Next:"下一步",Previous:"上一步",Finish:"结束导览"},Popconfirm:{cancelText:"取消",okText:"确定"},Transfer:{titles:["",""],searchPlaceholder:"请输入搜索内容",itemUnit:"项",itemsUnit:"项",remove:"删除",selectCurrent:"全选当页",removeCurrent:"删除当页",selectAll:"全选所有",deselectAll:"取消全选",removeAll:"删除全部",selectInvert:"反选当页"},Upload:{uploading:"文件上传中",removeFile:"删除文件",uploadError:"上传错误",previewFile:"预览文件",downloadFile:"下载文件"},Empty:{description:"暂无数据"},Icon:{icon:"图标"},Text:{edit:"编辑",copy:"复制",copied:"复制成功",expand:"展开",collapse:"收起"},Form:{optional:"（可选）",defaultValidateMessages:{default:"字段验证错误${label}",required:"请输入${label}",enum:"${label}必须是其中一个[${enum}]",whitespace:"${label}不能为空字符",date:{format:"${label}日期格式无效",parse:"${label}不能转换为日期",invalid:"${label}是一个无效日期"},types:{string:z,method:z,array:z,object:z,number:z,date:z,boolean:z,integer:z,float:z,regexp:z,email:z,url:z,hex:z},string:{len:"${label}须为${len}个字符",min:"${label}最少${min}个字符",max:"${label}最多${max}个字符",range:"${label}须在${min}-${max}字符之间"},number:{len:"${label}必须等于${len}",min:"${label}最小值为${min}",max:"${label}最大值为${max}",range:"${label}须在${min}-${max}之间"},array:{len:"须为${len}个${label}",min:"最少${min}个${label}",max:"最多${max}个${label}",range:"${label}数量须在${min}-${max}之间"},pattern:{mismatch:"${label}与模式不匹配${pattern}"}}},Image:{preview:"预览"},QRCode:{expired:"二维码过期",refresh:"点击刷新",scanned:"已扫描"},ColorPicker:{presetEmpty:"暂无",transparent:"无色",singleColor:"单色",gradientColor:"渐变色"}};return dt.default=w,dt}var Et,Tn;function Fs(){return Tn||(Tn=1,Et=Bs()),Et}var Ms=Fs();const _n=ls(Ms);var kt={exports:{}},Es=kt.exports,Rn;function qs(){return Rn||(Rn=1,function(c,S){(function(L,D){c.exports=D(is())})(Es,function(L){function D(w){return w&&typeof w=="object"&&"default"in w?w:{default:w}}var N=D(L),z={name:"zh-cn",weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"周日_周一_周二_周三_周四_周五_周六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),ordinal:function(w,W){return W==="W"?w+"周":w+"日"},weekStart:1,yearStart:4,formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日Ah点mm分",LLLL:"YYYY年M月D日ddddAh点mm分",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},relativeTime:{future:"%s内",past:"%s前",s:"几秒",m:"1 分钟",mm:"%d 分钟",h:"1 小时",hh:"%d 小时",d:"1 天",dd:"%d 天",M:"1 个月",MM:"%d 个月",y:"1 年",yy:"%d 年"},meridiem:function(w,W){var V=100*w+W;return V<600?"凌晨":V<900?"早上":V<1100?"上午":V<1300?"中午":V<1800?"下午":"晚上"}};return N.default.locale(z,null,!0),z})}(kt)),kt.exports}qs();He.locale("zh-cn");const{Header:Os,Sider:Ws,Content:Ys}=qt,{Title:Oe,Text:l}=as,B=bs.create({baseURL:"http://fpm.nslemons.com"});B.interceptors.request.use(c=>{const S=localStorage.getItem("token");return S&&(c.headers.Authorization=`Bearer ${S}`),c});B.interceptors.response.use(c=>c,c=>{var S;return((S=c.response)==null?void 0:S.status)===401&&localStorage.removeItem("token"),Promise.reject(c)});const Us=({onLogin:c})=>{const[S,L]=a.useState(!1);a.useEffect(()=>{document.title="发票管理-登录"},[]);const D=async N=>{var z,w,W,V;L(!0);try{const he={emailOrUsername:N.email,password:N.password},A=await B.post("/api/auth/login",he);A.data.success&&(localStorage.setItem("token",A.data.data.token),k.success("登录成功！"),c())}catch(he){console.error("登录失败:",he),console.error("登录API调用失败:",(z=he.response)==null?void 0:z.status,(w=he.response)==null?void 0:w.data),k.error(((V=(W=he.response)==null?void 0:W.data)==null?void 0:V.message)||"登录失败，请检查网络连接或联系管理员")}finally{L(!1)}};return e.jsxs("div",{className:"login-container",children:[e.jsxs("div",{className:"animated-person",children:[e.jsx("div",{className:"person-head"}),e.jsx("div",{className:"person-body",children:e.jsx("div",{className:"person-arms"})}),e.jsxs("div",{className:"person-legs",children:[e.jsx("div",{className:"person-leg"}),e.jsx("div",{className:"person-leg"})]})]}),e.jsxs("div",{className:"floating-icons",children:[e.jsx("div",{className:"floating-icon",children:"📊"}),e.jsx("div",{className:"floating-icon",children:"💼"}),e.jsx("div",{className:"floating-icon",children:"📋"}),e.jsx("div",{className:"floating-icon",children:"💰"})]}),e.jsx("div",{className:"cloud cloud1"}),e.jsx("div",{className:"cloud cloud2"}),e.jsxs("div",{className:"particles",children:[e.jsx("div",{className:"particle"}),e.jsx("div",{className:"particle"}),e.jsx("div",{className:"particle"}),e.jsx("div",{className:"particle"}),e.jsx("div",{className:"particle"}),e.jsx("div",{className:"particle"}),e.jsx("div",{className:"particle"}),e.jsx("div",{className:"particle"}),e.jsx("div",{className:"particle"})]}),e.jsxs("div",{className:"geometric-shapes",children:[e.jsx("div",{className:"shape triangle"}),e.jsx("div",{className:"shape circle"}),e.jsx("div",{className:"shape square"})]}),e.jsxs("div",{className:"light-rays",children:[e.jsx("div",{className:"light-ray"}),e.jsx("div",{className:"light-ray"}),e.jsx("div",{className:"light-ray"})]}),e.jsx(C,{className:"login-card",style:{width:400,zIndex:10,position:"relative"},children:e.jsxs(we,{direction:"vertical",size:"large",style:{width:"100%"},children:[e.jsxs("div",{style:{textAlign:"center"},children:[e.jsx(Oe,{level:2,children:"发票管理系统"}),e.jsx(l,{type:"secondary",children:"请登录您的账户"})]}),e.jsxs(d,{onFinish:D,size:"large",children:[e.jsx(d.Item,{name:"email",rules:[{required:!0,message:"请输入用户名或邮箱地址!"}],children:e.jsx($,{prefix:e.jsx(Ct,{}),placeholder:"用户名或邮箱地址"})}),e.jsx(d.Item,{name:"password",rules:[{required:!0,message:"请输入密码!"}],children:e.jsx($.Password,{prefix:e.jsx(Ot,{}),placeholder:"密码"})}),e.jsx(d.Item,{children:e.jsx(f,{type:"primary",htmlType:"submit",loading:S,style:{width:"100%"},children:"登录"})})]})]})})]})},Ln=()=>{const c=Ht(),[S,L]=a.useState(null),[D,N]=a.useState(!0),[z,w]=a.useState([]),[W,V]=a.useState([]),[he,A]=a.useState([]),[ie,I]=a.useState(new Date().getFullYear()),[je,me]=a.useState("year"),[u,g]=a.useState([]),[Q,J]=a.useState(null),[xe,te]=a.useState(!1),[pe,m]=a.useState([]),[T,j]=a.useState(new Date().toLocaleString("zh-CN"));a.useEffect(()=>{q();const F=setInterval(()=>{q(!0)},3e4);return()=>clearInterval(F)},[ie,je]),a.useEffect(()=>{const F=setInterval(()=>{j(new Date().toLocaleString("zh-CN"))},1e3);return()=>clearInterval(F)},[]);const q=async(F=!1)=>{var P,ne,se,K,ye,Ce,Ue;try{F?te(!0):N(!0);const Ge=[B.get(`/api/invoices/stats/summary?year=${ie}`).catch(()=>({data:{data:null}})),B.get(`/api/invoices/stats/quarterly?year=${ie}`).catch(()=>({data:{data:{companies:[]}}})),B.get("/api/invoices?page=1&pageSize=20&sortBy=createdAt&sortOrder=desc").catch(()=>({data:{data:{data:[]}}})),B.get(`/api/invoices/stats/company-summary?years=${ie}`).catch(()=>({data:{data:{companies:[]}}}))],[Qe,M,re,p]=await Promise.all(Ge);console.log("API响应状态:",{stats:Qe.status||"error",quarterly:M.status||"error",invoices:re.status||"error",companySummary:p.status||"error"});const O=((P=Qe.data)==null?void 0:P.data)||{totalInvoices:0,totalAmount:0,statusStats:[]};L(O);const G=((se=(ne=M.data)==null?void 0:ne.data)==null?void 0:se.companies)||[];if(G&&G.length>0){const Te=[],Ne=["jan","feb","mar","apr","may","jun","jul","aug","sep","oct","nov","dec"],De=["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"];Ne.forEach((y,_)=>{const s=G.reduce((o,x)=>{const v=(x==null?void 0:x.monthly)||{};return o+(v[y]||0)},0);Te.push({month:De[_],amount:s,count:G.reduce((o,x)=>{const v=(x==null?void 0:x.monthly)||{};return o+(v[y]>0?1:0)},0)})}),w(Te);const i=G.map(y=>{const _=(y==null?void 0:y.monthly)||{},s=Object.values(_).reduce((o,x)=>o+(Number(x)||0),0);return{name:(y==null?void 0:y.name)||"未知公司",taxId:(y==null?void 0:y.taxId)||"",total:s,monthlyData:_}}).filter(y=>y.total>0).sort((y,_)=>_.total-y.total).slice(0,8);V(i)}else w([]),V([]);const de=((ye=(K=p.data)==null?void 0:K.data)==null?void 0:ye.companies)||[];if(de&&de.length>0){const Te=de.length,Ne=de.filter(y=>{const _=(y==null?void 0:y.yearlyData)||{};return Object.values(_).some(s=>Number(s)>0)}).length,De=de.reduce((y,_)=>{const s=(_==null?void 0:_.yearlyData)||{};return y+Object.values(s).reduce((o,x)=>o+(Number(x)||0),0)},0),i=Te>0?De/Te:0;J({totalCompanies:Te,activeCompanies:Ne,avgAmount:i,inactiveCompanies:Te-Ne})}else J({totalCompanies:0,activeCompanies:0,avgAmount:0,inactiveCompanies:0});const Re=((Ue=(Ce=re.data)==null?void 0:Ce.data)==null?void 0:Ue.data)||[];if(A(Re),G&&G.length>0){const Ne=Math.floor(new Date().getMonth()/3)+1,De=G.map(i=>{const y=(i==null?void 0:i.monthly)||{};let _=0,s=0;switch(s=Object.values(y).reduce((o,x)=>o+(Number(x)||0),0),Ne){case 1:_=(y.jan||0)+(y.feb||0)+(y.mar||0);break;case 2:_=(y.apr||0)+(y.may||0)+(y.jun||0);break;case 3:_=(y.jul||0)+(y.aug||0)+(y.sep||0);break;case 4:_=(y.oct||0)+(y.nov||0)+(y.dec||0);break}return{name:(i==null?void 0:i.name)||"未知公司",taxId:(i==null?void 0:i.taxId)||"",quarterlyAmount:_,yearlyAmount:s,isOverLimit:_>3e5,monthly:y}}).filter(i=>i.quarterlyAmount>0||i.yearlyAmount>0).sort((i,y)=>y.quarterlyAmount-i.quarterlyAmount);m(De)}else m([])}catch(Ge){console.error("获取仪表板数据失败:",Ge),F||k.error("获取仪表板数据失败"),L({totalInvoices:0,totalAmount:0,statusStats:[]}),w([]),V([]),A([]),J({totalCompanies:0,activeCompanies:0,avgAmount:0,inactiveCompanies:0})}finally{N(!1),te(!1)}},Z=F=>{if(!F||isNaN(F))return"¥0万";if(F>=1e4){const P=F/1e4;return P%1===0?`¥${P}万`:`¥${P.toFixed(2).replace(/\.?0+$/,"")}万`}return F.toLocaleString()},Ie=F=>{var ne;if(!(S!=null&&S.statusStats)||!Array.isArray(S.statusStats))return 0;const P=S.statusStats.find(se=>se.status===F);return((ne=P==null?void 0:P._count)==null?void 0:ne._all)||0},Me=(()=>{if(!z||z.length===0||!he)return{count:0,amount:0};const F=new Date().getMonth(),P=z[F];return{count:he.filter(se=>{try{return se!=null&&se.invoiceDate?new Date(se.invoiceDate).getMonth()===F:!1}catch{return!1}}).length,amount:(P==null?void 0:P.amount)||0}})();return D?e.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minHeight:"60vh",background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",borderRadius:"20px",margin:"24px",color:"white"},children:[e.jsx(us,{size:"large",style:{color:"white"}}),e.jsx("div",{style:{marginTop:24,fontSize:"18px",fontWeight:"bold"},children:"正在加载仪表板数据..."}),e.jsx("div",{style:{marginTop:8,opacity:.8},children:"请稍候，正在获取最新的发票统计信息"})]}):e.jsx("div",{style:{background:"linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)",height:"calc(100vh - 150px)",overflow:"hidden",display:"flex",flexDirection:"column"},children:e.jsxs("div",{style:{flex:1,overflow:"auto",padding:"16px"},children:[e.jsx("div",{style:{marginBottom:32,background:"rgba(255, 255, 255, 0.9)",borderRadius:"20px",padding:"24px",backdropFilter:"blur(10px)",boxShadow:"0 8px 32px rgba(0, 0, 0, 0.1)"},children:e.jsxs(R,{justify:"space-between",align:"middle",children:[e.jsx(r,{children:e.jsxs(Oe,{level:1,style:{margin:0,background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",fontSize:"32px",fontWeight:"bold"},children:[e.jsx(xs,{style:{marginRight:12,color:"#667eea"}}),T]})}),e.jsx(r,{children:e.jsxs(we,{size:"large",children:[e.jsx(f,{type:"primary",icon:e.jsx(ms,{}),onClick:()=>q(),style:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",border:"none",borderRadius:"12px",height:"40px",paddingLeft:"20px",paddingRight:"20px"},children:"刷新数据"}),e.jsx(ce,{value:ie,onChange:I,style:{width:120},size:"large",suffixIcon:e.jsx(ps,{}),children:Array.from({length:10},(F,P)=>{const ne=new Date().getFullYear()-P;return e.jsxs(ce.Option,{value:ne,children:[ne,"年"]},ne)})})]})})]})}),e.jsxs(R,{gutter:[24,24],style:{marginBottom:24},children:[e.jsx(r,{xs:24,sm:12,lg:6,children:e.jsxs(C,{hoverable:!0,style:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",border:"none",borderRadius:"20px",boxShadow:"0 10px 30px rgba(102, 126, 234, 0.3)",transition:"all 0.3s ease",overflow:"hidden",position:"relative"},styles:{body:{padding:"24px"}},children:[e.jsx("div",{style:{position:"absolute",top:"-20px",right:"-20px",width:"80px",height:"80px",background:"rgba(255,255,255,0.1)",borderRadius:"50%"}}),e.jsxs("div",{style:{position:"relative",zIndex:1},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start",marginBottom:16},children:[e.jsxs("div",{children:[e.jsx("div",{style:{color:"rgba(255,255,255,0.8)",fontSize:"14px",marginBottom:8},children:"发票总数"}),e.jsx("div",{style:{color:"white",fontSize:"32px",fontWeight:"bold",lineHeight:1},children:((S==null?void 0:S.totalInvoices)||0).toLocaleString()}),e.jsx("div",{style:{color:"rgba(255,255,255,0.6)",fontSize:"12px",marginTop:4},children:"全部发票数量统计"})]}),e.jsx("div",{style:{background:"rgba(255,255,255,0.2)",borderRadius:"12px",padding:"12px",backdropFilter:"blur(10px)"},children:e.jsx(Wt,{style:{color:"white",fontSize:"24px"}})})]}),e.jsx("div",{style:{background:"rgba(255,255,255,0.1)",height:"4px",borderRadius:"2px",overflow:"hidden"},children:e.jsx("div",{style:{background:"rgba(255,255,255,0.8)",height:"100%",width:"85%",borderRadius:"2px",transition:"width 1s ease"}})})]})]})}),e.jsx(r,{xs:24,sm:12,lg:6,children:e.jsxs(C,{hoverable:!0,style:{background:"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",border:"none",borderRadius:"20px",boxShadow:"0 10px 30px rgba(240, 147, 251, 0.3)",transition:"all 0.3s ease",overflow:"hidden",position:"relative"},styles:{body:{padding:"24px"}},children:[e.jsx("div",{style:{position:"absolute",top:"-20px",right:"-20px",width:"80px",height:"80px",background:"rgba(255,255,255,0.1)",borderRadius:"50%"}}),e.jsxs("div",{style:{position:"relative",zIndex:1},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start",marginBottom:16},children:[e.jsxs("div",{children:[e.jsx("div",{style:{color:"rgba(255,255,255,0.8)",fontSize:"14px",marginBottom:8},children:"总金额"}),e.jsx("div",{style:{color:"white",fontSize:"32px",fontWeight:"bold",lineHeight:1},children:Z((S==null?void 0:S.totalAmount)||0)}),e.jsx("div",{style:{color:"rgba(255,255,255,0.6)",fontSize:"12px",marginTop:4},children:"发票总金额统计"})]}),e.jsx("div",{style:{background:"rgba(255,255,255,0.2)",borderRadius:"12px",padding:"12px",backdropFilter:"blur(10px)"},children:e.jsx(ys,{style:{color:"white",fontSize:"24px"}})})]}),e.jsx("div",{style:{background:"rgba(255,255,255,0.1)",height:"4px",borderRadius:"2px",overflow:"hidden"},children:e.jsx("div",{style:{background:"rgba(255,255,255,0.8)",height:"100%",width:"92%",borderRadius:"2px",transition:"width 1s ease"}})})]})]})}),e.jsx(r,{xs:24,sm:12,lg:6,children:e.jsxs(C,{hoverable:!0,style:{background:"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",border:"none",borderRadius:"20px",boxShadow:"0 10px 30px rgba(79, 172, 254, 0.3)",transition:"all 0.3s ease",overflow:"hidden",position:"relative"},styles:{body:{padding:"24px"}},children:[e.jsx("div",{style:{position:"absolute",top:"-20px",right:"-20px",width:"80px",height:"80px",background:"rgba(255,255,255,0.1)",borderRadius:"50%"}}),e.jsxs("div",{style:{position:"relative",zIndex:1},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start",marginBottom:16},children:[e.jsxs("div",{children:[e.jsx("div",{style:{color:"rgba(255,255,255,0.8)",fontSize:"14px",marginBottom:8},children:"本月新增"}),e.jsx("div",{style:{color:"white",fontSize:"32px",fontWeight:"bold",lineHeight:1},children:Me.count}),e.jsxs("div",{style:{color:"rgba(255,255,255,0.6)",fontSize:"12px",marginTop:4},children:["金额: ",Z(Me.amount),"元"]})]}),e.jsx("div",{style:{background:"rgba(255,255,255,0.2)",borderRadius:"12px",padding:"12px",backdropFilter:"blur(10px)"},children:e.jsx(Dt,{style:{color:"white",fontSize:"24px"}})})]}),e.jsx("div",{style:{background:"rgba(255,255,255,0.1)",height:"4px",borderRadius:"2px",overflow:"hidden"},children:e.jsx("div",{style:{background:"rgba(255,255,255,0.8)",height:"100%",width:"68%",borderRadius:"2px",transition:"width 1s ease"}})})]})]})}),e.jsx(r,{xs:24,sm:12,lg:6,children:e.jsxs(C,{hoverable:!0,style:{background:"linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",border:"none",borderRadius:"20px",boxShadow:"0 10px 30px rgba(67, 233, 123, 0.3)",transition:"all 0.3s ease",overflow:"hidden",position:"relative"},styles:{body:{padding:"24px"}},children:[e.jsx("div",{style:{position:"absolute",top:"-20px",right:"-20px",width:"80px",height:"80px",background:"rgba(255,255,255,0.1)",borderRadius:"50%"}}),e.jsxs("div",{style:{position:"relative",zIndex:1},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start",marginBottom:16},children:[e.jsxs("div",{children:[e.jsx("div",{style:{color:"rgba(255,255,255,0.8)",fontSize:"14px",marginBottom:8},children:"正常发票"}),e.jsx("div",{style:{color:"white",fontSize:"32px",fontWeight:"bold",lineHeight:1},children:Ie("NORMAL").toLocaleString()}),e.jsx("div",{style:{color:"rgba(255,255,255,0.6)",fontSize:"12px",marginTop:4},children:"状态正常的发票"})]}),e.jsx("div",{style:{background:"rgba(255,255,255,0.2)",borderRadius:"12px",padding:"12px",backdropFilter:"blur(10px)"},children:e.jsx(Yt,{style:{color:"white",fontSize:"24px"}})})]}),e.jsx("div",{style:{background:"rgba(255,255,255,0.1)",height:"4px",borderRadius:"2px",overflow:"hidden"},children:e.jsx("div",{style:{background:"rgba(255,255,255,0.8)",height:"100%",width:`${Math.round(Ie("NORMAL")/((S==null?void 0:S.totalInvoices)||1)*100)}%`,borderRadius:"2px",transition:"width 1s ease"}})})]})]})})]}),e.jsx(R,{gutter:[16,16],style:{marginBottom:16},children:e.jsx(r,{xs:24,children:e.jsx(C,{title:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[e.jsx(ln,{style:{color:"#1890ff"}}),e.jsx("span",{children:"季度/年度公司开票统计"}),e.jsx("span",{style:{fontSize:"12px",color:"#999",marginLeft:"8px"},children:"(超过30万元的公司将以红色背景显示)"})]}),extra:e.jsx(f,{type:"link",onClick:()=>c("/reports/quarterly-summary"),children:"查看详情"}),style:{borderRadius:"12px"},children:e.jsx("div",{style:{overflow:"hidden"},children:pe.length>0?e.jsx("div",{style:{display:"flex",gap:"16px",overflowX:"auto",paddingBottom:"8px",scrollbarWidth:"thin"},children:pe.map((F,P)=>e.jsx("div",{style:{minWidth:"auto",maxWidth:"none",flexShrink:0,width:"fit-content"},children:e.jsx(C,{size:"small",hoverable:!0,style:{backgroundColor:F.isOverLimit?"rgba(255, 77, 79, 0.1)":"rgba(24, 144, 255, 0.05)",border:F.isOverLimit?"2px solid #ff4d4f":"1px solid #d9d9d9",borderRadius:"12px",transition:"all 0.3s ease",height:"120px",position:"relative"},styles:{body:{padding:"12px",position:"relative"}},children:e.jsxs("div",{style:{textAlign:"center",height:"100%",display:"flex",flexDirection:"column",justifyContent:"space-between"},children:[e.jsx("div",{style:{position:"absolute",top:"8px",right:"8px",width:"24px",height:"24px",borderRadius:"50%",backgroundColor:F.isOverLimit?"#ff4d4f":P<3?["#ffd700","#c0c0c0","#cd7f32"][P]:"#1890ff",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontSize:"12px",fontWeight:"bold",zIndex:1},children:P+1}),e.jsx("div",{style:{fontWeight:"bold",fontSize:"14px",color:F.isOverLimit?"#ff4d4f":"#333",marginBottom:"8px",paddingRight:"32px",wordWrap:"break-word",lineHeight:"1.2"},title:F.name,children:F.name}),e.jsxs("div",{style:{color:F.isOverLimit?"#ff4d4f":"#1890ff",fontWeight:"bold",fontSize:"16px",marginBottom:"4px"},children:["季度: ¥",(F.quarterlyAmount/1e4).toFixed(2),"万"]}),e.jsxs("div",{style:{color:"#666",fontSize:"14px",marginBottom:"8px"},children:["年度: ¥",(F.yearlyAmount/1e4).toFixed(2),"万"]}),F.isOverLimit&&e.jsx("div",{style:{fontSize:"12px",color:"#ff4d4f",backgroundColor:"rgba(255, 77, 79, 0.2)",padding:"2px 8px",borderRadius:"12px",alignSelf:"center"},children:"超限"})]})})},P))}):e.jsxs("div",{style:{textAlign:"center",color:"#999",padding:"40px 0"},children:[e.jsx(ln,{style:{fontSize:"48px",marginBottom:"16px"}}),e.jsx("div",{children:"暂无本季度开票数据"})]})})})})}),e.jsx(R,{gutter:[16,16],children:e.jsx(r,{xs:24,children:e.jsx(C,{title:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[e.jsx(an,{style:{color:"#1890ff"}}),e.jsxs("span",{children:[ie,"年季度开票汇总"]})]}),extra:e.jsx(f,{type:"link",onClick:()=>c("/reports/quarterly-summary"),children:"查看详情"}),style:{borderRadius:"12px"},children:pe.length>0?e.jsx(h,{columns:[{title:"序号",key:"index",width:80,align:"center",render:(F,P,ne)=>ne+1},{title:"公司名称",dataIndex:"name",key:"name",width:300,fixed:"left"},{title:"一季度",key:"q1",width:150,align:"right",className:"quarterly-column",onHeaderCell:()=>({style:{backgroundColor:"#bae7ff",fontWeight:"bold"}}),onCell:F=>{var se,K,ye;const P=(((se=F.monthly)==null?void 0:se.jan)||0)+(((K=F.monthly)==null?void 0:K.feb)||0)+(((ye=F.monthly)==null?void 0:ye.mar)||0);let ne="#e6f7ff";return P>=25e4&&P<3e5?ne="rgba(255, 165, 0, 0.8)":P>=3e5&&(ne="rgba(255, 0, 0, 0.7)"),{style:{backgroundColor:ne}}},render:(F,P)=>{var se,K,ye;const ne=(((se=P.monthly)==null?void 0:se.jan)||0)+(((K=P.monthly)==null?void 0:K.feb)||0)+(((ye=P.monthly)==null?void 0:ye.mar)||0);return e.jsx("span",{style:{fontWeight:"bold"},children:Z(ne)})}},{title:"二季度",key:"q2",width:150,align:"right",className:"quarterly-column",onHeaderCell:()=>({style:{backgroundColor:"#bae7ff",fontWeight:"bold"}}),onCell:F=>{var se,K,ye;const P=(((se=F.monthly)==null?void 0:se.apr)||0)+(((K=F.monthly)==null?void 0:K.may)||0)+(((ye=F.monthly)==null?void 0:ye.jun)||0);let ne="#e6f7ff";return P>=25e4&&P<3e5?ne="rgba(255, 165, 0, 0.8)":P>=3e5&&(ne="rgba(255, 0, 0, 0.7)"),{style:{backgroundColor:ne}}},render:(F,P)=>{var se,K,ye;const ne=(((se=P.monthly)==null?void 0:se.apr)||0)+(((K=P.monthly)==null?void 0:K.may)||0)+(((ye=P.monthly)==null?void 0:ye.jun)||0);return e.jsx("span",{style:{fontWeight:"bold"},children:Z(ne)})}},{title:"三季度",key:"q3",width:150,align:"right",className:"quarterly-column",onHeaderCell:()=>({style:{backgroundColor:"#bae7ff",fontWeight:"bold"}}),onCell:F=>{var se,K,ye;const P=(((se=F.monthly)==null?void 0:se.jul)||0)+(((K=F.monthly)==null?void 0:K.aug)||0)+(((ye=F.monthly)==null?void 0:ye.sep)||0);let ne="#e6f7ff";return P>=25e4&&P<3e5?ne="rgba(255, 165, 0, 0.8)":P>=3e5&&(ne="rgba(255, 0, 0, 0.7)"),{style:{backgroundColor:ne}}},render:(F,P)=>{var se,K,ye;const ne=(((se=P.monthly)==null?void 0:se.jul)||0)+(((K=P.monthly)==null?void 0:K.aug)||0)+(((ye=P.monthly)==null?void 0:ye.sep)||0);return e.jsx("span",{style:{fontWeight:"bold"},children:Z(ne)})}},{title:"四季度",key:"q4",width:150,align:"right",className:"quarterly-column",onHeaderCell:()=>({style:{backgroundColor:"#bae7ff",fontWeight:"bold"}}),onCell:F=>{var se,K,ye;const P=(((se=F.monthly)==null?void 0:se.oct)||0)+(((K=F.monthly)==null?void 0:K.nov)||0)+(((ye=F.monthly)==null?void 0:ye.dec)||0);let ne="#e6f7ff";return P>=25e4&&P<3e5?ne="rgba(255, 165, 0, 0.8)":P>=3e5&&(ne="rgba(255, 0, 0, 0.7)"),{style:{backgroundColor:ne}}},render:(F,P)=>{var se,K,ye;const ne=(((se=P.monthly)==null?void 0:se.oct)||0)+(((K=P.monthly)==null?void 0:K.nov)||0)+(((ye=P.monthly)==null?void 0:ye.dec)||0);return e.jsx("span",{style:{fontWeight:"bold"},children:Z(ne)})}},{title:"年度总计",key:"total",width:150,align:"right",fixed:"right",onHeaderCell:()=>({style:{backgroundColor:"#52c41a",color:"white",fontWeight:"bold"}}),onCell:()=>({style:{backgroundColor:"#f6ffed"}}),render:(F,P)=>e.jsx("span",{style:{fontWeight:"bold",color:"#52c41a",fontSize:"16px"},children:Z(P.yearlyAmount||0)})}],dataSource:pe.slice(0,10),rowKey:F=>F.id||F.name,pagination:!1,scroll:{x:1e3},size:"small",bordered:!0,locale:{emptyText:"暂无数据，请先在开票管理中添加发票数据"}}):e.jsxs("div",{style:{textAlign:"center",color:"#999",padding:"40px 0"},children:[e.jsx(an,{style:{fontSize:"48px",marginBottom:"16px"}}),e.jsxs("div",{children:["暂无",ie,"年季度开票数据"]})]})})})})]})})},Vs=({userInfo:c})=>{var Qe;const[S,L]=a.useState([]),[D,N]=a.useState(!0),[z,w]=a.useState(!1),[W,V]=a.useState(!1),[he,A]=a.useState(null),[ie,I]=a.useState(""),[je,me]=a.useState(""),[u,g]=a.useState(!1),[Q,J]=a.useState(null),[xe,te]=a.useState(!1),[pe,m]=a.useState(!1),[T,j]=a.useState(null),[q,Z]=a.useState({current:1,pageSize:20,total:0}),[Ie]=d.useForm();a.useEffect(()=>{$e()},[]);const $e=async(M="",re="",p=1,O)=>{var G;try{N(!0);const de=O||q.pageSize,Re=new URLSearchParams({page:p.toString(),pageSize:de.toString()});M&&Re.append("search",M),re&&Re.append("organization",re);const Ne=(await B.get(`/api/companies?${Re}`)).data.data;L((Ne==null?void 0:Ne.data)||Ne||[]),Z({current:p,pageSize:de,total:((G=Ne==null?void 0:Ne.pagination)==null?void 0:G.total)||(Ne==null?void 0:Ne.total)||0})}catch(de){console.error("获取公司列表失败:",de),k.error("获取公司列表失败")}finally{N(!1)}},Me=M=>{I(M),$e(M,je,1)},F=M=>{me(M),$e(ie,M,1)},P=async M=>{var p,O;te(!0),J(null);const re=new FormData;re.append("file",M);try{const de=(await B.post("/api/companies/import",re,{headers:{"Content-Type":"multipart/form-data"}})).data.data;J(de),de.successCount>0?k.success(`公司导入成功！成功导入 ${de.successCount} 条记录`):k.success(`公司导入完成！成功导入 ${de.successCount} 条记录`),$e(ie,je)}catch(G){console.error("公司导入失败:",G),k.error(((O=(p=G.response)==null?void 0:p.data)==null?void 0:O.message)||"公司导入失败")}finally{te(!1)}},ne=async()=>{try{const M=await B.get("/api/companies/template",{responseType:"blob"}),re=new Blob([M.data],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),p=window.URL.createObjectURL(re),O=document.createElement("a");O.href=p,O.download="公司导入模板.xlsx",document.body.appendChild(O),O.click(),document.body.removeChild(O),window.URL.revokeObjectURL(p),k.success("模板下载成功！")}catch(M){console.error("下载模板失败:",M),k.error("下载模板失败")}},se=M=>{A(M);const re={...M,registrationDate:M.registrationDate?He(M.registrationDate):null,lastInvoiceDate:M.lastInvoiceDate?He(M.lastInvoiceDate):null};Ie.setFieldsValue(re),w(!0)},K=()=>{A(null),Ie.resetFields(),Ie.setFieldsValue({organization:(c==null?void 0:c.name)||""}),V(!0)},ye=M=>{j(M),m(!0)},Ce=async M=>{try{await B.delete(`/api/companies/${M.id}`),k.success("公司删除成功！"),$e(ie,je)}catch(re){console.error("删除公司失败:",re),k.error("删除公司失败")}},Ue=async()=>{var M,re,p,O,G,de,Re,Te,Ne;try{const De=await Ie.validateFields(),i={...De,registrationDate:De.registrationDate?De.registrationDate.toISOString():null,lastInvoiceDate:De.lastInvoiceDate?De.lastInvoiceDate.toISOString():null,email:((M=De.email)==null?void 0:M.trim())||null,phone:((re=De.phone)==null?void 0:re.trim())||null,address:((p=De.address)==null?void 0:p.trim())||null,contact:((O=De.contact)==null?void 0:O.trim())||null,organization:((G=De.organization)==null?void 0:G.trim())||null,remarks:((de=De.remarks)==null?void 0:de.trim())||null};Object.keys(i).forEach(y=>{i[y]===void 0&&delete i[y]}),he?(await B.put(`/api/companies/${he.id}`,i),k.success("公司信息更新成功！")):(await B.post("/api/companies",i),k.success("公司添加成功！")),w(!1),V(!1),A(null),Ie.resetFields(),$e(ie,je)}catch(De){console.error("保存公司失败:",De),console.error("错误详情:",(Re=De.response)==null?void 0:Re.data);const i=((Ne=(Te=De.response)==null?void 0:Te.data)==null?void 0:Ne.message)||"保存失败";k.error(i)}},Ge=[{title:"序号",key:"index",width:60,render:(M,re,p)=>p+1},{title:"公司名称",dataIndex:"name",key:"name"},{title:"税号",dataIndex:"taxId",key:"taxId"},{title:"所属用户",dataIndex:"organization",key:"organization"},{title:"联系人",dataIndex:"contact",key:"contact"},{title:"电话",dataIndex:"phone",key:"phone"},{title:"最新发票日期",dataIndex:"latestInvoiceDate",key:"latestInvoiceDate",render:M=>M?new Date(M).toLocaleDateString("zh-CN"):"-"},{title:"状态",dataIndex:"isActive",key:"isActive",render:M=>e.jsx(ze,{color:M?"success":"error",children:M?"激活":"禁用"})},{title:"注册日期",dataIndex:"registrationDate",key:"registrationDate",render:M=>M?new Date(M).toLocaleDateString():"-"},{title:"地址",dataIndex:"address",key:"address"},{title:"操作",key:"action",render:(M,re)=>{var p,O;return e.jsxs(we,{children:[e.jsx(f,{type:"link",icon:e.jsx(Vt,{}),onClick:()=>ye(re),children:"详情"}),((p=re.permissions)==null?void 0:p.canEdit)&&e.jsx(f,{type:"link",icon:e.jsx(It,{}),onClick:()=>se(re),children:"编辑"}),((O=re.permissions)==null?void 0:O.canDelete)&&e.jsx(Bn,{title:"确定要删除这个公司吗？",onConfirm:()=>Ce(re),children:e.jsx(f,{type:"link",danger:!0,icon:e.jsx(yt,{}),children:"删除"})})]})}}];return e.jsxs("div",{children:[e.jsxs("div",{style:{marginBottom:16},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:16},children:[e.jsx(Oe,{level:2,children:"公司管理"}),e.jsxs(we,{children:[e.jsx(f,{icon:e.jsx(tt,{}),onClick:ne,children:"导出模板"}),e.jsx(f,{icon:e.jsx(rt,{}),onClick:()=>g(!0),children:"批量导入"}),e.jsx(f,{type:"primary",icon:e.jsx(Dt,{}),onClick:K,children:"添加公司"})]})]}),e.jsxs("div",{style:{display:"flex",gap:16,alignItems:"center"},children:[e.jsx($.Search,{placeholder:"搜索公司名称或税号",allowClear:!0,style:{width:300},value:ie,onSearch:Me,onChange:M=>{M.target.value||Me("")}}),e.jsx($,{placeholder:"过滤所属用户",allowClear:!0,style:{width:200},value:je,onChange:M=>{F(M.target.value)},prefix:e.jsx(st,{})})]})]}),e.jsx(C,{children:e.jsx(h,{columns:Ge,dataSource:S,rowKey:"id",loading:D,size:"small",pagination:{current:q.current,pageSize:q.pageSize,total:q.total,showSizeChanger:!0,showQuickJumper:!0,showTotal:(M,re)=>`共 ${M} 条记录，第 ${re[0]}-${re[1]} 条`,pageSizeOptions:["10","20","50","100"],onChange:(M,re)=>{re!==q.pageSize?(Z(p=>({...p,pageSize:re,current:1})),$e(ie,je,1,re)):$e(ie,je,M)},onShowSizeChange:(M,re)=>{Z(p=>({...p,pageSize:re,current:1})),$e(ie,je,1,re)}}})}),e.jsx(Se,{title:"编辑公司",open:z,onOk:Ue,onCancel:()=>{w(!1),Ie.resetFields()},width:800,children:e.jsxs(d,{form:Ie,layout:"horizontal",labelCol:{span:6},wrapperCol:{span:18},children:[e.jsxs(R,{gutter:16,children:[e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"公司名称",name:"name",rules:[{required:!0,message:"请输入公司名称"}],children:e.jsx($,{})})}),e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"税号",name:"taxId",rules:[{required:!0,message:"请输入税号"}],children:e.jsx($,{})})})]}),e.jsxs(R,{gutter:16,children:[e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"所属用户",name:"organization",children:e.jsx($,{placeholder:"所属用户姓名"})})}),e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"联系人",name:"contact",children:e.jsx($,{})})})]}),e.jsxs(R,{gutter:16,children:[e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"电话",name:"phone",children:e.jsx($,{})})}),e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"邮箱",name:"email",rules:[{type:"email",message:"请输入有效的邮箱地址"}],children:e.jsx($,{placeholder:"请输入邮箱地址（可选）"})})})]}),e.jsx(R,{gutter:16,children:e.jsx(r,{span:24,children:e.jsx(d.Item,{label:"地址",name:"address",labelCol:{span:3},wrapperCol:{span:21},children:e.jsx($,{})})})}),e.jsxs(R,{gutter:16,children:[e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"注册日期",name:"registrationDate",children:e.jsx(Ke,{style:{width:"100%"},placeholder:"选择注册日期",format:"YYYY-MM-DD"})})}),e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"状态",name:"isActive",valuePropName:"checked",children:e.jsx(on,{checkedChildren:"激活",unCheckedChildren:"禁用"})})})]}),e.jsx(R,{gutter:16,children:e.jsx(r,{span:24,children:e.jsx(d.Item,{label:"备注",name:"remarks",labelCol:{span:3},wrapperCol:{span:21},children:e.jsx($.TextArea,{rows:12,placeholder:"请输入备注信息..."})})})})]})}),e.jsx(Se,{title:"添加公司",open:W,onOk:Ue,onCancel:()=>{V(!1),Ie.resetFields()},width:800,children:e.jsxs(d,{form:Ie,layout:"horizontal",labelCol:{span:6},wrapperCol:{span:18},children:[e.jsxs(R,{gutter:16,children:[e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"公司名称",name:"name",rules:[{required:!0,message:"请输入公司名称"}],children:e.jsx($,{})})}),e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"税号",name:"taxId",rules:[{required:!0,message:"请输入税号"}],children:e.jsx($,{})})})]}),e.jsxs(R,{gutter:16,children:[e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"所属用户",name:"organization",children:e.jsx($,{placeholder:"所属用户姓名"})})}),e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"联系人",name:"contact",children:e.jsx($,{})})})]}),e.jsxs(R,{gutter:16,children:[e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"电话",name:"phone",children:e.jsx($,{})})}),e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"邮箱",name:"email",rules:[{type:"email",message:"请输入有效的邮箱地址"}],children:e.jsx($,{placeholder:"请输入邮箱地址（可选）"})})})]}),e.jsx(R,{gutter:16,children:e.jsx(r,{span:24,children:e.jsx(d.Item,{label:"地址",name:"address",labelCol:{span:3},wrapperCol:{span:21},children:e.jsx($,{})})})}),e.jsxs(R,{gutter:16,children:[e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"注册日期",name:"registrationDate",children:e.jsx(Ke,{style:{width:"100%"},placeholder:"选择注册日期",format:"YYYY-MM-DD"})})}),e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"状态",name:"isActive",valuePropName:"checked",initialValue:!0,children:e.jsx(on,{checkedChildren:"激活",unCheckedChildren:"禁用"})})})]}),e.jsx(R,{gutter:16,children:e.jsx(r,{span:24,children:e.jsx(d.Item,{label:"备注",name:"remarks",labelCol:{span:3},wrapperCol:{span:21},children:e.jsx($.TextArea,{rows:12,placeholder:"请输入备注信息..."})})})})]})}),e.jsx(Se,{title:"批量导入公司",open:u,onCancel:()=>{g(!1),J(null)},footer:[e.jsx(f,{onClick:()=>{g(!1),J(null)},children:"关闭"},"close")],width:800,children:e.jsxs(we,{direction:"vertical",size:"large",style:{width:"100%"},children:[e.jsxs("div",{children:[e.jsx(Oe,{level:4,children:"导入步骤"}),e.jsxs("ol",{children:[e.jsx("li",{children:"下载公司导入模板"}),e.jsx("li",{children:"按照模板格式填写公司数据"}),e.jsx("li",{children:"上传填写好的Excel文件"})]})]}),e.jsx("div",{children:e.jsxs(we,{children:[e.jsx(f,{type:"primary",icon:e.jsx(rt,{}),onClick:ne,children:"下载模板"}),e.jsx("input",{type:"file",accept:".xlsx,.xls",onChange:M=>{var p;const re=(p=M.target.files)==null?void 0:p[0];re&&P(re)},style:{display:"none"},id:"company-excel-upload"}),e.jsx(f,{type:"primary",icon:e.jsx(rt,{}),loading:xe,onClick:()=>{var M;return(M=document.getElementById("company-excel-upload"))==null?void 0:M.click()},children:xe?"上传中...":"上传Excel文件"})]})}),Q&&e.jsxs(C,{title:"导入结果",style:{marginTop:16},children:[e.jsxs(R,{gutter:16,children:[e.jsx(r,{span:6,children:e.jsx(ue,{title:"总记录数",value:Q.totalCount})}),e.jsx(r,{span:6,children:e.jsx(ue,{title:"成功导入",value:Q.successCount,valueStyle:{color:"#3f8600"}})}),e.jsx(r,{span:6,children:e.jsx(ue,{title:"失败记录",value:Q.failureCount,valueStyle:{color:"#cf1322"}})}),e.jsx(r,{span:6,children:e.jsx(ue,{title:"重复记录",value:Q.duplicateCount,valueStyle:{color:"#fa8c16"}})})]}),Q.errors&&Q.errors.length>0&&e.jsxs("div",{style:{marginTop:16},children:[e.jsx(Oe,{level:5,children:"错误详情"}),e.jsx(_e,{size:"small",dataSource:Q.errors,renderItem:(M,re)=>e.jsx(_e.Item,{children:e.jsxs(l,{type:"danger",children:["第 ",M.row," 行: ",M.message]})})})]})]})]})}),e.jsx(Se,{title:"公司详情",open:pe,onCancel:()=>m(!1),footer:[e.jsx(f,{type:"primary",icon:e.jsx(It,{}),onClick:()=>{m(!1),se(T)},children:"编辑"},"edit"),e.jsx(f,{onClick:()=>m(!1),children:"关闭"},"close")],width:800,children:T&&e.jsxs("div",{children:[e.jsx(C,{title:"基本信息",size:"small",style:{marginBottom:16},children:e.jsxs(R,{gutter:[16,16],children:[e.jsx(r,{span:12,children:e.jsxs("div",{style:{display:"flex",marginBottom:12},children:[e.jsx(l,{type:"secondary",style:{width:"80px",flexShrink:0},children:"公司名称:"}),e.jsx(l,{strong:!0,style:{fontSize:"16px"},children:T.name})]})}),e.jsx(r,{span:12,children:e.jsxs("div",{style:{display:"flex",marginBottom:12},children:[e.jsx(l,{type:"secondary",style:{width:"80px",flexShrink:0},children:"税号:"}),e.jsx(l,{strong:!0,children:T.taxId})]})}),e.jsx(r,{span:12,children:e.jsxs("div",{style:{display:"flex",marginBottom:12},children:[e.jsx(l,{type:"secondary",style:{width:"80px",flexShrink:0},children:"所属用户:"}),e.jsx(l,{children:T.organization||"-"})]})}),e.jsx(r,{span:12,children:e.jsxs("div",{style:{display:"flex",marginBottom:12},children:[e.jsx(l,{type:"secondary",style:{width:"80px",flexShrink:0},children:"联系人:"}),e.jsx(l,{children:T.contact||"-"})]})})]})}),e.jsx(C,{title:"联系信息",size:"small",style:{marginBottom:16},children:e.jsxs(R,{gutter:[16,16],children:[e.jsx(r,{span:12,children:e.jsxs("div",{style:{display:"flex",marginBottom:12},children:[e.jsx(l,{type:"secondary",style:{width:"80px",flexShrink:0},children:"电话:"}),e.jsx(l,{children:T.phone||"-"})]})}),e.jsx(r,{span:12,children:e.jsxs("div",{style:{display:"flex",marginBottom:12},children:[e.jsx(l,{type:"secondary",style:{width:"80px",flexShrink:0},children:"邮箱:"}),e.jsx(l,{children:T.email||"-"})]})}),e.jsx(r,{span:24,children:e.jsxs("div",{style:{display:"flex",marginBottom:12},children:[e.jsx(l,{type:"secondary",style:{width:"80px",flexShrink:0},children:"地址:"}),e.jsx(l,{children:T.address||"-"})]})})]})}),T.remarks&&e.jsx(C,{title:"备注信息",size:"small",style:{marginBottom:16},children:e.jsx("div",{style:{padding:"12px",backgroundColor:"#fafafa",borderRadius:"6px",border:"1px solid #d9d9d9"},children:e.jsx(l,{children:T.remarks})})}),T.registrationDate&&e.jsx(C,{title:"重要日期",size:"small",style:{marginBottom:16},children:e.jsx(R,{gutter:[16,16],children:e.jsx(r,{span:12,children:e.jsxs("div",{style:{display:"flex",marginBottom:12},children:[e.jsx(l,{type:"secondary",style:{width:"100px",flexShrink:0},children:"注册日期:"}),e.jsx(l,{children:new Date(T.registrationDate).toLocaleDateString()})]})})})}),e.jsx(C,{title:"统计信息",size:"small",children:e.jsxs(R,{gutter:16,children:[e.jsx(r,{span:8,children:e.jsx(ue,{title:"关联发票数量",value:((Qe=T._count)==null?void 0:Qe.invoices)||0,suffix:"张",valueStyle:{color:"#1890ff"}})}),e.jsx(r,{span:8,children:e.jsx(ue,{title:"创建时间",value:T.createdAt?new Date(T.createdAt).toLocaleDateString():"-",valueStyle:{color:"#666"}})}),e.jsx(r,{span:8,children:e.jsx(ue,{title:"更新时间",value:T.updatedAt?new Date(T.updatedAt).toLocaleDateString():"-",valueStyle:{color:"#666"}})})]})})]})})]})},Hs=()=>{var De;const[c,S]=a.useState([]),[L,D]=a.useState(!0),[N,z]=a.useState(!1),[w,W]=a.useState(!1),[V,he]=a.useState(!1),[A,ie]=a.useState(null),[I,je]=a.useState(null),[me,u]=a.useState([]),[g,Q]=a.useState(null),[J,xe]=a.useState(!1),[te,pe]=a.useState({current:1,pageSize:20,total:0}),m=()=>{const i=new Date,y=i.getFullYear(),_=i.getMonth(),o=Math.floor(_/3)*3;return`${y}-${String(o+1).padStart(2,"0")}-01`},T=()=>new Date().toISOString().split("T")[0],[j,q]=a.useState({search:"",startDate:m(),endDate:T(),status:"",sellerCompanies:[]}),[Z,Ie]=a.useState([]),[$e,Me]=a.useState(!1),[F,P]=a.useState(""),[ne,se]=a.useState(""),[K]=d.useForm();a.useEffect(()=>{Ce(),ye()},[]);const ye=async()=>{try{const y=(await B.get("/api/companies/active")).data.data||[];Ie(Array.isArray(y)?y:[])}catch(i){console.error("获取公司列表失败:",i)}},Ce=async(i={},y=1,_)=>{var s;try{D(!0);const o=_||te.pageSize,x=new URLSearchParams({page:y.toString(),pageSize:o.toString()}),v={...j,...i};v.search&&x.append("search",v.search),v.startDate&&x.append("startDate",v.startDate),v.endDate&&x.append("endDate",v.endDate),v.status&&x.append("status",v.status),v.sellerCompanies&&Array.isArray(v.sellerCompanies)&&v.sellerCompanies.length>0&&v.sellerCompanies.forEach(We=>{x.append("sellerCompanies",We)});const fe=(await B.get(`/api/received-invoices?${x}`)).data.data;S(fe.data||[]),pe({current:y,pageSize:o,total:((s=fe.pagination)==null?void 0:s.total)||0})}catch(o){console.error("获取取得发票列表失败:",o),k.error("获取取得发票列表失败")}finally{D(!1)}},Ue=()=>{Ce(j,1)},Ge=()=>{q({search:"",startDate:m(),endDate:T(),status:"",sellerCompanies:[]}),Ce({},1)},Qe=i=>{Ce(j,i.current)},M=i=>({NORMAL:"green",CANCELLED:"red"})[i]||"default",re=i=>({NORMAL:"正常",CANCELLED:"作废"})[i]||i,p=async i=>{try{const _=(await B.get(`/api/received-invoices/${i.id}`)).data.data;ie(_),u(_.receivedInvoiceItems||[]);const s={..._,invoiceDate:_.invoiceDate?_.invoiceDate.split("T")[0]:""};K.setFieldsValue(s),W(!0)}catch{k.error("获取取得发票详情失败")}},O=async i=>{try{await B.delete(`/api/received-invoices/${i.id}`),k.success("取得发票删除成功！"),Ce()}catch{k.error("删除取得发票失败")}},G=async()=>{try{const y={...await K.validateFields(),receivedInvoiceItems:me};A?(await B.put(`/api/received-invoices/${A.id}`,y),k.success("取得发票更新成功！")):(await B.post("/api/received-invoices",y),k.success("取得发票添加成功！")),W(!1),z(!1),ie(null),u([]),K.resetFields(),Ce()}catch(i){console.error("保存取得发票失败:",i),k.error("保存失败")}},de=async i=>{var y,_;try{const s=await B.get(`/api/received-invoices/${i.id}`);je(s.data.data),he(!0)}catch(s){k.error(((_=(y=s.response)==null?void 0:y.data)==null?void 0:_.message)||"获取取得发票详情失败")}},Re=async(i,y)=>{var _,s;try{const o=new FormData;o.append("file",y),o.append("invoiceId",i),o.append("fileType","IMAGE"),o.append("isOriginal","true"),o.append("invoiceType","received");const x=await B.post("/api/upload/invoice-attachment",o,{headers:{"Content-Type":"multipart/form-data"}});return k.success("发票文件上传成功！"),x.data.data}catch(o){throw console.error("发票文件上传失败:",o),k.error(((s=(_=o.response)==null?void 0:_.data)==null?void 0:s.message)||"发票文件上传失败"),o}},Te=async i=>{var y,_,s,o;try{const v=(await B.get(`/api/received-invoices/${i.id}`)).data.data,X=v.attachments||[];Q({...v,attachments:X}),xe(!0)}catch(x){console.error("查看发票文件失败:",x),console.error("错误详情:",(y=x.response)==null?void 0:y.data),console.error("错误状态码:",(_=x.response)==null?void 0:_.status),k.error(((o=(s=x.response)==null?void 0:s.data)==null?void 0:o.message)||"查看发票文件失败")}},Ne=[{title:"序号",key:"index",width:50,fixed:"left",render:(i,y,_)=>(te.current-1)*te.pageSize+_+1},{title:"发票号码",dataIndex:"invoiceNumber",key:"invoiceNumber",width:120,fixed:"left"},{title:"发票代码",dataIndex:"invoiceCode",key:"invoiceCode",width:110},{title:"开票日期",dataIndex:"invoiceDate",key:"invoiceDate",width:90,render:i=>i==null?void 0:i.split("T")[0]},{title:"购买方",dataIndex:"buyerName",key:"buyerName",width:180},{title:"销售方",dataIndex:"sellerName",key:"sellerName",width:180},{title:"金额",dataIndex:"amount",key:"amount",width:100,align:"right",render:i=>`¥${(i==null?void 0:i.toLocaleString())||0}`},{title:"税额",dataIndex:"taxAmount",key:"taxAmount",width:100,align:"right",render:i=>`¥${(i==null?void 0:i.toLocaleString())||0}`},{title:"价税合计",dataIndex:"totalAmount",key:"totalAmount",width:110,align:"right",render:i=>`¥${(i==null?void 0:i.toLocaleString())||0}`},{title:"状态",dataIndex:"status",key:"status",width:70,render:i=>e.jsx(ze,{color:M(i),children:re(i)})},{title:"操作",key:"action",width:180,fixed:"right",render:(i,y)=>e.jsxs(we,{children:[e.jsx(f,{type:"link",size:"small",onClick:()=>de(y),children:"详情"}),e.jsx(f,{type:"link",size:"small",icon:e.jsx(it,{}),onClick:()=>Te(y),children:"查看发票"}),e.jsx(Ut,{menu:{items:[{key:"edit",label:"编辑",icon:e.jsx(It,{}),onClick:()=>p(y)},{key:"delete",label:"删除",icon:e.jsx(yt,{}),danger:!0,onClick:()=>{Se.confirm({title:"确认删除",content:"确定要删除这条取得发票记录吗？",okText:"确定",cancelText:"取消",onOk:()=>O(y)})}}]},trigger:["click"],children:e.jsxs(f,{type:"link",size:"small",children:["操作 ",e.jsx("span",{style:{fontSize:"10px"},children:"▼"})]})})]})}];return e.jsxs("div",{children:[e.jsx(C,{style:{marginBottom:16},children:e.jsxs(d,{layout:"inline",children:[e.jsx(d.Item,{label:"搜索",children:e.jsx($,{placeholder:"发票号码、购买方、税号",style:{width:250},value:j.search,onChange:i=>q({...j,search:i.target.value}),allowClear:!0})}),e.jsx(d.Item,{label:"销售方",children:e.jsx(ce,{mode:"tags",placeholder:"选择或输入销售方",style:{width:250},value:j.sellerCompanies,onChange:i=>{const y=Array.isArray(i)?i:i?[i]:[];q({...j,sellerCompanies:y})},allowClear:!0,showSearch:!0,filterOption:(i,y)=>{var _;return(_=String((y==null?void 0:y.children)||""))==null?void 0:_.toLowerCase().includes(i.toLowerCase())},tokenSeparators:[","],children:Array.isArray(Z)&&Z.map(i=>e.jsx(ce.Option,{value:i.name,children:i.name},i.id))})}),e.jsxs(d.Item,{label:"开票日期",children:[e.jsx(Ke,{value:j.startDate?He(j.startDate):null,onChange:i=>q({...j,startDate:i?i.format("YYYY-MM-DD"):""}),size:"small",style:{width:150}}),e.jsx("span",{style:{margin:"0 8px"},children:"至"}),e.jsx(Ke,{value:j.endDate?He(j.endDate):null,onChange:i=>q({...j,endDate:i?i.format("YYYY-MM-DD"):""}),size:"small",style:{width:150}})]}),e.jsx(d.Item,{label:"状态",children:e.jsxs("select",{value:j.status,onChange:i=>q({...j,status:i.target.value}),style:{width:120,height:32,border:"1px solid #d9d9d9",borderRadius:6,padding:"0 8px"},children:[e.jsx("option",{value:"",children:"全部"}),e.jsx("option",{value:"NORMAL",children:"正常"}),e.jsx("option",{value:"CANCELLED",children:"作废"})]})}),e.jsx(d.Item,{children:e.jsxs(we,{children:[e.jsx(f,{type:"primary",onClick:Ue,children:"搜索"}),e.jsx(f,{onClick:Ge,children:"重置"})]})})]})}),e.jsx(C,{style:{height:"calc(100vh - 280px)",display:"flex",flexDirection:"column"},children:e.jsx("div",{style:{flex:1,overflow:"hidden"},children:e.jsx(h,{columns:Ne,dataSource:c,rowKey:"id",scroll:{x:1800,y:"calc(100vh - 430px)"},size:"small",loading:L,locale:{emptyText:"暂无数据，请先添加取得发票数据"},pagination:{current:te.current,pageSize:te.pageSize,total:te.total,showSizeChanger:!0,showQuickJumper:!0,showTotal:(i,y)=>`共 ${i} 条记录，第 ${y[0]}-${y[1]} 条`,pageSizeOptions:["10","20","50","100"],onChange:(i,y)=>{y!==te.pageSize?(pe(_=>({..._,pageSize:y,current:1})),Ce(j,1,y)):Ce(j,i)},onShowSizeChange:(i,y)=>{pe(_=>({..._,pageSize:y,current:1})),Ce(j,1,y)}},onChange:Qe,rowClassName:(i,y)=>{const _=[],s=new Date(i.invoiceDate).getFullYear();if(y>0){const o=c[y-1],x=new Date(o.invoiceDate).getFullYear();s!==x&&_.push("cross-year-top")}if(y<c.length-1){const o=c[y+1],x=new Date(o.invoiceDate).getFullYear();s!==x&&_.push("cross-year-bottom")}return _.join(" ")},summary:i=>{const y=c.reduce((o,x)=>o+(parseFloat(x.totalAmount)||0),0),_=c.reduce((o,x)=>o+(parseFloat(x.taxAmount)||0),0),s=c.length;return e.jsx(h.Summary,{fixed:"bottom",children:e.jsxs(h.Summary.Row,{style:{backgroundColor:"#fafafa",fontWeight:"bold"},children:[e.jsx(h.Summary.Cell,{index:0,colSpan:3,children:e.jsxs("span",{style:{fontSize:"16px",color:"#1890ff",fontWeight:"bold"},children:["当前页合计 - 共 ",s," 张取得发票"]})}),e.jsx(h.Summary.Cell,{index:3}),e.jsx(h.Summary.Cell,{index:4}),e.jsx(h.Summary.Cell,{index:5}),e.jsx(h.Summary.Cell,{index:6,align:"right",children:e.jsxs("span",{style:{fontSize:"16px",color:"#666"},children:["¥",(y-_).toLocaleString()]})}),e.jsx(h.Summary.Cell,{index:7,align:"right",children:e.jsxs("span",{style:{fontSize:"16px",color:"#faad14"},children:["¥",_.toLocaleString()]})}),e.jsx(h.Summary.Cell,{index:8,align:"right",children:e.jsxs("span",{style:{fontSize:"16px",color:"#52c41a",fontWeight:"bold"},children:["¥",y.toLocaleString()]})}),e.jsx(h.Summary.Cell,{index:9}),e.jsx(h.Summary.Cell,{index:10})]})})}})})}),e.jsx(Se,{title:e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",paddingRight:"24px"},children:[e.jsx("span",{children:"取得发票详情"}),I&&e.jsx(ze,{color:M(I.status),style:{fontSize:"14px",padding:"4px 12px"},children:re(I.status)})]}),open:V,onCancel:()=>he(!1),footer:[e.jsx(f,{type:"primary",icon:e.jsx(it,{}),onClick:()=>Te(I),children:"查看发票"},"view-file"),e.jsx(f,{onClick:()=>he(!1),children:"关闭"},"close")],width:1200,children:I&&e.jsxs("div",{children:[e.jsx(C,{title:"基本信息",size:"small",style:{marginBottom:16},children:e.jsxs(R,{gutter:[16,8],children:[e.jsx(r,{span:6,children:e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"发票号码"}),e.jsx("div",{style:{fontWeight:"bold",fontSize:"16px"},children:I.invoiceNumber})]})}),e.jsx(r,{span:6,children:e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"发票代码"}),e.jsx("div",{style:{fontWeight:"bold",fontSize:"16px"},children:I.invoiceCode})]})}),e.jsx(r,{span:6,children:e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"开票日期"}),e.jsx("div",{style:{fontWeight:"bold",fontSize:"16px"},children:(De=I.invoiceDate)==null?void 0:De.split("T")[0]})]})}),e.jsx(r,{span:6,children:e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"开票人"}),e.jsx("div",{style:{fontWeight:"bold",fontSize:"16px"},children:I.drawer||"-"})]})}),e.jsx(r,{span:24,children:e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"备注"}),e.jsx("div",{style:{marginTop:4,padding:"8px",backgroundColor:"#f5f5f5",borderRadius:"4px",minHeight:"32px"},children:I.remarks||""})]})})]})}),e.jsxs(R,{gutter:16,style:{marginBottom:16},children:[e.jsx(r,{span:12,children:e.jsxs(C,{title:"购买方信息",size:"small",children:[e.jsxs("div",{style:{marginBottom:12},children:[e.jsx(l,{type:"secondary",children:"公司名称"}),e.jsx("div",{style:{fontWeight:"bold",fontSize:"16px",marginTop:4},children:I.buyerName})]}),e.jsxs("div",{style:{marginBottom:12},children:[e.jsx(l,{type:"secondary",children:"纳税人识别号"}),e.jsx("div",{style:{fontWeight:"bold",marginTop:4},children:I.buyerTaxId})]}),e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"地址电话"}),e.jsxs("div",{style:{marginTop:4},children:[I.buyerAddress," ",I.buyerPhone]})]})]})}),e.jsx(r,{span:12,children:e.jsxs(C,{title:"销售方信息",size:"small",children:[e.jsxs("div",{style:{marginBottom:12},children:[e.jsx(l,{type:"secondary",children:"公司名称"}),e.jsx("div",{style:{fontWeight:"bold",fontSize:"16px",marginTop:4},children:I.sellerName})]}),e.jsxs("div",{style:{marginBottom:12},children:[e.jsx(l,{type:"secondary",children:"纳税人识别号"}),e.jsx("div",{style:{fontWeight:"bold",marginTop:4},children:I.sellerTaxId})]}),e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"地址电话"}),e.jsxs("div",{style:{marginTop:4},children:[I.sellerAddress," ",I.sellerPhone]})]})]})})]}),e.jsx(C,{title:"金额信息",size:"small",style:{marginBottom:16},children:e.jsxs(R,{gutter:16,children:[e.jsx(r,{span:8,children:e.jsx(ue,{title:"金额",value:I.amount||0,precision:2,prefix:"¥",valueStyle:{color:"#1890ff"}})}),e.jsx(r,{span:8,children:e.jsx(ue,{title:"税额",value:I.taxAmount||0,precision:2,prefix:"¥",valueStyle:{color:"#faad14"}})}),e.jsx(r,{span:8,children:e.jsx(ue,{title:"价税合计",value:I.totalAmount||0,precision:2,prefix:"¥",valueStyle:{color:"#52c41a",fontSize:"20px",fontWeight:"bold"}})})]})}),I.invoiceItems&&I.invoiceItems.length>0&&e.jsx(C,{title:"发票明细",size:"small",style:{marginBottom:16},children:e.jsx(h,{dataSource:I.invoiceItems,rowKey:"id",size:"small",pagination:!1,columns:[{title:"商品名称",dataIndex:"itemName",key:"itemName"},{title:"规格型号",dataIndex:"specification",key:"specification"},{title:"单位",dataIndex:"unit",key:"unit",width:80},{title:"数量",dataIndex:"quantity",key:"quantity",width:100,align:"right"},{title:"单价",dataIndex:"unitPrice",key:"unitPrice",width:120,align:"right",render:i=>`¥${i==null?void 0:i.toLocaleString()}`},{title:"金额",dataIndex:"amount",key:"amount",width:120,align:"right",render:i=>`¥${i==null?void 0:i.toLocaleString()}`},{title:"税率",dataIndex:"taxRate",key:"taxRate",width:100,align:"right",render:i=>`${(i*100).toFixed(2)}%`},{title:"税额",dataIndex:"taxAmount",key:"taxAmount",width:120,align:"right",render:i=>`¥${i==null?void 0:i.toLocaleString()}`},{title:"价税合计",dataIndex:"totalAmount",key:"totalAmount",width:130,align:"right",render:i=>`¥${(i==null?void 0:i.toLocaleString())||0}`}]})})]})}),e.jsx(Se,{title:`发票文件预览 - ${(g==null?void 0:g.invoiceNumber)||""}`,open:J,onCancel:()=>xe(!1),footer:[e.jsx(f,{onClick:()=>xe(!1),children:"关闭"},"close")],width:1200,style:{top:20},children:g&&e.jsxs("div",{style:{padding:"20px"},children:[g.attachments&&g.attachments.length>0?e.jsxs("div",{children:[e.jsxs(Oe,{level:5,style:{marginBottom:16},children:["发票附件 (",g.attachments.length,"个文件)"]}),e.jsx(_e,{dataSource:g.attachments,renderItem:(i,y)=>e.jsx(_e.Item,{style:{border:"1px solid #f0f0f0",borderRadius:"8px",marginBottom:"12px",padding:"16px",backgroundColor:"#fafafa"},children:e.jsx(_e.Item.Meta,{avatar:e.jsx("div",{style:{fontSize:"24px"},children:i.fileType==="PDF"?"📄":"🖼️"}),title:e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx("span",{children:i.fileName}),e.jsxs(we,{children:[e.jsx(ze,{color:i.fileType==="PDF"?"red":"blue",children:i.fileType}),i.isOriginal&&e.jsx(ze,{color:"green",children:"原件"})]})]}),description:e.jsxs("div",{children:[e.jsxs(l,{type:"secondary",children:["文件大小: ",(i.fileSize/1024).toFixed(1)," KB"]}),e.jsx("br",{}),e.jsxs(we,{style:{marginTop:8},children:[e.jsx(f,{size:"small",icon:e.jsx(Vt,{}),onClick:()=>{const _=localStorage.getItem("token"),s=`http://localhost:3001/api/upload/attachment/${i.id}?preview=true&type=received&token=${_}`;P(s),se(i.fileName),Me(!0)},children:"预览"}),e.jsx(f,{size:"small",icon:e.jsx(tt,{}),onClick:()=>{const _=localStorage.getItem("token"),s=`http://localhost:3001/api/upload/attachment/${i.id}?type=received&token=${_}`,o=document.createElement("a");o.href=s,o.download=i.fileName,document.body.appendChild(o),o.click(),document.body.removeChild(o)},children:"下载"}),e.jsx(f,{size:"small",danger:!0,icon:e.jsx(yt,{}),onClick:()=>{Se.confirm({title:"确认删除",content:`确定要删除文件"${i.fileName}"吗？此操作不可撤销。`,okText:"确定",cancelText:"取消",onOk:async()=>{var _,s;try{await B.delete(`/api/upload/invoice-attachment/${i.id}?invoiceType=received`),k.success("文件删除成功");const x=(await B.get(`/api/received-invoices/${g.id}`)).data.data;Q({...x,attachments:x.attachments||[]})}catch(o){console.error("删除文件失败:",o),k.error(((s=(_=o.response)==null?void 0:_.data)==null?void 0:s.message)||"删除文件失败")}}})},children:"删除"})]})]})})},i.id)})]}):e.jsxs("div",{style:{textAlign:"center",padding:"40px"},children:[e.jsx("div",{style:{fontSize:"48px",marginBottom:"16px"},children:"📄"}),e.jsx(Oe,{level:4,type:"secondary",children:"暂无发票附件"}),e.jsx(l,{type:"secondary",children:"请先上传发票文件"})]}),e.jsxs("div",{style:{textAlign:"center",marginTop:"16px"},children:[e.jsx("input",{type:"file",accept:"image/*,.pdf",onChange:async i=>{var _;const y=(_=i.target.files)==null?void 0:_[0];if(y)try{await Re(g.id,y);const o=(await B.get(`/api/received-invoices/${g.id}`)).data.data;Q({...o,attachments:o.attachments||[]}),i.target.value=""}catch(s){console.error("上传文件失败:",s)}},style:{display:"none"},id:`received-invoice-file-upload-${g.id}`}),e.jsx(f,{type:"primary",icon:e.jsx(rt,{}),onClick:()=>{var i;return(i=document.getElementById(`received-invoice-file-upload-${g.id}`))==null?void 0:i.click()},children:"上传发票文件"})]})]})}),e.jsx(Se,{title:"编辑取得发票",open:w,onOk:G,onCancel:()=>{W(!1),K.resetFields(),u([])},width:1200,children:e.jsxs(d,{form:K,layout:"vertical",children:[e.jsxs(R,{gutter:16,children:[e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"发票号码",name:"invoiceNumber",rules:[{required:!0,message:"请输入发票号码"}],children:e.jsx($,{})})}),e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"发票代码",name:"invoiceCode",children:e.jsx($,{})})})]}),e.jsxs(R,{gutter:16,children:[e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"开票日期",name:"invoiceDate",rules:[{required:!0,message:"请选择开票日期"}],children:e.jsx(Ke,{size:"small",style:{width:"100%"},format:"YYYY-MM-DD"})})}),e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"状态",name:"status",initialValue:"NORMAL",children:e.jsxs(ce,{children:[e.jsx(ce.Option,{value:"NORMAL",children:"正常"}),e.jsx(ce.Option,{value:"CANCELLED",children:"作废"})]})})})]}),e.jsxs(R,{gutter:16,children:[e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"购买方名称",name:"buyerName",rules:[{required:!0,message:"请输入购买方名称"}],children:e.jsx($,{})})}),e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"购买方税号",name:"buyerTaxId",children:e.jsx($,{})})})]}),e.jsxs(R,{gutter:16,children:[e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"销售方名称",name:"sellerName",rules:[{required:!0,message:"请输入销售方名称"}],children:e.jsx($,{})})}),e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"销售方税号",name:"sellerTaxId",children:e.jsx($,{})})})]}),e.jsxs(R,{gutter:16,children:[e.jsx(r,{span:8,children:e.jsx(d.Item,{label:"金额",name:"amount",rules:[{required:!0,message:"请输入金额"}],children:e.jsx($,{type:"number",step:"0.01"})})}),e.jsx(r,{span:8,children:e.jsx(d.Item,{label:"税额",name:"taxAmount",rules:[{required:!0,message:"请输入税额"}],children:e.jsx($,{type:"number",step:"0.01"})})}),e.jsx(r,{span:8,children:e.jsx(d.Item,{label:"价税合计",name:"totalAmount",rules:[{required:!0,message:"请输入价税合计"}],children:e.jsx($,{type:"number",step:"0.01"})})})]}),e.jsx(d.Item,{label:"备注",name:"remarks",children:e.jsx($.TextArea,{rows:3,placeholder:"请输入备注信息..."})})]})}),e.jsx(Se,{title:`文件预览 - ${ne}`,open:$e,onCancel:()=>Me(!1),footer:[e.jsx(f,{onClick:()=>Me(!1),children:"关闭"},"close")],width:"60vw",style:{top:0},styles:{body:{padding:0,overflow:"hidden"}},centered:!0,children:F&&e.jsx("div",{style:{width:"100%",height:"70vh",display:"flex",justifyContent:"center",alignItems:"center",overflow:"hidden"},children:e.jsx("iframe",{src:F,style:{width:"100%",height:"100%",border:"none",maxWidth:"100%",maxHeight:"100%"},title:"文件预览",onLoad:()=>{}})})})]})},Ks=()=>{var _;const[c,S]=a.useState([]),[L,D]=a.useState(!0),[N,z]=a.useState(!1),[w,W]=a.useState(!1),[V,he]=a.useState(!1),[A,ie]=a.useState(null),[I,je]=a.useState(null),[me,u]=a.useState([]),[g,Q]=a.useState(null),[J,xe]=a.useState(!1),[te,pe]=a.useState({current:1,pageSize:20,total:0}),m=()=>{const s=new Date,o=s.getFullYear(),x=s.getMonth(),X=Math.floor(x/3)*3;return`${o}-${String(X+1).padStart(2,"0")}-01`},T=()=>new Date().toISOString().split("T")[0],[j,q]=a.useState(()=>({search:"",startDate:m(),endDate:T(),status:"",sellerCompanies:[]})),[Z,Ie]=a.useState([]),[$e,Me]=a.useState(!1),[F,P]=a.useState(""),[ne,se]=a.useState(""),[K]=d.useForm();a.useEffect(()=>{Ce(),ye()},[]);const ye=async()=>{try{const o=(await B.get("/api/companies/active")).data.data||[];Ie(Array.isArray(o)?o:[])}catch(s){console.error("获取公司列表失败:",s)}},Ce=async(s={},o=1,x)=>{var v;try{D(!0);const X=x||te.pageSize,fe=new URLSearchParams({page:o.toString(),pageSize:X.toString()}),We={...j,...s};We.search&&fe.append("search",We.search),We.startDate&&fe.append("startDate",We.startDate),We.endDate&&fe.append("endDate",We.endDate),We.status&&fe.append("status",We.status),We.sellerCompanies&&Array.isArray(We.sellerCompanies)&&We.sellerCompanies.length>0&&We.sellerCompanies.forEach(Nt=>{fe.append("sellerCompanies",Nt)});const lt=(await B.get(`/api/invoices?${fe}`)).data.data;S(lt.data||[]),pe({current:o,pageSize:X,total:((v=lt.pagination)==null?void 0:v.total)||0})}catch(X){console.error("获取发票列表失败:",X),k.error("获取发票列表失败")}finally{D(!1)}},Ue=()=>{Ce(j,1)},Ge=()=>{q({search:"",startDate:m(),endDate:T(),status:"",sellerCompanies:[]}),Ce({},1)},Qe=s=>{Ce(j,s.current)},M=s=>({NORMAL:"green",CANCELLED:"red"})[s]||"default",re=s=>({NORMAL:"正常",CANCELLED:"作废"})[s]||s,p=async s=>{try{const x=(await B.get(`/api/invoices/${s.id}`)).data.data;ie(x),u(x.invoiceItems||[]);const v={...x,invoiceDate:x.invoiceDate?x.invoiceDate.split("T")[0]:""};K.setFieldsValue(v),W(!0)}catch(o){console.error("获取发票详情失败:",o),k.error("获取发票详情失败")}},O=()=>{const s={itemName:"",specification:"",unit:"",quantity:0,unitPrice:0,amount:0,taxRate:.13,taxAmount:0,totalAmount:0};u([...me,s])},G=s=>{const o=me.filter((x,v)=>v!==s);u(o)},de=(s,o,x)=>{const v=[...me];if(v[s]={...v[s],[o]:x},["quantity","unitPrice"].includes(o)){const X=o==="quantity"?x:v[s].quantity,fe=o==="unitPrice"?x:v[s].unitPrice;v[s].amount=X*fe,v[s].taxAmount=v[s].amount*v[s].taxRate,v[s].totalAmount=v[s].amount+v[s].taxAmount}else o==="taxRate"?(v[s].taxAmount=v[s].amount*x,v[s].totalAmount=v[s].amount+v[s].taxAmount):o==="amount"&&(v[s].taxAmount=x*v[s].taxRate,v[s].totalAmount=x+v[s].taxAmount);u(v)},Re=async s=>{try{await B.delete(`/api/invoices/${s.id}`),k.success("发票删除成功！"),Ce()}catch(o){console.error("删除发票失败:",o),k.error("删除发票失败")}},Te=async()=>{try{const o={...await K.validateFields(),invoiceItems:me};A?(await B.put(`/api/invoices/${A.id}`,o),k.success("发票更新成功！")):(await B.post("/api/invoices",o),k.success("发票添加成功！")),W(!1),z(!1),ie(null),u([]),K.resetFields(),Ce()}catch(s){console.error("保存发票失败:",s),k.error("保存失败")}},Ne=async s=>{try{const o=await B.get(`/api/invoices/${s.id}`);je(o.data.data),he(!0)}catch(o){console.error("获取发票详情失败:",o),k.error("获取发票详情失败")}},De=async(s,o)=>{var x,v;try{const X=new FormData;X.append("file",o),X.append("invoiceId",s),X.append("fileType","IMAGE"),X.append("isOriginal","true"),X.append("invoiceType","issued");const fe=await B.post("/api/upload/invoice-attachment",X,{headers:{"Content-Type":"multipart/form-data"}});return k.success("发票文件上传成功！"),fe.data.data}catch(X){throw console.error("发票文件上传失败:",X),k.error(((v=(x=X.response)==null?void 0:x.data)==null?void 0:v.message)||"发票文件上传失败"),X}},i=async s=>{var o,x;try{const X=(await B.get(`/api/invoices/${s.id}`)).data.data,fe=X.attachments||[];Q({...X,attachments:fe}),xe(!0)}catch(v){console.error("查看发票文件失败:",v),k.error(((x=(o=v.response)==null?void 0:o.data)==null?void 0:x.message)||"查看发票文件失败")}},y=[{title:"序号",key:"index",width:50,fixed:"left",render:(s,o,x)=>(te.current-1)*te.pageSize+x+1},{title:"发票号码",dataIndex:"invoiceNumber",key:"invoiceNumber",width:120,fixed:"left"},{title:"发票代码",dataIndex:"invoiceCode",key:"invoiceCode",width:110},{title:"开票日期",dataIndex:"invoiceDate",key:"invoiceDate",width:90,render:s=>s==null?void 0:s.split("T")[0]},{title:"购买方",dataIndex:"buyerName",key:"buyerName",width:180},{title:"销售方",dataIndex:"sellerName",key:"sellerName",width:180},{title:"金额",dataIndex:"amount",key:"amount",width:100,align:"right",render:s=>`¥${(s==null?void 0:s.toLocaleString())||0}`},{title:"税额",dataIndex:"taxAmount",key:"taxAmount",width:100,align:"right",render:s=>`¥${(s==null?void 0:s.toLocaleString())||0}`},{title:"价税合计",dataIndex:"totalAmount",key:"totalAmount",width:110,align:"right",render:s=>`¥${(s==null?void 0:s.toLocaleString())||0}`},{title:"状态",dataIndex:"status",key:"status",width:70,render:s=>e.jsx(ze,{color:M(s),children:re(s)})},{title:"操作",key:"action",width:180,fixed:"right",render:(s,o)=>e.jsxs(we,{children:[e.jsx(f,{type:"link",size:"small",onClick:()=>Ne(o),children:"详情"}),e.jsx(f,{type:"link",size:"small",icon:e.jsx(it,{}),onClick:()=>i(o),children:"查看发票"}),e.jsx(Ut,{menu:{items:[{key:"edit",label:"编辑",icon:e.jsx(It,{}),onClick:()=>p(o)},{key:"delete",label:"删除",icon:e.jsx(yt,{}),danger:!0,onClick:()=>{Se.confirm({title:"确认删除",content:"确定要删除这条发票记录吗？",okText:"确定",cancelText:"取消",onOk:()=>Re(o)})}}]},trigger:["click"],children:e.jsxs(f,{type:"link",size:"small",children:["操作 ",e.jsx("span",{style:{fontSize:"10px"},children:"▼"})]})})]})}];return e.jsxs("div",{children:[e.jsx(C,{style:{marginBottom:16},children:e.jsxs(d,{layout:"inline",children:[e.jsx(d.Item,{label:"搜索",children:e.jsx($,{placeholder:"发票号码、购买方、税号",style:{width:250},value:j.search,onChange:s=>q({...j,search:s.target.value}),allowClear:!0})}),e.jsx(d.Item,{label:"销售方",children:e.jsx(ce,{mode:"tags",placeholder:"选择或输入销售方",style:{width:250},value:j.sellerCompanies,onChange:s=>{const o=Array.isArray(s)?s:s?[s]:[];q({...j,sellerCompanies:o})},allowClear:!0,showSearch:!0,filterOption:(s,o)=>{var x;return(x=String((o==null?void 0:o.children)||""))==null?void 0:x.toLowerCase().includes(s.toLowerCase())},tokenSeparators:[","],children:Array.isArray(Z)&&Z.map(s=>e.jsx(ce.Option,{value:s.name,children:s.name},s.id))})}),e.jsxs(d.Item,{label:"开票日期",children:[e.jsx(Ke,{value:j.startDate?He(j.startDate):null,onChange:s=>q({...j,startDate:s?s.format("YYYY-MM-DD"):""}),size:"small",style:{width:150}}),e.jsx("span",{style:{margin:"0 8px"},children:"至"}),e.jsx(Ke,{value:j.endDate?He(j.endDate):null,onChange:s=>q({...j,endDate:s?s.format("YYYY-MM-DD"):""}),size:"small",style:{width:150}})]}),e.jsx(d.Item,{label:"状态",children:e.jsxs("select",{value:j.status,onChange:s=>q({...j,status:s.target.value}),style:{width:120,height:32,border:"1px solid #d9d9d9",borderRadius:6,padding:"0 8px"},children:[e.jsx("option",{value:"",children:"全部"}),e.jsx("option",{value:"NORMAL",children:"正常"}),e.jsx("option",{value:"CANCELLED",children:"作废"})]})}),e.jsx(d.Item,{children:e.jsxs(we,{children:[e.jsx(f,{type:"primary",onClick:Ue,children:"搜索"}),e.jsx(f,{onClick:Ge,children:"重置"})]})})]})}),e.jsx(C,{style:{height:"calc(100vh - 280px)",display:"flex",flexDirection:"column"},children:e.jsx("div",{style:{flex:1,overflow:"hidden"},children:e.jsx(h,{columns:y,dataSource:c,rowKey:"id",scroll:{x:1800,y:"calc(100vh - 430px)"},size:"small",loading:L,pagination:{current:te.current,pageSize:te.pageSize,total:te.total,showSizeChanger:!0,showQuickJumper:!0,showTotal:(s,o)=>`共 ${s} 条记录，第 ${o[0]}-${o[1]} 条`,pageSizeOptions:["10","20","50","100"],onChange:(s,o)=>{o!==te.pageSize?(pe(x=>({...x,pageSize:o,current:1})),Ce(j,1,o)):Ce(j,s)},onShowSizeChange:(s,o)=>{pe(x=>({...x,pageSize:o,current:1})),Ce(j,1,o)}},onChange:Qe,rowClassName:(s,o)=>{const x=[],v=new Date(s.invoiceDate).getFullYear();if(o>0){const X=c[o-1],fe=new Date(X.invoiceDate).getFullYear();v!==fe&&x.push("cross-year-top")}if(o<c.length-1){const X=c[o+1],fe=new Date(X.invoiceDate).getFullYear();v!==fe&&x.push("cross-year-bottom")}return x.join(" ")},summary:s=>{const o=c.reduce((X,fe)=>X+(parseFloat(fe.totalAmount)||0),0),x=c.reduce((X,fe)=>X+(parseFloat(fe.taxAmount)||0),0),v=c.length;return e.jsx(h.Summary,{fixed:"bottom",children:e.jsxs(h.Summary.Row,{style:{backgroundColor:"#fafafa",fontWeight:"bold"},children:[e.jsx(h.Summary.Cell,{index:0,colSpan:3,children:e.jsxs("span",{style:{fontSize:"16px",color:"#1890ff",fontWeight:"bold"},children:["当前页合计 - 共 ",v," 张发票"]})}),e.jsx(h.Summary.Cell,{index:3}),e.jsx(h.Summary.Cell,{index:4}),e.jsx(h.Summary.Cell,{index:5}),e.jsx(h.Summary.Cell,{index:6,align:"right",children:e.jsxs("span",{style:{fontSize:"16px",color:"#666"},children:["¥",(o-x).toLocaleString()]})}),e.jsx(h.Summary.Cell,{index:7,align:"right",children:e.jsxs("span",{style:{fontSize:"16px",color:"#faad14"},children:["¥",x.toLocaleString()]})}),e.jsx(h.Summary.Cell,{index:8,align:"right",children:e.jsxs("span",{style:{fontSize:"16px",color:"#52c41a",fontWeight:"bold"},children:["¥",o.toLocaleString()]})}),e.jsx(h.Summary.Cell,{index:9}),e.jsx(h.Summary.Cell,{index:10})]})})}})})}),e.jsx(Se,{title:e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",paddingRight:"24px"},children:[e.jsx("span",{children:"发票详情"}),I&&e.jsx(ze,{color:M(I.status),style:{fontSize:"14px",padding:"4px 12px"},children:re(I.status)})]}),open:V,onCancel:()=>he(!1),footer:[e.jsx(f,{type:"primary",icon:e.jsx(it,{}),onClick:()=>i(I),children:"查看发票"},"view-file"),e.jsx(f,{onClick:()=>he(!1),children:"关闭"},"close")],width:1200,children:I&&e.jsxs("div",{children:[e.jsx(C,{title:"基本信息",size:"small",style:{marginBottom:16},children:e.jsxs(R,{gutter:[16,8],children:[e.jsx(r,{span:6,children:e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"发票号码"}),e.jsx("div",{style:{fontWeight:"bold",fontSize:"16px"},children:I.invoiceNumber})]})}),e.jsx(r,{span:6,children:e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"发票代码"}),e.jsx("div",{style:{fontWeight:"bold",fontSize:"16px"},children:I.invoiceCode})]})}),e.jsx(r,{span:6,children:e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"开票日期"}),e.jsx("div",{style:{fontWeight:"bold",fontSize:"16px"},children:(_=I.invoiceDate)==null?void 0:_.split("T")[0]})]})}),e.jsx(r,{span:6,children:e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"开票人"}),e.jsx("div",{style:{fontWeight:"bold",fontSize:"16px"},children:I.drawer||"-"})]})}),e.jsx(r,{span:24,children:e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"备注"}),e.jsx("div",{style:{marginTop:4,padding:"8px",backgroundColor:"#f5f5f5",borderRadius:"4px",minHeight:"32px"},children:I.remarks||""})]})})]})}),e.jsxs(R,{gutter:16,style:{marginBottom:16},children:[e.jsx(r,{span:12,children:e.jsxs(C,{title:"购买方信息",size:"small",children:[e.jsxs("div",{style:{marginBottom:12},children:[e.jsx(l,{type:"secondary",children:"公司名称"}),e.jsx("div",{style:{fontWeight:"bold",fontSize:"16px",marginTop:4},children:I.buyerName})]}),e.jsxs("div",{style:{marginBottom:12},children:[e.jsx(l,{type:"secondary",children:"纳税人识别号"}),e.jsx("div",{style:{fontWeight:"bold",marginTop:4},children:I.buyerTaxId})]}),e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"地址电话"}),e.jsxs("div",{style:{marginTop:4},children:[I.buyerAddress," ",I.buyerPhone]})]})]})}),e.jsx(r,{span:12,children:e.jsxs(C,{title:"销售方信息",size:"small",children:[e.jsxs("div",{style:{marginBottom:12},children:[e.jsx(l,{type:"secondary",children:"公司名称"}),e.jsx("div",{style:{fontWeight:"bold",fontSize:"16px",marginTop:4},children:I.sellerName})]}),e.jsxs("div",{style:{marginBottom:12},children:[e.jsx(l,{type:"secondary",children:"纳税人识别号"}),e.jsx("div",{style:{fontWeight:"bold",marginTop:4},children:I.sellerTaxId})]}),e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"地址电话"}),e.jsxs("div",{style:{marginTop:4},children:[I.sellerAddress," ",I.sellerPhone]})]})]})})]}),e.jsx(C,{title:"金额信息",size:"small",style:{marginBottom:16},children:e.jsxs(R,{gutter:16,children:[e.jsx(r,{span:8,children:e.jsx(ue,{title:"金额",value:I.amount||0,precision:2,prefix:"¥",valueStyle:{color:"#1890ff"}})}),e.jsx(r,{span:8,children:e.jsx(ue,{title:"税额",value:I.taxAmount||0,precision:2,prefix:"¥",valueStyle:{color:"#faad14"}})}),e.jsx(r,{span:8,children:e.jsx(ue,{title:"价税合计",value:I.totalAmount||0,precision:2,prefix:"¥",valueStyle:{color:"#52c41a",fontSize:"20px",fontWeight:"bold"}})})]})}),I.invoiceItems&&I.invoiceItems.length>0&&e.jsx(C,{title:"发票明细",size:"small",style:{marginBottom:16},children:e.jsx(h,{dataSource:I.invoiceItems,rowKey:"id",size:"small",pagination:!1,columns:[{title:"商品名称",dataIndex:"itemName",key:"itemName"},{title:"规格型号",dataIndex:"specification",key:"specification"},{title:"单位",dataIndex:"unit",key:"unit",width:80},{title:"数量",dataIndex:"quantity",key:"quantity",width:100,align:"right"},{title:"单价",dataIndex:"unitPrice",key:"unitPrice",width:120,align:"right",render:s=>`¥${s==null?void 0:s.toLocaleString()}`},{title:"金额",dataIndex:"amount",key:"amount",width:120,align:"right",render:s=>`¥${s==null?void 0:s.toLocaleString()}`},{title:"税率",dataIndex:"taxRate",key:"taxRate",width:100,align:"right",render:s=>`${(s*100).toFixed(2)}%`},{title:"税额",dataIndex:"taxAmount",key:"taxAmount",width:120,align:"right",render:s=>`¥${s==null?void 0:s.toLocaleString()}`},{title:"价税合计",dataIndex:"totalAmount",key:"totalAmount",width:130,align:"right",render:s=>`¥${(s==null?void 0:s.toLocaleString())||0}`}]})})]})}),e.jsx(Se,{title:"编辑发票",open:w,onOk:Te,onCancel:()=>{W(!1),K.resetFields(),u([])},width:1400,children:e.jsxs(d,{form:K,layout:"horizontal",labelCol:{span:6},wrapperCol:{span:18},children:[e.jsxs(C,{title:"基本信息",size:"small",style:{marginBottom:12},children:[e.jsxs(R,{gutter:12,children:[e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"发票号码",name:"invoiceNumber",rules:[{required:!0,message:"请输入发票号码"}],style:{marginBottom:12},children:e.jsx($,{})})}),e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"发票代码",name:"invoiceCode",rules:[{required:!0,message:"请输入发票代码"}],style:{marginBottom:12},children:e.jsx($,{})})})]}),e.jsxs(R,{gutter:12,children:[e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"开票日期",name:"invoiceDate",rules:[{required:!0,message:"请选择开票日期"}],style:{marginBottom:12},children:e.jsx(Ke,{size:"small",style:{width:"100%"},format:"YYYY-MM-DD"})})}),e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"开票人",name:"drawer",style:{marginBottom:12},children:e.jsx($,{})})})]}),e.jsx(R,{gutter:12,children:e.jsx(r,{span:24,children:e.jsx(d.Item,{label:"备注",name:"remarks",labelCol:{span:3},wrapperCol:{span:21},style:{marginBottom:12},children:e.jsx($.TextArea,{rows:3,placeholder:"请输入备注信息..."})})})})]}),e.jsxs(R,{gutter:12,style:{marginBottom:12},children:[e.jsx(r,{span:12,children:e.jsxs(C,{title:"购买方信息",size:"small",children:[e.jsx(d.Item,{label:"公司名称",name:"buyerName",rules:[{required:!0,message:"请输入购买方名称"}],labelCol:{span:8},wrapperCol:{span:16},style:{marginBottom:10},children:e.jsx($,{})}),e.jsx(d.Item,{label:"纳税人识别号",name:"buyerTaxId",rules:[{required:!0,message:"请输入购买方税号"}],labelCol:{span:8},wrapperCol:{span:16},style:{marginBottom:10},children:e.jsx($,{})}),e.jsx(d.Item,{label:"地址电话",name:"buyerAddress",labelCol:{span:8},wrapperCol:{span:16},style:{marginBottom:10},children:e.jsx($,{})})]})}),e.jsx(r,{span:12,children:e.jsxs(C,{title:"销售方信息",size:"small",children:[e.jsx(d.Item,{label:"公司名称",name:"sellerName",rules:[{required:!0,message:"请输入销售方名称"}],labelCol:{span:8},wrapperCol:{span:16},style:{marginBottom:10},children:e.jsx($,{})}),e.jsx(d.Item,{label:"纳税人识别号",name:"sellerTaxId",rules:[{required:!0,message:"请输入销售方税号"}],labelCol:{span:8},wrapperCol:{span:16},style:{marginBottom:10},children:e.jsx($,{})}),e.jsx(d.Item,{label:"地址电话",name:"sellerAddress",labelCol:{span:8},wrapperCol:{span:16},style:{marginBottom:10},children:e.jsx($,{})})]})})]}),e.jsxs(C,{title:"金额信息",size:"small",style:{marginBottom:12},children:[e.jsxs(R,{gutter:12,children:[e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"金额",name:"amount",rules:[{required:!0,message:"请输入金额"}],style:{marginBottom:12},children:e.jsx($,{type:"number",step:"0.01"})})}),e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"税额",name:"taxAmount",rules:[{required:!0,message:"请输入税额"}],style:{marginBottom:12},children:e.jsx($,{type:"number",step:"0.01"})})})]}),e.jsxs(R,{gutter:12,children:[e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"价税合计",name:"totalAmount",rules:[{required:!0,message:"请输入价税合计"}],style:{marginBottom:12},children:e.jsx($,{type:"number",step:"0.01"})})}),e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"发票状态",name:"status",style:{marginBottom:12},children:e.jsxs("select",{style:{width:"100%",height:32,border:"1px solid #d9d9d9",borderRadius:6,padding:"0 8px"},children:[e.jsx("option",{value:"NORMAL",children:"正常"}),e.jsx("option",{value:"CANCELLED",children:"作废"})]})})})]})]}),e.jsxs(C,{title:"发票明细",size:"small",children:[e.jsx(f,{type:"dashed",onClick:O,style:{width:"100%",marginBottom:16},icon:e.jsx(Dt,{}),children:"添加明细"}),e.jsx(h,{dataSource:me,rowKey:(s,o)=>`item-${o}`,size:"small",pagination:!1,scroll:{x:1200},columns:[{title:"商品名称",dataIndex:"itemName",key:"itemName",width:150,render:(s,o,x)=>e.jsx($,{value:s,onChange:v=>de(x,"itemName",v.target.value)})},{title:"规格型号",dataIndex:"specification",key:"specification",width:120,render:(s,o,x)=>e.jsx($,{value:s,onChange:v=>de(x,"specification",v.target.value)})},{title:"单位",dataIndex:"unit",key:"unit",width:80,render:(s,o,x)=>e.jsx($,{value:s,onChange:v=>de(x,"unit",v.target.value)})},{title:"数量",dataIndex:"quantity",key:"quantity",width:100,render:(s,o,x)=>e.jsx($,{type:"number",step:"0.01",value:s,onChange:v=>de(x,"quantity",parseFloat(v.target.value)||0)})},{title:"单价",dataIndex:"unitPrice",key:"unitPrice",width:120,render:(s,o,x)=>e.jsx($,{type:"number",step:"0.01",value:s,onChange:v=>de(x,"unitPrice",parseFloat(v.target.value)||0)})},{title:"金额",dataIndex:"amount",key:"amount",width:120,render:(s,o,x)=>e.jsx($,{type:"number",step:"0.01",value:s,onChange:v=>de(x,"amount",parseFloat(v.target.value)||0)})},{title:"税率",dataIndex:"taxRate",key:"taxRate",width:100,render:(s,o,x)=>e.jsx($,{type:"number",step:"0.01",value:s,onChange:v=>de(x,"taxRate",parseFloat(v.target.value)||0)})},{title:"税额",dataIndex:"taxAmount",key:"taxAmount",width:120,render:(s,o,x)=>e.jsx($,{type:"number",step:"0.01",value:s,onChange:v=>de(x,"taxAmount",parseFloat(v.target.value)||0)})},{title:"价税合计",dataIndex:"totalAmount",key:"totalAmount",width:130,render:(s,o,x)=>e.jsx($,{type:"number",step:"0.01",value:s,onChange:v=>de(x,"totalAmount",parseFloat(v.target.value)||0)})},{title:"操作",key:"action",width:80,render:(s,o,x)=>e.jsx(f,{type:"link",danger:!0,onClick:()=>G(x),children:"删除"})}]})]})]})}),e.jsx(Se,{title:"添加发票",open:N,onOk:Te,onCancel:()=>{z(!1),K.resetFields()},width:1400,children:e.jsxs(d,{form:K,layout:"vertical",children:[e.jsxs(R,{gutter:16,children:[e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"发票号码",name:"invoiceNumber",rules:[{required:!0,message:"请输入发票号码"}],children:e.jsx($,{})})}),e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"发票代码",name:"invoiceCode",rules:[{required:!0,message:"请输入发票代码"}],children:e.jsx($,{})})})]}),e.jsxs(R,{gutter:16,children:[e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"开票日期",name:"invoiceDate",rules:[{required:!0,message:"请选择开票日期"}],children:e.jsx(Ke,{size:"small",style:{width:"100%"},format:"YYYY-MM-DD"})})}),e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"发票状态",name:"status",initialValue:"NORMAL",children:e.jsxs("select",{style:{width:"100%",height:32,border:"1px solid #d9d9d9",borderRadius:6,padding:"0 8px"},children:[e.jsx("option",{value:"NORMAL",children:"正常"}),e.jsx("option",{value:"CANCELLED",children:"作废"})]})})})]}),e.jsxs(R,{gutter:16,children:[e.jsx(r,{span:8,children:e.jsx(d.Item,{label:"金额",name:"amount",rules:[{required:!0,message:"请输入金额"}],children:e.jsx($,{type:"number",step:"0.01"})})}),e.jsx(r,{span:8,children:e.jsx(d.Item,{label:"税额",name:"taxAmount",rules:[{required:!0,message:"请输入税额"}],children:e.jsx($,{type:"number",step:"0.01"})})}),e.jsx(r,{span:8,children:e.jsx(d.Item,{label:"价税合计",name:"totalAmount",rules:[{required:!0,message:"请输入价税合计"}],children:e.jsx($,{type:"number",step:"0.01"})})})]}),e.jsxs(R,{gutter:16,children:[e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"购买方名称",name:"buyerName",rules:[{required:!0,message:"请输入购买方名称"}],children:e.jsx($,{})})}),e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"购买方税号",name:"buyerTaxId",rules:[{required:!0,message:"请输入购买方税号"}],children:e.jsx($,{})})})]}),e.jsxs(R,{gutter:16,children:[e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"销售方名称",name:"sellerName",rules:[{required:!0,message:"请输入销售方名称"}],children:e.jsx($,{})})}),e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"销售方税号",name:"sellerTaxId",rules:[{required:!0,message:"请输入销售方税号"}],children:e.jsx($,{})})})]})]})}),e.jsx(Se,{title:`发票文件预览 - ${(g==null?void 0:g.invoiceNumber)||""}`,open:J,onCancel:()=>xe(!1),footer:[e.jsx(f,{onClick:()=>xe(!1),children:"关闭"},"close")],width:1200,style:{top:20},children:g&&e.jsxs("div",{style:{padding:"20px"},children:[g.attachments&&g.attachments.length>0?e.jsxs("div",{children:[e.jsxs(Oe,{level:5,style:{marginBottom:16},children:["发票附件 (",g.attachments.length,"个文件)"]}),e.jsx(_e,{dataSource:g.attachments,renderItem:(s,o)=>e.jsx(_e.Item,{style:{border:"1px solid #f0f0f0",borderRadius:"8px",marginBottom:"12px",padding:"16px",backgroundColor:"#fafafa"},children:e.jsx(_e.Item.Meta,{avatar:e.jsx("div",{style:{fontSize:"24px"},children:s.fileType==="PDF"?"📄":"🖼️"}),title:e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx("span",{children:s.fileName}),e.jsxs(we,{children:[e.jsx(ze,{color:s.fileType==="PDF"?"red":"blue",children:s.fileType}),s.isOriginal&&e.jsx(ze,{color:"green",children:"原件"})]})]}),description:e.jsxs("div",{children:[e.jsxs(l,{type:"secondary",children:["文件大小: ",(s.fileSize/1024).toFixed(1)," KB"]}),e.jsx("br",{}),e.jsxs(we,{style:{marginTop:8},children:[e.jsx(f,{size:"small",icon:e.jsx(Vt,{}),onClick:()=>{const x=localStorage.getItem("token"),v=`http://localhost:3001/api/upload/attachment/${s.id}?preview=true&type=issued&token=${x}`;P(v),se(s.fileName),Me(!0)},children:"预览"}),e.jsx(f,{size:"small",icon:e.jsx(tt,{}),onClick:()=>{const x=localStorage.getItem("token"),v=`http://localhost:3001/api/upload/attachment/${s.id}?type=issued&token=${x}`,X=document.createElement("a");X.href=v,X.download=s.fileName,document.body.appendChild(X),X.click(),document.body.removeChild(X)},children:"下载"}),e.jsx(f,{size:"small",danger:!0,icon:e.jsx(yt,{}),onClick:()=>{Se.confirm({title:"确认删除",content:`确定要删除文件 "${s.fileName}" 吗？`,onOk:async()=>{var x,v;try{await B.delete(`/api/upload/invoice-attachment/${s.id}?invoiceType=issued`),k.success("文件删除成功");const fe=(await B.get(`/api/invoices/${g.id}`)).data.data;Q({...fe,attachments:fe.attachments||[]})}catch(X){console.error("删除文件失败:",X),k.error(((v=(x=X.response)==null?void 0:x.data)==null?void 0:v.message)||"删除文件失败")}}})},children:"删除"})]})]})})},s.id)})]}):e.jsxs("div",{style:{textAlign:"center",padding:"40px"},children:[e.jsx("div",{style:{fontSize:"48px",marginBottom:"16px"},children:"📄"}),e.jsx(Oe,{level:4,type:"secondary",children:"暂无发票附件"}),e.jsx(l,{type:"secondary",children:"请先上传发票文件"})]}),e.jsxs("div",{style:{textAlign:"center",marginTop:"16px"},children:[e.jsx("input",{type:"file",accept:"image/*,.pdf",onChange:async s=>{var x;const o=(x=s.target.files)==null?void 0:x[0];if(o)try{await De(g.id,o);const X=(await B.get(`/api/invoices/${g.id}`)).data.data;Q({...X,attachments:X.attachments||[]}),s.target.value=""}catch(v){console.error("上传文件失败:",v)}},style:{display:"none"},id:`invoice-file-upload-${g.id}`}),e.jsx(f,{type:"primary",icon:e.jsx(rt,{}),onClick:()=>{var s;return(s=document.getElementById(`invoice-file-upload-${g.id}`))==null?void 0:s.click()},children:"上传发票文件"})]})]})}),e.jsx(Se,{title:`文件预览 - ${ne}`,open:$e,onCancel:()=>Me(!1),footer:[e.jsx(f,{onClick:()=>Me(!1),children:"关闭"},"close")],width:"60vw",style:{top:0},styles:{body:{padding:0,overflow:"hidden"}},centered:!0,children:F&&e.jsx("div",{style:{width:"100%",height:"70vh",display:"flex",justifyContent:"center",alignItems:"center",overflow:"hidden"},children:e.jsx("iframe",{src:F,style:{width:"100%",height:"100%",border:"none",maxWidth:"100%",maxHeight:"100%"},title:"文件预览",onLoad:()=>{}})})})]})},Gs=()=>{const[c,S]=a.useState([]),[L,D]=a.useState(!1),[N,z]=a.useState(!1),[w,W]=a.useState(!1),[V,he]=a.useState(null),[A]=d.useForm(),[ie,I]=a.useState([]),[je,me]=a.useState({current:1,pageSize:20,total:0}),[u,g]=a.useState({search:"",role:"",status:""}),[Q,J]=a.useState(!1),[xe,te]=a.useState(null),[pe,m]=a.useState([]),[T,j]=a.useState(!1),[q]=d.useForm();a.useEffect(()=>{Z(),Ie()},[]);const Z=async(p={},O=1)=>{D(!0);try{const G=new URLSearchParams({page:O.toString(),pageSize:je.pageSize.toString()}),de={...u,...p};de.search&&G.append("search",de.search),de.role&&G.append("role",de.role),de.status&&G.append("status",de.status);const Te=(await B.get(`/api/users?${G}`)).data.data;S(Te.data||[]),me({current:O,pageSize:je.pageSize,total:Te.total||0})}catch(G){console.error("获取用户列表失败:",G),k.error("获取用户列表失败")}finally{D(!1)}},Ie=async()=>{try{const O=(await B.get("/api/companies/active")).data.data||[];I(Array.isArray(O)?O:[])}catch(p){console.error("获取公司列表失败:",p)}},$e=()=>{Z(u,1)},Me=()=>{g({search:"",role:"",status:""}),Z({},1)},F=p=>{Z(u,p.current)},P=()=>{he(null),A.resetFields(),W(!0)},ne=p=>{var O;he(p),A.setFieldsValue({...p,companyIds:((O=p.userCompanies)==null?void 0:O.map(G=>G.companyId))||[]}),z(!0)},se=async()=>{try{const p=await A.validateFields();V?(await B.put(`/api/users/${V.id}`,p),k.success("用户更新成功！")):(await B.post("/api/users",p),k.success("用户添加成功！")),z(!1),W(!1),he(null),A.resetFields(),Z()}catch(p){console.error("保存用户失败:",p),k.error("保存失败")}},K=async p=>{try{await B.delete(`/api/users/${p.id}`),k.success("用户删除成功！"),Z()}catch(O){console.error("删除用户失败:",O),k.error("删除用户失败")}},ye=async p=>{te(p),J(!0);try{const O=await B.get(`/api/users/${p.id}/menu-permissions`);m(O.data.data||[])}catch(O){console.error("获取用户菜单权限失败:",O),m([{id:"dashboard",key:"dashboard",name:"仪表板",parentId:null,canView:!1,canEdit:!1,canDelete:!1,canExport:!1},{id:"companies",key:"companies",name:"公司管理",parentId:null,canView:!1,canEdit:!1,canDelete:!1,canExport:!1},{id:"invoices",key:"invoices",name:"发票管理",parentId:null,canView:!1,canEdit:!1,canDelete:!1,canExport:!1},{id:"issued-invoices",key:"issued-invoices",name:"开具发票",parentId:"invoices",canView:!1,canEdit:!1,canDelete:!1,canExport:!1},{id:"received-invoices",key:"received-invoices",name:"取得发票",parentId:"invoices",canView:!1,canEdit:!1,canDelete:!1,canExport:!1},{id:"users",key:"users",name:"用户管理",parentId:null,canView:!1,canEdit:!1,canDelete:!1,canExport:!1},{id:"reports",key:"reports",name:"报表分析",parentId:null,canView:!1,canEdit:!1,canDelete:!1,canExport:!1},{id:"invoice-relations",key:"invoice-relations",name:"开票关系",parentId:"reports",canView:!1,canEdit:!1,canDelete:!1,canExport:!1}])}},Ce=async()=>{try{await B.put(`/api/users/${xe.id}/menu-permissions`,{permissions:pe.map(p=>({menuId:p.id,canView:p.canView,canEdit:p.canEdit,canDelete:p.canDelete,canExport:p.canExport}))}),k.success("菜单权限更新成功！"),J(!1),te(null),m([])}catch(p){console.error("更新菜单权限失败:",p),k.error("更新菜单权限失败")}},Ue=(p,O,G)=>{m(de=>de.map(Re=>Re.id===p?{...Re,[O]:G}:Re))},Ge=p=>{te(p),q.resetFields(),j(!0)},Qe=()=>{const p=Wn();q.setFieldsValue({newPassword:p,confirmPassword:p}),k.success("已生成16位随机密码")},M=async()=>{try{const p=await q.validateFields();await B.put(`/api/users/${xe.id}/password`,{newPassword:p.newPassword}),k.success("密码修改成功！"),j(!1),te(null),q.resetFields()}catch(p){console.error("修改密码失败:",p),k.error("修改密码失败")}},re=[{title:"用户名",dataIndex:"username",key:"username"},{title:"邮箱",dataIndex:"email",key:"email"},{title:"姓名",dataIndex:"name",key:"name"},{title:"角色",dataIndex:"role",key:"role",render:p=>{const O={ADMIN:"管理员",USER:"普通用户"};return e.jsx(ze,{color:p==="ADMIN"?"red":"blue",children:O[p]||p})}},{title:"状态",dataIndex:"status",key:"status",render:p=>{const O={ACTIVE:"激活",INACTIVE:"禁用"};return e.jsx(ze,{color:p==="ACTIVE"?"green":"red",children:O[p]||p})}},{title:"权限公司",key:"companies",render:p=>{const O=p.userCompanies||[];return O.length>0?e.jsx("div",{children:O.map(G=>{var de;return e.jsx(ze,{children:((de=G.company)==null?void 0:de.name)||G.companyId},G.companyId)})}):e.jsx(l,{type:"secondary",children:"无权限限制"})}},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",render:p=>new Date(p).toLocaleDateString()},{title:"操作",key:"action",render:(p,O)=>e.jsxs(we,{children:[e.jsx(f,{type:"link",size:"small",onClick:()=>ne(O),children:"编辑"}),e.jsx(f,{type:"link",size:"small",onClick:()=>Ge(O),children:"修改密码"}),e.jsx(f,{type:"link",size:"small",onClick:()=>ye(O),children:"菜单权限"}),e.jsx(Bn,{title:"确定要删除这个用户吗？",onConfirm:()=>K(O),okText:"确定",cancelText:"取消",children:e.jsx(f,{type:"link",size:"small",danger:!0,children:"删除"})})]})}];return e.jsxs("div",{children:[e.jsx(C,{style:{marginBottom:16},children:e.jsxs(d,{layout:"inline",children:[e.jsx(d.Item,{label:"搜索",children:e.jsx($,{placeholder:"用户名、邮箱、姓名",style:{width:200},value:u.search,onChange:p=>g({...u,search:p.target.value}),allowClear:!0})}),e.jsx(d.Item,{label:"角色",children:e.jsxs(ce,{placeholder:"选择角色",style:{width:120},value:u.role,onChange:p=>g({...u,role:p}),allowClear:!0,children:[e.jsx(ce.Option,{value:"ADMIN",children:"管理员"}),e.jsx(ce.Option,{value:"USER",children:"普通用户"})]})}),e.jsx(d.Item,{label:"状态",children:e.jsxs(ce,{placeholder:"选择状态",style:{width:120},value:u.status,onChange:p=>g({...u,status:p}),allowClear:!0,children:[e.jsx(ce.Option,{value:"ACTIVE",children:"激活"}),e.jsx(ce.Option,{value:"INACTIVE",children:"禁用"})]})}),e.jsx(d.Item,{children:e.jsxs(we,{children:[e.jsx(f,{type:"primary",onClick:$e,children:"搜索"}),e.jsx(f,{onClick:Me,children:"重置"})]})})]})}),e.jsxs(C,{children:[e.jsxs("div",{style:{marginBottom:16,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx(Oe,{level:3,children:"用户管理"}),e.jsx(f,{type:"primary",icon:e.jsx(Dt,{}),onClick:P,children:"添加用户"})]}),e.jsx(h,{columns:re,dataSource:c,rowKey:"id",loading:L,pagination:{...je,showTotal:p=>`共 ${p} 条记录`,showSizeChanger:!0,showQuickJumper:!0},onChange:F})]}),e.jsx(Se,{title:V?"编辑用户":"添加用户",open:N||w,onOk:se,onCancel:()=>{z(!1),W(!1),he(null),A.resetFields()},width:600,children:e.jsxs(d,{form:A,layout:"vertical",children:[e.jsxs(R,{gutter:16,children:[e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"用户名",name:"username",rules:[{required:!0,message:"请输入用户名"}],children:e.jsx($,{})})}),e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"邮箱",name:"email",rules:[{required:!0,message:"请输入邮箱"},{type:"email",message:"请输入有效的邮箱地址"}],children:e.jsx($,{})})})]}),e.jsxs(R,{gutter:16,children:[e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"姓名",name:"name",rules:[{required:!0,message:"请输入姓名"}],children:e.jsx($,{})})}),e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"角色",name:"role",rules:[{required:!0,message:"请选择角色"}],children:e.jsxs(ce,{children:[e.jsx(ce.Option,{value:"ADMIN",children:"管理员"}),e.jsx(ce.Option,{value:"USER",children:"普通用户"})]})})})]}),e.jsxs(R,{gutter:16,children:[e.jsx(r,{span:12,children:e.jsx(d.Item,{label:"状态",name:"status",rules:[{required:!0,message:"请选择状态"}],children:e.jsxs(ce,{children:[e.jsx(ce.Option,{value:"ACTIVE",children:"激活"}),e.jsx(ce.Option,{value:"INACTIVE",children:"禁用"})]})})}),e.jsx(r,{span:12,children:!V&&e.jsx(d.Item,{label:"密码",name:"password",rules:[{required:!0,message:"请输入密码"}],children:e.jsx($.Password,{})})})]}),e.jsx(d.Item,{label:"权限公司",name:"companyIds",help:"只能查看授权公司的权限数据",children:e.jsx(ce,{mode:"multiple",placeholder:"选择用户可以访问的公司",allowClear:!0,children:ie.map(p=>e.jsx(ce.Option,{value:p.id,children:p.name},p.id))})})]})}),e.jsxs(Se,{title:`设置用户菜单权限 - ${(xe==null?void 0:xe.name)||""}`,open:Q,onOk:Ce,onCancel:()=>{J(!1),te(null),m([])},width:800,okText:"保存",cancelText:"取消",children:[e.jsx("div",{style:{maxHeight:"500px",overflowY:"auto"},children:e.jsx(h,{dataSource:pe,rowKey:"id",pagination:!1,size:"small",columns:[{title:"菜单名称",dataIndex:"name",key:"name",render:(p,O)=>e.jsxs("span",{style:{paddingLeft:O.parentId?20:0},children:[O.parentId&&"└ ",p]})},{title:"查看",dataIndex:"canView",key:"canView",width:80,render:(p,O)=>e.jsx(wt,{checked:p,onChange:G=>Ue(O.id,"canView",G.target.checked)})},{title:"编辑",dataIndex:"canEdit",key:"canEdit",width:80,render:(p,O)=>e.jsx(wt,{checked:p,onChange:G=>Ue(O.id,"canEdit",G.target.checked)})},{title:"删除",dataIndex:"canDelete",key:"canDelete",width:80,render:(p,O)=>e.jsx(wt,{checked:p,onChange:G=>Ue(O.id,"canDelete",G.target.checked)})},{title:"导出",dataIndex:"canExport",key:"canExport",width:80,render:(p,O)=>e.jsx(wt,{checked:p,onChange:G=>Ue(O.id,"canExport",G.target.checked)})}]})}),e.jsx("div",{style:{marginTop:16,padding:12,backgroundColor:"#f5f5f5",borderRadius:4},children:e.jsxs(l,{type:"secondary",children:[e.jsx("strong",{children:"权限说明："}),e.jsx("br",{}),"• ",e.jsx("strong",{children:"查看"}),"：可以访问该菜单页面",e.jsx("br",{}),"• ",e.jsx("strong",{children:"编辑"}),"：可以修改数据",e.jsx("br",{}),"• ",e.jsx("strong",{children:"删除"}),"：可以删除数据",e.jsx("br",{}),"• ",e.jsx("strong",{children:"导出"}),"：可以导出数据"]})})]}),e.jsx(Se,{title:`修改密码 - ${(xe==null?void 0:xe.name)||""}`,open:T,onCancel:()=>{j(!1),te(null),q.resetFields()},footer:[e.jsx(f,{type:"default",onClick:Qe,style:{float:"left"},children:"随机密码"},"generate"),e.jsx(f,{onClick:()=>{j(!1),te(null),q.resetFields()},children:"取消"},"cancel"),e.jsx(f,{type:"primary",onClick:M,children:"确定"},"submit")],children:e.jsxs(d,{form:q,layout:"vertical",children:[e.jsx(d.Item,{label:"新密码",name:"newPassword",rules:[{required:!0,message:"请输入新密码"},{min:6,message:"密码至少6个字符"}],children:e.jsx($.Password,{placeholder:"请输入新密码"})}),e.jsx(d.Item,{label:"确认密码",name:"confirmPassword",dependencies:["newPassword"],rules:[{required:!0,message:"请确认新密码"},({getFieldValue:p})=>({validator(O,G){return!G||p("newPassword")===G?Promise.resolve():Promise.reject(new Error("两次输入的密码不一致"))}})],children:e.jsx($.Password,{placeholder:"请再次输入新密码"})})]})})]})},Qs=()=>{var pe;const[c,S]=a.useState([]),[L,D]=a.useState(!1),[N,z]=a.useState({current:1,pageSize:20,total:0}),[w,W]=a.useState({username:"",operationName:"",method:"",path:"",isSuccess:void 0,startDate:"",endDate:""}),[V,he]=a.useState(!1),[A,ie]=a.useState(null),[I,je]=a.useState(null);a.useEffect(()=>{me(),u()},[]);const me=async(m=w,T=N.current)=>{try{D(!0);const j=new URLSearchParams;j.append("page",T.toString()),j.append("pageSize",N.pageSize.toString()),Object.entries(m).forEach(([Z,Ie])=>{Ie!==""&&Ie!==void 0&&Ie!==null&&j.append(Z,Ie.toString())});const q=await B.get(`/api/operation-logs?${j}`);q.data.success&&(S(q.data.data.data),z({...N,current:T,total:q.data.data.total}))}catch(j){console.error("获取操作日志失败:",j),k.error("获取操作日志失败")}finally{D(!1)}},u=async()=>{try{const m=await B.get("/api/operation-logs/stats/summary");m.data.success&&je(m.data.data)}catch(m){console.error("获取统计数据失败:",m)}},g=()=>{me(w,1)},Q=()=>{const m={username:"",operationName:"",method:"",path:"",isSuccess:void 0,startDate:"",endDate:""};W(m),me(m,1)},J=m=>{me(w,m.current)},xe=m=>{ie(m),he(!0)},te=[{title:"操作时间",dataIndex:"createdAt",key:"createdAt",width:160,render:m=>He(m).format("YYYY-MM-DD HH:mm:ss")},{title:"操作用户",dataIndex:"username",key:"username",width:120,render:(m,T)=>m||"未登录用户"},{title:"操作名称",dataIndex:"operationName",key:"operationName",width:150},{title:"请求方法",dataIndex:"method",key:"method",width:80,render:m=>e.jsx(ze,{color:m==="GET"?"blue":m==="POST"?"green":m==="PUT"?"orange":m==="DELETE"?"red":"default",children:m})},{title:"请求路径",dataIndex:"path",key:"path",width:200,ellipsis:!0},{title:"状态",dataIndex:"isSuccess",key:"isSuccess",width:80,render:m=>e.jsx(ze,{color:m?"success":"error",icon:m?e.jsx(Yt,{}):e.jsx(cn,{}),children:m?"成功":"失败"})},{title:"耗时",dataIndex:"duration",key:"duration",width:80,render:m=>m?`${m}ms`:"-"},{title:"IP地址",dataIndex:"ipAddress",key:"ipAddress",width:120,ellipsis:!0},{title:"操作",key:"action",width:80,render:(m,T)=>e.jsx(f,{type:"link",size:"small",onClick:()=>xe(T),children:"详情"})}];return e.jsxs("div",{children:[e.jsx(Oe,{level:2,children:"操作日志"}),I&&e.jsxs(R,{gutter:[16,16],style:{marginBottom:16},children:[e.jsx(r,{span:6,children:e.jsx(C,{children:e.jsx(ue,{title:"今日操作",value:I.today.total,prefix:e.jsx(dn,{}),suffix:"次"})})}),e.jsx(r,{span:6,children:e.jsx(C,{children:e.jsx(ue,{title:"今日成功",value:I.today.success,prefix:e.jsx(Yt,{}),suffix:"次",valueStyle:{color:"#3f8600"}})})}),e.jsx(r,{span:6,children:e.jsx(C,{children:e.jsx(ue,{title:"今日失败",value:I.today.failed,prefix:e.jsx(cn,{}),suffix:"次",valueStyle:{color:"#cf1322"}})})}),e.jsx(r,{span:6,children:e.jsx(C,{children:e.jsx(ue,{title:"本周操作",value:I.week.total,prefix:e.jsx(dn,{}),suffix:"次"})})})]}),e.jsx(C,{style:{marginBottom:16},children:e.jsxs(d,{layout:"inline",children:[e.jsx(d.Item,{label:"用户名",children:e.jsx($,{placeholder:"搜索用户名",style:{width:150},value:w.username,onChange:m=>W({...w,username:m.target.value}),allowClear:!0})}),e.jsx(d.Item,{label:"操作名称",children:e.jsx($,{placeholder:"搜索操作名称",style:{width:150},value:w.operationName,onChange:m=>W({...w,operationName:m.target.value}),allowClear:!0})}),e.jsx(d.Item,{label:"请求方法",children:e.jsxs(ce,{placeholder:"选择方法",style:{width:120},value:w.method,onChange:m=>W({...w,method:m}),allowClear:!0,children:[e.jsx(ce.Option,{value:"GET",children:"GET"}),e.jsx(ce.Option,{value:"POST",children:"POST"}),e.jsx(ce.Option,{value:"PUT",children:"PUT"}),e.jsx(ce.Option,{value:"DELETE",children:"DELETE"})]})}),e.jsx(d.Item,{label:"状态",children:e.jsxs(ce,{placeholder:"选择状态",style:{width:120},value:w.isSuccess,onChange:m=>W({...w,isSuccess:m}),allowClear:!0,children:[e.jsx(ce.Option,{value:!0,children:"成功"}),e.jsx(ce.Option,{value:!1,children:"失败"})]})}),e.jsx(d.Item,{label:"开始时间",children:e.jsx(Ke,{value:w.startDate?He(w.startDate):null,onChange:m=>W({...w,startDate:m?m.format("YYYY-MM-DD"):""}),style:{width:150}})}),e.jsx(d.Item,{label:"结束时间",children:e.jsx(Ke,{value:w.endDate?He(w.endDate):null,onChange:m=>W({...w,endDate:m?m.format("YYYY-MM-DD"):""}),style:{width:150}})}),e.jsx(d.Item,{children:e.jsxs(we,{children:[e.jsx(f,{type:"primary",onClick:g,children:"搜索"}),e.jsx(f,{onClick:Q,children:"重置"})]})})]})}),e.jsx(C,{children:e.jsx(h,{columns:te,dataSource:c,rowKey:"id",loading:L,pagination:{...N,showSizeChanger:!0,showQuickJumper:!0,showTotal:(m,T)=>`第 ${T[0]}-${T[1]} 条，共 ${m} 条`},onChange:J,size:"small",scroll:{x:1200,y:600}})}),e.jsx(Se,{title:"操作日志详情",open:V,onCancel:()=>he(!1),footer:[e.jsx(f,{onClick:()=>he(!1),children:"关闭"},"close")],width:1200,children:A&&e.jsx("div",{children:e.jsxs(R,{gutter:[16,16],children:[e.jsxs(r,{span:8,children:[e.jsx(l,{strong:!0,children:"操作时间："}),e.jsx(l,{children:He(A.createdAt).format("YYYY-MM-DD HH:mm:ss")})]}),e.jsxs(r,{span:8,children:[e.jsx(l,{strong:!0,children:"操作用户："}),e.jsx(l,{children:A.username||"未登录用户"})]}),e.jsxs(r,{span:8,children:[e.jsx(l,{strong:!0,children:"操作名称："}),e.jsx(l,{children:A.operationName})]}),e.jsxs(r,{span:8,children:[e.jsx(l,{strong:!0,children:"请求方法："}),e.jsx(ze,{color:A.method==="GET"?"blue":A.method==="POST"?"green":A.method==="PUT"?"orange":A.method==="DELETE"?"red":"default",children:A.method})]}),e.jsxs(r,{span:8,children:[e.jsx(l,{strong:!0,children:"状态码："}),e.jsx(l,{children:A.statusCode})]}),e.jsxs(r,{span:8,children:[e.jsx(l,{strong:!0,children:"执行状态："}),e.jsx(ze,{color:A.isSuccess?"success":"error",children:A.isSuccess?"成功":"失败"})]}),e.jsxs(r,{span:8,children:[e.jsx(l,{strong:!0,children:"耗时："}),e.jsx(l,{children:A.duration?`${A.duration}ms`:"-"})]}),e.jsxs(r,{span:8,children:[e.jsx(l,{strong:!0,children:"IP地址："}),e.jsx(l,{children:A.ipAddress})]}),e.jsxs(r,{span:8,children:[e.jsx(l,{strong:!0,children:"用户角色："}),e.jsx(l,{children:((pe=A.user)==null?void 0:pe.role)||"-"})]}),e.jsxs(r,{span:24,children:[e.jsx(l,{strong:!0,children:"请求路径："}),e.jsx(l,{code:!0,children:A.path})]}),e.jsxs(r,{span:24,children:[e.jsx(l,{strong:!0,children:"浏览器信息："}),e.jsx(l,{ellipsis:!0,children:A.userAgent})]}),A.requestParams&&e.jsxs(r,{span:24,children:[e.jsx(l,{strong:!0,children:"请求参数："}),e.jsx("pre",{style:{background:"#f5f5f5",padding:"8px",borderRadius:"4px",maxHeight:"200px",overflow:"auto",fontSize:"12px"},children:JSON.stringify(A.requestParams,null,2)})]}),A.responseData&&e.jsxs(r,{span:24,children:[e.jsx(l,{strong:!0,children:"返回数据："}),e.jsx("pre",{style:{background:"#f5f5f5",padding:"8px",borderRadius:"4px",maxHeight:"200px",overflow:"auto",fontSize:"12px"},children:JSON.stringify(A.responseData,null,2)})]}),A.errorMessage&&e.jsxs(r,{span:24,children:[e.jsx(l,{strong:!0,children:"错误信息："}),e.jsx(l,{type:"danger",children:A.errorMessage})]})]})})})]})},Js=()=>{const c=Mn(),S=Ht(),[L,D]=a.useState(new Date().getFullYear()),[N,z]=a.useState([]),[w,W]=a.useState([]),[V,he]=a.useState([]),[A,ie]=a.useState(!1),[I,je]=a.useState([]),[me,u]=a.useState([]),[g,Q]=a.useState([]),[J,xe]=a.useState(!1),[te,pe]=a.useState([new Date().getFullYear()]),[m,T]=a.useState([]),[j,q]=a.useState([]),[Z,Ie]=a.useState(!1),[$e,Me]=a.useState([]),[F,P]=a.useState([]),[ne,se]=a.useState([]),[K,ye]=a.useState(!1),[Ce,Ue]=a.useState(!1),[Ge,Qe]=a.useState([]),[M,re]=a.useState(null),[p,O]=a.useState([]),[G,de]=a.useState(!1),[Re,Te]=a.useState([]),[Ne,De]=a.useState([]),[i,y]=a.useState(!1),[_,s]=a.useState({issuedInvoices:[],receivedInvoices:[]}),[o,x]=a.useState(null),[v,X]=a.useState(!1),[fe,We]=a.useState({issuedGroups:[],receivedGroups:[]}),[Kt,lt]=a.useState(!1),[Nt,Yn]=a.useState([]),[At,Un]=a.useState(""),E=U=>{if(U==null||isNaN(U))return"¥0万";const ae=U/1e4;return`¥${ae.toFixed(ae%1===0?0:2)}万`},$t=async()=>{ie(!0);try{const U=new URLSearchParams;U.append("year",L.toString()),w.length>0&&w.forEach(H=>{U.append("companyIds",H)});const ae=await B.get(`/api/invoices/stats/quarterly?${U}`);he(ae.data.data.companies||[])}catch(U){console.error("获取季度统计数据失败:",U),k.error("获取季度统计数据失败"),he([])}finally{ie(!1)}},Vn=()=>{$t()},Hn=async()=>{if(!V||V.length===0){k.warning("暂无数据可导出");return}try{const U=V.map((oe,le)=>({序号:le+1,公司名称:oe.name,税号:oe.taxId,"1月":oe.monthly.jan||0,"2月":oe.monthly.feb||0,"3月":oe.monthly.mar||0,一季度:(oe.monthly.jan||0)+(oe.monthly.feb||0)+(oe.monthly.mar||0),"4月":oe.monthly.apr||0,"5月":oe.monthly.may||0,"6月":oe.monthly.jun||0,二季度:(oe.monthly.apr||0)+(oe.monthly.may||0)+(oe.monthly.jun||0),"7月":oe.monthly.jul||0,"8月":oe.monthly.aug||0,"9月":oe.monthly.sep||0,三季度:(oe.monthly.jul||0)+(oe.monthly.aug||0)+(oe.monthly.sep||0),"10月":oe.monthly.oct||0,"11月":oe.monthly.nov||0,"12月":oe.monthly.dec||0,四季度:(oe.monthly.oct||0)+(oe.monthly.nov||0)+(oe.monthly.dec||0),年度总计:Object.values(oe.monthly).reduce((qe,et)=>qe+(et||0),0)})),ae=await jn(()=>import("./xlsx-HQyCTDxi.js"),[],import.meta.url),H=ae.utils.json_to_sheet(U),ve=[{wch:6},{wch:25},{wch:20},{wch:15},{wch:15},{wch:15},{wch:15},{wch:15},{wch:15},{wch:15},{wch:15},{wch:15},{wch:15},{wch:15},{wch:15},{wch:15},{wch:15},{wch:15},{wch:15},{wch:18}];H["!cols"]=ve;const ge=ae.utils.decode_range(H["!ref"]);for(let oe=ge.s.r+1;oe<=ge.e.r;++oe)for(let le=3;le<=ge.e.c;++le){const qe=ae.utils.encode_cell({r:oe,c:le});H[qe]&&typeof H[qe].v=="number"&&(H[qe].z="#,##0.00")}for(let oe=ge.s.c;oe<=ge.e.c;++oe){const le=ae.utils.encode_cell({r:0,c:oe});H[le]&&(H[le].s={fill:{fgColor:{rgb:"4F81BD"}},font:{color:{rgb:"FFFFFF"},bold:!0},alignment:{horizontal:"center",vertical:"center"}})}const Be=ae.utils.book_new();ae.utils.book_append_sheet(Be,H,`${L}年季度开票汇总`),ae.writeFile(Be,`${L}年季度开票汇总.xlsx`),k.success("导出成功")}catch(U){console.error("导出失败:",U),k.error("导出失败")}},Kn=async()=>{xe(!0);try{const U=new URLSearchParams;I.length>0&&I.forEach(ve=>{U.append("years",ve.toString())}),me.length>0&&me.forEach(ve=>{U.append("companyIds",ve)});const H=(await B.get(`/api/invoices/stats/company-summary?${U}`)).data.data.companies||[];if(me.length===1){const ve=H.map(ge=>({...ge,q1:ge.monthly.jan+ge.monthly.feb+ge.monthly.mar,q2:ge.monthly.apr+ge.monthly.may+ge.monthly.jun,q3:ge.monthly.jul+ge.monthly.aug+ge.monthly.sep,q4:ge.monthly.oct+ge.monthly.nov+ge.monthly.dec,yearTotal:Object.values(ge.monthly).reduce((Be,oe)=>Be+oe,0)}));Q(ve)}else Q(H)}catch(U){console.error("获取公司开票汇总数据失败:",U),k.error("获取公司开票汇总数据失败"),Q([])}finally{xe(!1)}},Gt=async()=>{Ie(!0);try{const U=new URLSearchParams;te.length>0&&U.append("years",te.join(",")),m.length>0&&U.append("companyIds",m.join(","));const H=(await B.get(`/api/invoices/stats/invoice-count-summary?${U}`)).data.data||{},ve=H.companies||[],ge=H.summary||{},Be=H.availableYears||[];q(ve),Me(Be)}catch(U){console.error("获取发票张数汇总数据失败:",U),k.error("获取发票张数汇总数据失败"),q([])}finally{Ie(!1)}},Gn=()=>{Gt()},[Qn,gt]=a.useState(!1),[Le,jt]=a.useState(null),[Pe,ft]=a.useState(null),Qt=async U=>{try{const ae=await B.get(`/api/invoices/${U.id}`);jt(ae.data.data),ft(null),gt(!0)}catch(ae){console.error("获取发票详情失败:",ae),k.error("获取发票详情失败")}},Jt=async U=>{var ae,H;try{const ve=await B.get(`/api/received-invoices/${U.id}`);ft(ve.data.data),jt(null),gt(!0)}catch(ve){k.error(((H=(ae=ve.response)==null?void 0:ae.data)==null?void 0:H.message)||"获取取得发票详情失败")}},Zt=async()=>{de(!0);try{const U=new URLSearchParams;Re.length>0&&U.append("userIds",Re.join(","));const ae=await B.get(`/api/invoices/stats/user-invoice-summary?${U}`);if(ae.data.success){const{userSummary:H,allUsers:ve}=ae.data.data;O(H),De(ve)}else k.error(ae.data.message||"获取用户开票汇总失败")}catch(U){console.error("获取用户开票汇总失败:",U),k.error("获取用户开票汇总失败")}finally{de(!1)}},Jn=async(U,ae)=>{try{const H=new URLSearchParams;H.append("ownerUserId",U),H.append("otherUserId",ae);const ve=await B.get(`/api/invoices/stats/user-invoice-detail?${H}`);ve.data.success?(s(ve.data.data),y(!0)):k.error(ve.data.message||"获取详细信息失败")}catch(H){console.error("获取用户开票详细信息失败:",H),k.error("获取详细信息失败")}},Zn=async(U,ae)=>{try{const H=new URLSearchParams;H.append("ownerUserId",U),H.append("otherUserId",ae);const ve=await B.get(`/api/invoices/stats/user-invoice-group?${H}`);ve.data.success?(We(ve.data.data),X(!0)):k.error(ve.data.message||"获取汇总信息失败")}catch(H){console.error("获取用户开票汇总分组信息失败:",H),k.error("获取汇总信息失败")}},Xt=async(U,ae,H,ve,ge)=>{try{const Be=new URLSearchParams;Be.append("ownerUserId",U),Be.append("otherUserId",ae),Be.append("buyerName",H),Be.append("sellerName",ve),Be.append("invoiceType",ge);const oe=await B.get(`/api/invoices/stats/user-invoice-group-list?${Be}`);oe.data.success?(Yn(oe.data.data),Un(`${H} → ${ve} (${ge==="issued"?"开具发票":"取得发票"})`),lt(!0)):k.error(oe.data.message||"获取发票列表失败")}catch(Be){console.error("获取用户开票汇总分组发票列表失败:",Be),k.error("获取发票列表失败")}},Xn=()=>{if(me.length===0){k.warning("请先选择公司");return}Kn()},es=async()=>{if(!g||g.length===0){k.warning("暂无数据可导出");return}try{const U=g.map((le,qe)=>({序号:qe+1,公司名称:le.name,年度:le.year,"1月":le.monthly.jan||0,"2月":le.monthly.feb||0,"3月":le.monthly.mar||0,一季度:(le.monthly.jan||0)+(le.monthly.feb||0)+(le.monthly.mar||0),"4月":le.monthly.apr||0,"5月":le.monthly.may||0,"6月":le.monthly.jun||0,二季度:(le.monthly.apr||0)+(le.monthly.may||0)+(le.monthly.jun||0),"7月":le.monthly.jul||0,"8月":le.monthly.aug||0,"9月":le.monthly.sep||0,三季度:(le.monthly.jul||0)+(le.monthly.aug||0)+(le.monthly.sep||0),"10月":le.monthly.oct||0,"11月":le.monthly.nov||0,"12月":le.monthly.dec||0,四季度:(le.monthly.oct||0)+(le.monthly.nov||0)+(le.monthly.dec||0),年度总计:Object.values(le.monthly).reduce((et,bt)=>et+(bt||0),0)})),ae=await jn(()=>import("./xlsx-HQyCTDxi.js"),[],import.meta.url),H=ae.utils.json_to_sheet(U),ve=[{wch:6},{wch:25},{wch:8},{wch:15},{wch:15},{wch:15},{wch:15},{wch:15},{wch:15},{wch:15},{wch:15},{wch:15},{wch:15},{wch:15},{wch:15},{wch:15},{wch:15},{wch:15},{wch:15},{wch:18}];H["!cols"]=ve;const ge=ae.utils.decode_range(H["!ref"]);for(let le=ge.s.r+1;le<=ge.e.r;++le)for(let qe=3;qe<=ge.e.c;++qe){const et=ae.utils.encode_cell({r:le,c:qe});H[et]&&typeof H[et].v=="number"&&(H[et].z="#,##0.00")}for(let le=ge.s.c;le<=ge.e.c;++le){const qe=ae.utils.encode_cell({r:0,c:le});H[qe]&&(H[qe].s={fill:{fgColor:{rgb:"4F81BD"}},font:{color:{rgb:"FFFFFF"},bold:!0},alignment:{horizontal:"center",vertical:"center"}})}const Be=ae.utils.book_new(),oe=I.length>0?I.join(","):"全部";ae.utils.book_append_sheet(Be,H,"公司开票汇总"),ae.writeFile(Be,`${oe}年公司开票汇总.xlsx`),k.success("导出成功")}catch(U){console.error("导出失败:",U),k.error("导出失败")}};a.useEffect(()=>{(async()=>{try{const H=(await B.get("/api/companies/active")).data.data||[];z(Array.isArray(H)?H:[])}catch(ae){console.error("获取公司列表失败:",ae),z([])}})()},[]),a.useEffect(()=>{c.pathname.includes("quarterly-summary")?L&&$t():c.pathname.includes("company-summary")||(c.pathname.includes("invoice-count-summary")?Gt():c.pathname.includes("user-summary")&&Zt())},[c.pathname]),a.useEffect(()=>{c.pathname.includes("quarterly-summary")&&L&&$t()},[L,w]);const[Je,en]=a.useState({chains:[],nodes:[],edges:[],stats:{}}),[tn,nn]=a.useState(!1),[Ve,vt]=a.useState({companyName:"",startDate:"",endDate:"",maxDepth:5}),[Ze,Tt]=a.useState({current:1,pageSize:10,total:0}),ts=async()=>{nn(!0);try{const U=new URLSearchParams;Ve.companyName&&U.append("companyName",Ve.companyName),Ve.startDate&&U.append("startDate",Ve.startDate),Ve.endDate&&U.append("endDate",Ve.endDate),U.append("maxDepth",Ve.maxDepth.toString());const H=(await B.get(`/api/invoice-relations/penetration?${U}`)).data.data||{chains:[],nodes:[],edges:[],stats:{}};en(H),Tt(ve=>{var ge;return{...ve,total:((ge=H.chains)==null?void 0:ge.length)||0}})}catch(U){console.error("获取开票关系穿透图失败:",U),k.error("获取开票关系穿透图失败"),en({chains:[],nodes:[],edges:[],stats:{}})}finally{nn(!1)}},ns=()=>{var ae,H,ve,ge,Be,oe,le,qe,et,bt;const U=c.pathname;if(U.includes("quarterly-summary")){const be=[{title:"序号",dataIndex:"id",key:"id",width:60,align:"center",fixed:"left"},{title:"公司名称",dataIndex:"name",key:"name",width:250,fixed:"left"},{title:"1月",dataIndex:["monthly","jan"],key:"jan",width:100,align:"right",render:E},{title:"2月",dataIndex:["monthly","feb"],key:"feb",width:100,align:"right",render:E},{title:"3月",dataIndex:["monthly","mar"],key:"mar",width:100,align:"right",render:E},{title:"一季度",key:"q1",width:120,align:"right",className:"quarterly-column",onHeaderCell:()=>({style:{backgroundColor:"#bae7ff",fontWeight:"bold"}}),onCell:n=>{const b=n.monthly.jan+n.monthly.feb+n.monthly.mar;let Y="#e6f7ff";return b>=25e4&&b<3e5?Y="rgba(255, 165, 0, 0.8)":b>=3e5&&(Y="rgba(255, 0, 0, 0.7)"),{style:{backgroundColor:Y}}},render:n=>{const b=n.monthly.jan+n.monthly.feb+n.monthly.mar;return e.jsx("span",{style:{fontWeight:"bold"},children:E(b)})}},{title:"4月",dataIndex:["monthly","apr"],key:"apr",width:100,align:"right",render:E},{title:"5月",dataIndex:["monthly","may"],key:"may",width:100,align:"right",render:E},{title:"6月",dataIndex:["monthly","jun"],key:"jun",width:100,align:"right",render:E},{title:"二季度",key:"q2",width:120,align:"right",onHeaderCell:()=>({style:{backgroundColor:"#bae7ff",fontWeight:"bold"}}),onCell:n=>{const b=n.monthly.apr+n.monthly.may+n.monthly.jun;let Y="#e6f7ff";return b>=25e4&&b<3e5?Y="rgba(255, 165, 0, 0.8)":b>=3e5&&(Y="rgba(255, 0, 0, 0.7)"),{style:{backgroundColor:Y}}},render:n=>{const b=n.monthly.apr+n.monthly.may+n.monthly.jun;return e.jsx("span",{style:{fontWeight:"bold"},children:E(b)})}},{title:"7月",dataIndex:["monthly","jul"],key:"jul",width:100,align:"right",render:E},{title:"8月",dataIndex:["monthly","aug"],key:"aug",width:100,align:"right",render:E},{title:"9月",dataIndex:["monthly","sep"],key:"sep",width:100,align:"right",render:E},{title:"三季度",key:"q3",width:120,align:"right",onHeaderCell:()=>({style:{backgroundColor:"#bae7ff",fontWeight:"bold"}}),onCell:n=>{const b=n.monthly.jul+n.monthly.aug+n.monthly.sep;let Y="#e6f7ff";return b>=25e4&&b<3e5?Y="rgba(255, 165, 0, 0.8)":b>=3e5&&(Y="rgba(255, 0, 0, 0.7)"),{style:{backgroundColor:Y}}},render:n=>{const b=n.monthly.jul+n.monthly.aug+n.monthly.sep;return e.jsx("span",{style:{fontWeight:"bold"},children:E(b)})}},{title:"10月",dataIndex:["monthly","oct"],key:"oct",width:100,align:"right",render:E},{title:"11月",dataIndex:["monthly","nov"],key:"nov",width:100,align:"right",render:E},{title:"12月",dataIndex:["monthly","dec"],key:"dec",width:100,align:"right",render:E},{title:"四季度",key:"q4",width:120,align:"right",onHeaderCell:()=>({style:{backgroundColor:"#bae7ff",fontWeight:"bold"}}),onCell:n=>{const b=n.monthly.oct+n.monthly.nov+n.monthly.dec;let Y="#e6f7ff";return b>=25e4&&b<3e5?Y="rgba(255, 165, 0, 0.8)":b>=3e5&&(Y="rgba(255, 0, 0, 0.7)"),{style:{backgroundColor:Y}}},render:n=>{const b=n.monthly.oct+n.monthly.nov+n.monthly.dec;return e.jsx("span",{style:{fontWeight:"bold"},children:E(b)})}},{title:"年度总计",key:"total",width:120,align:"right",fixed:"right",onHeaderCell:()=>({style:{backgroundColor:"#52c41a",color:"white",fontWeight:"bold"}}),onCell:()=>({style:{backgroundColor:"#f6ffed"}}),render:n=>{const b=Object.values(n.monthly).reduce((Y,ke)=>Y+(Number(ke)||0),0);return e.jsx("span",{style:{fontWeight:"bold",color:"#52c41a",fontSize:"16px"},children:E(b)})}}],t=V;return e.jsxs("div",{children:[e.jsx(C,{title:"查询条件",size:"small",style:{marginBottom:16},children:e.jsxs(R,{gutter:16,align:"bottom",children:[e.jsx(r,{span:6,children:e.jsx(d.Item,{label:e.jsxs("span",{children:[e.jsx(l,{strong:!0,children:"年度"})," ",e.jsx(l,{type:"danger",children:"*"})]}),style:{marginBottom:0},children:e.jsx(ce,{value:L,onChange:D,style:{width:"100%"},placeholder:"请选择年度",size:"small",children:Array.from({length:new Date().getFullYear()-2019+1},(n,b)=>new Date().getFullYear()-b).filter(n=>n>=2019).map(n=>e.jsxs(ce.Option,{value:n,children:[n,"年"]},n))})})}),e.jsx(r,{span:10,children:e.jsx(d.Item,{label:e.jsx(l,{strong:!0,children:"公司"}),style:{marginBottom:0},children:e.jsx(ce,{mode:"multiple",value:w,onChange:W,style:{width:"100%"},placeholder:"请选择公司（不选则显示全部）",allowClear:!0,size:"small",children:Array.isArray(N)&&N.map(n=>e.jsx(ce.Option,{value:n.id,children:n.name},n.id))})})}),e.jsx(r,{span:8,children:e.jsxs(we,{children:[e.jsx(f,{type:"primary",icon:e.jsx(st,{}),size:"small",onClick:Vn,loading:A,children:"查询"}),e.jsx(f,{icon:e.jsx(tt,{}),size:"small",onClick:Hn,children:"导出"})]})})]})}),e.jsx(C,{style:{marginBottom:16},children:e.jsx(h,{columns:be,dataSource:t,rowKey:n=>n.id||n.name,pagination:!1,scroll:{x:1800},size:"small",bordered:!0,loading:A,locale:{emptyText:"暂无数据，请先在开票管理中添加发票数据"},summary:()=>{if(!t||t.length===0)return null;const n={jan:0,feb:0,mar:0,apr:0,may:0,jun:0,jul:0,aug:0,sep:0,oct:0,nov:0,dec:0};t.forEach(Ye=>{Ye.monthly&&Object.keys(n).forEach(nt=>{n[nt]+=Ye.monthly[nt]||0})});const b=n.jan+n.feb+n.mar,Y=n.apr+n.may+n.jun,ke=n.jul+n.aug+n.sep,ee=n.oct+n.nov+n.dec,Ee=b+Y+ke+ee;return e.jsxs(h.Summary.Row,{style:{backgroundColor:"#ffe7ba",fontWeight:"bold"},children:[e.jsx(h.Summary.Cell,{index:0,colSpan:2,align:"center",children:e.jsx("span",{style:{fontSize:"16px",color:"#d46b08",fontWeight:"bold"},children:"合计"})}),e.jsx(h.Summary.Cell,{index:2,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:E(n.jan)})}),e.jsx(h.Summary.Cell,{index:3,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:E(n.feb)})}),e.jsx(h.Summary.Cell,{index:4,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:E(n.mar)})}),e.jsx(h.Summary.Cell,{index:5,align:"right",children:e.jsx("span",{style:{backgroundColor:"#bae7ff",color:"#1890ff",fontWeight:"bold",padding:"4px"},children:E(b)})}),e.jsx(h.Summary.Cell,{index:6,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:E(n.apr)})}),e.jsx(h.Summary.Cell,{index:7,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:E(n.may)})}),e.jsx(h.Summary.Cell,{index:8,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:E(n.jun)})}),e.jsx(h.Summary.Cell,{index:9,align:"right",children:e.jsx("span",{style:{backgroundColor:"#bae7ff",color:"#1890ff",fontWeight:"bold",padding:"4px"},children:E(Y)})}),e.jsx(h.Summary.Cell,{index:10,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:E(n.jul)})}),e.jsx(h.Summary.Cell,{index:11,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:E(n.aug)})}),e.jsx(h.Summary.Cell,{index:12,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:E(n.sep)})}),e.jsx(h.Summary.Cell,{index:13,align:"right",children:e.jsx("span",{style:{backgroundColor:"#bae7ff",color:"#1890ff",fontWeight:"bold",padding:"4px"},children:E(ke)})}),e.jsx(h.Summary.Cell,{index:14,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:E(n.oct)})}),e.jsx(h.Summary.Cell,{index:15,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:E(n.nov)})}),e.jsx(h.Summary.Cell,{index:16,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:E(n.dec)})}),e.jsx(h.Summary.Cell,{index:17,align:"right",children:e.jsx("span",{style:{backgroundColor:"#bae7ff",color:"#1890ff",fontWeight:"bold",padding:"4px"},children:E(ee)})}),e.jsx(h.Summary.Cell,{index:18,align:"right",children:e.jsx("span",{style:{fontWeight:"bold",color:"#52c41a",fontSize:"16px",backgroundColor:"#f6ffed",padding:"4px"},children:E(Ee)})})]})}})})]})}if(U.includes("invoice-count-summary")){const be=[{title:"序号",key:"index",width:60,align:"center",render:(t,n,b)=>b+1},{title:"公司名称",dataIndex:"name",key:"name",width:200,fixed:"left",onCell:(t,n)=>{if(!j||j.length===0||n===void 0)return{};const b=t.name;let Y=0,ke=!0;for(let ee=0;ee<n;ee++)if(j[ee].name===b){ke=!1;break}if(ke)for(let ee=n;ee<j.length&&j[ee].name===b;ee++)Y++;return{rowSpan:ke?Y:0}}},{title:"年度",dataIndex:"year",key:"year",width:80,align:"center"},{title:"1月",dataIndex:["monthly","jan"],key:"jan",width:80,align:"right",render:t=>t||0},{title:"2月",dataIndex:["monthly","feb"],key:"feb",width:80,align:"right",render:t=>t||0},{title:"3月",dataIndex:["monthly","mar"],key:"mar",width:80,align:"right",render:t=>t||0},{title:"一季度",key:"q1",width:100,align:"right",onHeaderCell:()=>({style:{backgroundColor:"#bae7ff",fontWeight:"bold"}}),onCell:t=>{const n=t.monthly||{};return{style:{backgroundColor:(n.jan||0)+(n.feb||0)+(n.mar||0)===0?"#FFFF00":"#e6f7ff"}}},render:t=>{const n=t.monthly||{},b=(n.jan||0)+(n.feb||0)+(n.mar||0);return e.jsx("span",{style:{fontWeight:"bold"},children:b})}},{title:"4月",dataIndex:["monthly","apr"],key:"apr",width:80,align:"right",render:t=>t||0},{title:"5月",dataIndex:["monthly","may"],key:"may",width:80,align:"right",render:t=>t||0},{title:"6月",dataIndex:["monthly","jun"],key:"jun",width:80,align:"right",render:t=>t||0},{title:"二季度",key:"q2",width:100,align:"right",onHeaderCell:()=>({style:{backgroundColor:"#bae7ff",fontWeight:"bold"}}),onCell:t=>{const n=t.monthly||{};return{style:{backgroundColor:(n.apr||0)+(n.may||0)+(n.jun||0)===0?"#FFFF00":"#e6f7ff"}}},render:t=>{const n=t.monthly||{},b=(n.apr||0)+(n.may||0)+(n.jun||0);return e.jsx("span",{style:{fontWeight:"bold"},children:b})}},{title:"7月",dataIndex:["monthly","jul"],key:"jul",width:80,align:"right",render:t=>t||0},{title:"8月",dataIndex:["monthly","aug"],key:"aug",width:80,align:"right",render:t=>t||0},{title:"9月",dataIndex:["monthly","sep"],key:"sep",width:80,align:"right",render:t=>t||0},{title:"三季度",key:"q3",width:100,align:"right",onHeaderCell:()=>({style:{backgroundColor:"#bae7ff",fontWeight:"bold"}}),onCell:t=>{const n=t.monthly||{};return{style:{backgroundColor:(n.jul||0)+(n.aug||0)+(n.sep||0)===0?"#FFFF00":"#e6f7ff"}}},render:t=>{const n=t.monthly||{},b=(n.jul||0)+(n.aug||0)+(n.sep||0);return e.jsx("span",{style:{fontWeight:"bold"},children:b})}},{title:"10月",dataIndex:["monthly","oct"],key:"oct",width:80,align:"right",render:t=>t||0},{title:"11月",dataIndex:["monthly","nov"],key:"nov",width:80,align:"right",render:t=>t||0},{title:"12月",dataIndex:["monthly","dec"],key:"dec",width:80,align:"right",render:t=>t||0},{title:"四季度",key:"q4",width:100,align:"right",onHeaderCell:()=>({style:{backgroundColor:"#bae7ff",fontWeight:"bold"}}),onCell:t=>{const n=t.monthly||{};return{style:{backgroundColor:(n.oct||0)+(n.nov||0)+(n.dec||0)===0?"#FFFF00":"#e6f7ff"}}},render:t=>{const n=t.monthly||{},b=(n.oct||0)+(n.nov||0)+(n.dec||0);return e.jsx("span",{style:{fontWeight:"bold"},children:b})}},{title:"年度总计",key:"yearTotal",width:120,align:"right",fixed:"right",onHeaderCell:()=>({style:{backgroundColor:"#f6ffed",fontWeight:"bold"}}),onCell:()=>({style:{backgroundColor:"#f6ffed"}}),render:t=>{const n=t.monthly||{},b=Object.values(n).reduce((Y,ke)=>Y+(Number(ke)||0),0);return e.jsx("span",{style:{fontWeight:"bold",color:"#52c41a",fontSize:"16px"},children:b})}}];return e.jsxs("div",{children:[e.jsx(C,{title:"查询条件",size:"small",style:{marginBottom:16},children:e.jsxs(R,{gutter:16,align:"bottom",children:[e.jsx(r,{span:6,children:e.jsx(d.Item,{label:e.jsx(l,{strong:!0,children:"年度"}),style:{marginBottom:0},children:e.jsx(ce,{mode:"multiple",value:te,onChange:pe,style:{width:"100%"},placeholder:"请选择年度（不选则查询所有年度）",allowClear:!0,size:"small",children:($e.length>0?$e:Array.from({length:new Date().getFullYear()-2019+1},(t,n)=>new Date().getFullYear()-n).filter(t=>t>=2019)).map(t=>e.jsxs(ce.Option,{value:t,children:[t,"年"]},t))})})}),e.jsx(r,{span:10,children:e.jsx(d.Item,{label:e.jsx(l,{strong:!0,children:"公司"}),style:{marginBottom:0},children:e.jsx(ce,{mode:"multiple",value:m,onChange:t=>T(t),style:{width:"100%"},placeholder:"请选择公司（不选则查询所有公司）",size:"small",maxTagCount:"responsive",children:Array.isArray(N)&&N.map(t=>e.jsx(ce.Option,{value:t.id,children:t.name},t.id))})})}),e.jsx(r,{span:8,children:e.jsxs(we,{children:[e.jsx(f,{type:"primary",icon:e.jsx(st,{}),size:"small",onClick:Gn,loading:Z,children:"查询"}),e.jsx(f,{icon:e.jsx(tt,{}),size:"small",onClick:()=>{k.info("导出功能开发中...")},children:"导出"})]})})]})}),e.jsx(C,{style:{marginBottom:16,height:"calc(100vh - 280px)",display:"flex",flexDirection:"column"},children:e.jsx("div",{style:{flex:1,overflow:"hidden"},children:e.jsx(h,{columns:be,dataSource:j,rowKey:t=>t.id||`${t.name||"unknown"}_${t.year||"unknown"}`,pagination:{pageSize:20,showSizeChanger:!0,showQuickJumper:!0,showTotal:(t,n)=>`共 ${t} 条记录，第 ${n[0]}-${n[1]} 条`,pageSizeOptions:["10","20","50","100"],position:["bottomCenter"],style:{position:"sticky",bottom:0,backgroundColor:"white",zIndex:1,padding:"8px 0",borderTop:"1px solid #f0f0f0"}},size:"small",bordered:!0,loading:Z,locale:{emptyText:"暂无数据，请先在开票管理中添加发票数据"},scroll:{x:1800,y:"calc(100vh - 400px)"},summary:()=>{if(!j||j.length===0)return null;const t={jan:0,feb:0,mar:0,apr:0,may:0,jun:0,jul:0,aug:0,sep:0,oct:0,nov:0,dec:0};j.forEach(Ee=>{Ee.monthly&&Object.keys(t).forEach(Ye=>{t[Ye]+=Ee.monthly[Ye]||0})});const n=t.jan+t.feb+t.mar,b=t.apr+t.may+t.jun,Y=t.jul+t.aug+t.sep,ke=t.oct+t.nov+t.dec,ee=n+b+Y+ke;return e.jsxs(h.Summary.Row,{style:{backgroundColor:"#ffe7ba",fontWeight:"bold"},children:[e.jsx(h.Summary.Cell,{index:0,colSpan:3,align:"center",children:e.jsx("span",{style:{fontSize:"16px",color:"#d46b08",fontWeight:"bold"},children:"合计"})}),e.jsx(h.Summary.Cell,{index:3,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:t.jan})}),e.jsx(h.Summary.Cell,{index:4,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:t.feb})}),e.jsx(h.Summary.Cell,{index:5,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:t.mar})}),e.jsx(h.Summary.Cell,{index:6,align:"right",children:e.jsx("span",{style:{backgroundColor:"#bae7ff",color:"#1890ff",fontWeight:"bold",padding:"4px"},children:n})}),e.jsx(h.Summary.Cell,{index:7,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:t.apr})}),e.jsx(h.Summary.Cell,{index:8,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:t.may})}),e.jsx(h.Summary.Cell,{index:9,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:t.jun})}),e.jsx(h.Summary.Cell,{index:10,align:"right",children:e.jsx("span",{style:{backgroundColor:"#bae7ff",color:"#1890ff",fontWeight:"bold",padding:"4px"},children:b})}),e.jsx(h.Summary.Cell,{index:11,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:t.jul})}),e.jsx(h.Summary.Cell,{index:12,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:t.aug})}),e.jsx(h.Summary.Cell,{index:13,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:t.sep})}),e.jsx(h.Summary.Cell,{index:14,align:"right",children:e.jsx("span",{style:{backgroundColor:"#bae7ff",color:"#1890ff",fontWeight:"bold",padding:"4px"},children:Y})}),e.jsx(h.Summary.Cell,{index:15,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:t.oct})}),e.jsx(h.Summary.Cell,{index:16,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:t.nov})}),e.jsx(h.Summary.Cell,{index:17,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:t.dec})}),e.jsx(h.Summary.Cell,{index:18,align:"right",children:e.jsx("span",{style:{backgroundColor:"#bae7ff",color:"#1890ff",fontWeight:"bold",padding:"4px"},children:ke})}),e.jsx(h.Summary.Cell,{index:19,align:"right",children:e.jsx("span",{style:{fontWeight:"bold",color:"#52c41a",fontSize:"16px",backgroundColor:"#f6ffed",padding:"4px"},children:ee})})]})}})})})]})}if(U.includes("company-summary")){const be=[{title:"序号",key:"index",width:60,align:"center",render:(t,n,b)=>b+1},{title:"公司名称",dataIndex:"name",key:"name",width:200,fixed:"left",onCell:(t,n)=>{if(!g||g.length===0||n===void 0)return{};const b=t.name;let Y=0,ke=!0;for(let ee=0;ee<n;ee++)if(g[ee].name===b){ke=!1;break}if(ke)for(let ee=n;ee<g.length&&g[ee].name===b;ee++)Y++;return{rowSpan:ke?Y:0}}},{title:"年度",dataIndex:"year",key:"year",width:80,align:"center"},{title:"1月",dataIndex:["monthly","jan"],key:"jan",width:80,align:"right",render:t=>E(t)},{title:"2月",dataIndex:["monthly","feb"],key:"feb",width:80,align:"right",render:t=>E(t)},{title:"3月",dataIndex:["monthly","mar"],key:"mar",width:80,align:"right",render:t=>E(t)},{title:"一季度",key:"q1",width:100,align:"right",onHeaderCell:()=>({style:{backgroundColor:"#bae7ff",fontWeight:"bold"}}),onCell:t=>{const n=t.monthly||{},b=(n.jan||0)+(n.feb||0)+(n.mar||0);let Y="#e6f7ff";return b>=25e4&&b<3e5?Y="rgba(255, 165, 0, 0.8)":b>=3e5&&(Y="rgba(255, 0, 0, 0.7)"),{style:{backgroundColor:Y}}},render:t=>{const n=t.monthly||{},b=(n.jan||0)+(n.feb||0)+(n.mar||0);return e.jsx("span",{style:{fontWeight:"bold"},children:E(b)})}},{title:"4月",dataIndex:["monthly","apr"],key:"apr",width:80,align:"right",render:t=>E(t)},{title:"5月",dataIndex:["monthly","may"],key:"may",width:80,align:"right",render:t=>E(t)},{title:"6月",dataIndex:["monthly","jun"],key:"jun",width:80,align:"right",render:t=>E(t)},{title:"二季度",key:"q2",width:100,align:"right",onHeaderCell:()=>({style:{backgroundColor:"#bae7ff",fontWeight:"bold"}}),onCell:t=>{const n=t.monthly||{},b=(n.apr||0)+(n.may||0)+(n.jun||0);let Y="#e6f7ff";return b>=25e4&&b<3e5?Y="rgba(255, 165, 0, 0.8)":b>=3e5&&(Y="rgba(255, 0, 0, 0.7)"),{style:{backgroundColor:Y}}},render:t=>{const n=t.monthly||{},b=(n.apr||0)+(n.may||0)+(n.jun||0);return e.jsx("span",{style:{fontWeight:"bold"},children:E(b)})}},{title:"7月",dataIndex:["monthly","jul"],key:"jul",width:80,align:"right",render:t=>E(t)},{title:"8月",dataIndex:["monthly","aug"],key:"aug",width:80,align:"right",render:t=>E(t)},{title:"9月",dataIndex:["monthly","sep"],key:"sep",width:80,align:"right",render:t=>E(t)},{title:"三季度",key:"q3",width:100,align:"right",onHeaderCell:()=>({style:{backgroundColor:"#bae7ff",fontWeight:"bold"}}),onCell:t=>{const n=t.monthly||{},b=(n.jul||0)+(n.aug||0)+(n.sep||0);let Y="#e6f7ff";return b>=25e4&&b<3e5?Y="rgba(255, 165, 0, 0.8)":b>=3e5&&(Y="rgba(255, 0, 0, 0.7)"),{style:{backgroundColor:Y}}},render:t=>{const n=t.monthly||{},b=(n.jul||0)+(n.aug||0)+(n.sep||0);return e.jsx("span",{style:{fontWeight:"bold"},children:E(b)})}},{title:"10月",dataIndex:["monthly","oct"],key:"oct",width:80,align:"right",render:t=>E(t)},{title:"11月",dataIndex:["monthly","nov"],key:"nov",width:80,align:"right",render:t=>E(t)},{title:"12月",dataIndex:["monthly","dec"],key:"dec",width:80,align:"right",render:t=>E(t)},{title:"四季度",key:"q4",width:100,align:"right",onHeaderCell:()=>({style:{backgroundColor:"#bae7ff",fontWeight:"bold"}}),onCell:t=>{const n=t.monthly||{},b=(n.oct||0)+(n.nov||0)+(n.dec||0);let Y="#e6f7ff";return b>=25e4&&b<3e5?Y="rgba(255, 165, 0, 0.8)":b>=3e5&&(Y="rgba(255, 0, 0, 0.7)"),{style:{backgroundColor:Y}}},render:t=>{const n=t.monthly||{},b=(n.oct||0)+(n.nov||0)+(n.dec||0);return e.jsx("span",{style:{fontWeight:"bold"},children:E(b)})}},{title:"年度总计",key:"yearTotal",width:120,align:"right",fixed:"right",onHeaderCell:()=>({style:{backgroundColor:"#f6ffed",fontWeight:"bold"}}),onCell:()=>({style:{backgroundColor:"#f6ffed"}}),render:t=>{const n=t.monthly||{},b=Object.values(n).reduce((Y,ke)=>Y+(Number(ke)||0),0);return e.jsx("span",{style:{fontWeight:"bold",color:"#52c41a",fontSize:"16px"},children:E(b)})}}];return e.jsxs("div",{children:[e.jsx(C,{title:"查询条件",size:"small",style:{marginBottom:16},children:e.jsxs(R,{gutter:16,align:"bottom",children:[e.jsx(r,{span:6,children:e.jsx(d.Item,{label:e.jsx(l,{strong:!0,children:"年度"}),style:{marginBottom:0},children:e.jsx(ce,{mode:"multiple",value:I,onChange:je,style:{width:"100%"},placeholder:"请选择年度（不选则显示全部）",allowClear:!0,size:"small",children:Array.from({length:new Date().getFullYear()-2019+1},(t,n)=>new Date().getFullYear()-n).filter(t=>t>=2019).map(t=>e.jsxs(ce.Option,{value:t,children:[t,"年"]},t))})})}),e.jsx(r,{span:10,children:e.jsx(d.Item,{label:e.jsxs("span",{children:[e.jsx(l,{strong:!0,children:"公司"})," ",e.jsx(l,{type:"danger",children:"*"})]}),style:{marginBottom:0},children:e.jsx(ce,{mode:"multiple",value:me,onChange:t=>u(t),style:{width:"100%"},placeholder:"请选择公司（必选，支持多选）",size:"small",maxTagCount:"responsive",children:Array.isArray(N)&&N.map(t=>e.jsx(ce.Option,{value:t.id,children:t.name},t.id))})})}),e.jsx(r,{span:8,children:e.jsxs(we,{children:[e.jsx(f,{type:"primary",icon:e.jsx(st,{}),size:"small",onClick:Xn,loading:J,children:"查询"}),e.jsx(f,{icon:e.jsx(tt,{}),size:"small",onClick:es,children:"导出"})]})})]})}),e.jsx(C,{style:{marginBottom:16,height:"calc(100vh - 280px)",display:"flex",flexDirection:"column"},children:e.jsx("div",{style:{flex:1,overflow:"hidden"},children:e.jsx(h,{columns:be,dataSource:g,rowKey:t=>`${t.name}_${t.year}`,pagination:{pageSize:20,showSizeChanger:!0,showQuickJumper:!0,showTotal:(t,n)=>`共 ${t} 条记录，第 ${n[0]}-${n[1]} 条`,pageSizeOptions:["10","20","50","100"],position:["bottomCenter"],style:{position:"sticky",bottom:0,backgroundColor:"white",zIndex:1,padding:"8px 0",borderTop:"1px solid #f0f0f0"}},size:"small",bordered:!0,loading:J,locale:{emptyText:"暂无数据，请先在开票管理中添加发票数据"},scroll:{x:1800,y:"calc(100vh - 400px)"},summary:()=>{if(!g||g.length===0)return null;const t={jan:0,feb:0,mar:0,apr:0,may:0,jun:0,jul:0,aug:0,sep:0,oct:0,nov:0,dec:0};g.forEach(Ee=>{Ee.monthly&&Object.keys(t).forEach(Ye=>{t[Ye]+=Ee.monthly[Ye]||0})});const n=t.jan+t.feb+t.mar,b=t.apr+t.may+t.jun,Y=t.jul+t.aug+t.sep,ke=t.oct+t.nov+t.dec,ee=n+b+Y+ke;return e.jsxs(h.Summary.Row,{style:{backgroundColor:"#ffe7ba",fontWeight:"bold"},children:[e.jsx(h.Summary.Cell,{index:0,colSpan:3,align:"center",children:e.jsx("span",{style:{fontSize:"16px",color:"#d46b08",fontWeight:"bold"},children:"合计"})}),e.jsx(h.Summary.Cell,{index:3,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:E(t.jan)})}),e.jsx(h.Summary.Cell,{index:4,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:E(t.feb)})}),e.jsx(h.Summary.Cell,{index:5,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:E(t.mar)})}),e.jsx(h.Summary.Cell,{index:6,align:"right",children:e.jsx("span",{style:{backgroundColor:"#bae7ff",color:"#1890ff",fontWeight:"bold",padding:"4px"},children:E(n)})}),e.jsx(h.Summary.Cell,{index:7,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:E(t.apr)})}),e.jsx(h.Summary.Cell,{index:8,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:E(t.may)})}),e.jsx(h.Summary.Cell,{index:9,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:E(t.jun)})}),e.jsx(h.Summary.Cell,{index:10,align:"right",children:e.jsx("span",{style:{backgroundColor:"#bae7ff",color:"#1890ff",fontWeight:"bold",padding:"4px"},children:E(b)})}),e.jsx(h.Summary.Cell,{index:11,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:E(t.jul)})}),e.jsx(h.Summary.Cell,{index:12,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:E(t.aug)})}),e.jsx(h.Summary.Cell,{index:13,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:E(t.sep)})}),e.jsx(h.Summary.Cell,{index:14,align:"right",children:e.jsx("span",{style:{backgroundColor:"#bae7ff",color:"#1890ff",fontWeight:"bold",padding:"4px"},children:E(Y)})}),e.jsx(h.Summary.Cell,{index:15,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:E(t.oct)})}),e.jsx(h.Summary.Cell,{index:16,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:E(t.nov)})}),e.jsx(h.Summary.Cell,{index:17,align:"right",children:e.jsx("span",{style:{color:"#d46b08"},children:E(t.dec)})}),e.jsx(h.Summary.Cell,{index:18,align:"right",children:e.jsx("span",{style:{backgroundColor:"#bae7ff",color:"#1890ff",fontWeight:"bold",padding:"4px"},children:E(ke)})}),e.jsx(h.Summary.Cell,{index:19,align:"right",children:e.jsx("span",{style:{fontWeight:"bold",color:"#52c41a",fontSize:"16px",backgroundColor:"#f6ffed",padding:"4px"},children:E(ee)})})]})}})})})]})}if(U.includes("user-summary")){const be=[{title:"序号",key:"index",width:60,align:"center",render:(t,n,b)=>b+1},{title:"所属用户",key:"ownerUser",width:150,render:(t,n)=>e.jsxs("div",{children:[e.jsx("div",{style:{fontWeight:"bold"},children:n.ownerUsername}),e.jsx("div",{style:{fontSize:"12px",color:"#666"},children:n.ownerEmail})]})},{title:"其他所属用户",key:"otherUser",width:150,render:(t,n)=>e.jsxs("div",{children:[e.jsx("div",{style:{fontWeight:"bold"},children:n.otherUsername}),e.jsx("div",{style:{fontSize:"12px",color:"#666"},children:n.otherEmail})]})},{title:"开具发票总金额",dataIndex:"issuedAmount",key:"issuedAmount",width:150,align:"right",render:t=>e.jsxs("span",{style:{fontWeight:"bold",color:t>0?"#f5222d":"#999",fontSize:"14px"},children:["¥",t.toLocaleString("zh-CN",{minimumFractionDigits:2})]})},{title:"取得发票总金额",dataIndex:"receivedAmount",key:"receivedAmount",width:150,align:"right",render:t=>e.jsxs("span",{style:{fontWeight:"bold",color:t>0?"#52c41a":"#999",fontSize:"14px"},children:["¥",t.toLocaleString("zh-CN",{minimumFractionDigits:2})]})},{title:"操作",key:"action",width:160,align:"center",render:(t,n)=>e.jsxs(we,{children:[e.jsx(f,{type:"link",size:"small",onClick:()=>{x(n),Jn(n.ownerUserId,n.otherUserId)},children:"查看详细"}),e.jsx(f,{type:"link",size:"small",onClick:()=>{x(n),Zn(n.ownerUserId,n.otherUserId)},children:"查看汇总"})]})}];return e.jsxs("div",{children:[e.jsx(C,{title:"查询条件",size:"small",style:{marginBottom:16},children:e.jsxs(R,{gutter:16,align:"bottom",children:[e.jsx(r,{span:8,children:e.jsx(d.Item,{label:e.jsx(l,{strong:!0,children:"所属用户"}),style:{marginBottom:0},children:e.jsx(ce,{mode:"multiple",value:Re,onChange:Te,style:{width:"100%"},placeholder:"请选择所属用户（不选则查询所有用户）",allowClear:!0,size:"small",showSearch:!0,filterOption:(t,n)=>String((n==null?void 0:n.label)??"").toLowerCase().includes(t.toLowerCase()),children:Ne.map(t=>e.jsxs(ce.Option,{value:t.id,label:t.username,children:[t.username," (",t.email,")"]},t.id))})})}),e.jsx(r,{span:8,children:e.jsxs(we,{children:[e.jsx(f,{type:"primary",icon:e.jsx(st,{}),size:"small",onClick:Zt,loading:G,children:"查询"}),e.jsx(f,{icon:e.jsx(tt,{}),size:"small",onClick:()=>{k.info("导出功能开发中...")},children:"导出"})]})})]})}),e.jsx(C,{style:{marginBottom:16},children:e.jsx(h,{columns:be,dataSource:p,rowKey:t=>`${t.ownerUserId}-${t.otherUserId}`,pagination:{showSizeChanger:!0,showQuickJumper:!0,showTotal:(t,n)=>`共 ${t} 条记录，第 ${n[0]}-${n[1]} 条`,pageSizeOptions:["10","20","50","100"],defaultPageSize:20},size:"small",bordered:!0,loading:G,locale:{emptyText:"暂无数据，请先在开票管理中添加发票数据"},scroll:{x:800}})}),e.jsx(Se,{title:o?`${o.ownerUsername} ↔ ${o.otherUsername} 开票详情`:"开票详情",open:i,onCancel:()=>y(!1),footer:[e.jsx(f,{onClick:()=>y(!1),children:"关闭"},"close")],width:1400,style:{top:20},bodyStyle:{height:"70vh",overflow:"auto"},children:e.jsx(hn,{defaultActiveKey:"issued",items:[{key:"issued",label:`📤 开具发票 (总金额: ¥${(_.issuedInvoices||[]).reduce((t,n)=>t+Number(n.totalAmount||0),0).toLocaleString("zh-CN",{minimumFractionDigits:2})})`,children:e.jsx(h,{columns:[{title:"发票号码",dataIndex:"invoiceNumber",key:"invoiceNumber",width:150},{title:"开票日期",dataIndex:"invoiceDate",key:"invoiceDate",width:120,render:t=>new Date(t).toLocaleDateString("zh-CN")},{title:"购买方",dataIndex:"buyerName",key:"buyerName",width:200},{title:"销售方",dataIndex:"sellerName",key:"sellerName",width:200},{title:"价税合计",dataIndex:"totalAmount",key:"totalAmount",width:120,align:"right",render:t=>e.jsxs("span",{style:{fontWeight:"bold",color:"#f5222d"},children:["¥",t.toLocaleString("zh-CN",{minimumFractionDigits:2})]})},{title:"查看发票",key:"action",width:100,align:"center",render:(t,n)=>e.jsx(f,{type:"link",size:"small",onClick:()=>Qt(n),children:"查看发票"})}],dataSource:_.issuedInvoices,rowKey:t=>`issued-${t.id}`,pagination:{pageSize:10},size:"small",scroll:{y:"calc(60vh - 100px)"}})},{key:"received",label:`📥 取得发票 (总金额: ¥${(_.receivedInvoices||[]).reduce((t,n)=>t+Number(n.totalAmount||0),0).toLocaleString("zh-CN",{minimumFractionDigits:2})})`,children:e.jsx(h,{columns:[{title:"发票号码",dataIndex:"invoiceNumber",key:"invoiceNumber",width:150},{title:"开票日期",dataIndex:"invoiceDate",key:"invoiceDate",width:120,render:t=>new Date(t).toLocaleDateString("zh-CN")},{title:"购买方",dataIndex:"buyerName",key:"buyerName",width:200},{title:"销售方",dataIndex:"sellerName",key:"sellerName",width:200},{title:"价税合计",dataIndex:"totalAmount",key:"totalAmount",width:120,align:"right",render:t=>e.jsxs("span",{style:{fontWeight:"bold",color:"#52c41a"},children:["¥",t.toLocaleString("zh-CN",{minimumFractionDigits:2})]})},{title:"查看发票",key:"action",width:100,align:"center",render:(t,n)=>e.jsx(f,{type:"link",size:"small",onClick:()=>Jt(n),children:"查看发票"})}],dataSource:_.receivedInvoices,rowKey:t=>`received-${t.id}`,pagination:{pageSize:10},size:"small",scroll:{y:"calc(60vh - 200px)"}})}]})}),e.jsxs(Se,{title:Le?"开具发票详情":Pe?"取得发票详情":"发票详情",open:Qn,onCancel:()=>{gt(!1),jt(null),ft(null)},footer:[e.jsx(f,{onClick:()=>{gt(!1),jt(null),ft(null)},children:"关闭"},"close")],width:1200,children:[Le&&e.jsxs("div",{children:[e.jsx(C,{title:"基本信息",size:"small",style:{marginBottom:16},children:e.jsxs(R,{gutter:[16,8],children:[e.jsx(r,{span:6,children:e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"发票号码"}),e.jsx("div",{style:{fontWeight:"bold",fontSize:"16px"},children:Le.invoiceNumber})]})}),e.jsx(r,{span:6,children:e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"发票代码"}),e.jsx("div",{style:{fontWeight:"bold",fontSize:"16px"},children:Le.invoiceCode})]})}),e.jsx(r,{span:6,children:e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"开票日期"}),e.jsx("div",{style:{fontWeight:"bold",fontSize:"16px"},children:(ae=Le.invoiceDate)==null?void 0:ae.split("T")[0]})]})}),e.jsx(r,{span:6,children:e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"开票人"}),e.jsx("div",{style:{fontWeight:"bold",fontSize:"16px"},children:Le.drawer||"-"})]})}),e.jsx(r,{span:24,children:e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"备注"}),e.jsx("div",{style:{marginTop:4,padding:"8px",backgroundColor:"#f5f5f5",borderRadius:"4px",minHeight:"32px"},children:Le.remarks||""})]})})]})}),e.jsxs(R,{gutter:16,style:{marginBottom:16},children:[e.jsx(r,{span:12,children:e.jsxs(C,{title:"购买方信息",size:"small",children:[e.jsxs("div",{style:{marginBottom:12},children:[e.jsx(l,{type:"secondary",children:"公司名称"}),e.jsx("div",{style:{fontWeight:"bold",fontSize:"16px",marginTop:4},children:Le.buyerName})]}),e.jsxs("div",{style:{marginBottom:12},children:[e.jsx(l,{type:"secondary",children:"纳税人识别号"}),e.jsx("div",{style:{fontWeight:"bold",marginTop:4},children:Le.buyerTaxId})]}),e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"地址电话"}),e.jsxs("div",{style:{marginTop:4},children:[Le.buyerAddress," ",Le.buyerPhone]})]})]})}),e.jsx(r,{span:12,children:e.jsxs(C,{title:"销售方信息",size:"small",children:[e.jsxs("div",{style:{marginBottom:12},children:[e.jsx(l,{type:"secondary",children:"公司名称"}),e.jsx("div",{style:{fontWeight:"bold",fontSize:"16px",marginTop:4},children:Le.sellerName})]}),e.jsxs("div",{style:{marginBottom:12},children:[e.jsx(l,{type:"secondary",children:"纳税人识别号"}),e.jsx("div",{style:{fontWeight:"bold",marginTop:4},children:Le.sellerTaxId})]}),e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"地址电话"}),e.jsxs("div",{style:{marginTop:4},children:[Le.sellerAddress," ",Le.sellerPhone]})]})]})})]}),e.jsx(C,{title:"金额信息",size:"small",style:{marginBottom:16},children:e.jsxs(R,{gutter:16,children:[e.jsx(r,{span:8,children:e.jsx(ue,{title:"金额",value:Le.amount||0,precision:2,prefix:"¥",valueStyle:{color:"#1890ff"}})}),e.jsx(r,{span:8,children:e.jsx(ue,{title:"税额",value:Le.taxAmount||0,precision:2,prefix:"¥",valueStyle:{color:"#faad14"}})}),e.jsx(r,{span:8,children:e.jsx(ue,{title:"价税合计",value:Le.totalAmount||0,precision:2,prefix:"¥",valueStyle:{color:"#52c41a",fontSize:"20px",fontWeight:"bold"}})})]})}),Le.invoiceItems&&Le.invoiceItems.length>0&&e.jsx(C,{title:"发票明细",size:"small",style:{marginBottom:16},children:e.jsx(h,{dataSource:Le.invoiceItems,rowKey:"id",size:"small",pagination:!1,columns:[{title:"商品名称",dataIndex:"itemName",key:"itemName"},{title:"规格型号",dataIndex:"specification",key:"specification"},{title:"单位",dataIndex:"unit",key:"unit",width:80},{title:"数量",dataIndex:"quantity",key:"quantity",width:100,align:"right"},{title:"单价",dataIndex:"unitPrice",key:"unitPrice",width:120,align:"right",render:t=>`¥${t==null?void 0:t.toLocaleString()}`},{title:"金额",dataIndex:"amount",key:"amount",width:120,align:"right",render:t=>`¥${t==null?void 0:t.toLocaleString()}`},{title:"税率",dataIndex:"taxRate",key:"taxRate",width:100,align:"right",render:t=>`${(t*100).toFixed(2)}%`},{title:"税额",dataIndex:"taxAmount",key:"taxAmount",width:120,align:"right",render:t=>`¥${t==null?void 0:t.toLocaleString()}`},{title:"价税合计",dataIndex:"totalAmount",key:"totalAmount",width:130,align:"right",render:t=>`¥${(t==null?void 0:t.toLocaleString())||0}`}]})})]}),Pe&&e.jsxs("div",{children:[e.jsx(C,{title:"基本信息",size:"small",style:{marginBottom:16},children:e.jsxs(R,{gutter:[16,8],children:[e.jsx(r,{span:6,children:e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"发票号码"}),e.jsx("div",{style:{fontWeight:"bold",fontSize:"16px"},children:Pe.invoiceNumber})]})}),e.jsx(r,{span:6,children:e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"发票代码"}),e.jsx("div",{style:{fontWeight:"bold",fontSize:"16px"},children:Pe.invoiceCode})]})}),e.jsx(r,{span:6,children:e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"开票日期"}),e.jsx("div",{style:{fontWeight:"bold",fontSize:"16px"},children:(H=Pe.invoiceDate)==null?void 0:H.split("T")[0]})]})}),e.jsx(r,{span:6,children:e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"开票人"}),e.jsx("div",{style:{fontWeight:"bold",fontSize:"16px"},children:Pe.drawer||"-"})]})}),e.jsx(r,{span:24,children:e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"备注"}),e.jsx("div",{style:{marginTop:4,padding:"8px",backgroundColor:"#f5f5f5",borderRadius:"4px",minHeight:"32px"},children:Pe.remarks||""})]})})]})}),e.jsxs(R,{gutter:16,style:{marginBottom:16},children:[e.jsx(r,{span:12,children:e.jsxs(C,{title:"购买方信息",size:"small",children:[e.jsxs("div",{style:{marginBottom:12},children:[e.jsx(l,{type:"secondary",children:"公司名称"}),e.jsx("div",{style:{fontWeight:"bold",fontSize:"16px",marginTop:4},children:Pe.buyerName})]}),e.jsxs("div",{style:{marginBottom:12},children:[e.jsx(l,{type:"secondary",children:"纳税人识别号"}),e.jsx("div",{style:{fontWeight:"bold",marginTop:4},children:Pe.buyerTaxId})]}),e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"地址电话"}),e.jsxs("div",{style:{marginTop:4},children:[Pe.buyerAddress," ",Pe.buyerPhone]})]})]})}),e.jsx(r,{span:12,children:e.jsxs(C,{title:"销售方信息",size:"small",children:[e.jsxs("div",{style:{marginBottom:12},children:[e.jsx(l,{type:"secondary",children:"公司名称"}),e.jsx("div",{style:{fontWeight:"bold",fontSize:"16px",marginTop:4},children:Pe.sellerName})]}),e.jsxs("div",{style:{marginBottom:12},children:[e.jsx(l,{type:"secondary",children:"纳税人识别号"}),e.jsx("div",{style:{fontWeight:"bold",marginTop:4},children:Pe.sellerTaxId})]}),e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"地址电话"}),e.jsxs("div",{style:{marginTop:4},children:[Pe.sellerAddress," ",Pe.sellerPhone]})]})]})})]}),e.jsx(C,{title:"金额信息",size:"small",style:{marginBottom:16},children:e.jsxs(R,{gutter:16,children:[e.jsx(r,{span:8,children:e.jsx(ue,{title:"金额",value:Pe.amount||0,precision:2,prefix:"¥",valueStyle:{color:"#1890ff"}})}),e.jsx(r,{span:8,children:e.jsx(ue,{title:"税额",value:Pe.taxAmount||0,precision:2,prefix:"¥",valueStyle:{color:"#faad14"}})}),e.jsx(r,{span:8,children:e.jsx(ue,{title:"价税合计",value:Pe.totalAmount||0,precision:2,prefix:"¥",valueStyle:{color:"#52c41a",fontSize:"20px",fontWeight:"bold"}})})]})}),Pe.invoiceItems&&Pe.invoiceItems.length>0&&e.jsx(C,{title:"发票明细",size:"small",style:{marginBottom:16},children:e.jsx(h,{dataSource:Pe.invoiceItems,rowKey:"id",size:"small",pagination:!1,columns:[{title:"商品名称",dataIndex:"itemName",key:"itemName"},{title:"规格型号",dataIndex:"specification",key:"specification"},{title:"单位",dataIndex:"unit",key:"unit",width:80},{title:"数量",dataIndex:"quantity",key:"quantity",width:100,align:"right"},{title:"单价",dataIndex:"unitPrice",key:"unitPrice",width:120,align:"right",render:t=>`¥${t==null?void 0:t.toLocaleString()}`},{title:"金额",dataIndex:"amount",key:"amount",width:120,align:"right",render:t=>`¥${t==null?void 0:t.toLocaleString()}`},{title:"税率",dataIndex:"taxRate",key:"taxRate",width:100,align:"right",render:t=>`${(t*100).toFixed(2)}%`},{title:"税额",dataIndex:"taxAmount",key:"taxAmount",width:120,align:"right",render:t=>`¥${t==null?void 0:t.toLocaleString()}`},{title:"价税合计",dataIndex:"totalAmount",key:"totalAmount",width:130,align:"right",render:t=>`¥${(t==null?void 0:t.toLocaleString())||0}`}]})})]})]}),e.jsx(Se,{title:o?`${o.ownerUsername} ↔ ${o.otherUsername} 开票汇总`:"开票汇总",open:v,onCancel:()=>X(!1),footer:[e.jsx(f,{onClick:()=>X(!1),children:"关闭"},"close")],width:1400,style:{top:20},bodyStyle:{height:"70vh",overflow:"auto"},children:e.jsx(hn,{defaultActiveKey:"issued",items:[{key:"issued",label:`📤 开具发票汇总 (总金额: ¥${(fe.issuedGroups||[]).reduce((t,n)=>t+Number(n.totalAmount||0),0).toLocaleString("zh-CN",{minimumFractionDigits:2})})`,children:e.jsx(h,{columns:[{title:"购买方",dataIndex:"buyerName",key:"buyerName",width:200},{title:"销售方",dataIndex:"sellerName",key:"sellerName",width:200},{title:"价税合计",dataIndex:"totalAmount",key:"totalAmount",width:150,align:"right",render:t=>e.jsxs("span",{style:{fontWeight:"bold",color:"#f5222d"},children:["¥",t.toLocaleString("zh-CN",{minimumFractionDigits:2})]})},{title:"发票列表",key:"action",width:100,align:"center",render:(t,n)=>e.jsxs(f,{type:"link",size:"small",onClick:()=>Xt(fe.ownerUserId,fe.otherUserId,n.buyerName,n.sellerName,"issued"),children:["查看列表 (",n.count,")"]})}],dataSource:fe.issuedGroups,rowKey:t=>`issued-${t.buyerName}-${t.sellerName}`,pagination:{pageSize:10},size:"small",scroll:{y:"calc(60vh - 200px)"}})},{key:"received",label:`📥 取得发票汇总 (总金额: ¥${(fe.receivedGroups||[]).reduce((t,n)=>t+Number(n.totalAmount||0),0).toLocaleString("zh-CN",{minimumFractionDigits:2})})`,children:e.jsx(h,{columns:[{title:"购买方",dataIndex:"buyerName",key:"buyerName",width:200},{title:"销售方",dataIndex:"sellerName",key:"sellerName",width:200},{title:"价税合计",dataIndex:"totalAmount",key:"totalAmount",width:150,align:"right",render:t=>e.jsxs("span",{style:{fontWeight:"bold",color:"#52c41a"},children:["¥",t.toLocaleString("zh-CN",{minimumFractionDigits:2})]})},{title:"发票列表",key:"action",width:100,align:"center",render:(t,n)=>e.jsxs(f,{type:"link",size:"small",onClick:()=>Xt(fe.ownerUserId,fe.otherUserId,n.buyerName,n.sellerName,"received"),children:["查看列表 (",n.count,")"]})}],dataSource:fe.receivedGroups,rowKey:t=>`received-${t.buyerName}-${t.sellerName}`,pagination:{pageSize:10},size:"small",scroll:{y:"calc(60vh - 200px)"}})}]})}),e.jsx(Se,{title:At,open:Kt,onCancel:()=>lt(!1),footer:[e.jsx(f,{onClick:()=>lt(!1),children:"关闭"},"close")],width:1200,style:{top:20},children:e.jsx(h,{columns:[{title:"发票号码",dataIndex:"invoiceNumber",key:"invoiceNumber",width:150},{title:"开票日期",dataIndex:"invoiceDate",key:"invoiceDate",width:120,render:t=>new Date(t).toLocaleDateString("zh-CN")},{title:"购买方",dataIndex:"buyerName",key:"buyerName",width:200},{title:"销售方",dataIndex:"sellerName",key:"sellerName",width:200},{title:"价税合计",dataIndex:"totalAmount",key:"totalAmount",width:120,align:"right",render:t=>e.jsxs("span",{style:{fontWeight:"bold",color:At.includes("开具发票")?"#f5222d":"#52c41a"},children:["¥",t.toLocaleString("zh-CN",{minimumFractionDigits:2})]})},{title:"查看发票",key:"action",width:100,align:"center",render:(t,n)=>e.jsx(f,{type:"link",size:"small",onClick:()=>{At.includes("开具发票")?Qt(n):Jt(n)},children:"查看发票"})}],dataSource:Nt,rowKey:"id",pagination:{pageSize:10},size:"small",scroll:{y:400}})})]})}return U.includes("relations")?e.jsxs("div",{style:{height:"calc(100vh - 150px)",overflow:"hidden",display:"flex",flexDirection:"column"},children:[e.jsx(C,{title:"筛选条件",size:"small",style:{marginBottom:12},children:e.jsxs(R,{gutter:[12,8],align:"bottom",children:[e.jsx(r,{span:6,children:e.jsxs("div",{children:[e.jsx(l,{strong:!0,style:{fontSize:"12px"},children:"公司名称"}),e.jsx($,{placeholder:"输入公司名称进行筛选",value:Ve.companyName,onChange:be=>vt(t=>({...t,companyName:be.target.value})),size:"small",style:{marginTop:4}})]})}),e.jsx(r,{span:4,children:e.jsxs("div",{children:[e.jsx(l,{strong:!0,style:{fontSize:"12px"},children:"开始日期"}),e.jsx(Ke,{value:Ve.startDate?He(Ve.startDate):null,onChange:be=>vt(t=>({...t,startDate:be?be.format("YYYY-MM-DD"):""})),size:"small",style:{width:"100%",marginTop:4}})]})}),e.jsx(r,{span:4,children:e.jsxs("div",{children:[e.jsx(l,{strong:!0,style:{fontSize:"12px"},children:"结束日期"}),e.jsx(Ke,{value:Ve.endDate?He(Ve.endDate):null,onChange:be=>vt(t=>({...t,endDate:be?be.format("YYYY-MM-DD"):""})),size:"small",style:{width:"100%",marginTop:4}})]})}),e.jsx(r,{span:4,children:e.jsxs("div",{children:[e.jsx(l,{strong:!0,style:{fontSize:"12px"},children:"最大深度"}),e.jsx(js,{min:1,max:10,value:Ve.maxDepth,onChange:be=>vt(t=>({...t,maxDepth:be||5})),size:"small",style:{width:"100%",marginTop:4}})]})}),e.jsx(r,{span:6,children:e.jsx("div",{children:e.jsx(f,{type:"primary",onClick:ts,loading:tn,size:"small",children:"查询"})})})]})}),e.jsx(C,{title:"穿透关系统计",size:"small",style:{marginBottom:12},children:e.jsxs(R,{gutter:12,children:[e.jsx(r,{span:6,children:e.jsx(C,{size:"small",style:{textAlign:"center",backgroundColor:"#f0f9ff",padding:"8px"},children:e.jsx(ue,{title:"穿透链条数",value:((ve=Je.pagination)==null?void 0:ve.totalChains)||((ge=Je.stats)==null?void 0:ge.totalChains)||0,prefix:e.jsx(fs,{style:{color:"#1890ff"}}),valueStyle:{color:"#1890ff",fontSize:"25px",fontWeight:"bold"}})})}),e.jsx(r,{span:6,children:e.jsx(C,{size:"small",style:{textAlign:"center",backgroundColor:"#f6ffed",padding:"8px"},children:e.jsx(ue,{title:"涉及公司数",value:((Be=Je.pagination)==null?void 0:Be.totalNodes)||((oe=Je.stats)==null?void 0:oe.totalNodes)||0,prefix:e.jsx(Pn,{style:{color:"#52c41a"}}),valueStyle:{color:"#52c41a",fontSize:"25px",fontWeight:"bold"}})})}),e.jsx(r,{span:6,children:e.jsx(C,{size:"small",style:{textAlign:"center",backgroundColor:"#fff7e6",padding:"8px"},children:e.jsx(ue,{title:"关系连接数",value:((le=Je.pagination)==null?void 0:le.totalEdges)||((qe=Je.stats)==null?void 0:qe.totalEdges)||0,prefix:e.jsx(st,{style:{color:"#fa8c16"}}),valueStyle:{color:"#fa8c16",fontSize:"25px",fontWeight:"bold"}})})}),e.jsx(r,{span:6,children:e.jsx(C,{size:"small",style:{textAlign:"center",backgroundColor:"#fff1f0",padding:"8px"},children:e.jsx(ue,{title:"总金额(万元)",value:(((et=Je.pagination)==null?void 0:et.totalAmount)||((bt=Je.stats)==null?void 0:bt.totalAmount)||0)/1e4,prefix:"¥",precision:2,formatter:be=>`${Number(be).toLocaleString()}`,valueStyle:{color:"#f5222d",fontSize:"25px",fontWeight:"bold"}})})})]})}),e.jsx(C,{title:"穿透关系链",loading:tn,style:{flex:1,display:"flex",flexDirection:"column",minHeight:0},styles:{body:{flex:1,display:"flex",flexDirection:"column",padding:"16px",overflow:"hidden"}},children:Je.chains&&Je.chains.length>0?e.jsxs("div",{style:{display:"flex",flexDirection:"column",height:"100%",flex:1},children:[e.jsx("div",{style:{flex:1,overflowY:"auto",marginBottom:16},children:e.jsx(_e,{dataSource:Je.chains.slice((Ze.current-1)*Ze.pageSize,Ze.current*Ze.pageSize),renderItem:(be,t)=>{var n,b;return e.jsx(_e.Item,{children:e.jsx(C,{size:"small",style:{width:"100%"},children:e.jsxs(R,{gutter:16,children:[e.jsxs(r,{span:16,children:[e.jsxs("div",{style:{marginBottom:8},children:[e.jsxs(l,{strong:!0,children:["链条 ",(Ze.current-1)*Ze.pageSize+t+1,"："]}),e.jsxs(ze,{color:"blue",style:{marginLeft:8},children:["深度: ",be.depth]}),e.jsxs(ze,{color:"green",children:["金额: ¥",(n=be.totalAmount)==null?void 0:n.toLocaleString()]})]}),e.jsxs("div",{style:{fontSize:"16px",fontWeight:"bold",color:"#1890ff"},children:[(b=be.companies)==null?void 0:b.join(" → "),be.isCircular&&e.jsxs("span",{style:{color:"#52c41a"},children:[" → ",be.companies[0]," (循环)"]})]})]}),e.jsx(r,{span:8,children:e.jsx(f,{type:"link",onClick:()=>{Se.info({title:`链条详情 - 链条 ${(Ze.current-1)*Ze.pageSize+t+1}`,width:800,content:e.jsx("div",{children:e.jsx(h,{size:"small",dataSource:be.details||[],columns:[{title:"销售方",dataIndex:"from",key:"from"},{title:"购买方",dataIndex:"to",key:"to"},{title:"发票数量",dataIndex:"count",key:"count"},{title:"总金额",dataIndex:"totalAmount",key:"totalAmount",render:Y=>`¥${Y==null?void 0:Y.toLocaleString()}`},{title:"操作",key:"action",render:(Y,ke)=>e.jsx(f,{type:"link",size:"small",onClick:()=>{Se.info({title:`${ke.from} → ${ke.to} 发票列表`,width:1e3,content:e.jsx("div",{children:e.jsx(h,{size:"small",dataSource:ke.invoices||[],rowKey:ee=>`penetration-invoice-${ee.id}-${ee.source}`,columns:[{title:"发票号码",dataIndex:"invoiceNumber",key:"invoiceNumber",width:180,render:ee=>e.jsx("span",{style:{whiteSpace:"nowrap"},children:ee})},{title:"发票日期",dataIndex:"invoiceDate",key:"invoiceDate",render:ee=>ee==null?void 0:ee.split("T")[0]},{title:"金额",dataIndex:"totalAmount",key:"totalAmount",render:ee=>`¥${ee==null?void 0:ee.toLocaleString()}`},{title:"发票来源",dataIndex:"source",key:"source",width:100,render:ee=>e.jsx(ze,{color:ee==="issued"?"blue":"green",children:ee==="issued"?"开具发票":"取得发票"})},{title:"发票状态",dataIndex:"status",key:"status",width:100,render:ee=>{const Ye={NORMAL:{text:"正常",color:"success"},CANCELLED:{text:"作废",color:"error"}}[ee]||{text:ee||"未知",color:"default"};return e.jsx(ze,{color:Ye.color,children:Ye.text})}},{title:"操作",key:"action",render:(ee,Ee)=>e.jsx(f,{type:"link",size:"small",onClick:async()=>{var Ye;try{const nt=Ee.source==="received"?`/api/received-invoices/${Ee.id}`:`/api/invoices/${Ee.id}`,Ae=(await B.get(nt)).data.data;Se.info({title:`发票详情 - ${Ae.invoiceNumber}`,width:1200,content:e.jsxs("div",{children:[e.jsx(C,{title:"基本信息",size:"small",style:{marginBottom:16},children:e.jsxs(R,{gutter:[16,8],children:[e.jsx(r,{span:6,children:e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"发票号码"}),e.jsx("div",{style:{fontWeight:"bold",fontSize:"16px"},children:Ae.invoiceNumber})]})}),e.jsx(r,{span:6,children:e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"发票代码"}),e.jsx("div",{style:{fontWeight:"bold",fontSize:"16px"},children:Ae.invoiceCode})]})}),e.jsx(r,{span:6,children:e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"开票日期"}),e.jsx("div",{style:{fontWeight:"bold",fontSize:"16px"},children:(Ye=Ae.invoiceDate)==null?void 0:Ye.split("T")[0]})]})}),e.jsx(r,{span:6,children:e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"开票人"}),e.jsx("div",{style:{fontWeight:"bold",fontSize:"16px"},children:Ae.drawer||"-"})]})}),Ae.remarks&&e.jsx(r,{span:24,children:e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"备注"}),e.jsx("div",{style:{marginTop:4,padding:"8px",backgroundColor:"#f5f5f5",borderRadius:"4px"},children:Ae.remarks})]})})]})}),e.jsxs(R,{gutter:16,style:{marginBottom:16},children:[e.jsx(r,{span:12,children:e.jsxs(C,{title:"购买方信息",size:"small",children:[e.jsxs("div",{style:{marginBottom:12},children:[e.jsx(l,{type:"secondary",children:"公司名称"}),e.jsx("div",{style:{fontWeight:"bold",fontSize:"16px",marginTop:4},children:Ae.buyerName})]}),e.jsxs("div",{style:{marginBottom:12},children:[e.jsx(l,{type:"secondary",children:"纳税人识别号"}),e.jsx("div",{style:{fontWeight:"bold",marginTop:4},children:Ae.buyerTaxId})]}),e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"地址电话"}),e.jsxs("div",{style:{marginTop:4},children:[Ae.buyerAddress," ",Ae.buyerPhone]})]})]})}),e.jsx(r,{span:12,children:e.jsxs(C,{title:"销售方信息",size:"small",children:[e.jsxs("div",{style:{marginBottom:12},children:[e.jsx(l,{type:"secondary",children:"公司名称"}),e.jsx("div",{style:{fontWeight:"bold",fontSize:"16px",marginTop:4},children:Ae.sellerName})]}),e.jsxs("div",{style:{marginBottom:12},children:[e.jsx(l,{type:"secondary",children:"纳税人识别号"}),e.jsx("div",{style:{fontWeight:"bold",marginTop:4},children:Ae.sellerTaxId})]}),e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(l,{type:"secondary",children:"地址电话"}),e.jsxs("div",{style:{marginTop:4},children:[Ae.sellerAddress," ",Ae.sellerPhone]})]})]})})]}),e.jsx(C,{title:"金额信息",size:"small",style:{marginBottom:16},children:e.jsxs(R,{gutter:16,children:[e.jsx(r,{span:8,children:e.jsx(ue,{title:"金额",value:Ae.amount||0,precision:2,prefix:"¥",valueStyle:{color:"#1890ff"}})}),e.jsx(r,{span:8,children:e.jsx(ue,{title:"税额",value:Ae.taxAmount||0,precision:2,prefix:"¥",valueStyle:{color:"#faad14"}})}),e.jsx(r,{span:8,children:e.jsx(ue,{title:"价税合计",value:Ae.totalAmount||0,precision:2,prefix:"¥",valueStyle:{color:"#52c41a",fontSize:"20px",fontWeight:"bold"}})})]})}),(Ae.invoiceItems&&Ae.invoiceItems.length>0||Ae.receivedInvoiceItems&&Ae.receivedInvoiceItems.length>0)&&e.jsx(C,{title:"发票明细",size:"small",style:{marginBottom:16},children:e.jsx(h,{dataSource:Ae.invoiceItems||Ae.receivedInvoiceItems||[],rowKey:Fe=>`invoice-detail-${Fe.id}`,size:"small",pagination:!1,columns:[{title:"商品名称",dataIndex:"itemName",key:"itemName"},{title:"规格型号",dataIndex:"specification",key:"specification"},{title:"单位",dataIndex:"unit",key:"unit",width:80},{title:"数量",dataIndex:"quantity",key:"quantity",width:100,align:"right"},{title:"单价",dataIndex:"unitPrice",key:"unitPrice",width:120,align:"right",render:Fe=>`¥${Fe==null?void 0:Fe.toLocaleString()}`},{title:"金额",dataIndex:"amount",key:"amount",width:120,align:"right",render:Fe=>`¥${Fe==null?void 0:Fe.toLocaleString()}`},{title:"税率",dataIndex:"taxRate",key:"taxRate",width:100,align:"right",render:Fe=>`${(Fe*100).toFixed(2)}%`},{title:"税额",dataIndex:"taxAmount",key:"taxAmount",width:120,align:"right",render:Fe=>`¥${Fe==null?void 0:Fe.toLocaleString()}`},{title:"价税合计",dataIndex:"totalAmount",key:"totalAmount",width:130,align:"right",render:Fe=>`¥${(Fe==null?void 0:Fe.toLocaleString())||0}`}]})})]})})}catch(nt){console.error("获取发票详情失败:",nt),k.error("获取发票详情失败")}},children:"查看发票"})}],pagination:!1,scroll:{y:300},summary:()=>{const ee=(ke.invoices||[]).reduce((Ye,nt)=>Ye+(parseFloat(nt.totalAmount)||0),0),Ee=(ke.invoices||[]).length;return e.jsxs(h.Summary.Row,{style:{backgroundColor:"#fafafa",fontWeight:"bold"},children:[e.jsx(h.Summary.Cell,{index:0,children:e.jsxs("span",{style:{fontSize:"14px",color:"#1890ff",fontWeight:"bold"},children:["合计 (",Ee,"张)"]})}),e.jsx(h.Summary.Cell,{index:1}),e.jsx(h.Summary.Cell,{index:2,children:e.jsxs("span",{style:{fontSize:"14px",color:"#52c41a",fontWeight:"bold"},children:["¥",ee==null?void 0:ee.toLocaleString()]})}),e.jsx(h.Summary.Cell,{index:3}),e.jsx(h.Summary.Cell,{index:4}),e.jsx(h.Summary.Cell,{index:5})]})}})})})},children:"查看发票列表"})}],pagination:!1,summary:()=>{const Y=(be.details||[]).reduce((ee,Ee)=>ee+(Ee.totalAmount||0),0),ke=(be.details||[]).reduce((ee,Ee)=>ee+(Ee.count||0),0);return e.jsxs(h.Summary.Row,{style:{backgroundColor:"#fafafa",fontWeight:"bold"},children:[e.jsx(h.Summary.Cell,{index:0,colSpan:2,children:e.jsx("span",{style:{fontSize:"14px",color:"#1890ff",fontWeight:"bold"},children:"合计"})}),e.jsx(h.Summary.Cell,{index:2,children:e.jsx("span",{style:{fontSize:"14px",color:"#1890ff",fontWeight:"bold"},children:ke})}),e.jsx(h.Summary.Cell,{index:3,children:e.jsxs("span",{style:{fontSize:"14px",color:"#52c41a",fontWeight:"bold"},children:["¥",Y==null?void 0:Y.toLocaleString()]})}),e.jsx(h.Summary.Cell,{index:4})]})}})})})},children:"查看详情"})})]})})})},pagination:!1})}),e.jsx("div",{style:{borderTop:"1px solid #f0f0f0",paddingTop:16,backgroundColor:"#fafafa",flexShrink:0},children:e.jsx(vs,{current:Ze.current,total:Ze.total,pageSize:Ze.pageSize,showSizeChanger:!0,showQuickJumper:!0,showTotal:(be,t)=>`第 ${t[0]}-${t[1]} 条，共 ${be} 条`,pageSizeOptions:["5","10","20","50"],style:{textAlign:"center"},onChange:(be,t)=>{Tt(n=>({...n,current:be,pageSize:t||n.pageSize}))},onShowSizeChange:(be,t)=>{Tt(n=>({...n,current:1,pageSize:t}))}})})]}):e.jsx(un,{description:"暂无穿透关系链数据",image:un.PRESENTED_IMAGE_SIMPLE})})]}):e.jsxs("div",{children:[e.jsx(C,{title:"报表中心",style:{marginBottom:16},children:e.jsx(l,{type:"secondary",children:"请从左侧菜单选择具体的报表类型"})}),e.jsxs(R,{gutter:[16,16],children:[e.jsx(r,{span:6,children:e.jsx(C,{hoverable:!0,onClick:()=>S("/reports/quarterly-summary"),children:e.jsx(C.Meta,{avatar:e.jsx(at,{style:{backgroundColor:"#722ed1"},children:"季"}),title:"季度开票汇总",description:"查看季度开票统计汇总"})})}),e.jsx(r,{span:6,children:e.jsx(C,{hoverable:!0,onClick:()=>S("/reports/company-summary"),children:e.jsx(C.Meta,{avatar:e.jsx(at,{style:{backgroundColor:"#52c41a"},children:"公"}),title:"公司开票汇总",description:"查看公司按年度开票汇总"})})}),e.jsx(r,{span:6,children:e.jsx(C,{hoverable:!0,onClick:()=>S("/reports/invoice-count-summary"),children:e.jsx(C.Meta,{avatar:e.jsx(at,{style:{backgroundColor:"#fa8c16"},children:"张"}),title:"发票张数汇总",description:"查看发票张数统计汇总"})})}),e.jsx(r,{span:6,children:e.jsx(C,{hoverable:!0,onClick:()=>S("/reports/user-summary"),children:e.jsx(C.Meta,{avatar:e.jsx(at,{style:{backgroundColor:"#13c2c2"},children:"用"}),title:"用户开票汇总",description:"查看用户间开票往来汇总"})})})]}),e.jsx(R,{gutter:[16,16],style:{marginTop:16},children:e.jsx(r,{span:6,children:e.jsx(C,{hoverable:!0,onClick:()=>S("/reports/relations"),children:e.jsx(C.Meta,{avatar:e.jsx(at,{style:{backgroundColor:"#1890ff"},children:"关"}),title:"开票关系",description:"查看公司间开票关系穿透图"})})})})]})};return e.jsx("div",{children:ns()})},Zs=()=>{const[c,S]=a.useState(!1),[L,D]=a.useState(null),[N,z]=a.useState(!1),[w,W]=a.useState(null),[V,he]=a.useState(["1","2"]),A=async()=>{try{const u=await B.get("/api/invoices/template",{responseType:"blob"}),g=new Blob([u.data],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),Q=window.URL.createObjectURL(g),J=document.createElement("a");J.href=Q,J.download="开具发票导入模板.xlsx",document.body.appendChild(J),J.click(),document.body.removeChild(J),window.URL.revokeObjectURL(Q),k.success("开具发票模板下载成功！")}catch(u){console.error("下载模板失败:",u),k.error("下载模板失败")}},ie=async u=>{var te,pe;S(!0),D(null);const g=[];let Q=0,J=0,xe=0;try{for(let m=0;m<u.length;m++){const T=u[m],j=new FormData;j.append("file",T);try{const Z=(await B.post("/api/invoices/import",j,{headers:{"Content-Type":"multipart/form-data"}})).data.data;g.push({fileName:T.name,success:!0,...Z}),Q+=Z.successCount,J+=Z.failureCount,xe+=Z.duplicateCount}catch(q){console.error(`文件 ${T.name} 导入失败:`,q),g.push({fileName:T.name,success:!1,error:((pe=(te=q.response)==null?void 0:te.data)==null?void 0:pe.message)||"导入失败"})}}D({files:g,totalSuccess:Q,totalFailure:J,totalDuplicate:xe,totalFiles:u.length}),k.success(`批量导入完成！共处理 ${u.length} 个文件，成功导入 ${Q} 条记录`)}catch(m){console.error("批量导入失败:",m),k.error("批量导入失败")}finally{S(!1)}},I=async()=>{try{const u=await B.get("/api/received-invoices/template",{responseType:"blob"}),g=new Blob([u.data],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),Q=window.URL.createObjectURL(g),J=document.createElement("a");J.href=Q,J.download="取得发票导入模板.xlsx",document.body.appendChild(J),J.click(),document.body.removeChild(J),window.URL.revokeObjectURL(Q),k.success("取得发票模板下载成功！")}catch(u){console.error("下载取得发票模板失败:",u),k.error("下载取得发票模板失败")}},je=async u=>{var te,pe;z(!0),W(null);const g=[];let Q=0,J=0,xe=0;try{for(let m=0;m<u.length;m++){const T=u[m],j=new FormData;j.append("file",T);try{const Z=(await B.post("/api/received-invoices/import",j,{headers:{"Content-Type":"multipart/form-data"}})).data.data;g.push({fileName:T.name,success:!0,...Z}),Q+=Z.successCount,J+=Z.failureCount,xe+=Z.duplicateCount}catch(q){console.error(`文件 ${T.name} 导入失败:`,q),g.push({fileName:T.name,success:!1,error:((pe=(te=q.response)==null?void 0:te.data)==null?void 0:pe.message)||"导入失败"})}}W({files:g,totalSuccess:Q,totalFailure:J,totalDuplicate:xe,totalFiles:u.length}),k.success(`批量导入完成！共处理 ${u.length} 个文件，成功导入 ${Q} 条记录`)}catch(m){console.error("批量导入失败:",m),k.error("批量导入失败")}finally{z(!1)}},me=async(u,g)=>{var Q,J,xe;try{let te=0,pe=0,m=0;const T=[];for(let j=0;j<u.length;j++){const q=u[j];try{const Z=q.name,Ie=Z.match(/(\d{15,20})/);if(!Ie){T.push({fileName:Z,success:!1,error:"文件名中未找到税号（需要15-20位数字）"}),pe++;continue}const $e=Ie[1],F=((Q=(await B.get(`/api/${g==="issued"?"invoices":"received-invoices"}?search=${$e}&pageSize=1`)).data.data)==null?void 0:Q.data)||[];if(F.length===0){T.push({fileName:Z,success:!1,error:`未找到税号为 ${$e} 的发票`}),pe++;continue}const P=F[0],se=(await B.get(`/api/${g==="issued"?"invoices":"received-invoices"}/${P.id}`)).data.data;if(se.attachments&&se.attachments.length>0&&se.attachments.find(Ce=>Ce.originalName===Z||Ce.fileName===Z)){T.push({fileName:Z,success:!1,error:"该发票已有文件上传，跳过重复上传",isDuplicate:!0}),m++;continue}const K=new FormData;K.append("file",q),K.append("invoiceId",P.id),K.append("fileType",q.type.startsWith("image/")?"IMAGE":"PDF"),K.append("isOriginal","true"),K.append("invoiceType",g),await B.post("/api/upload/invoice-attachment",K,{headers:{"Content-Type":"multipart/form-data"}}),T.push({fileName:Z,success:!0,invoiceNumber:P.invoiceNumber,taxId:$e}),te++}catch(Z){console.error(`文件 ${q.name} 上传失败:`,Z),T.push({fileName:q.name,success:!1,error:((xe=(J=Z.response)==null?void 0:J.data)==null?void 0:xe.message)||"上传失败"}),pe++}}Se.info({title:"批量上传发票文件结果",width:800,content:e.jsxs("div",{children:[e.jsxs(R,{gutter:16,style:{marginBottom:16},children:[e.jsx(r,{span:8,children:e.jsx(ue,{title:"成功上传",value:te,valueStyle:{color:"#3f8600"}})}),e.jsx(r,{span:8,children:e.jsx(ue,{title:"失败上传",value:pe,valueStyle:{color:"#cf1322"}})}),e.jsx(r,{span:8,children:e.jsx(ue,{title:"重复跳过",value:m,valueStyle:{color:"#fa8c16"}})})]}),e.jsx(Oe,{level:5,children:"详细结果"}),e.jsx(_e,{size:"small",dataSource:T,style:{maxHeight:400,overflow:"auto"},renderItem:j=>e.jsx(_e.Item,{children:e.jsx(_e.Item.Meta,{title:e.jsxs(we,{children:[e.jsx("span",{children:j.fileName}),j.success?e.jsx(ze,{color:"green",children:"成功"}):j.isDuplicate?e.jsx(ze,{color:"orange",children:"重复跳过"}):e.jsx(ze,{color:"red",children:"失败"})]}),description:j.success?e.jsxs(l,{children:["匹配发票: ",j.invoiceNumber," (税号: ",j.taxId,")"]}):e.jsx(l,{type:"danger",children:j.error})})})})]})}),k.success(`批量上传完成！成功 ${te} 个，失败 ${pe} 个，重复跳过 ${m} 个`)}catch(te){console.error("批量上传发票文件失败:",te),k.error("批量上传发票文件失败")}};return e.jsxs("div",{children:[e.jsx(Oe,{level:2,children:"Excel导入"}),e.jsx(gs,{activeKey:V,onChange:he,size:"large",items:[{key:"1",label:e.jsx("span",{style:{fontSize:"16px",fontWeight:"bold"},children:"📄 Excel导入开具发票"}),children:e.jsxs(we,{direction:"vertical",size:"large",style:{width:"100%"},children:[e.jsxs("div",{children:[e.jsx(Oe,{level:4,children:"导入步骤"}),e.jsxs("ol",{children:[e.jsx("li",{children:"下载开具发票Excel模板"}),e.jsx("li",{children:"按照模板格式填写开具发票数据"}),e.jsx("li",{children:"上传填写好的Excel文件（支持同时选择多个文件进行批量导入）"})]})]}),e.jsx("div",{children:e.jsxs(we,{children:[e.jsx(f,{type:"primary",icon:e.jsx(tt,{}),onClick:A,style:{background:"linear-gradient(135deg, #1890ff 0%, #096dd9 100%)",border:"none"},children:"下载开具发票模板"}),e.jsx("input",{type:"file",accept:".xlsx,.xls",multiple:!0,onChange:u=>{const g=u.target.files;if(g&&g.length>0){const Q=Array.from(g),J=new DataTransfer;Q.forEach(xe=>J.items.add(xe)),ie(J.files)}u.target.value=""},style:{display:"none"},id:"excel-upload"}),e.jsx(f,{type:"primary",icon:e.jsx(rt,{}),loading:c,onClick:()=>{var u;return(u=document.getElementById("excel-upload"))==null?void 0:u.click()},style:{background:"linear-gradient(135deg, #1890ff 0%, #096dd9 100%)",border:"none"},children:c?"批量上传中...":"上传开具发票文件（可多选）"}),e.jsx("input",{type:"file",accept:"image/*,.pdf",multiple:!0,onChange:u=>{const g=u.target.files;g&&g.length>0&&me(g,"issued")},style:{display:"none"},id:"issued-invoice-files-upload"}),e.jsx(f,{type:"default",icon:e.jsx(it,{}),onClick:()=>{var u;return(u=document.getElementById("issued-invoice-files-upload"))==null?void 0:u.click()},style:{background:"linear-gradient(135deg, #fa8c16 0%, #d46b08 100%)",border:"none",color:"white"},children:"上传发票文件(可多选)"})]})}),L&&e.jsxs(C,{title:"开具发票批量导入结果",style:{marginTop:16},children:[e.jsxs(R,{gutter:16,style:{marginBottom:16},children:[e.jsx(r,{span:6,children:e.jsx(ue,{title:"处理文件数",value:L.totalFiles})}),e.jsx(r,{span:6,children:e.jsx(ue,{title:"成功导入",value:L.totalSuccess,valueStyle:{color:"#3f8600"}})}),e.jsx(r,{span:6,children:e.jsx(ue,{title:"失败记录",value:L.totalFailure,valueStyle:{color:"#cf1322"}})}),e.jsx(r,{span:6,children:e.jsx(ue,{title:"重复记录",value:L.totalDuplicate,valueStyle:{color:"#fa8c16"}})})]}),e.jsx(Oe,{level:5,children:"文件处理详情"}),e.jsx(_e,{size:"small",dataSource:L.files,renderItem:u=>e.jsx(_e.Item,{actions:[u.success&&u.errors&&u.errors.length>0&&e.jsx(f,{type:"link",size:"small",onClick:()=>{Se.info({title:`${u.fileName} - 详细错误信息`,width:800,content:e.jsxs("div",{children:[e.jsxs(l,{type:"secondary",children:["共 ",u.errors.length," 条错误记录："]}),e.jsx(_e,{size:"small",dataSource:u.errors,style:{marginTop:16,maxHeight:400,overflow:"auto"},renderItem:(g,Q)=>e.jsx(_e.Item,{children:e.jsxs("div",{children:[e.jsxs(l,{type:"danger",children:[e.jsxs("strong",{children:["第 ",g.row," 行:"]})," ",g.message]}),g.field&&g.value&&e.jsx("div",{style:{marginTop:4,fontSize:"12px",color:"#666"},children:e.jsxs(l,{type:"secondary",children:["字段: ",g.field," | 值: ",g.value]})})]})})})]})})},children:"查看错误详情"})].filter(Boolean),children:e.jsx(_e.Item.Meta,{title:e.jsxs(we,{children:[e.jsx("span",{children:u.fileName}),u.success?e.jsx(ze,{color:"green",children:"成功"}):e.jsx(ze,{color:"red",children:"失败"})]}),description:u.success?e.jsxs(we,{children:[e.jsxs(l,{children:["成功: ",u.successCount]}),e.jsxs(l,{children:["失败: ",u.failureCount]}),e.jsxs(l,{children:["重复: ",u.duplicateCount]}),u.errors&&u.errors.length>0&&e.jsxs(l,{type:"warning",children:["有 ",u.errors.length," 条错误记录"]})]}):e.jsx(l,{type:"danger",children:u.error})})})})]})]})},{key:"2",label:e.jsx("span",{style:{fontSize:"16px",fontWeight:"bold"},children:"📋 Excel导入取得发票"}),children:e.jsxs(we,{direction:"vertical",size:"large",style:{width:"100%"},children:[e.jsxs("div",{children:[e.jsx(Oe,{level:4,children:"导入步骤"}),e.jsxs("ol",{children:[e.jsx("li",{children:"下载取得发票Excel模板"}),e.jsx("li",{children:"按照模板格式填写取得发票数据"}),e.jsx("li",{children:"上传填写好的Excel文件（支持同时选择多个文件进行批量导入）"})]})]}),e.jsx("div",{children:e.jsxs(we,{children:[e.jsx(f,{type:"primary",icon:e.jsx(tt,{}),onClick:I,style:{background:"linear-gradient(135deg, #52c41a 0%, #389e0d 100%)",border:"none"},children:"下载取得发票模板"}),e.jsx("input",{type:"file",accept:".xlsx,.xls",multiple:!0,onChange:u=>{const g=u.target.files;if(g&&g.length>0){const Q=Array.from(g),J=new DataTransfer;Q.forEach(xe=>J.items.add(xe)),je(J.files)}u.target.value=""},style:{display:"none"},id:"received-excel-upload"}),e.jsx(f,{type:"primary",icon:e.jsx(rt,{}),loading:N,onClick:()=>{var u;return(u=document.getElementById("received-excel-upload"))==null?void 0:u.click()},style:{background:"linear-gradient(135deg, #52c41a 0%, #389e0d 100%)",border:"none"},children:N?"批量上传中...":"上传取得发票文件（可多选）"}),e.jsx("input",{type:"file",accept:"image/*,.pdf",multiple:!0,onChange:u=>{const g=u.target.files;g&&g.length>0&&me(g,"received")},style:{display:"none"},id:"received-invoice-files-upload"}),e.jsx(f,{type:"default",icon:e.jsx(it,{}),onClick:()=>{var u;return(u=document.getElementById("received-invoice-files-upload"))==null?void 0:u.click()},style:{background:"linear-gradient(135deg, #fa8c16 0%, #d46b08 100%)",border:"none",color:"white"},children:"上传发票文件(可多选)"})]})}),w&&e.jsxs(C,{title:"取得发票批量导入结果",style:{marginTop:16},children:[e.jsxs(R,{gutter:16,style:{marginBottom:16},children:[e.jsx(r,{span:6,children:e.jsx(ue,{title:"处理文件数",value:w.totalFiles})}),e.jsx(r,{span:6,children:e.jsx(ue,{title:"成功导入",value:w.totalSuccess,valueStyle:{color:"#3f8600"}})}),e.jsx(r,{span:6,children:e.jsx(ue,{title:"失败记录",value:w.totalFailure,valueStyle:{color:"#cf1322"}})}),e.jsx(r,{span:6,children:e.jsx(ue,{title:"重复记录",value:w.totalDuplicate,valueStyle:{color:"#fa8c16"}})})]}),e.jsx(Oe,{level:5,children:"文件处理详情"}),e.jsx(_e,{size:"small",dataSource:w.files,renderItem:u=>e.jsx(_e.Item,{actions:[u.success&&u.errors&&u.errors.length>0&&e.jsx(f,{type:"link",size:"small",onClick:()=>{Se.info({title:`${u.fileName} - 详细错误信息`,width:800,content:e.jsxs("div",{children:[e.jsxs(l,{type:"secondary",children:["共 ",u.errors.length," 条错误记录："]}),e.jsx(_e,{size:"small",dataSource:u.errors,style:{marginTop:16,maxHeight:400,overflow:"auto"},renderItem:(g,Q)=>e.jsx(_e.Item,{children:e.jsxs("div",{children:[e.jsxs(l,{type:"danger",children:[e.jsxs("strong",{children:["第 ",g.row," 行:"]})," ",g.message]}),g.field&&g.value&&e.jsx("div",{style:{marginTop:4,fontSize:"12px",color:"#666"},children:e.jsxs(l,{type:"secondary",children:["字段: ",g.field," | 值: ",g.value]})})]})})})]})})},children:"查看错误详情"})].filter(Boolean),children:e.jsx(_e.Item.Meta,{title:e.jsxs(we,{children:[e.jsx("span",{children:u.fileName}),u.success?e.jsx(ze,{color:"green",children:"成功"}):e.jsx(ze,{color:"red",children:"失败"})]}),description:u.success?e.jsxs(we,{children:[e.jsxs(l,{children:["成功: ",u.successCount]}),e.jsxs(l,{children:["失败: ",u.failureCount]}),e.jsxs(l,{children:["重复: ",u.duplicateCount]}),u.errors&&u.errors.length>0&&e.jsxs(l,{type:"warning",children:["有 ",u.errors.length," 条错误记录"]})]}):e.jsx(l,{type:"danger",children:u.error})})})})]})]})}]})]})},Wn=()=>{const c="ABCDEFGHIJKLMNOPQRSTUVWXYZ",S="abcdefghijklmnopqrstuvwxyz",L="0123456789",D=c+S+L;let N="";N+=c[Math.floor(Math.random()*c.length)],N+=S[Math.floor(Math.random()*S.length)],N+=L[Math.floor(Math.random()*L.length)];for(let z=3;z<16;z++)N+=D[Math.floor(Math.random()*D.length)];return N.split("").sort(()=>Math.random()-.5).join("")},Xs=({onLogout:c})=>{const[S,L]=a.useState(!1),[D,N]=a.useState(null),[z,w]=a.useState(!1),[W,V]=a.useState(!1),[he,A]=a.useState([]),ie=Ht(),I=Mn(),[je]=d.useForm(),me={"/":"仪表板","/dashboard":"仪表板","/companies":"公司管理","/invoices":"开具发票","/received-invoices":"取得发票","/import":"Excel导入","/users":"用户管理","/operation-logs":"操作日志","/reports":"报表中心","/reports/quarterly-summary":"季度开票汇总","/reports/company-summary":"公司开票汇总","/reports/invoice-count-summary":"发票张数汇总","/reports/user-summary":"用户开票汇总","/reports/relations":"开票关系"},u=m=>{const T=me[m]||"发票管理系统";document.title=`发票管理-${T}`};a.useEffect(()=>{g(),J()},[]),a.useEffect(()=>{u(I.pathname)},[I.pathname]);const g=async()=>{var m;try{if(!localStorage.getItem("token")){console.error("未找到登录令牌");return}const j=await B.get("/api/auth/me");if(j.data.success){const q=j.data.data;N({id:q.id,name:q.name,email:q.email,role:Q(q.role),department:q.department||"未设置",phone:q.phone||"未设置",lastLogin:q.lastLogin||new Date().toISOString(),createdAt:q.createdAt})}}catch(T){console.error("获取用户信息失败:",T),((m=T.response)==null?void 0:m.status)===401&&(localStorage.removeItem("token"),c())}},Q=m=>({ADMIN:"系统管理员",FINANCE:"财务人员",BUSINESS:"业务人员",AUDITOR:"审计人员",USER:"普通用户"})[m]||m,J=async()=>{try{if(!localStorage.getItem("token")){console.error("未找到登录令牌");return}const T=await B.get("/api/menus/user/current/accessible");if(T.data.success){const j=T.data.data,q=pe(j);A(q)}}catch(m){console.error("获取用户菜单失败:",m),A([{key:"/dashboard",icon:e.jsx(rn,{}),label:"仪表板"}])}},xe=()=>{const m=Wn();je.setFieldsValue({newPassword:m,confirmPassword:m}),k.success("已生成16位随机密码")},te=async()=>{var m,T;try{const j=await je.validateFields();await B.put("/api/auth/change-password",{currentPassword:j.currentPassword,newPassword:j.newPassword}),k.success("密码修改成功！"),V(!1),je.resetFields()}catch(j){console.error("修改密码失败:",j),k.error(((T=(m=j.response)==null?void 0:m.data)==null?void 0:T.message)||"修改密码失败")}},pe=m=>{const T={DashboardOutlined:e.jsx(rn,{}),BankOutlined:e.jsx(Pn,{}),FileTextOutlined:e.jsx(Wt,{}),ImportOutlined:e.jsx(hs,{}),UserOutlined:e.jsx(Ct,{}),BarChartOutlined:e.jsx(cs,{}),NodeIndexOutlined:e.jsx(st,{})};return m.map(j=>{const q={key:j.path||`/${j.key}`,icon:T[j.icon]||e.jsx(Wt,{}),label:j.name};return j.children&&j.children.length>0&&(q.children=pe(j.children)),q})};return e.jsxs(qt,{style:{minHeight:"100vh"},children:[e.jsxs(Ws,{trigger:null,collapsible:!0,collapsed:S,children:[e.jsx("div",{style:{height:64,display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontSize:"18px",fontWeight:"bold"},children:S?"发票":"发票管理系统"}),e.jsx(ds,{theme:"dark",mode:"inline",selectedKeys:[I.pathname],defaultOpenKeys:I.pathname.startsWith("/reports")?["/reports"]:[],items:he,onClick:({key:m})=>ie(m)})]}),e.jsxs(qt,{children:[e.jsxs(Os,{style:{padding:"0 16px",background:"#fff",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx(f,{type:"text",onClick:()=>L(!S),style:{fontSize:"16px",width:64,height:64},children:S?">":"<"}),e.jsx(we,{children:e.jsx(Ut,{menu:{items:[{key:"userInfo",label:"查看详细信息",icon:e.jsx(Ct,{}),onClick:()=>w(!0)},{key:"changePassword",label:"修改密码",icon:e.jsx(Ot,{}),onClick:()=>V(!0)},{type:"divider"},{key:"logout",label:"退出登录",icon:e.jsx(Ot,{}),onClick:c}]},trigger:["click"],children:e.jsx(f,{type:"text",style:{height:"auto",padding:"8px 12px"},children:e.jsxs(we,{children:[e.jsx(Ct,{}),e.jsx("span",{children:(D==null?void 0:D.name)||"用户"})]})})})})]}),e.jsx(Ys,{style:{margin:"16px",padding:24,background:"#fff"},children:e.jsxs(Fn,{children:[e.jsx(Xe,{path:"/",element:e.jsx(Ln,{})}),e.jsx(Xe,{path:"/dashboard",element:e.jsx(Ln,{})}),e.jsx(Xe,{path:"/companies",element:e.jsx(Vs,{userInfo:D})}),e.jsx(Xe,{path:"/invoices",element:e.jsx(Ks,{})}),e.jsx(Xe,{path:"/received-invoices",element:e.jsx(Hs,{})}),e.jsx(Xe,{path:"/import",element:e.jsx(Zs,{})}),e.jsx(Xe,{path:"/users",element:e.jsx(Gs,{})}),e.jsx(Xe,{path:"/operation-logs",element:e.jsx(Qs,{})}),e.jsx(Xe,{path:"/reports/*",element:e.jsx(Js,{})})]})})]}),e.jsx(Se,{title:"用户详细信息",open:z,onCancel:()=>w(!1),footer:[e.jsx(f,{onClick:()=>w(!1),children:"关闭"},"close")],width:800,children:D&&e.jsxs("div",{children:[e.jsx(C,{title:"基本信息",size:"small",style:{marginBottom:16},children:e.jsxs(R,{gutter:[16,16],children:[e.jsx(r,{span:12,children:e.jsxs("div",{style:{display:"flex",marginBottom:12},children:[e.jsx(l,{type:"secondary",style:{width:"80px",flexShrink:0},children:"用户名称:"}),e.jsx(l,{strong:!0,style:{fontSize:"16px"},children:D.name})]})}),e.jsx(r,{span:12,children:e.jsxs("div",{style:{display:"flex",marginBottom:12},children:[e.jsx(l,{type:"secondary",style:{width:"80px",flexShrink:0},children:"邮箱地址:"}),e.jsx(l,{strong:!0,children:D.email})]})}),e.jsx(r,{span:12,children:e.jsxs("div",{style:{display:"flex",marginBottom:12},children:[e.jsx(l,{type:"secondary",style:{width:"80px",flexShrink:0},children:"用户角色:"}),e.jsx(ze,{color:"blue",children:D.role})]})}),e.jsx(r,{span:12,children:e.jsxs("div",{style:{display:"flex",marginBottom:12},children:[e.jsx(l,{type:"secondary",style:{width:"80px",flexShrink:0},children:"所属部门:"}),e.jsx(l,{children:D.department})]})})]})}),e.jsx(C,{title:"联系信息",size:"small",style:{marginBottom:16},children:e.jsxs(R,{gutter:[16,16],children:[e.jsx(r,{span:12,children:e.jsxs("div",{style:{display:"flex",marginBottom:12},children:[e.jsx(l,{type:"secondary",style:{width:"80px",flexShrink:0},children:"联系电话:"}),e.jsx(l,{children:D.phone})]})}),e.jsx(r,{span:12,children:e.jsxs("div",{style:{display:"flex",marginBottom:12},children:[e.jsx(l,{type:"secondary",style:{width:"80px",flexShrink:0},children:"用户ID:"}),e.jsx(l,{type:"secondary",style:{wordBreak:"break-all",fontSize:"12px"},children:D.id})]})})]})}),e.jsx(C,{title:"登录信息",size:"small",children:e.jsxs(R,{gutter:[16,16],children:[e.jsx(r,{span:12,children:e.jsxs("div",{style:{display:"flex",marginBottom:12},children:[e.jsx(l,{type:"secondary",style:{width:"80px",flexShrink:0},children:"最后登录:"}),e.jsx(l,{children:new Date(D.lastLogin).toLocaleString()})]})}),e.jsx(r,{span:12,children:e.jsxs("div",{style:{display:"flex",marginBottom:12},children:[e.jsx(l,{type:"secondary",style:{width:"80px",flexShrink:0},children:"注册时间:"}),e.jsx(l,{children:new Date(D.createdAt).toLocaleDateString()})]})})]})})]})}),e.jsx(Se,{title:"修改密码",open:W,onCancel:()=>{V(!1),je.resetFields()},width:400,footer:[e.jsx(f,{type:"default",onClick:xe,style:{float:"left"},children:"随机密码"},"generate"),e.jsx(f,{onClick:()=>{V(!1),je.resetFields()},children:"取消"},"cancel"),e.jsx(f,{type:"primary",onClick:te,children:"确定"},"submit")],children:e.jsxs(d,{form:je,layout:"vertical",style:{marginTop:16},children:[e.jsx(d.Item,{label:"当前密码",name:"currentPassword",rules:[{required:!0,message:"请输入当前密码"}],children:e.jsx($.Password,{placeholder:"请输入当前密码"})}),e.jsx(d.Item,{label:"新密码",name:"newPassword",rules:[{required:!0,message:"请输入新密码"},{min:6,message:"密码至少6个字符"}],children:e.jsx($.Password,{placeholder:"请输入新密码（至少6个字符）"})}),e.jsx(d.Item,{label:"确认新密码",name:"confirmPassword",dependencies:["newPassword"],rules:[{required:!0,message:"请确认新密码"},({getFieldValue:m})=>({validator(T,j){return!j||m("newPassword")===j?Promise.resolve():Promise.reject(new Error("两次输入的密码不一致"))}})],children:e.jsx($.Password,{placeholder:"请再次输入新密码"})})]})})]})};function er(){const[c,S]=a.useState(!1),[L,D]=a.useState(!0);return a.useEffect(()=>{localStorage.getItem("token")&&S(!0),D(!1)},[]),L?e.jsx(sn,{locale:_n,children:e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",fontSize:"16px"},children:"加载中..."})}):e.jsx(sn,{locale:_n,children:e.jsx(os,{children:e.jsx(ws,{children:e.jsxs(Fn,{children:[e.jsx(Xe,{path:"/login",element:c?e.jsx(xn,{to:"/dashboard",replace:!0}):e.jsx(Us,{onLogin:()=>S(!0)})}),e.jsx(Xe,{path:"/*",element:c?e.jsx(Xs,{onLogout:()=>{localStorage.removeItem("token"),S(!1)}}):e.jsx(xn,{to:"/login",replace:!0})})]})})})})}Is.createRoot(document.getElementById("root")).render(e.jsx(a.StrictMode,{children:e.jsx(er,{})}));
