import express from 'express';
import { PrismaClient } from '@prisma/client';
import Joi from 'joi';
import { AppError, asyncHandler } from '../middleware/errorHandler';
import { authenticate, AuthenticatedRequest } from '../middleware/auth';

const router = express.Router();
const prisma = new PrismaClient();

// 验证schemas
const menuSchema = Joi.object({
  key: Joi.string().required(),
  name: Joi.string().required(),
  path: Joi.string().allow(null, ''),
  icon: Joi.string().allow(null, ''),
  parentId: Joi.string().allow(null, ''),
  sort: Joi.number().integer().default(0),
  isActive: Joi.boolean().default(true),
  description: Joi.string().allow(null, ''),
});

const userMenuPermissionSchema = Joi.object({
  userId: Joi.string().required(),
  menuId: Joi.string().required(),
  canView: Joi.boolean().default(true),
  canEdit: Joi.boolean().default(false),
  canDelete: Joi.boolean().default(false),
  canExport: Joi.boolean().default(false),
});

// 获取所有菜单（树形结构）
router.get('/', authenticate, asyncHandler(async (req: AuthenticatedRequest, res: express.Response) => {
  const menus = await prisma.$queryRaw`
    SELECT
      id, \`key\`, name, path, icon, parentId, sort, isActive, description,
      createdAt, updatedAt
    FROM menus
    WHERE isActive = true
    ORDER BY parentId ASC, sort ASC, name ASC
  ` as any[];

  // 构建树形结构
  const menuTree = buildMenuTree(menus);

  res.json({
    success: true,
    data: menuTree,
  });
}));

// 获取用户菜单权限
router.get('/user/:userId', authenticate, asyncHandler(async (req: AuthenticatedRequest, res: express.Response) => {
  const { userId } = req.params;

  const userMenus = await prisma.$queryRaw`
    SELECT
      m.id, m.\`key\`, m.name, m.path, m.icon, m.parentId, m.sort,
      ump.canView, ump.canEdit, ump.canDelete, ump.canExport
    FROM menus m
    LEFT JOIN user_menu_permissions ump ON m.id = ump.menuId AND ump.userId = ${userId}
    WHERE m.isActive = true
    ORDER BY m.parentId ASC, m.sort ASC, m.name ASC
  ` as any[];

  // 构建树形结构
  const menuTree = buildMenuTree(userMenus);

  res.json({
    success: true,
    data: menuTree,
  });
}));

// 获取用户有权限的菜单
router.get('/user/:userId/accessible', authenticate, asyncHandler(async (req: AuthenticatedRequest, res: express.Response) => {
  const { userId } = req.params;

  // 如果userId是'current'，使用当前登录用户的ID
  const targetUserId = userId === 'current' ? req.user!.id : userId;

  // 首先检查用户角色
  const user = await prisma.user.findUnique({
    where: { id: targetUserId },
    select: { role: true }
  });

  let accessibleMenus: any[];

  if (user?.role === 'ADMIN') {
    // 管理员可以访问所有菜单
    accessibleMenus = await prisma.$queryRaw`
      SELECT
        m.id, m.\`key\`, m.name, m.path, m.icon, m.parentId, CAST(m.sort AS SIGNED) as sort,
        CAST(1 AS SIGNED) as canView,
        CAST(1 AS SIGNED) as canEdit,
        CAST(1 AS SIGNED) as canDelete,
        CAST(1 AS SIGNED) as canExport
      FROM menus m
      WHERE m.isActive = true
      ORDER BY m.parentId ASC, m.sort ASC, m.name ASC
    ` as any[];
  } else {
    // 普通用户只能访问有明确权限的菜单
    accessibleMenus = await prisma.$queryRaw`
      SELECT
        m.id, m.\`key\`, m.name, m.path, m.icon, m.parentId, CAST(m.sort AS SIGNED) as sort,
        CAST(ump.canView AS SIGNED) as canView,
        CAST(COALESCE(ump.canEdit, false) AS SIGNED) as canEdit,
        CAST(COALESCE(ump.canDelete, false) AS SIGNED) as canDelete,
        CAST(COALESCE(ump.canExport, false) AS SIGNED) as canExport
      FROM menus m
      INNER JOIN user_menu_permissions ump ON m.id = ump.menuId AND ump.userId = ${targetUserId}
      WHERE m.isActive = true AND ump.canView = true
      ORDER BY m.parentId ASC, m.sort ASC, m.name ASC
    ` as any[];
  }

  // 转换BigInt为普通数字
  const processedMenus = accessibleMenus.map(menu => ({
    ...menu,
    sort: Number(menu.sort),
    canView: Boolean(Number(menu.canView)),
    canEdit: Boolean(Number(menu.canEdit)),
    canDelete: Boolean(Number(menu.canDelete)),
    canExport: Boolean(Number(menu.canExport)),
  }));

  // 构建树形结构
  const menuTree = buildMenuTree(processedMenus);

  res.json({
    success: true,
    data: menuTree,
  });
}));

// 创建菜单
router.post('/', authenticate, asyncHandler(async (req: AuthenticatedRequest, res: express.Response) => {
  const { error, value } = menuSchema.validate(req.body);
  if (error) {
    throw new AppError(error.details[0].message, 400);
  }

  const menu = await prisma.$executeRaw`
    INSERT INTO menus (\`key\`, name, path, icon, parentId, sort, isActive, description, createdAt, updatedAt)
    VALUES (${value.key}, ${value.name}, ${value.path}, ${value.icon}, ${value.parentId}, ${value.sort}, ${value.isActive}, ${value.description}, NOW(), NOW())
  `;

  res.status(201).json({
    success: true,
    message: '菜单创建成功',
  });
}));

// 调试：获取用户菜单权限详情（临时调试用，生产环境应该移除）
router.get('/user/:userId/permissions/debug', asyncHandler(async (req: AuthenticatedRequest, res: express.Response) => {
  const { userId } = req.params;

  // 获取用户信息
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: { id: true, username: true, role: true }
  });

  if (!user) {
    throw new AppError('用户不存在', 404);
  }

  // 获取用户的菜单权限
  const permissions = await prisma.$queryRaw`
    SELECT
      ump.userId,
      ump.menuId,
      ump.canView,
      ump.canEdit,
      ump.canDelete,
      ump.canExport,
      m.name as menuName,
      m.path as menuPath,
      m.key as menuKey
    FROM user_menu_permissions ump
    JOIN menus m ON ump.menuId = m.id
    WHERE ump.userId = ${userId}
    ORDER BY m.sort
  `;

  // 获取所有菜单
  const allMenus = await prisma.$queryRaw`
    SELECT id, name, path, \`key\`, parentId, sort, isActive
    FROM menus
    WHERE isActive = 1
    ORDER BY sort
  `;

  res.json({
    success: true,
    data: {
      user,
      permissions,
      allMenus,
      hasPermissions: Array.isArray(permissions) && permissions.length > 0
    }
  });
}));

// 更新用户菜单权限
router.put('/user/:userId/permissions', authenticate, asyncHandler(async (req: AuthenticatedRequest, res: express.Response) => {
  const { userId } = req.params;
  const { permissions } = req.body;

  if (!Array.isArray(permissions)) {
    throw new AppError('权限数据格式错误', 400);
  }

  // 开始事务
  await prisma.$transaction(async (tx) => {
    // 删除用户现有权限
    await tx.$executeRaw`DELETE FROM user_menu_permissions WHERE userId = ${userId}`;

    // 插入新权限
    for (const permission of permissions) {
      const { error, value } = userMenuPermissionSchema.validate({
        userId,
        ...permission,
      });

      if (error) {
        throw new AppError(`权限数据验证失败: ${error.details[0].message}`, 400);
      }

      await tx.$executeRaw`
        INSERT INTO user_menu_permissions (userId, menuId, canView, canEdit, canDelete, canExport, createdAt, updatedAt)
        VALUES (${value.userId}, ${value.menuId}, ${value.canView}, ${value.canEdit}, ${value.canDelete}, ${value.canExport}, NOW(), NOW())
      `;
    }
  });

  res.json({
    success: true,
    message: '用户菜单权限更新成功',
  });
}));

// 初始化新菜单（临时API，用于添加新的报表菜单）
router.post('/init-new-menus', authenticate, asyncHandler(async (req: AuthenticatedRequest, res: express.Response) => {
  try {
    // 查找报表中心的父菜单ID
    const reportsParent = await prisma.$queryRaw`
      SELECT id FROM menus WHERE \`key\` = 'reports' LIMIT 1
    ` as any[];

    if (!reportsParent || reportsParent.length === 0) {
      throw new AppError('找不到报表中心父菜单', 404);
    }

    const parentId = reportsParent[0].id;

    // 检查菜单是否已存在
    const existingMenus = await prisma.$queryRaw`
      SELECT \`key\` FROM menus WHERE \`key\` IN ('reports-invoice-count-summary', 'reports-user-summary')
    ` as any[];

    const existingKeys = existingMenus.map(m => m.key);

    // 添加发票张数汇总菜单（如果不存在）
    if (!existingKeys.includes('reports-invoice-count-summary')) {
      await prisma.$executeRaw`
        INSERT INTO menus (\`key\`, name, path, icon, parentId, sort, isActive, description, createdAt, updatedAt)
        VALUES (
          'reports-invoice-count-summary',
          '发票张数汇总',
          '/reports/invoice-count-summary',
          NULL,
          ${parentId},
          30,
          1,
          '按公司和季度统计发票张数',
          NOW(),
          NOW()
        )
      `;
    }

    // 添加所属用户开票汇总菜单（如果不存在）
    if (!existingKeys.includes('reports-user-summary')) {
      await prisma.$executeRaw`
        INSERT INTO menus (\`key\`, name, path, icon, parentId, sort, isActive, description, createdAt, updatedAt)
        VALUES (
          'reports-user-summary',
          '所属用户开票汇总',
          '/reports/user-summary',
          NULL,
          ${parentId},
          40,
          1,
          '按所属用户统计开票金额汇总',
          NOW(),
          NOW()
        )
      `;
    }

    // 查看添加的菜单
    const newMenus = await prisma.$queryRaw`
      SELECT id, \`key\`, name, path, parentId, sort, isActive, description
      FROM menus
      WHERE \`key\` IN ('reports-invoice-count-summary', 'reports-user-summary')
      ORDER BY sort
    `;

    res.json({
      success: true,
      data: newMenus,
      message: '新菜单初始化成功',
    });

  } catch (error) {
    console.error('初始化新菜单失败:', error);
    res.status(500).json({
      success: false,
      message: '初始化新菜单失败',
    });
  }
}));

// 构建菜单树形结构的辅助函数
function buildMenuTree(menus: any[]): any[] {
  const menuMap = new Map();
  const rootMenus: any[] = [];

  // 创建菜单映射
  menus.forEach(menu => {
    menuMap.set(menu.id, { ...menu, children: [] });
  });

  // 构建树形结构
  menus.forEach(menu => {
    const menuItem = menuMap.get(menu.id);
    if (menu.parentId && menuMap.has(menu.parentId)) {
      menuMap.get(menu.parentId).children.push(menuItem);
    } else {
      rootMenus.push(menuItem);
    }
  });

  return rootMenus;
}

export default router;
