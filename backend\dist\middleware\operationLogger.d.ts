import { Request, Response, NextFunction } from 'express';
interface AuthenticatedRequest extends Request {
    user?: {
        id: string;
        username: string;
        email: string;
        name: string;
        role: string;
    };
}
export declare const operationLogger: (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
export {};
//# sourceMappingURL=operationLogger.d.ts.map