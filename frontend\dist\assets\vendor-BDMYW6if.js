import{r as y,R as _t}from"./antd-vendor-trf5ErUE.js";/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function z(){return z=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},z.apply(this,arguments)}var L;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(L||(L={}));const Ae="popstate";function Ft(e){e===void 0&&(e={});function t(r,s){let{pathname:o,search:i,hash:a}=r.location;return me("",{pathname:o,search:i,hash:a},s.state&&s.state.usr||null,s.state&&s.state.key||"default")}function n(r,s){return typeof s=="string"?s:Qe(s)}return Dt(t,n,null,e)}function x(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Ge(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function kt(){return Math.random().toString(36).substr(2,8)}function Ne(e,t){return{usr:e.state,key:e.key,idx:t}}function me(e,t,n,r){return n===void 0&&(n=null),z({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?q(t):t,{state:n,key:t&&t.key||r||kt()})}function Qe(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function q(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function Dt(e,t,n,r){r===void 0&&(r={});let{window:s=document.defaultView,v5Compat:o=!1}=r,i=s.history,a=L.Pop,f=null,c=u();c==null&&(c=0,i.replaceState(z({},i.state,{idx:c}),""));function u(){return(i.state||{idx:null}).idx}function d(){a=L.Pop;let h=u(),b=h==null?null:h-c;c=h,f&&f({action:a,location:m.location,delta:b})}function g(h,b){a=L.Push;let R=me(m.location,h,b);c=u()+1;let S=Ne(R,c),T=m.createHref(R);try{i.pushState(S,"",T)}catch(C){if(C instanceof DOMException&&C.name==="DataCloneError")throw C;s.location.assign(T)}o&&f&&f({action:a,location:m.location,delta:1})}function E(h,b){a=L.Replace;let R=me(m.location,h,b);c=u();let S=Ne(R,c),T=m.createHref(R);i.replaceState(S,"",T),o&&f&&f({action:a,location:m.location,delta:0})}function p(h){let b=s.location.origin!=="null"?s.location.origin:s.location.href,R=typeof h=="string"?h:Qe(h);return R=R.replace(/ $/,"%20"),x(b,"No window.location.(origin|href) available to create URL for href: "+R),new URL(R,b)}let m={get action(){return a},get location(){return e(s,i)},listen(h){if(f)throw new Error("A history only accepts one active listener");return s.addEventListener(Ae,d),f=h,()=>{s.removeEventListener(Ae,d),f=null}},createHref(h){return t(s,h)},createURL:p,encodeLocation(h){let b=p(h);return{pathname:b.pathname,search:b.search,hash:b.hash}},push:g,replace:E,go(h){return i.go(h)}};return m}var Ue;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Ue||(Ue={}));function jt(e,t,n){return n===void 0&&(n="/"),It(e,t,n)}function It(e,t,n,r){let s=typeof t=="string"?q(t):t,o=et(s.pathname||"/",n);if(o==null)return null;let i=Ze(e);qt(i);let a=null;for(let f=0;a==null&&f<i.length;++f){let c=Zt(o);a=Xt(i[f],c)}return a}function Ze(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let s=(o,i,a)=>{let f={relativePath:a===void 0?o.path||"":a,caseSensitive:o.caseSensitive===!0,childrenIndex:i,route:o};f.relativePath.startsWith("/")&&(x(f.relativePath.startsWith(r),'Absolute route path "'+f.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),f.relativePath=f.relativePath.slice(r.length));let c=k([r,f.relativePath]),u=n.concat(f);o.children&&o.children.length>0&&(x(o.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+c+'".')),Ze(o.children,t,u,c)),!(o.path==null&&!o.index)&&t.push({path:c,score:Vt(c,o.index),routesMeta:u})};return e.forEach((o,i)=>{var a;if(o.path===""||!((a=o.path)!=null&&a.includes("?")))s(o,i);else for(let f of Ye(o.path))s(o,i,f)}),t}function Ye(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,s=n.endsWith("?"),o=n.replace(/\?$/,"");if(r.length===0)return s?[o,""]:[o];let i=Ye(r.join("/")),a=[];return a.push(...i.map(f=>f===""?o:[o,f].join("/"))),s&&a.push(...i),a.map(f=>e.startsWith("/")&&f===""?"/":f)}function qt(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:Kt(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const Mt=/^:[\w-]+$/,$t=3,Ht=2,zt=1,Wt=10,Jt=-2,Be=e=>e==="*";function Vt(e,t){let n=e.split("/"),r=n.length;return n.some(Be)&&(r+=Jt),t&&(r+=Ht),n.filter(s=>!Be(s)).reduce((s,o)=>s+(Mt.test(o)?$t:o===""?zt:Wt),r)}function Kt(e,t){return e.length===t.length&&e.slice(0,-1).every((r,s)=>r===t[s])?e[e.length-1]-t[t.length-1]:0}function Xt(e,t,n){let{routesMeta:r}=e,s={},o="/",i=[];for(let a=0;a<r.length;++a){let f=r[a],c=a===r.length-1,u=o==="/"?t:t.slice(o.length)||"/",d=Gt({path:f.relativePath,caseSensitive:f.caseSensitive,end:c},u),g=f.route;if(!d)return null;Object.assign(s,d.params),i.push({params:s,pathname:k([o,d.pathname]),pathnameBase:nn(k([o,d.pathnameBase])),route:g}),d.pathnameBase!=="/"&&(o=k([o,d.pathnameBase]))}return i}function Gt(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=Qt(e.path,e.caseSensitive,e.end),s=t.match(n);if(!s)return null;let o=s[0],i=o.replace(/(.)\/+$/,"$1"),a=s.slice(1);return{params:r.reduce((c,u,d)=>{let{paramName:g,isOptional:E}=u;if(g==="*"){let m=a[d]||"";i=o.slice(0,o.length-m.length).replace(/(.)\/+$/,"$1")}const p=a[d];return E&&!p?c[g]=void 0:c[g]=(p||"").replace(/%2F/g,"/"),c},{}),pathname:o,pathnameBase:i,pattern:e}}function Qt(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Ge(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],s="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,a,f)=>(r.push({paramName:a,isOptional:f!=null}),f?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),s+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?s+="\\/*$":e!==""&&e!=="/"&&(s+="(?:(?=\\/|$))"),[new RegExp(s,t?void 0:"i"),r]}function Zt(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Ge(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function et(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function Yt(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:s=""}=typeof e=="string"?q(e):e;return{pathname:n?n.startsWith("/")?n:en(n,t):t,search:rn(r),hash:sn(s)}}function en(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(s=>{s===".."?n.length>1&&n.pop():s!=="."&&n.push(s)}),n.length>1?n.join("/"):"/"}function fe(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function tn(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function tt(e,t){let n=tn(e);return t?n.map((r,s)=>s===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function nt(e,t,n,r){r===void 0&&(r=!1);let s;typeof e=="string"?s=q(e):(s=z({},e),x(!s.pathname||!s.pathname.includes("?"),fe("?","pathname","search",s)),x(!s.pathname||!s.pathname.includes("#"),fe("#","pathname","hash",s)),x(!s.search||!s.search.includes("#"),fe("#","search","hash",s)));let o=e===""||s.pathname==="",i=o?"/":s.pathname,a;if(i==null)a=n;else{let d=t.length-1;if(!r&&i.startsWith("..")){let g=i.split("/");for(;g[0]==="..";)g.shift(),d-=1;s.pathname=g.join("/")}a=d>=0?t[d]:"/"}let f=Yt(s,a),c=i&&i!=="/"&&i.endsWith("/"),u=(o||i===".")&&n.endsWith("/");return!f.pathname.endsWith("/")&&(c||u)&&(f.pathname+="/"),f}const k=e=>e.join("/").replace(/\/\/+/g,"/"),nn=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),rn=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,sn=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function on(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const rt=["post","put","patch","delete"];new Set(rt);const an=["get",...rt];new Set(an);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function W(){return W=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},W.apply(this,arguments)}const xe=y.createContext(null),ln=y.createContext(null),V=y.createContext(null),re=y.createContext(null),I=y.createContext({outlet:null,matches:[],isDataRoute:!1}),st=y.createContext(null);function K(){return y.useContext(re)!=null}function ve(){return K()||x(!1),y.useContext(re).location}function ot(e){y.useContext(V).static||y.useLayoutEffect(e)}function cn(){let{isDataRoute:e}=y.useContext(I);return e?Sn():un()}function un(){K()||x(!1);let e=y.useContext(xe),{basename:t,future:n,navigator:r}=y.useContext(V),{matches:s}=y.useContext(I),{pathname:o}=ve(),i=JSON.stringify(tt(s,n.v7_relativeSplatPath)),a=y.useRef(!1);return ot(()=>{a.current=!0}),y.useCallback(function(c,u){if(u===void 0&&(u={}),!a.current)return;if(typeof c=="number"){r.go(c);return}let d=nt(c,JSON.parse(i),o,u.relative==="path");e==null&&t!=="/"&&(d.pathname=d.pathname==="/"?t:k([t,d.pathname])),(u.replace?r.replace:r.push)(d,u.state,u)},[t,r,i,o,e])}function fn(e,t){return dn(e,t)}function dn(e,t,n,r){K()||x(!1);let{navigator:s}=y.useContext(V),{matches:o}=y.useContext(I),i=o[o.length-1],a=i?i.params:{};i&&i.pathname;let f=i?i.pathnameBase:"/";i&&i.route;let c=ve(),u;if(t){var d;let h=typeof t=="string"?q(t):t;f==="/"||(d=h.pathname)!=null&&d.startsWith(f)||x(!1),u=h}else u=c;let g=u.pathname||"/",E=g;if(f!=="/"){let h=f.replace(/^\//,"").split("/");E="/"+g.replace(/^\//,"").split("/").slice(h.length).join("/")}let p=jt(e,{pathname:E}),m=gn(p&&p.map(h=>Object.assign({},h,{params:Object.assign({},a,h.params),pathname:k([f,s.encodeLocation?s.encodeLocation(h.pathname).pathname:h.pathname]),pathnameBase:h.pathnameBase==="/"?f:k([f,s.encodeLocation?s.encodeLocation(h.pathnameBase).pathname:h.pathnameBase])})),o,n,r);return t&&m?y.createElement(re.Provider,{value:{location:W({pathname:"/",search:"",hash:"",state:null,key:"default"},u),navigationType:L.Pop}},m):m}function hn(){let e=Rn(),t=on(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,s={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return y.createElement(y.Fragment,null,y.createElement("h2",null,"Unexpected Application Error!"),y.createElement("h3",{style:{fontStyle:"italic"}},t),n?y.createElement("pre",{style:s},n):null,null)}const pn=y.createElement(hn,null);class mn extends y.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?y.createElement(I.Provider,{value:this.props.routeContext},y.createElement(st.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function yn(e){let{routeContext:t,match:n,children:r}=e,s=y.useContext(xe);return s&&s.static&&s.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(s.staticContext._deepestRenderedBoundaryId=n.route.id),y.createElement(I.Provider,{value:t},r)}function gn(e,t,n,r){var s;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var o;if(!n)return null;if(n.errors)e=n.matches;else if((o=r)!=null&&o.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let i=e,a=(s=n)==null?void 0:s.errors;if(a!=null){let u=i.findIndex(d=>d.route.id&&(a==null?void 0:a[d.route.id])!==void 0);u>=0||x(!1),i=i.slice(0,Math.min(i.length,u+1))}let f=!1,c=-1;if(n&&r&&r.v7_partialHydration)for(let u=0;u<i.length;u++){let d=i[u];if((d.route.HydrateFallback||d.route.hydrateFallbackElement)&&(c=u),d.route.id){let{loaderData:g,errors:E}=n,p=d.route.loader&&g[d.route.id]===void 0&&(!E||E[d.route.id]===void 0);if(d.route.lazy||p){f=!0,c>=0?i=i.slice(0,c+1):i=[i[0]];break}}}return i.reduceRight((u,d,g)=>{let E,p=!1,m=null,h=null;n&&(E=a&&d.route.id?a[d.route.id]:void 0,m=d.route.errorElement||pn,f&&(c<0&&g===0?(xn("route-fallback"),p=!0,h=null):c===g&&(p=!0,h=d.route.hydrateFallbackElement||null)));let b=t.concat(i.slice(0,g+1)),R=()=>{let S;return E?S=m:p?S=h:d.route.Component?S=y.createElement(d.route.Component,null):d.route.element?S=d.route.element:S=u,y.createElement(yn,{match:d,routeContext:{outlet:u,matches:b,isDataRoute:n!=null},children:S})};return n&&(d.route.ErrorBoundary||d.route.errorElement||g===0)?y.createElement(mn,{location:n.location,revalidation:n.revalidation,component:m,error:E,children:R(),routeContext:{outlet:null,matches:b,isDataRoute:!0}}):R()},null)}var it=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(it||{}),at=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(at||{});function wn(e){let t=y.useContext(xe);return t||x(!1),t}function bn(e){let t=y.useContext(ln);return t||x(!1),t}function En(e){let t=y.useContext(I);return t||x(!1),t}function lt(e){let t=En(),n=t.matches[t.matches.length-1];return n.route.id||x(!1),n.route.id}function Rn(){var e;let t=y.useContext(st),n=bn(),r=lt();return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function Sn(){let{router:e}=wn(it.UseNavigateStable),t=lt(at.UseNavigateStable),n=y.useRef(!1);return ot(()=>{n.current=!0}),y.useCallback(function(s,o){o===void 0&&(o={}),n.current&&(typeof s=="number"?e.navigate(s):e.navigate(s,W({fromRouteId:t},o)))},[e,t])}const Le={};function xn(e,t,n){Le[e]||(Le[e]=!0)}function vn(e,t){e==null||e.v7_startTransition,e==null||e.v7_relativeSplatPath}function is(e){let{to:t,replace:n,state:r,relative:s}=e;K()||x(!1);let{future:o,static:i}=y.useContext(V),{matches:a}=y.useContext(I),{pathname:f}=ve(),c=cn(),u=nt(t,tt(a,o.v7_relativeSplatPath),f,s==="path"),d=JSON.stringify(u);return y.useEffect(()=>c(JSON.parse(d),{replace:n,state:r,relative:s}),[c,d,s,n,r]),null}function Cn(e){x(!1)}function On(e){let{basename:t="/",children:n=null,location:r,navigationType:s=L.Pop,navigator:o,static:i=!1,future:a}=e;K()&&x(!1);let f=t.replace(/^\/*/,"/"),c=y.useMemo(()=>({basename:f,navigator:o,static:i,future:W({v7_relativeSplatPath:!1},a)}),[f,a,o,i]);typeof r=="string"&&(r=q(r));let{pathname:u="/",search:d="",hash:g="",state:E=null,key:p="default"}=r,m=y.useMemo(()=>{let h=et(u,f);return h==null?null:{location:{pathname:h,search:d,hash:g,state:E,key:p},navigationType:s}},[f,u,d,g,E,p,s]);return m==null?null:y.createElement(V.Provider,{value:c},y.createElement(re.Provider,{children:n,value:m}))}function as(e){let{children:t,location:n}=e;return fn(ye(t),n)}new Promise(()=>{});function ye(e,t){t===void 0&&(t=[]);let n=[];return y.Children.forEach(e,(r,s)=>{if(!y.isValidElement(r))return;let o=[...t,s];if(r.type===y.Fragment){n.push.apply(n,ye(r.props.children,o));return}r.type!==Cn&&x(!1),!r.props.index||!r.props.children||x(!1);let i={id:r.props.id||o.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(i.children=ye(r.props.children,o)),n.push(i)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const Tn="6";try{window.__reactRouterVersion=Tn}catch{}const Pn="startTransition",_e=_t[Pn];function ls(e){let{basename:t,children:n,future:r,window:s}=e,o=y.useRef();o.current==null&&(o.current=Ft({window:s,v5Compat:!0}));let i=o.current,[a,f]=y.useState({action:i.action,location:i.location}),{v7_startTransition:c}=r||{},u=y.useCallback(d=>{c&&_e?_e(()=>f(d)):f(d)},[f,c]);return y.useLayoutEffect(()=>i.listen(u),[i,u]),y.useEffect(()=>vn(r),[r]),y.createElement(On,{basename:t,children:n,location:a.location,navigationType:a.action,navigator:i,future:r})}var Fe;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Fe||(Fe={}));var ke;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(ke||(ke={}));function ct(e,t){return function(){return e.apply(t,arguments)}}const{toString:An}=Object.prototype,{getPrototypeOf:Ce}=Object,{iterator:se,toStringTag:ut}=Symbol,oe=(e=>t=>{const n=An.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),N=e=>(e=e.toLowerCase(),t=>oe(t)===e),ie=e=>t=>typeof t===e,{isArray:M}=Array,J=ie("undefined");function Nn(e){return e!==null&&!J(e)&&e.constructor!==null&&!J(e.constructor)&&P(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const ft=N("ArrayBuffer");function Un(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&ft(e.buffer),t}const Bn=ie("string"),P=ie("function"),dt=ie("number"),ae=e=>e!==null&&typeof e=="object",Ln=e=>e===!0||e===!1,Z=e=>{if(oe(e)!=="object")return!1;const t=Ce(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(ut in e)&&!(se in e)},_n=N("Date"),Fn=N("File"),kn=N("Blob"),Dn=N("FileList"),jn=e=>ae(e)&&P(e.pipe),In=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||P(e.append)&&((t=oe(e))==="formdata"||t==="object"&&P(e.toString)&&e.toString()==="[object FormData]"))},qn=N("URLSearchParams"),[Mn,$n,Hn,zn]=["ReadableStream","Request","Response","Headers"].map(N),Wn=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function X(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,s;if(typeof e!="object"&&(e=[e]),M(e))for(r=0,s=e.length;r<s;r++)t.call(null,e[r],r,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let a;for(r=0;r<i;r++)a=o[r],t.call(null,e[a],a,e)}}function ht(e,t){t=t.toLowerCase();const n=Object.keys(e);let r=n.length,s;for(;r-- >0;)if(s=n[r],t===s.toLowerCase())return s;return null}const F=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:globalThis,pt=e=>!J(e)&&e!==F;function ge(){const{caseless:e}=pt(this)&&this||{},t={},n=(r,s)=>{const o=e&&ht(t,s)||s;Z(t[o])&&Z(r)?t[o]=ge(t[o],r):Z(r)?t[o]=ge({},r):M(r)?t[o]=r.slice():t[o]=r};for(let r=0,s=arguments.length;r<s;r++)arguments[r]&&X(arguments[r],n);return t}const Jn=(e,t,n,{allOwnKeys:r}={})=>(X(t,(s,o)=>{n&&P(s)?e[o]=ct(s,n):e[o]=s},{allOwnKeys:r}),e),Vn=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Kn=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},Xn=(e,t,n,r)=>{let s,o,i;const a={};if(t=t||{},e==null)return t;do{for(s=Object.getOwnPropertyNames(e),o=s.length;o-- >0;)i=s[o],(!r||r(i,e,t))&&!a[i]&&(t[i]=e[i],a[i]=!0);e=n!==!1&&Ce(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},Gn=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},Qn=e=>{if(!e)return null;if(M(e))return e;let t=e.length;if(!dt(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},Zn=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Ce(Uint8Array)),Yn=(e,t)=>{const r=(e&&e[se]).call(e);let s;for(;(s=r.next())&&!s.done;){const o=s.value;t.call(e,o[0],o[1])}},er=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},tr=N("HTMLFormElement"),nr=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,s){return r.toUpperCase()+s}),De=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),rr=N("RegExp"),mt=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};X(n,(s,o)=>{let i;(i=t(s,o,e))!==!1&&(r[o]=i||s)}),Object.defineProperties(e,r)},sr=e=>{mt(e,(t,n)=>{if(P(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(P(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},or=(e,t)=>{const n={},r=s=>{s.forEach(o=>{n[o]=!0})};return M(e)?r(e):r(String(e).split(t)),n},ir=()=>{},ar=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function lr(e){return!!(e&&P(e.append)&&e[ut]==="FormData"&&e[se])}const cr=e=>{const t=new Array(10),n=(r,s)=>{if(ae(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[s]=r;const o=M(r)?[]:{};return X(r,(i,a)=>{const f=n(i,s+1);!J(f)&&(o[a]=f)}),t[s]=void 0,o}}return r};return n(e,0)},ur=N("AsyncFunction"),fr=e=>e&&(ae(e)||P(e))&&P(e.then)&&P(e.catch),yt=((e,t)=>e?setImmediate:t?((n,r)=>(F.addEventListener("message",({source:s,data:o})=>{s===F&&o===n&&r.length&&r.shift()()},!1),s=>{r.push(s),F.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",P(F.postMessage)),dr=typeof queueMicrotask<"u"?queueMicrotask.bind(F):typeof process<"u"&&process.nextTick||yt,hr=e=>e!=null&&P(e[se]),l={isArray:M,isArrayBuffer:ft,isBuffer:Nn,isFormData:In,isArrayBufferView:Un,isString:Bn,isNumber:dt,isBoolean:Ln,isObject:ae,isPlainObject:Z,isReadableStream:Mn,isRequest:$n,isResponse:Hn,isHeaders:zn,isUndefined:J,isDate:_n,isFile:Fn,isBlob:kn,isRegExp:rr,isFunction:P,isStream:jn,isURLSearchParams:qn,isTypedArray:Zn,isFileList:Dn,forEach:X,merge:ge,extend:Jn,trim:Wn,stripBOM:Vn,inherits:Kn,toFlatObject:Xn,kindOf:oe,kindOfTest:N,endsWith:Gn,toArray:Qn,forEachEntry:Yn,matchAll:er,isHTMLForm:tr,hasOwnProperty:De,hasOwnProp:De,reduceDescriptors:mt,freezeMethods:sr,toObjectSet:or,toCamelCase:nr,noop:ir,toFiniteNumber:ar,findKey:ht,global:F,isContextDefined:pt,isSpecCompliantForm:lr,toJSONObject:cr,isAsyncFn:ur,isThenable:fr,setImmediate:yt,asap:dr,isIterable:hr};function w(e,t,n,r,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),s&&(this.response=s,this.status=s.status?s.status:null)}l.inherits(w,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:l.toJSONObject(this.config),code:this.code,status:this.status}}});const gt=w.prototype,wt={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{wt[e]={value:e}});Object.defineProperties(w,wt);Object.defineProperty(gt,"isAxiosError",{value:!0});w.from=(e,t,n,r,s,o)=>{const i=Object.create(gt);return l.toFlatObject(e,i,function(f){return f!==Error.prototype},a=>a!=="isAxiosError"),w.call(i,e.message,t,n,r,s),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const pr=null;function we(e){return l.isPlainObject(e)||l.isArray(e)}function bt(e){return l.endsWith(e,"[]")?e.slice(0,-2):e}function je(e,t,n){return e?e.concat(t).map(function(s,o){return s=bt(s),!n&&o?"["+s+"]":s}).join(n?".":""):t}function mr(e){return l.isArray(e)&&!e.some(we)}const yr=l.toFlatObject(l,{},null,function(t){return/^is[A-Z]/.test(t)});function le(e,t,n){if(!l.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=l.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(m,h){return!l.isUndefined(h[m])});const r=n.metaTokens,s=n.visitor||u,o=n.dots,i=n.indexes,f=(n.Blob||typeof Blob<"u"&&Blob)&&l.isSpecCompliantForm(t);if(!l.isFunction(s))throw new TypeError("visitor must be a function");function c(p){if(p===null)return"";if(l.isDate(p))return p.toISOString();if(!f&&l.isBlob(p))throw new w("Blob is not supported. Use a Buffer instead.");return l.isArrayBuffer(p)||l.isTypedArray(p)?f&&typeof Blob=="function"?new Blob([p]):Buffer.from(p):p}function u(p,m,h){let b=p;if(p&&!h&&typeof p=="object"){if(l.endsWith(m,"{}"))m=r?m:m.slice(0,-2),p=JSON.stringify(p);else if(l.isArray(p)&&mr(p)||(l.isFileList(p)||l.endsWith(m,"[]"))&&(b=l.toArray(p)))return m=bt(m),b.forEach(function(S,T){!(l.isUndefined(S)||S===null)&&t.append(i===!0?je([m],T,o):i===null?m:m+"[]",c(S))}),!1}return we(p)?!0:(t.append(je(h,m,o),c(p)),!1)}const d=[],g=Object.assign(yr,{defaultVisitor:u,convertValue:c,isVisitable:we});function E(p,m){if(!l.isUndefined(p)){if(d.indexOf(p)!==-1)throw Error("Circular reference detected in "+m.join("."));d.push(p),l.forEach(p,function(b,R){(!(l.isUndefined(b)||b===null)&&s.call(t,b,l.isString(R)?R.trim():R,m,g))===!0&&E(b,m?m.concat(R):[R])}),d.pop()}}if(!l.isObject(e))throw new TypeError("data must be an object");return E(e),t}function Ie(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function Oe(e,t){this._pairs=[],e&&le(e,this,t)}const Et=Oe.prototype;Et.append=function(t,n){this._pairs.push([t,n])};Et.toString=function(t){const n=t?function(r){return t.call(this,r,Ie)}:Ie;return this._pairs.map(function(s){return n(s[0])+"="+n(s[1])},"").join("&")};function gr(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Rt(e,t,n){if(!t)return e;const r=n&&n.encode||gr;l.isFunction(n)&&(n={serialize:n});const s=n&&n.serialize;let o;if(s?o=s(t,n):o=l.isURLSearchParams(t)?t.toString():new Oe(t,n).toString(r),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class qe{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){l.forEach(this.handlers,function(r){r!==null&&t(r)})}}const St={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},wr=typeof URLSearchParams<"u"?URLSearchParams:Oe,br=typeof FormData<"u"?FormData:null,Er=typeof Blob<"u"?Blob:null,Rr={isBrowser:!0,classes:{URLSearchParams:wr,FormData:br,Blob:Er},protocols:["http","https","file","blob","url","data"]},Te=typeof window<"u"&&typeof document<"u",be=typeof navigator=="object"&&navigator||void 0,Sr=Te&&(!be||["ReactNative","NativeScript","NS"].indexOf(be.product)<0),xr=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",vr=Te&&window.location.href||"http://localhost",Cr=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Te,hasStandardBrowserEnv:Sr,hasStandardBrowserWebWorkerEnv:xr,navigator:be,origin:vr},Symbol.toStringTag,{value:"Module"})),O={...Cr,...Rr};function Or(e,t){return le(e,new O.classes.URLSearchParams,Object.assign({visitor:function(n,r,s,o){return O.isNode&&l.isBuffer(n)?(this.append(r,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function Tr(e){return l.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Pr(e){const t={},n=Object.keys(e);let r;const s=n.length;let o;for(r=0;r<s;r++)o=n[r],t[o]=e[o];return t}function xt(e){function t(n,r,s,o){let i=n[o++];if(i==="__proto__")return!0;const a=Number.isFinite(+i),f=o>=n.length;return i=!i&&l.isArray(s)?s.length:i,f?(l.hasOwnProp(s,i)?s[i]=[s[i],r]:s[i]=r,!a):((!s[i]||!l.isObject(s[i]))&&(s[i]=[]),t(n,r,s[i],o)&&l.isArray(s[i])&&(s[i]=Pr(s[i])),!a)}if(l.isFormData(e)&&l.isFunction(e.entries)){const n={};return l.forEachEntry(e,(r,s)=>{t(Tr(r),s,n,0)}),n}return null}function Ar(e,t,n){if(l.isString(e))try{return(t||JSON.parse)(e),l.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const G={transitional:St,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",s=r.indexOf("application/json")>-1,o=l.isObject(t);if(o&&l.isHTMLForm(t)&&(t=new FormData(t)),l.isFormData(t))return s?JSON.stringify(xt(t)):t;if(l.isArrayBuffer(t)||l.isBuffer(t)||l.isStream(t)||l.isFile(t)||l.isBlob(t)||l.isReadableStream(t))return t;if(l.isArrayBufferView(t))return t.buffer;if(l.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let a;if(o){if(r.indexOf("application/x-www-form-urlencoded")>-1)return Or(t,this.formSerializer).toString();if((a=l.isFileList(t))||r.indexOf("multipart/form-data")>-1){const f=this.env&&this.env.FormData;return le(a?{"files[]":t}:t,f&&new f,this.formSerializer)}}return o||s?(n.setContentType("application/json",!1),Ar(t)):t}],transformResponse:[function(t){const n=this.transitional||G.transitional,r=n&&n.forcedJSONParsing,s=this.responseType==="json";if(l.isResponse(t)||l.isReadableStream(t))return t;if(t&&l.isString(t)&&(r&&!this.responseType||s)){const i=!(n&&n.silentJSONParsing)&&s;try{return JSON.parse(t)}catch(a){if(i)throw a.name==="SyntaxError"?w.from(a,w.ERR_BAD_RESPONSE,this,null,this.response):a}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:O.classes.FormData,Blob:O.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};l.forEach(["delete","get","head","post","put","patch"],e=>{G.headers[e]={}});const Nr=l.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Ur=e=>{const t={};let n,r,s;return e&&e.split(`
`).forEach(function(i){s=i.indexOf(":"),n=i.substring(0,s).trim().toLowerCase(),r=i.substring(s+1).trim(),!(!n||t[n]&&Nr[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},Me=Symbol("internals");function H(e){return e&&String(e).trim().toLowerCase()}function Y(e){return e===!1||e==null?e:l.isArray(e)?e.map(Y):String(e)}function Br(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const Lr=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function de(e,t,n,r,s){if(l.isFunction(r))return r.call(this,t,n);if(s&&(t=n),!!l.isString(t)){if(l.isString(r))return t.indexOf(r)!==-1;if(l.isRegExp(r))return r.test(t)}}function _r(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function Fr(e,t){const n=l.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(s,o,i){return this[r].call(this,t,s,o,i)},configurable:!0})})}let A=class{constructor(t){t&&this.set(t)}set(t,n,r){const s=this;function o(a,f,c){const u=H(f);if(!u)throw new Error("header name must be a non-empty string");const d=l.findKey(s,u);(!d||s[d]===void 0||c===!0||c===void 0&&s[d]!==!1)&&(s[d||f]=Y(a))}const i=(a,f)=>l.forEach(a,(c,u)=>o(c,u,f));if(l.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(l.isString(t)&&(t=t.trim())&&!Lr(t))i(Ur(t),n);else if(l.isObject(t)&&l.isIterable(t)){let a={},f,c;for(const u of t){if(!l.isArray(u))throw TypeError("Object iterator must return a key-value pair");a[c=u[0]]=(f=a[c])?l.isArray(f)?[...f,u[1]]:[f,u[1]]:u[1]}i(a,n)}else t!=null&&o(n,t,r);return this}get(t,n){if(t=H(t),t){const r=l.findKey(this,t);if(r){const s=this[r];if(!n)return s;if(n===!0)return Br(s);if(l.isFunction(n))return n.call(this,s,r);if(l.isRegExp(n))return n.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=H(t),t){const r=l.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||de(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let s=!1;function o(i){if(i=H(i),i){const a=l.findKey(r,i);a&&(!n||de(r,r[a],a,n))&&(delete r[a],s=!0)}}return l.isArray(t)?t.forEach(o):o(t),s}clear(t){const n=Object.keys(this);let r=n.length,s=!1;for(;r--;){const o=n[r];(!t||de(this,this[o],o,t,!0))&&(delete this[o],s=!0)}return s}normalize(t){const n=this,r={};return l.forEach(this,(s,o)=>{const i=l.findKey(r,o);if(i){n[i]=Y(s),delete n[o];return}const a=t?_r(o):String(o).trim();a!==o&&delete n[o],n[a]=Y(s),r[a]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return l.forEach(this,(r,s)=>{r!=null&&r!==!1&&(n[s]=t&&l.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(s=>r.set(s)),r}static accessor(t){const r=(this[Me]=this[Me]={accessors:{}}).accessors,s=this.prototype;function o(i){const a=H(i);r[a]||(Fr(s,i),r[a]=!0)}return l.isArray(t)?t.forEach(o):o(t),this}};A.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);l.reduceDescriptors(A.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});l.freezeMethods(A);function he(e,t){const n=this||G,r=t||n,s=A.from(r.headers);let o=r.data;return l.forEach(e,function(a){o=a.call(n,o,s.normalize(),t?t.status:void 0)}),s.normalize(),o}function vt(e){return!!(e&&e.__CANCEL__)}function $(e,t,n){w.call(this,e??"canceled",w.ERR_CANCELED,t,n),this.name="CanceledError"}l.inherits($,w,{__CANCEL__:!0});function Ct(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new w("Request failed with status code "+n.status,[w.ERR_BAD_REQUEST,w.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function kr(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Dr(e,t){e=e||10;const n=new Array(e),r=new Array(e);let s=0,o=0,i;return t=t!==void 0?t:1e3,function(f){const c=Date.now(),u=r[o];i||(i=c),n[s]=f,r[s]=c;let d=o,g=0;for(;d!==s;)g+=n[d++],d=d%e;if(s=(s+1)%e,s===o&&(o=(o+1)%e),c-i<t)return;const E=u&&c-u;return E?Math.round(g*1e3/E):void 0}}function jr(e,t){let n=0,r=1e3/t,s,o;const i=(c,u=Date.now())=>{n=u,s=null,o&&(clearTimeout(o),o=null),e.apply(null,c)};return[(...c)=>{const u=Date.now(),d=u-n;d>=r?i(c,u):(s=c,o||(o=setTimeout(()=>{o=null,i(s)},r-d)))},()=>s&&i(s)]}const te=(e,t,n=3)=>{let r=0;const s=Dr(50,250);return jr(o=>{const i=o.loaded,a=o.lengthComputable?o.total:void 0,f=i-r,c=s(f),u=i<=a;r=i;const d={loaded:i,total:a,progress:a?i/a:void 0,bytes:f,rate:c||void 0,estimated:c&&a&&u?(a-i)/c:void 0,event:o,lengthComputable:a!=null,[t?"download":"upload"]:!0};e(d)},n)},$e=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},He=e=>(...t)=>l.asap(()=>e(...t)),Ir=O.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,O.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(O.origin),O.navigator&&/(msie|trident)/i.test(O.navigator.userAgent)):()=>!0,qr=O.hasStandardBrowserEnv?{write(e,t,n,r,s,o){const i=[e+"="+encodeURIComponent(t)];l.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),l.isString(r)&&i.push("path="+r),l.isString(s)&&i.push("domain="+s),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Mr(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function $r(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Ot(e,t,n){let r=!Mr(t);return e&&(r||n==!1)?$r(e,t):t}const ze=e=>e instanceof A?{...e}:e;function j(e,t){t=t||{};const n={};function r(c,u,d,g){return l.isPlainObject(c)&&l.isPlainObject(u)?l.merge.call({caseless:g},c,u):l.isPlainObject(u)?l.merge({},u):l.isArray(u)?u.slice():u}function s(c,u,d,g){if(l.isUndefined(u)){if(!l.isUndefined(c))return r(void 0,c,d,g)}else return r(c,u,d,g)}function o(c,u){if(!l.isUndefined(u))return r(void 0,u)}function i(c,u){if(l.isUndefined(u)){if(!l.isUndefined(c))return r(void 0,c)}else return r(void 0,u)}function a(c,u,d){if(d in t)return r(c,u);if(d in e)return r(void 0,c)}const f={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:a,headers:(c,u,d)=>s(ze(c),ze(u),d,!0)};return l.forEach(Object.keys(Object.assign({},e,t)),function(u){const d=f[u]||s,g=d(e[u],t[u],u);l.isUndefined(g)&&d!==a||(n[u]=g)}),n}const Tt=e=>{const t=j({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:s,xsrfCookieName:o,headers:i,auth:a}=t;t.headers=i=A.from(i),t.url=Rt(Ot(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),a&&i.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):"")));let f;if(l.isFormData(n)){if(O.hasStandardBrowserEnv||O.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((f=i.getContentType())!==!1){const[c,...u]=f?f.split(";").map(d=>d.trim()).filter(Boolean):[];i.setContentType([c||"multipart/form-data",...u].join("; "))}}if(O.hasStandardBrowserEnv&&(r&&l.isFunction(r)&&(r=r(t)),r||r!==!1&&Ir(t.url))){const c=s&&o&&qr.read(o);c&&i.set(s,c)}return t},Hr=typeof XMLHttpRequest<"u",zr=Hr&&function(e){return new Promise(function(n,r){const s=Tt(e);let o=s.data;const i=A.from(s.headers).normalize();let{responseType:a,onUploadProgress:f,onDownloadProgress:c}=s,u,d,g,E,p;function m(){E&&E(),p&&p(),s.cancelToken&&s.cancelToken.unsubscribe(u),s.signal&&s.signal.removeEventListener("abort",u)}let h=new XMLHttpRequest;h.open(s.method.toUpperCase(),s.url,!0),h.timeout=s.timeout;function b(){if(!h)return;const S=A.from("getAllResponseHeaders"in h&&h.getAllResponseHeaders()),C={data:!a||a==="text"||a==="json"?h.responseText:h.response,status:h.status,statusText:h.statusText,headers:S,config:e,request:h};Ct(function(_){n(_),m()},function(_){r(_),m()},C),h=null}"onloadend"in h?h.onloadend=b:h.onreadystatechange=function(){!h||h.readyState!==4||h.status===0&&!(h.responseURL&&h.responseURL.indexOf("file:")===0)||setTimeout(b)},h.onabort=function(){h&&(r(new w("Request aborted",w.ECONNABORTED,e,h)),h=null)},h.onerror=function(){r(new w("Network Error",w.ERR_NETWORK,e,h)),h=null},h.ontimeout=function(){let T=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const C=s.transitional||St;s.timeoutErrorMessage&&(T=s.timeoutErrorMessage),r(new w(T,C.clarifyTimeoutError?w.ETIMEDOUT:w.ECONNABORTED,e,h)),h=null},o===void 0&&i.setContentType(null),"setRequestHeader"in h&&l.forEach(i.toJSON(),function(T,C){h.setRequestHeader(C,T)}),l.isUndefined(s.withCredentials)||(h.withCredentials=!!s.withCredentials),a&&a!=="json"&&(h.responseType=s.responseType),c&&([g,p]=te(c,!0),h.addEventListener("progress",g)),f&&h.upload&&([d,E]=te(f),h.upload.addEventListener("progress",d),h.upload.addEventListener("loadend",E)),(s.cancelToken||s.signal)&&(u=S=>{h&&(r(!S||S.type?new $(null,e,h):S),h.abort(),h=null)},s.cancelToken&&s.cancelToken.subscribe(u),s.signal&&(s.signal.aborted?u():s.signal.addEventListener("abort",u)));const R=kr(s.url);if(R&&O.protocols.indexOf(R)===-1){r(new w("Unsupported protocol "+R+":",w.ERR_BAD_REQUEST,e));return}h.send(o||null)})},Wr=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,s;const o=function(c){if(!s){s=!0,a();const u=c instanceof Error?c:this.reason;r.abort(u instanceof w?u:new $(u instanceof Error?u.message:u))}};let i=t&&setTimeout(()=>{i=null,o(new w(`timeout ${t} of ms exceeded`,w.ETIMEDOUT))},t);const a=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(c=>{c.unsubscribe?c.unsubscribe(o):c.removeEventListener("abort",o)}),e=null)};e.forEach(c=>c.addEventListener("abort",o));const{signal:f}=r;return f.unsubscribe=()=>l.asap(a),f}},Jr=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let r=0,s;for(;r<n;)s=r+t,yield e.slice(r,s),r=s},Vr=async function*(e,t){for await(const n of Kr(e))yield*Jr(n,t)},Kr=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},We=(e,t,n,r)=>{const s=Vr(e,t);let o=0,i,a=f=>{i||(i=!0,r&&r(f))};return new ReadableStream({async pull(f){try{const{done:c,value:u}=await s.next();if(c){a(),f.close();return}let d=u.byteLength;if(n){let g=o+=d;n(g)}f.enqueue(new Uint8Array(u))}catch(c){throw a(c),c}},cancel(f){return a(f),s.return()}},{highWaterMark:2})},ce=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Pt=ce&&typeof ReadableStream=="function",Xr=ce&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),At=(e,...t)=>{try{return!!e(...t)}catch{return!1}},Gr=Pt&&At(()=>{let e=!1;const t=new Request(O.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Je=64*1024,Ee=Pt&&At(()=>l.isReadableStream(new Response("").body)),ne={stream:Ee&&(e=>e.body)};ce&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!ne[t]&&(ne[t]=l.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new w(`Response type '${t}' is not supported`,w.ERR_NOT_SUPPORT,r)})})})(new Response);const Qr=async e=>{if(e==null)return 0;if(l.isBlob(e))return e.size;if(l.isSpecCompliantForm(e))return(await new Request(O.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(l.isArrayBufferView(e)||l.isArrayBuffer(e))return e.byteLength;if(l.isURLSearchParams(e)&&(e=e+""),l.isString(e))return(await Xr(e)).byteLength},Zr=async(e,t)=>{const n=l.toFiniteNumber(e.getContentLength());return n??Qr(t)},Yr=ce&&(async e=>{let{url:t,method:n,data:r,signal:s,cancelToken:o,timeout:i,onDownloadProgress:a,onUploadProgress:f,responseType:c,headers:u,withCredentials:d="same-origin",fetchOptions:g}=Tt(e);c=c?(c+"").toLowerCase():"text";let E=Wr([s,o&&o.toAbortSignal()],i),p;const m=E&&E.unsubscribe&&(()=>{E.unsubscribe()});let h;try{if(f&&Gr&&n!=="get"&&n!=="head"&&(h=await Zr(u,r))!==0){let C=new Request(t,{method:"POST",body:r,duplex:"half"}),B;if(l.isFormData(r)&&(B=C.headers.get("content-type"))&&u.setContentType(B),C.body){const[_,Q]=$e(h,te(He(f)));r=We(C.body,Je,_,Q)}}l.isString(d)||(d=d?"include":"omit");const b="credentials"in Request.prototype;p=new Request(t,{...g,signal:E,method:n.toUpperCase(),headers:u.normalize().toJSON(),body:r,duplex:"half",credentials:b?d:void 0});let R=await fetch(p);const S=Ee&&(c==="stream"||c==="response");if(Ee&&(a||S&&m)){const C={};["status","statusText","headers"].forEach(Pe=>{C[Pe]=R[Pe]});const B=l.toFiniteNumber(R.headers.get("content-length")),[_,Q]=a&&$e(B,te(He(a),!0))||[];R=new Response(We(R.body,Je,_,()=>{Q&&Q(),m&&m()}),C)}c=c||"text";let T=await ne[l.findKey(ne,c)||"text"](R,e);return!S&&m&&m(),await new Promise((C,B)=>{Ct(C,B,{data:T,headers:A.from(R.headers),status:R.status,statusText:R.statusText,config:e,request:p})})}catch(b){throw m&&m(),b&&b.name==="TypeError"&&/Load failed|fetch/i.test(b.message)?Object.assign(new w("Network Error",w.ERR_NETWORK,e,p),{cause:b.cause||b}):w.from(b,b&&b.code,e,p)}}),Re={http:pr,xhr:zr,fetch:Yr};l.forEach(Re,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Ve=e=>`- ${e}`,es=e=>l.isFunction(e)||e===null||e===!1,Nt={getAdapter:e=>{e=l.isArray(e)?e:[e];const{length:t}=e;let n,r;const s={};for(let o=0;o<t;o++){n=e[o];let i;if(r=n,!es(n)&&(r=Re[(i=String(n)).toLowerCase()],r===void 0))throw new w(`Unknown adapter '${i}'`);if(r)break;s[i||"#"+o]=r}if(!r){const o=Object.entries(s).map(([a,f])=>`adapter ${a} `+(f===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(Ve).join(`
`):" "+Ve(o[0]):"as no adapter specified";throw new w("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return r},adapters:Re};function pe(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new $(null,e)}function Ke(e){return pe(e),e.headers=A.from(e.headers),e.data=he.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Nt.getAdapter(e.adapter||G.adapter)(e).then(function(r){return pe(e),r.data=he.call(e,e.transformResponse,r),r.headers=A.from(r.headers),r},function(r){return vt(r)||(pe(e),r&&r.response&&(r.response.data=he.call(e,e.transformResponse,r.response),r.response.headers=A.from(r.response.headers))),Promise.reject(r)})}const Ut="1.9.0",ue={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{ue[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const Xe={};ue.transitional=function(t,n,r){function s(o,i){return"[Axios v"+Ut+"] Transitional option '"+o+"'"+i+(r?". "+r:"")}return(o,i,a)=>{if(t===!1)throw new w(s(i," has been removed"+(n?" in "+n:"")),w.ERR_DEPRECATED);return n&&!Xe[i]&&(Xe[i]=!0,console.warn(s(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,i,a):!0}};ue.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function ts(e,t,n){if(typeof e!="object")throw new w("options must be an object",w.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let s=r.length;for(;s-- >0;){const o=r[s],i=t[o];if(i){const a=e[o],f=a===void 0||i(a,o,e);if(f!==!0)throw new w("option "+o+" must be "+f,w.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new w("Unknown option "+o,w.ERR_BAD_OPTION)}}const ee={assertOptions:ts,validators:ue},U=ee.validators;let D=class{constructor(t){this.defaults=t||{},this.interceptors={request:new qe,response:new qe}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;const o=s.stack?s.stack.replace(/^.+\n/,""):"";try{r.stack?o&&!String(r.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+o):r.stack=o}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=j(this.defaults,n);const{transitional:r,paramsSerializer:s,headers:o}=n;r!==void 0&&ee.assertOptions(r,{silentJSONParsing:U.transitional(U.boolean),forcedJSONParsing:U.transitional(U.boolean),clarifyTimeoutError:U.transitional(U.boolean)},!1),s!=null&&(l.isFunction(s)?n.paramsSerializer={serialize:s}:ee.assertOptions(s,{encode:U.function,serialize:U.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),ee.assertOptions(n,{baseUrl:U.spelling("baseURL"),withXsrfToken:U.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=o&&l.merge(o.common,o[n.method]);o&&l.forEach(["delete","get","head","post","put","patch","common"],p=>{delete o[p]}),n.headers=A.concat(i,o);const a=[];let f=!0;this.interceptors.request.forEach(function(m){typeof m.runWhen=="function"&&m.runWhen(n)===!1||(f=f&&m.synchronous,a.unshift(m.fulfilled,m.rejected))});const c=[];this.interceptors.response.forEach(function(m){c.push(m.fulfilled,m.rejected)});let u,d=0,g;if(!f){const p=[Ke.bind(this),void 0];for(p.unshift.apply(p,a),p.push.apply(p,c),g=p.length,u=Promise.resolve(n);d<g;)u=u.then(p[d++],p[d++]);return u}g=a.length;let E=n;for(d=0;d<g;){const p=a[d++],m=a[d++];try{E=p(E)}catch(h){m.call(this,h);break}}try{u=Ke.call(this,E)}catch(p){return Promise.reject(p)}for(d=0,g=c.length;d<g;)u=u.then(c[d++],c[d++]);return u}getUri(t){t=j(this.defaults,t);const n=Ot(t.baseURL,t.url,t.allowAbsoluteUrls);return Rt(n,t.params,t.paramsSerializer)}};l.forEach(["delete","get","head","options"],function(t){D.prototype[t]=function(n,r){return this.request(j(r||{},{method:t,url:n,data:(r||{}).data}))}});l.forEach(["post","put","patch"],function(t){function n(r){return function(o,i,a){return this.request(j(a||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}D.prototype[t]=n(),D.prototype[t+"Form"]=n(!0)});let ns=class Bt{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const r=this;this.promise.then(s=>{if(!r._listeners)return;let o=r._listeners.length;for(;o-- >0;)r._listeners[o](s);r._listeners=null}),this.promise.then=s=>{let o;const i=new Promise(a=>{r.subscribe(a),o=a}).then(s);return i.cancel=function(){r.unsubscribe(o)},i},t(function(o,i,a){r.reason||(r.reason=new $(o,i,a),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new Bt(function(s){t=s}),cancel:t}}};function rs(e){return function(n){return e.apply(null,n)}}function ss(e){return l.isObject(e)&&e.isAxiosError===!0}const Se={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Se).forEach(([e,t])=>{Se[t]=e});function Lt(e){const t=new D(e),n=ct(D.prototype.request,t);return l.extend(n,D.prototype,t,{allOwnKeys:!0}),l.extend(n,t,null,{allOwnKeys:!0}),n.create=function(s){return Lt(j(e,s))},n}const v=Lt(G);v.Axios=D;v.CanceledError=$;v.CancelToken=ns;v.isCancel=vt;v.VERSION=Ut;v.toFormData=le;v.AxiosError=w;v.Cancel=v.CanceledError;v.all=function(t){return Promise.all(t)};v.spread=rs;v.isAxiosError=ss;v.mergeConfig=j;v.AxiosHeaders=A;v.formToJSON=e=>xt(l.isHTMLForm(e)?new FormData(e):e);v.getAdapter=Nt.getAdapter;v.HttpStatusCode=Se;v.default=v;const{Axios:fs,AxiosError:ds,CanceledError:hs,isCancel:ps,CancelToken:ms,VERSION:ys,all:gs,Cancel:ws,isAxiosError:bs,spread:Es,toFormData:Rs,AxiosHeaders:Ss,HttpStatusCode:xs,formToJSON:vs,getAdapter:Cs,mergeConfig:Os}=v;export{ls as B,is as N,as as R,v as a,Cn as b,ve as c,cn as u};
