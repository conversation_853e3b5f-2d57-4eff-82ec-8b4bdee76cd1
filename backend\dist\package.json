{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "node build.js", "start": "node dist/index.js", "start:prod": "set NODE_ENV=production && node dist/index.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:migrate:deploy": "prisma migrate deploy", "db:studio": "prisma studio", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@prisma/client": "^6.8.2", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "multer": "^2.0.0", "mysql2": "^3.14.1", "prisma": "^6.8.2", "winston": "^3.17.0", "xlsx": "^0.18.5"}}