const express = require('express');
const path = require('path');

const app = express();
const PORT = 8080;

// 设置静态文件目录
app.use(express.static(path.join(__dirname, 'dist')));

// 处理所有路由，返回index.html（用于SPA路由）
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

app.listen(PORT, () => {
  console.log(`✅ 前端生产版本服务器启动成功！`);
  console.log(`🌐 地址: http://localhost:${PORT}`);
  console.log(`📁 静态文件目录: ${path.join(__dirname, 'dist')}`);
  console.log(`📅 启动时间: ${new Date().toLocaleString()}`);
  console.log('');
  console.log('🧪 测试说明:');
  console.log('  - 这是生产构建版本的测试服务器');
  console.log('  - 如果遇到React未定义错误，请检查浏览器控制台');
  console.log('  - 确保后端API服务器在 http://localhost:3001 运行');
});

module.exports = app;
