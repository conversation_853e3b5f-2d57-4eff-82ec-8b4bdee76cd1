/*! xlsx.js (C) 2013-present SheetJS -- http://sheetjs.com */var Ra={};Ra.version="0.18.5";var Pr=1200,Mt=1252,Xo=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],hi={0:1252,1:65001,2:65001,77:1e4,128:932,129:949,130:1361,134:936,136:950,161:1253,162:1254,163:1258,177:1255,178:1256,186:1257,204:1251,222:874,238:1250,255:1252,69:6969},xn=function(e){Xo.indexOf(e)!=-1&&(Mt=hi[0]=e)};function Go(){xn(1252)}var Gr=function(e){Pr=e,xn(e)};function An(){Gr(1200),Go()}function un(e){for(var t=[],r=0,a=e.length;r<a;++r)t[r]=e.charCodeAt(r);return t}function zo(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r)+(e.charCodeAt(2*r+1)<<8));return t.join("")}function vf(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r+1)+(e.charCodeAt(2*r)<<8));return t.join("")}var ra=function(e){var t=e.charCodeAt(0),r=e.charCodeAt(1);return t==255&&r==254?zo(e.slice(2)):t==254&&r==255?vf(e.slice(2)):t==65279?e.slice(1):e},ka=function(t){return String.fromCharCode(t)},qn=function(t){return String.fromCharCode(t)},Ie;function U_(e){Ie=e,Gr=function(t){Pr=t,xn(t)},ra=function(t){return t.charCodeAt(0)===255&&t.charCodeAt(1)===254?Ie.utils.decode(1200,un(t.slice(2))):t},ka=function(r){return Pr===1200?String.fromCharCode(r):Ie.utils.decode(Pr,[r&255,r>>8])[0]},qn=function(r){return Ie.utils.decode(Mt,[r])[0]},Vf()}var wt="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function Na(e){for(var t="",r=0,a=0,n=0,i=0,s=0,f=0,c=0,o=0;o<e.length;)r=e.charCodeAt(o++),i=r>>2,a=e.charCodeAt(o++),s=(r&3)<<4|a>>4,n=e.charCodeAt(o++),f=(a&15)<<2|n>>6,c=n&63,isNaN(a)?f=c=64:isNaN(n)&&(c=64),t+=wt.charAt(i)+wt.charAt(s)+wt.charAt(f)+wt.charAt(c);return t}function Dr(e){var t="",r=0,a=0,n=0,i=0,s=0,f=0,c=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var o=0;o<e.length;)i=wt.indexOf(e.charAt(o++)),s=wt.indexOf(e.charAt(o++)),r=i<<2|s>>4,t+=String.fromCharCode(r),f=wt.indexOf(e.charAt(o++)),a=(s&15)<<4|f>>2,f!==64&&(t+=String.fromCharCode(a)),c=wt.indexOf(e.charAt(o++)),n=(f&3)<<6|c,c!==64&&(t+=String.fromCharCode(n));return t}var Se=function(){return typeof Buffer<"u"&&typeof process<"u"&&typeof process.versions<"u"&&!!process.versions.node}(),dt=function(){if(typeof Buffer<"u"){var e=!Buffer.from;if(!e)try{Buffer.from("foo","utf8")}catch{e=!0}return e?function(t,r){return r?new Buffer(t,r):new Buffer(t)}:Buffer.from.bind(Buffer)}return function(){}}();function St(e){return Se?Buffer.alloc?Buffer.alloc(e):new Buffer(e):typeof Uint8Array<"u"?new Uint8Array(e):new Array(e)}function ss(e){return Se?Buffer.allocUnsafe?Buffer.allocUnsafe(e):new Buffer(e):typeof Uint8Array<"u"?new Uint8Array(e):new Array(e)}var Rr=function(t){return Se?dt(t,"binary"):t.split("").map(function(r){return r.charCodeAt(0)&255})};function za(e){if(typeof ArrayBuffer>"u")return Rr(e);for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),a=0;a!=e.length;++a)r[a]=e.charCodeAt(a)&255;return t}function Ct(e){if(Array.isArray(e))return e.map(function(a){return String.fromCharCode(a)}).join("");for(var t=[],r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}function $o(e){if(typeof Uint8Array>"u")throw new Error("Unsupported");return new Uint8Array(e)}function ui(e){if(typeof ArrayBuffer>"u")throw new Error("Unsupported");if(e instanceof ArrayBuffer)return ui(new Uint8Array(e));for(var t=new Array(e.length),r=0;r<e.length;++r)t[r]=e[r];return t}var cr=Se?function(e){return Buffer.concat(e.map(function(t){return Buffer.isBuffer(t)?t:dt(t)}))}:function(e){if(typeof Uint8Array<"u"){var t=0,r=0;for(t=0;t<e.length;++t)r+=e[t].length;var a=new Uint8Array(r),n=0;for(t=0,r=0;t<e.length;r+=n,++t)if(n=e[t].length,e[t]instanceof Uint8Array)a.set(e[t],r);else{if(typeof e[t]=="string")throw"wtf";a.set(new Uint8Array(e[t]),r)}return a}return[].concat.apply([],e.map(function(i){return Array.isArray(i)?i:[].slice.call(i)}))};function Ko(e){for(var t=[],r=0,a=e.length+250,n=St(e.length+255),i=0;i<e.length;++i){var s=e.charCodeAt(i);if(s<128)n[r++]=s;else if(s<2048)n[r++]=192|s>>6&31,n[r++]=128|s&63;else if(s>=55296&&s<57344){s=(s&1023)+64;var f=e.charCodeAt(++i)&1023;n[r++]=240|s>>8&7,n[r++]=128|s>>2&63,n[r++]=128|f>>6&15|(s&3)<<4,n[r++]=128|f&63}else n[r++]=224|s>>12&15,n[r++]=128|s>>6&63,n[r++]=128|s&63;r>a&&(t.push(n.slice(0,r)),r=0,n=St(65535),a=65530)}return t.push(n.slice(0,r)),cr(t)}var wr=/\u0000/g,Ta=/[\u0001-\u0006]/g;function aa(e){for(var t="",r=e.length-1;r>=0;)t+=e.charAt(r--);return t}function Yr(e,t){var r=""+e;return r.length>=t?r:$e("0",t-r.length)+r}function di(e,t){var r=""+e;return r.length>=t?r:$e(" ",t-r.length)+r}function dn(e,t){var r=""+e;return r.length>=t?r:r+$e(" ",t-r.length)}function Yo(e,t){var r=""+Math.round(e);return r.length>=t?r:$e("0",t-r.length)+r}function jo(e,t){var r=""+e;return r.length>=t?r:$e("0",t-r.length)+r}var fs=Math.pow(2,32);function Zt(e,t){if(e>fs||e<-fs)return Yo(e,t);var r=Math.round(e);return jo(r,t)}function vn(e,t){return t=t||0,e.length>=7+t&&(e.charCodeAt(t)|32)===103&&(e.charCodeAt(t+1)|32)===101&&(e.charCodeAt(t+2)|32)===110&&(e.charCodeAt(t+3)|32)===101&&(e.charCodeAt(t+4)|32)===114&&(e.charCodeAt(t+5)|32)===97&&(e.charCodeAt(t+6)|32)===108}var cs=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],Bn=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]];function Jo(e){return e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"上午/下午 "hh"時"mm"分"ss"秒 "',e}var ve={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"上午/下午 "hh"時"mm"分"ss"秒 "'},os={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},Zo={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function pn(e,t,r){for(var a=e<0?-1:1,n=e*a,i=0,s=1,f=0,c=1,o=0,l=0,h=Math.floor(n);o<t&&(h=Math.floor(n),f=h*s+i,l=h*o+c,!(n-h<5e-8));)n=1/(n-h),i=s,s=f,c=o,o=l;if(l>t&&(o>t?(l=c,f=i):(l=o,f=s)),!r)return[0,a*f,l];var d=Math.floor(a*f/l);return[d,a*f-d*l,l]}function kt(e,t,r){if(e>2958465||e<0)return null;var a=e|0,n=Math.floor(86400*(e-a)),i=0,s=[],f={D:a,T:n,u:86400*(e-a)-n,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(Math.abs(f.u)<1e-6&&(f.u=0),t&&t.date1904&&(a+=1462),f.u>.9999&&(f.u=0,++n==86400&&(f.T=n=0,++a,++f.D)),a===60)s=r?[1317,10,29]:[1900,2,29],i=3;else if(a===0)s=r?[1317,8,29]:[1900,1,0],i=6;else{a>60&&--a;var c=new Date(1900,0,1);c.setDate(c.getDate()+a-1),s=[c.getFullYear(),c.getMonth()+1,c.getDate()],i=c.getDay(),a<60&&(i=(i+6)%7),r&&(i=al(c,s))}return f.y=s[0],f.m=s[1],f.d=s[2],f.S=n%60,n=Math.floor(n/60),f.M=n%60,n=Math.floor(n/60),f.H=n,f.q=i,f}var pf=new Date(1899,11,31,0,0,0),qo=pf.getTime(),Qo=new Date(1900,2,1,0,0,0);function mf(e,t){var r=e.getTime();return t?r-=1461*24*60*60*1e3:e>=Qo&&(r+=24*60*60*1e3),(r-(qo+(e.getTimezoneOffset()-pf.getTimezoneOffset())*6e4))/(24*60*60*1e3)}function vi(e){return e.indexOf(".")==-1?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function el(e){return e.indexOf("E")==-1?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2")}function rl(e){var t=e<0?12:11,r=vi(e.toFixed(12));return r.length<=t||(r=e.toPrecision(10),r.length<=t)?r:e.toExponential(5)}function tl(e){var t=vi(e.toFixed(11));return t.length>(e<0?12:11)||t==="0"||t==="-0"?e.toPrecision(6):t}function Pa(e){var t=Math.floor(Math.log(Math.abs(e))*Math.LOG10E),r;return t>=-4&&t<=-1?r=e.toPrecision(10+t):Math.abs(t)<=9?r=rl(e):t===10?r=e.toFixed(10).substr(0,12):r=tl(e),vi(el(r.toUpperCase()))}function Bt(e,t){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(e|0)===e?e.toString(10):Pa(e);case"undefined":return"";case"object":if(e==null)return"";if(e instanceof Date)return Lr(14,mf(e,t&&t.date1904),t)}throw new Error("unsupported value in General format: "+e)}function al(e,t){t[0]-=581;var r=e.getDay();return e<60&&(r=(r+6)%7),r}function nl(e,t,r,a){var n="",i=0,s=0,f=r.y,c,o=0;switch(e){case 98:f=r.y+543;case 121:switch(t.length){case 1:case 2:c=f%100,o=2;break;default:c=f%1e4,o=4;break}break;case 109:switch(t.length){case 1:case 2:c=r.m,o=t.length;break;case 3:return Bn[r.m-1][1];case 5:return Bn[r.m-1][0];default:return Bn[r.m-1][2]}break;case 100:switch(t.length){case 1:case 2:c=r.d,o=t.length;break;case 3:return cs[r.q][0];default:return cs[r.q][1]}break;case 104:switch(t.length){case 1:case 2:c=1+(r.H+11)%12,o=t.length;break;default:throw"bad hour format: "+t}break;case 72:switch(t.length){case 1:case 2:c=r.H,o=t.length;break;default:throw"bad hour format: "+t}break;case 77:switch(t.length){case 1:case 2:c=r.M,o=t.length;break;default:throw"bad minute format: "+t}break;case 115:if(t!="s"&&t!="ss"&&t!=".0"&&t!=".00"&&t!=".000")throw"bad second format: "+t;return r.u===0&&(t=="s"||t=="ss")?Yr(r.S,t.length):(a>=2?s=a===3?1e3:100:s=a===1?10:1,i=Math.round(s*(r.S+r.u)),i>=60*s&&(i=0),t==="s"?i===0?"0":""+i/s:(n=Yr(i,2+a),t==="ss"?n.substr(0,2):"."+n.substr(2,t.length-1)));case 90:switch(t){case"[h]":case"[hh]":c=r.D*24+r.H;break;case"[m]":case"[mm]":c=(r.D*24+r.H)*60+r.M;break;case"[s]":case"[ss]":c=((r.D*24+r.H)*60+r.M)*60+Math.round(r.S+r.u);break;default:throw"bad abstime format: "+t}o=t.length===3?1:2;break;case 101:c=f,o=1;break}var l=o>0?Yr(c,o):"";return l}function Tt(e){var t=3;if(e.length<=t)return e;for(var r=e.length%t,a=e.substr(0,r);r!=e.length;r+=t)a+=(a.length>0?",":"")+e.substr(r,t);return a}var gf=/%/g;function il(e,t,r){var a=t.replace(gf,""),n=t.length-a.length;return ht(e,a,r*Math.pow(10,2*n))+$e("%",n)}function sl(e,t,r){for(var a=t.length-1;t.charCodeAt(a-1)===44;)--a;return ht(e,t.substr(0,a),r/Math.pow(10,3*(t.length-a)))}function _f(e,t){var r,a=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(t==0)return"0.0E+0";if(t<0)return"-"+_f(e,-t);var n=e.indexOf(".");n===-1&&(n=e.indexOf("E"));var i=Math.floor(Math.log(t)*Math.LOG10E)%n;if(i<0&&(i+=n),r=(t/Math.pow(10,i)).toPrecision(a+1+(n+i)%n),r.indexOf("e")===-1){var s=Math.floor(Math.log(t)*Math.LOG10E);for(r.indexOf(".")===-1?r=r.charAt(0)+"."+r.substr(1)+"E+"+(s-r.length+i):r+="E+"+(s-i);r.substr(0,2)==="0.";)r=r.charAt(0)+r.substr(2,n)+"."+r.substr(2+n),r=r.replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(f,c,o,l){return c+o+l.substr(0,(n+i)%n)+"."+l.substr(i)+"E"})}else r=t.toExponential(a);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}var wf=/# (\?+)( ?)\/( ?)(\d+)/;function fl(e,t,r){var a=parseInt(e[4],10),n=Math.round(t*a),i=Math.floor(n/a),s=n-i*a,f=a;return r+(i===0?"":""+i)+" "+(s===0?$e(" ",e[1].length+1+e[4].length):di(s,e[1].length)+e[2]+"/"+e[3]+Yr(f,e[4].length))}function cl(e,t,r){return r+(t===0?"":""+t)+$e(" ",e[1].length+2+e[4].length)}var kf=/^#*0*\.([0#]+)/,Tf=/\).*[0#]/,Ef=/\(###\) ###\\?-####/;function yr(e){for(var t="",r,a=0;a!=e.length;++a)switch(r=e.charCodeAt(a)){case 35:break;case 63:t+=" ";break;case 48:t+="0";break;default:t+=String.fromCharCode(r)}return t}function ls(e,t){var r=Math.pow(10,t);return""+Math.round(e*r)/r}function hs(e,t){var r=e-Math.floor(e),a=Math.pow(10,t);return t<(""+Math.round(r*a)).length?0:Math.round(r*a)}function ol(e,t){return t<(""+Math.round((e-Math.floor(e))*Math.pow(10,t))).length?1:0}function ll(e){return e<2147483647&&e>-2147483648?""+(e>=0?e|0:e-1|0):""+Math.floor(e)}function Ur(e,t,r){if(e.charCodeAt(0)===40&&!t.match(Tf)){var a=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?Ur("n",a,r):"("+Ur("n",a,-r)+")"}if(t.charCodeAt(t.length-1)===44)return sl(e,t,r);if(t.indexOf("%")!==-1)return il(e,t,r);if(t.indexOf("E")!==-1)return _f(t,r);if(t.charCodeAt(0)===36)return"$"+Ur(e,t.substr(t.charAt(1)==" "?2:1),r);var n,i,s,f,c=Math.abs(r),o=r<0?"-":"";if(t.match(/^00+$/))return o+Zt(c,t.length);if(t.match(/^[#?]+$/))return n=Zt(r,0),n==="0"&&(n=""),n.length>t.length?n:yr(t.substr(0,t.length-n.length))+n;if(i=t.match(wf))return fl(i,c,o);if(t.match(/^#+0+$/))return o+Zt(c,t.length-t.indexOf("0"));if(i=t.match(kf))return n=ls(r,i[1].length).replace(/^([^\.]+)$/,"$1."+yr(i[1])).replace(/\.$/,"."+yr(i[1])).replace(/\.(\d*)$/,function(p,u){return"."+u+$e("0",yr(i[1]).length-u.length)}),t.indexOf("0.")!==-1?n:n.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),i=t.match(/^(0*)\.(#*)$/))return o+ls(c,i[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,i[1].length?"0.":".");if(i=t.match(/^#{1,3},##0(\.?)$/))return o+Tt(Zt(c,0));if(i=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+Ur(e,t,-r):Tt(""+(Math.floor(r)+ol(r,i[1].length)))+"."+Yr(hs(r,i[1].length),i[1].length);if(i=t.match(/^#,#*,#0/))return Ur(e,t.replace(/^#,#*,/,""),r);if(i=t.match(/^([0#]+)(\\?-([0#]+))+$/))return n=aa(Ur(e,t.replace(/[\\-]/g,""),r)),s=0,aa(aa(t.replace(/\\/g,"")).replace(/[0#]/g,function(p){return s<n.length?n.charAt(s++):p==="0"?"0":""}));if(t.match(Ef))return n=Ur(e,"##########",r),"("+n.substr(0,3)+") "+n.substr(3,3)+"-"+n.substr(6);var l="";if(i=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(i[4].length,7),f=pn(c,Math.pow(10,s)-1,!1),n=""+o,l=ht("n",i[1],f[1]),l.charAt(l.length-1)==" "&&(l=l.substr(0,l.length-1)+"0"),n+=l+i[2]+"/"+i[3],l=dn(f[2],s),l.length<i[4].length&&(l=yr(i[4].substr(i[4].length-l.length))+l),n+=l,n;if(i=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(Math.max(i[1].length,i[4].length),7),f=pn(c,Math.pow(10,s)-1,!0),o+(f[0]||(f[1]?"":"0"))+" "+(f[1]?di(f[1],s)+i[2]+"/"+i[3]+dn(f[2],s):$e(" ",2*s+1+i[2].length+i[3].length));if(i=t.match(/^[#0?]+$/))return n=Zt(r,0),t.length<=n.length?n:yr(t.substr(0,t.length-n.length))+n;if(i=t.match(/^([#0?]+)\.([#0]+)$/)){n=""+r.toFixed(Math.min(i[2].length,10)).replace(/([^0])0+$/,"$1"),s=n.indexOf(".");var h=t.indexOf(".")-s,d=t.length-n.length-h;return yr(t.substr(0,h)+n+t.substr(t.length-d))}if(i=t.match(/^00,000\.([#0]*0)$/))return s=hs(r,i[1].length),r<0?"-"+Ur(e,t,-r):Tt(ll(r)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(p){return"00,"+(p.length<3?Yr(0,3-p.length):"")+p})+"."+Yr(s,i[1].length);switch(t){case"###,##0.00":return Ur(e,"#,##0.00",r);case"###,###":case"##,###":case"#,###":var v=Tt(Zt(c,0));return v!=="0"?o+v:"";case"###,###.00":return Ur(e,"###,##0.00",r).replace(/^0\./,".");case"#,###.00":return Ur(e,"#,##0.00",r).replace(/^0\./,".")}throw new Error("unsupported format |"+t+"|")}function hl(e,t,r){for(var a=t.length-1;t.charCodeAt(a-1)===44;)--a;return ht(e,t.substr(0,a),r/Math.pow(10,3*(t.length-a)))}function ul(e,t,r){var a=t.replace(gf,""),n=t.length-a.length;return ht(e,a,r*Math.pow(10,2*n))+$e("%",n)}function Sf(e,t){var r,a=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(t==0)return"0.0E+0";if(t<0)return"-"+Sf(e,-t);var n=e.indexOf(".");n===-1&&(n=e.indexOf("E"));var i=Math.floor(Math.log(t)*Math.LOG10E)%n;if(i<0&&(i+=n),r=(t/Math.pow(10,i)).toPrecision(a+1+(n+i)%n),!r.match(/[Ee]/)){var s=Math.floor(Math.log(t)*Math.LOG10E);r.indexOf(".")===-1?r=r.charAt(0)+"."+r.substr(1)+"E+"+(s-r.length+i):r+="E+"+(s-i),r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(f,c,o,l){return c+o+l.substr(0,(n+i)%n)+"."+l.substr(i)+"E"})}else r=t.toExponential(a);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}function et(e,t,r){if(e.charCodeAt(0)===40&&!t.match(Tf)){var a=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?et("n",a,r):"("+et("n",a,-r)+")"}if(t.charCodeAt(t.length-1)===44)return hl(e,t,r);if(t.indexOf("%")!==-1)return ul(e,t,r);if(t.indexOf("E")!==-1)return Sf(t,r);if(t.charCodeAt(0)===36)return"$"+et(e,t.substr(t.charAt(1)==" "?2:1),r);var n,i,s,f,c=Math.abs(r),o=r<0?"-":"";if(t.match(/^00+$/))return o+Yr(c,t.length);if(t.match(/^[#?]+$/))return n=""+r,r===0&&(n=""),n.length>t.length?n:yr(t.substr(0,t.length-n.length))+n;if(i=t.match(wf))return cl(i,c,o);if(t.match(/^#+0+$/))return o+Yr(c,t.length-t.indexOf("0"));if(i=t.match(kf))return n=(""+r).replace(/^([^\.]+)$/,"$1."+yr(i[1])).replace(/\.$/,"."+yr(i[1])),n=n.replace(/\.(\d*)$/,function(p,u){return"."+u+$e("0",yr(i[1]).length-u.length)}),t.indexOf("0.")!==-1?n:n.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),i=t.match(/^(0*)\.(#*)$/))return o+(""+c).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,i[1].length?"0.":".");if(i=t.match(/^#{1,3},##0(\.?)$/))return o+Tt(""+c);if(i=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+et(e,t,-r):Tt(""+r)+"."+$e("0",i[1].length);if(i=t.match(/^#,#*,#0/))return et(e,t.replace(/^#,#*,/,""),r);if(i=t.match(/^([0#]+)(\\?-([0#]+))+$/))return n=aa(et(e,t.replace(/[\\-]/g,""),r)),s=0,aa(aa(t.replace(/\\/g,"")).replace(/[0#]/g,function(p){return s<n.length?n.charAt(s++):p==="0"?"0":""}));if(t.match(Ef))return n=et(e,"##########",r),"("+n.substr(0,3)+") "+n.substr(3,3)+"-"+n.substr(6);var l="";if(i=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(i[4].length,7),f=pn(c,Math.pow(10,s)-1,!1),n=""+o,l=ht("n",i[1],f[1]),l.charAt(l.length-1)==" "&&(l=l.substr(0,l.length-1)+"0"),n+=l+i[2]+"/"+i[3],l=dn(f[2],s),l.length<i[4].length&&(l=yr(i[4].substr(i[4].length-l.length))+l),n+=l,n;if(i=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(Math.max(i[1].length,i[4].length),7),f=pn(c,Math.pow(10,s)-1,!0),o+(f[0]||(f[1]?"":"0"))+" "+(f[1]?di(f[1],s)+i[2]+"/"+i[3]+dn(f[2],s):$e(" ",2*s+1+i[2].length+i[3].length));if(i=t.match(/^[#0?]+$/))return n=""+r,t.length<=n.length?n:yr(t.substr(0,t.length-n.length))+n;if(i=t.match(/^([#0]+)\.([#0]+)$/)){n=""+r.toFixed(Math.min(i[2].length,10)).replace(/([^0])0+$/,"$1"),s=n.indexOf(".");var h=t.indexOf(".")-s,d=t.length-n.length-h;return yr(t.substr(0,h)+n+t.substr(t.length-d))}if(i=t.match(/^00,000\.([#0]*0)$/))return r<0?"-"+et(e,t,-r):Tt(""+r).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(p){return"00,"+(p.length<3?Yr(0,3-p.length):"")+p})+"."+Yr(0,i[1].length);switch(t){case"###,###":case"##,###":case"#,###":var v=Tt(""+c);return v!=="0"?o+v:"";default:if(t.match(/\.[0#?]*$/))return et(e,t.slice(0,t.lastIndexOf(".")),r)+yr(t.slice(t.lastIndexOf(".")))}throw new Error("unsupported format |"+t+"|")}function ht(e,t,r){return(r|0)===r?et(e,t,r):Ur(e,t,r)}function dl(e){for(var t=[],r=!1,a=0,n=0;a<e.length;++a)switch(e.charCodeAt(a)){case 34:r=!r;break;case 95:case 42:case 92:++a;break;case 59:t[t.length]=e.substr(n,a-n),n=a+1}if(t[t.length]=e.substr(n),r===!0)throw new Error("Format |"+e+"| unterminated string ");return t}var yf=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function Gt(e){for(var t=0,r="",a="";t<e.length;)switch(r=e.charAt(t)){case"G":vn(e,t)&&(t+=6),t++;break;case'"':for(;e.charCodeAt(++t)!==34&&t<e.length;);++t;break;case"\\":t+=2;break;case"_":t+=2;break;case"@":++t;break;case"B":case"b":if(e.charAt(t+1)==="1"||e.charAt(t+1)==="2")return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"上":if(e.substr(t,3).toUpperCase()==="A/P"||e.substr(t,5).toUpperCase()==="AM/PM"||e.substr(t,5).toUpperCase()==="上午/下午")return!0;++t;break;case"[":for(a=r;e.charAt(t++)!=="]"&&t<e.length;)a+=e.charAt(t);if(a.match(yf))return!0;break;case".":case"0":case"#":for(;t<e.length&&("0#?.,E+-%".indexOf(r=e.charAt(++t))>-1||r=="\\"&&e.charAt(t+1)=="-"&&"0#".indexOf(e.charAt(t+2))>-1););break;case"?":for(;e.charAt(++t)===r;);break;case"*":++t,(e.charAt(t)==" "||e.charAt(t)=="*")&&++t;break;case"(":case")":++t;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;t<e.length&&"0123456789".indexOf(e.charAt(++t))>-1;);break;case" ":++t;break;default:++t;break}return!1}function vl(e,t,r,a){for(var n=[],i="",s=0,f="",c="t",o,l,h,d="H";s<e.length;)switch(f=e.charAt(s)){case"G":if(!vn(e,s))throw new Error("unrecognized character "+f+" in "+e);n[n.length]={t:"G",v:"General"},s+=7;break;case'"':for(i="";(h=e.charCodeAt(++s))!==34&&s<e.length;)i+=String.fromCharCode(h);n[n.length]={t:"t",v:i},++s;break;case"\\":var v=e.charAt(++s),p=v==="("||v===")"?v:"t";n[n.length]={t:p,v},++s;break;case"_":n[n.length]={t:"t",v:" "},s+=2;break;case"@":n[n.length]={t:"T",v:t},++s;break;case"B":case"b":if(e.charAt(s+1)==="1"||e.charAt(s+1)==="2"){if(o==null&&(o=kt(t,r,e.charAt(s+1)==="2"),o==null))return"";n[n.length]={t:"X",v:e.substr(s,2)},c=f,s+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":f=f.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(t<0||o==null&&(o=kt(t,r),o==null))return"";for(i=f;++s<e.length&&e.charAt(s).toLowerCase()===f;)i+=f;f==="m"&&c.toLowerCase()==="h"&&(f="M"),f==="h"&&(f=d),n[n.length]={t:f,v:i},c=f;break;case"A":case"a":case"上":var u={t:f,v:f};if(o==null&&(o=kt(t,r)),e.substr(s,3).toUpperCase()==="A/P"?(o!=null&&(u.v=o.H>=12?"P":"A"),u.t="T",d="h",s+=3):e.substr(s,5).toUpperCase()==="AM/PM"?(o!=null&&(u.v=o.H>=12?"PM":"AM"),u.t="T",s+=5,d="h"):e.substr(s,5).toUpperCase()==="上午/下午"?(o!=null&&(u.v=o.H>=12?"下午":"上午"),u.t="T",s+=5,d="h"):(u.t="t",++s),o==null&&u.t==="T")return"";n[n.length]=u,c=f;break;case"[":for(i=f;e.charAt(s++)!=="]"&&s<e.length;)i+=e.charAt(s);if(i.slice(-1)!=="]")throw'unterminated "[" block: |'+i+"|";if(i.match(yf)){if(o==null&&(o=kt(t,r),o==null))return"";n[n.length]={t:"Z",v:i.toLowerCase()},c=i.charAt(1)}else i.indexOf("$")>-1&&(i=(i.match(/\$([^-\[\]]*)/)||[])[1]||"$",Gt(e)||(n[n.length]={t:"t",v:i}));break;case".":if(o!=null){for(i=f;++s<e.length&&(f=e.charAt(s))==="0";)i+=f;n[n.length]={t:"s",v:i};break}case"0":case"#":for(i=f;++s<e.length&&"0#?.,E+-%".indexOf(f=e.charAt(s))>-1;)i+=f;n[n.length]={t:"n",v:i};break;case"?":for(i=f;e.charAt(++s)===f;)i+=f;n[n.length]={t:f,v:i},c=f;break;case"*":++s,(e.charAt(s)==" "||e.charAt(s)=="*")&&++s;break;case"(":case")":n[n.length]={t:a===1?"t":f,v:f},++s;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(i=f;s<e.length&&"0123456789".indexOf(e.charAt(++s))>-1;)i+=e.charAt(s);n[n.length]={t:"D",v:i};break;case" ":n[n.length]={t:f,v:f},++s;break;case"$":n[n.length]={t:"t",v:"$"},++s;break;default:if(",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(f)===-1)throw new Error("unrecognized character "+f+" in "+e);n[n.length]={t:"t",v:f},++s;break}var m=0,k=0,A;for(s=n.length-1,c="t";s>=0;--s)switch(n[s].t){case"h":case"H":n[s].t=d,c="h",m<1&&(m=1);break;case"s":(A=n[s].v.match(/\.0+$/))&&(k=Math.max(k,A[0].length-1)),m<3&&(m=3);case"d":case"y":case"M":case"e":c=n[s].t;break;case"m":c==="s"&&(n[s].t="M",m<2&&(m=2));break;case"X":break;case"Z":m<1&&n[s].v.match(/[Hh]/)&&(m=1),m<2&&n[s].v.match(/[Mm]/)&&(m=2),m<3&&n[s].v.match(/[Ss]/)&&(m=3)}switch(m){case 0:break;case 1:o.u>=.5&&(o.u=0,++o.S),o.S>=60&&(o.S=0,++o.M),o.M>=60&&(o.M=0,++o.H);break;case 2:o.u>=.5&&(o.u=0,++o.S),o.S>=60&&(o.S=0,++o.M);break}var _="",I;for(s=0;s<n.length;++s)switch(n[s].t){case"t":case"T":case" ":case"D":break;case"X":n[s].v="",n[s].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":n[s].v=nl(n[s].t.charCodeAt(0),n[s].v,o,k),n[s].t="t";break;case"n":case"?":for(I=s+1;n[I]!=null&&((f=n[I].t)==="?"||f==="D"||(f===" "||f==="t")&&n[I+1]!=null&&(n[I+1].t==="?"||n[I+1].t==="t"&&n[I+1].v==="/")||n[s].t==="("&&(f===" "||f==="n"||f===")")||f==="t"&&(n[I].v==="/"||n[I].v===" "&&n[I+1]!=null&&n[I+1].t=="?"));)n[s].v+=n[I].v,n[I]={v:"",t:";"},++I;_+=n[s].v,s=I-1;break;case"G":n[s].t="t",n[s].v=Bt(t,r);break}var D="",P,x;if(_.length>0){_.charCodeAt(0)==40?(P=t<0&&_.charCodeAt(0)===45?-t:t,x=ht("n",_,P)):(P=t<0&&a>1?-t:t,x=ht("n",_,P),P<0&&n[0]&&n[0].t=="t"&&(x=x.substr(1),n[0].v="-"+n[0].v)),I=x.length-1;var M=n.length;for(s=0;s<n.length;++s)if(n[s]!=null&&n[s].t!="t"&&n[s].v.indexOf(".")>-1){M=s;break}var R=n.length;if(M===n.length&&x.indexOf("E")===-1){for(s=n.length-1;s>=0;--s)n[s]==null||"n?".indexOf(n[s].t)===-1||(I>=n[s].v.length-1?(I-=n[s].v.length,n[s].v=x.substr(I+1,n[s].v.length)):I<0?n[s].v="":(n[s].v=x.substr(0,I+1),I=-1),n[s].t="t",R=s);I>=0&&R<n.length&&(n[R].v=x.substr(0,I+1)+n[R].v)}else if(M!==n.length&&x.indexOf("E")===-1){for(I=x.indexOf(".")-1,s=M;s>=0;--s)if(!(n[s]==null||"n?".indexOf(n[s].t)===-1)){for(l=n[s].v.indexOf(".")>-1&&s===M?n[s].v.indexOf(".")-1:n[s].v.length-1,D=n[s].v.substr(l+1);l>=0;--l)I>=0&&(n[s].v.charAt(l)==="0"||n[s].v.charAt(l)==="#")&&(D=x.charAt(I--)+D);n[s].v=D,n[s].t="t",R=s}for(I>=0&&R<n.length&&(n[R].v=x.substr(0,I+1)+n[R].v),I=x.indexOf(".")+1,s=M;s<n.length;++s)if(!(n[s]==null||"n?(".indexOf(n[s].t)===-1&&s!==M)){for(l=n[s].v.indexOf(".")>-1&&s===M?n[s].v.indexOf(".")+1:0,D=n[s].v.substr(0,l);l<n[s].v.length;++l)I<x.length&&(D+=x.charAt(I++));n[s].v=D,n[s].t="t",R=s}}}for(s=0;s<n.length;++s)n[s]!=null&&"n?".indexOf(n[s].t)>-1&&(P=a>1&&t<0&&s>0&&n[s-1].v==="-"?-t:t,n[s].v=ht(n[s].t,n[s].v,P),n[s].t="t");var z="";for(s=0;s!==n.length;++s)n[s]!=null&&(z+=n[s].v);return z}var us=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function ds(e,t){if(t==null)return!1;var r=parseFloat(t[2]);switch(t[1]){case"=":if(e==r)return!0;break;case">":if(e>r)return!0;break;case"<":if(e<r)return!0;break;case"<>":if(e!=r)return!0;break;case">=":if(e>=r)return!0;break;case"<=":if(e<=r)return!0;break}return!1}function pl(e,t){var r=dl(e),a=r.length,n=r[a-1].indexOf("@");if(a<4&&n>-1&&--a,r.length>4)throw new Error("cannot find right format for |"+r.join("|")+"|");if(typeof t!="number")return[4,r.length===4||n>-1?r[r.length-1]:"@"];switch(r.length){case 1:r=n>-1?["General","General","General",r[0]]:[r[0],r[0],r[0],"@"];break;case 2:r=n>-1?[r[0],r[0],r[0],r[1]]:[r[0],r[1],r[0],"@"];break;case 3:r=n>-1?[r[0],r[1],r[0],r[2]]:[r[0],r[1],r[2],"@"];break}var i=t>0?r[0]:t<0?r[1]:r[2];if(r[0].indexOf("[")===-1&&r[1].indexOf("[")===-1)return[a,i];if(r[0].match(/\[[=<>]/)!=null||r[1].match(/\[[=<>]/)!=null){var s=r[0].match(us),f=r[1].match(us);return ds(t,s)?[a,r[0]]:ds(t,f)?[a,r[1]]:[a,r[s!=null&&f!=null?2:1]]}return[a,i]}function Lr(e,t,r){r==null&&(r={});var a="";switch(typeof e){case"string":e=="m/d/yy"&&r.dateNF?a=r.dateNF:a=e;break;case"number":e==14&&r.dateNF?a=r.dateNF:a=(r.table!=null?r.table:ve)[e],a==null&&(a=r.table&&r.table[os[e]]||ve[os[e]]),a==null&&(a=Zo[e]||"General");break}if(vn(a,0))return Bt(t,r);t instanceof Date&&(t=mf(t,r.date1904));var n=pl(a,t);if(vn(n[1]))return Bt(t,r);if(t===!0)t="TRUE";else if(t===!1)t="FALSE";else if(t===""||t==null)return"";return vl(n[1],t,r,n[0])}function at(e,t){if(typeof t!="number"){t=+t||-1;for(var r=0;r<392;++r){if(ve[r]==null){t<0&&(t=r);continue}if(ve[r]==e){t=r;break}}t<0&&(t=391)}return ve[t]=e,t}function $a(e){for(var t=0;t!=392;++t)e[t]!==void 0&&at(e[t],t)}function la(){ve=Jo()}var ml={format:Lr,load:at,_table:ve,load_table:$a,parse_date_code:kt,is_date:Gt,get_table:function(){return ml._table=ve}},gl={5:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',23:"General",24:"General",25:"General",26:"General",27:"m/d/yy",28:"m/d/yy",29:"m/d/yy",30:"m/d/yy",31:"m/d/yy",32:"h:mm:ss",33:"h:mm:ss",34:"h:mm:ss",35:"h:mm:ss",36:"m/d/yy",41:'_(* #,##0_);_(* (#,##0);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* (#,##0);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* (#,##0.00);_("$"* "-"??_);_(@_)',50:"m/d/yy",51:"m/d/yy",52:"m/d/yy",53:"m/d/yy",54:"m/d/yy",55:"m/d/yy",56:"m/d/yy",57:"m/d/yy",58:"m/d/yy",59:"0",60:"0.00",61:"#,##0",62:"#,##0.00",63:'"$"#,##0_);\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',67:"0%",68:"0.00%",69:"# ?/?",70:"# ??/??",71:"m/d/yy",72:"m/d/yy",73:"d-mmm-yy",74:"d-mmm",75:"mmm-yy",76:"h:mm",77:"h:mm:ss",78:"m/d/yy h:mm",79:"mm:ss",80:"[h]:mm:ss",81:"mmss.0"},xf=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g;function _l(e){var t=typeof e=="number"?ve[e]:e;return t=t.replace(xf,"(\\d+)"),new RegExp("^"+t+"$")}function wl(e,t,r){var a=-1,n=-1,i=-1,s=-1,f=-1,c=-1;(t.match(xf)||[]).forEach(function(h,d){var v=parseInt(r[d+1],10);switch(h.toLowerCase().charAt(0)){case"y":a=v;break;case"d":i=v;break;case"h":s=v;break;case"s":c=v;break;case"m":s>=0?f=v:n=v;break}}),c>=0&&f==-1&&n>=0&&(f=n,n=-1);var o=(""+(a>=0?a:new Date().getFullYear())).slice(-4)+"-"+("00"+(n>=1?n:1)).slice(-2)+"-"+("00"+(i>=1?i:1)).slice(-2);o.length==7&&(o="0"+o),o.length==8&&(o="20"+o);var l=("00"+(s>=0?s:0)).slice(-2)+":"+("00"+(f>=0?f:0)).slice(-2)+":"+("00"+(c>=0?c:0)).slice(-2);return s==-1&&f==-1&&c==-1?o:a==-1&&n==-1&&i==-1?l:o+"T"+l}var kl=function(){var e={};e.version="1.2.0";function t(){for(var x=0,M=new Array(256),R=0;R!=256;++R)x=R,x=x&1?-306674912^x>>>1:x>>>1,x=x&1?-306674912^x>>>1:x>>>1,x=x&1?-306674912^x>>>1:x>>>1,x=x&1?-306674912^x>>>1:x>>>1,x=x&1?-306674912^x>>>1:x>>>1,x=x&1?-306674912^x>>>1:x>>>1,x=x&1?-306674912^x>>>1:x>>>1,x=x&1?-306674912^x>>>1:x>>>1,M[R]=x;return typeof Int32Array<"u"?new Int32Array(M):M}var r=t();function a(x){var M=0,R=0,z=0,X=typeof Int32Array<"u"?new Int32Array(4096):new Array(4096);for(z=0;z!=256;++z)X[z]=x[z];for(z=0;z!=256;++z)for(R=x[z],M=256+z;M<4096;M+=256)R=X[M]=R>>>8^x[R&255];var b=[];for(z=1;z!=16;++z)b[z-1]=typeof Int32Array<"u"?X.subarray(z*256,z*256+256):X.slice(z*256,z*256+256);return b}var n=a(r),i=n[0],s=n[1],f=n[2],c=n[3],o=n[4],l=n[5],h=n[6],d=n[7],v=n[8],p=n[9],u=n[10],m=n[11],k=n[12],A=n[13],_=n[14];function I(x,M){for(var R=M^-1,z=0,X=x.length;z<X;)R=R>>>8^r[(R^x.charCodeAt(z++))&255];return~R}function D(x,M){for(var R=M^-1,z=x.length-15,X=0;X<z;)R=_[x[X++]^R&255]^A[x[X++]^R>>8&255]^k[x[X++]^R>>16&255]^m[x[X++]^R>>>24]^u[x[X++]]^p[x[X++]]^v[x[X++]]^d[x[X++]]^h[x[X++]]^l[x[X++]]^o[x[X++]]^c[x[X++]]^f[x[X++]]^s[x[X++]]^i[x[X++]]^r[x[X++]];for(z+=15;X<z;)R=R>>>8^r[(R^x[X++])&255];return~R}function P(x,M){for(var R=M^-1,z=0,X=x.length,b=0,J=0;z<X;)b=x.charCodeAt(z++),b<128?R=R>>>8^r[(R^b)&255]:b<2048?(R=R>>>8^r[(R^(192|b>>6&31))&255],R=R>>>8^r[(R^(128|b&63))&255]):b>=55296&&b<57344?(b=(b&1023)+64,J=x.charCodeAt(z++)&1023,R=R>>>8^r[(R^(240|b>>8&7))&255],R=R>>>8^r[(R^(128|b>>2&63))&255],R=R>>>8^r[(R^(128|J>>6&15|(b&3)<<4))&255],R=R>>>8^r[(R^(128|J&63))&255]):(R=R>>>8^r[(R^(224|b>>12&15))&255],R=R>>>8^r[(R^(128|b>>6&63))&255],R=R>>>8^r[(R^(128|b&63))&255]);return~R}return e.table=r,e.bstr=I,e.buf=D,e.str=P,e}(),de=function(){var t={};t.version="1.2.1";function r(g,E){for(var w=g.split("/"),T=E.split("/"),S=0,y=0,U=Math.min(w.length,T.length);S<U;++S){if(y=w[S].length-T[S].length)return y;if(w[S]!=T[S])return w[S]<T[S]?-1:1}return w.length-T.length}function a(g){if(g.charAt(g.length-1)=="/")return g.slice(0,-1).indexOf("/")===-1?g:a(g.slice(0,-1));var E=g.lastIndexOf("/");return E===-1?g:g.slice(0,E+1)}function n(g){if(g.charAt(g.length-1)=="/")return n(g.slice(0,-1));var E=g.lastIndexOf("/");return E===-1?g:g.slice(E+1)}function i(g,E){typeof E=="string"&&(E=new Date(E));var w=E.getHours();w=w<<6|E.getMinutes(),w=w<<5|E.getSeconds()>>>1,g.write_shift(2,w);var T=E.getFullYear()-1980;T=T<<4|E.getMonth()+1,T=T<<5|E.getDate(),g.write_shift(2,T)}function s(g){var E=g.read_shift(2)&65535,w=g.read_shift(2)&65535,T=new Date,S=w&31;w>>>=5;var y=w&15;w>>>=4,T.setMilliseconds(0),T.setFullYear(w+1980),T.setMonth(y-1),T.setDate(S);var U=E&31;E>>>=5;var K=E&63;return E>>>=6,T.setHours(E),T.setMinutes(K),T.setSeconds(U<<1),T}function f(g){hr(g,0);for(var E={},w=0;g.l<=g.length-4;){var T=g.read_shift(2),S=g.read_shift(2),y=g.l+S,U={};switch(T){case 21589:w=g.read_shift(1),w&1&&(U.mtime=g.read_shift(4)),S>5&&(w&2&&(U.atime=g.read_shift(4)),w&4&&(U.ctime=g.read_shift(4))),U.mtime&&(U.mt=new Date(U.mtime*1e3));break}g.l=y,E[T]=U}return E}var c;function o(){return c||(c={})}function l(g,E){if(g[0]==80&&g[1]==75)return is(g,E);if((g[0]|32)==109&&(g[1]|32)==105)return Mo(g,E);if(g.length<512)throw new Error("CFB file size "+g.length+" < 512");var w=3,T=512,S=0,y=0,U=0,K=0,B=0,W=[],H=g.slice(0,512);hr(H,0);var q=h(H);switch(w=q[0],w){case 3:T=512;break;case 4:T=4096;break;case 0:if(q[1]==0)return is(g,E);default:throw new Error("Major Version: Expected 3 or 4 saw "+w)}T!==512&&(H=g.slice(0,T),hr(H,28));var se=g.slice(0,T);d(H,w);var he=H.read_shift(4,"i");if(w===3&&he!==0)throw new Error("# Directory Sectors: Expected 0 saw "+he);H.l+=4,U=H.read_shift(4,"i"),H.l+=4,H.chk("00100000","Mini Stream Cutoff Size: "),K=H.read_shift(4,"i"),S=H.read_shift(4,"i"),B=H.read_shift(4,"i"),y=H.read_shift(4,"i");for(var ee=-1,oe=0;oe<109&&(ee=H.read_shift(4,"i"),!(ee<0));++oe)W[oe]=ee;var we=v(g,T);m(B,y,we,T,W);var Xe=A(we,U,W,T);Xe[U].name="!Directory",S>0&&K!==J&&(Xe[K].name="!MiniFAT"),Xe[W[0]].name="!FAT",Xe.fat_addrs=W,Xe.ssz=T;var Ge={},dr=[],ma=[],ga=[];_(U,Xe,we,dr,S,Ge,ma,K),p(ma,ga,dr),dr.shift();var _a={FileIndex:ma,FullPaths:ga};return E&&E.raw&&(_a.raw={header:se,sectors:we}),_a}function h(g){if(g[g.l]==80&&g[g.l+1]==75)return[0,0];g.chk(le,"Header Signature: "),g.l+=16;var E=g.read_shift(2,"u");return[g.read_shift(2,"u"),E]}function d(g,E){var w=9;switch(g.l+=2,w=g.read_shift(2)){case 9:if(E!=3)throw new Error("Sector Shift: Expected 9 saw "+w);break;case 12:if(E!=4)throw new Error("Sector Shift: Expected 12 saw "+w);break;default:throw new Error("Sector Shift: Expected 9 or 12 saw "+w)}g.chk("0600","Mini Sector Shift: "),g.chk("000000000000","Reserved: ")}function v(g,E){for(var w=Math.ceil(g.length/E)-1,T=[],S=1;S<w;++S)T[S-1]=g.slice(S*E,(S+1)*E);return T[w-1]=g.slice(w*E),T}function p(g,E,w){for(var T=0,S=0,y=0,U=0,K=0,B=w.length,W=[],H=[];T<B;++T)W[T]=H[T]=T,E[T]=w[T];for(;K<H.length;++K)T=H[K],S=g[T].L,y=g[T].R,U=g[T].C,W[T]===T&&(S!==-1&&W[S]!==S&&(W[T]=W[S]),y!==-1&&W[y]!==y&&(W[T]=W[y])),U!==-1&&(W[U]=T),S!==-1&&T!=W[T]&&(W[S]=W[T],H.lastIndexOf(S)<K&&H.push(S)),y!==-1&&T!=W[T]&&(W[y]=W[T],H.lastIndexOf(y)<K&&H.push(y));for(T=1;T<B;++T)W[T]===T&&(y!==-1&&W[y]!==y?W[T]=W[y]:S!==-1&&W[S]!==S&&(W[T]=W[S]));for(T=1;T<B;++T)if(g[T].type!==0){if(K=T,K!=W[K])do K=W[K],E[T]=E[K]+"/"+E[T];while(K!==0&&W[K]!==-1&&K!=W[K]);W[T]=-1}for(E[0]+="/",T=1;T<B;++T)g[T].type!==2&&(E[T]+="/")}function u(g,E,w){for(var T=g.start,S=g.size,y=[],U=T;w&&S>0&&U>=0;)y.push(E.slice(U*b,U*b+b)),S-=b,U=Nt(w,U*4);return y.length===0?G(0):cr(y).slice(0,g.size)}function m(g,E,w,T,S){var y=J;if(g===J){if(E!==0)throw new Error("DIFAT chain shorter than expected")}else if(g!==-1){var U=w[g],K=(T>>>2)-1;if(!U)return;for(var B=0;B<K&&(y=Nt(U,B*4))!==J;++B)S.push(y);m(Nt(U,T-4),E-1,w,T,S)}}function k(g,E,w,T,S){var y=[],U=[];S||(S=[]);var K=T-1,B=0,W=0;for(B=E;B>=0;){S[B]=!0,y[y.length]=B,U.push(g[B]);var H=w[Math.floor(B*4/T)];if(W=B*4&K,T<4+W)throw new Error("FAT boundary crossed: "+B+" 4 "+T);if(!g[H])break;B=Nt(g[H],W)}return{nodes:y,data:xs([U])}}function A(g,E,w,T){var S=g.length,y=[],U=[],K=[],B=[],W=T-1,H=0,q=0,se=0,he=0;for(H=0;H<S;++H)if(K=[],se=H+E,se>=S&&(se-=S),!U[se]){B=[];var ee=[];for(q=se;q>=0;){ee[q]=!0,U[q]=!0,K[K.length]=q,B.push(g[q]);var oe=w[Math.floor(q*4/T)];if(he=q*4&W,T<4+he)throw new Error("FAT boundary crossed: "+q+" 4 "+T);if(!g[oe]||(q=Nt(g[oe],he),ee[q]))break}y[se]={nodes:K,data:xs([B])}}return y}function _(g,E,w,T,S,y,U,K){for(var B=0,W=T.length?2:0,H=E[g].data,q=0,se=0,he;q<H.length;q+=128){var ee=H.slice(q,q+128);hr(ee,64),se=ee.read_shift(2),he=Fn(ee,0,se-W),T.push(he);var oe={name:he,type:ee.read_shift(1),color:ee.read_shift(1),L:ee.read_shift(4,"i"),R:ee.read_shift(4,"i"),C:ee.read_shift(4,"i"),clsid:ee.read_shift(16),state:ee.read_shift(4,"i"),start:0,size:0},we=ee.read_shift(2)+ee.read_shift(2)+ee.read_shift(2)+ee.read_shift(2);we!==0&&(oe.ct=I(ee,ee.l-8));var Xe=ee.read_shift(2)+ee.read_shift(2)+ee.read_shift(2)+ee.read_shift(2);Xe!==0&&(oe.mt=I(ee,ee.l-8)),oe.start=ee.read_shift(4,"i"),oe.size=ee.read_shift(4,"i"),oe.size<0&&oe.start<0&&(oe.size=oe.type=0,oe.start=J,oe.name=""),oe.type===5?(B=oe.start,S>0&&B!==J&&(E[B].name="!StreamData")):oe.size>=4096?(oe.storage="fat",E[oe.start]===void 0&&(E[oe.start]=k(w,oe.start,E.fat_addrs,E.ssz)),E[oe.start].name=oe.name,oe.content=E[oe.start].data.slice(0,oe.size)):(oe.storage="minifat",oe.size<0?oe.size=0:B!==J&&oe.start!==J&&E[B]&&(oe.content=u(oe,E[B].data,(E[K]||{}).data))),oe.content&&hr(oe.content,0),y[he]=oe,U.push(oe)}}function I(g,E){return new Date((fr(g,E+4)/1e7*Math.pow(2,32)+fr(g,E)/1e7-11644473600)*1e3)}function D(g,E){return o(),l(c.readFileSync(g),E)}function P(g,E){var w=E&&E.type;switch(w||Se&&Buffer.isBuffer(g)&&(w="buffer"),w||"base64"){case"file":return D(g,E);case"base64":return l(Rr(Dr(g)),E);case"binary":return l(Rr(g),E)}return l(g,E)}function x(g,E){var w=E||{},T=w.root||"Root Entry";if(g.FullPaths||(g.FullPaths=[]),g.FileIndex||(g.FileIndex=[]),g.FullPaths.length!==g.FileIndex.length)throw new Error("inconsistent CFB structure");g.FullPaths.length===0&&(g.FullPaths[0]=T+"/",g.FileIndex[0]={name:T,type:5}),w.CLSID&&(g.FileIndex[0].clsid=w.CLSID),M(g)}function M(g){var E="Sh33tJ5";if(!de.find(g,"/"+E)){var w=G(4);w[0]=55,w[1]=w[3]=50,w[2]=54,g.FileIndex.push({name:E,type:2,content:w,size:4,L:69,R:69,C:69}),g.FullPaths.push(g.FullPaths[0]+E),R(g)}}function R(g,E){x(g);for(var w=!1,T=!1,S=g.FullPaths.length-1;S>=0;--S){var y=g.FileIndex[S];switch(y.type){case 0:T?w=!0:(g.FileIndex.pop(),g.FullPaths.pop());break;case 1:case 2:case 5:T=!0,isNaN(y.R*y.L*y.C)&&(w=!0),y.R>-1&&y.L>-1&&y.R==y.L&&(w=!0);break;default:w=!0;break}}if(!(!w&&!E)){var U=new Date(1987,1,19),K=0,B=Object.create?Object.create(null):{},W=[];for(S=0;S<g.FullPaths.length;++S)B[g.FullPaths[S]]=!0,g.FileIndex[S].type!==0&&W.push([g.FullPaths[S],g.FileIndex[S]]);for(S=0;S<W.length;++S){var H=a(W[S][0]);T=B[H],T||(W.push([H,{name:n(H).replace("/",""),type:1,clsid:ue,ct:U,mt:U,content:null}]),B[H]=!0)}for(W.sort(function(he,ee){return r(he[0],ee[0])}),g.FullPaths=[],g.FileIndex=[],S=0;S<W.length;++S)g.FullPaths[S]=W[S][0],g.FileIndex[S]=W[S][1];for(S=0;S<W.length;++S){var q=g.FileIndex[S],se=g.FullPaths[S];if(q.name=n(se).replace("/",""),q.L=q.R=q.C=-(q.color=1),q.size=q.content?q.content.length:0,q.start=0,q.clsid=q.clsid||ue,S===0)q.C=W.length>1?1:-1,q.size=0,q.type=5;else if(se.slice(-1)=="/"){for(K=S+1;K<W.length&&a(g.FullPaths[K])!=se;++K);for(q.C=K>=W.length?-1:K,K=S+1;K<W.length&&a(g.FullPaths[K])!=a(se);++K);q.R=K>=W.length?-1:K,q.type=1}else a(g.FullPaths[S+1]||"")==a(se)&&(q.R=S+1),q.type=2}}}function z(g,E){var w=E||{};if(w.fileType=="mad")return Bo(g,w);switch(R(g),w.fileType){case"zip":return Ro(g,w)}var T=function(he){for(var ee=0,oe=0,we=0;we<he.FileIndex.length;++we){var Xe=he.FileIndex[we];if(Xe.content){var Ge=Xe.content.length;Ge>0&&(Ge<4096?ee+=Ge+63>>6:oe+=Ge+511>>9)}}for(var dr=he.FullPaths.length+3>>2,ma=ee+7>>3,ga=ee+127>>7,_a=ma+oe+dr+ga,Rt=_a+127>>7,Mn=Rt<=109?0:Math.ceil((Rt-109)/127);_a+Rt+Mn+127>>7>Rt;)Mn=++Rt<=109?0:Math.ceil((Rt-109)/127);var ot=[1,Mn,Rt,ga,dr,oe,ee,0];return he.FileIndex[0].size=ee<<6,ot[7]=(he.FileIndex[0].start=ot[0]+ot[1]+ot[2]+ot[3]+ot[4]+ot[5])+(ot[6]+7>>3),ot}(g),S=G(T[7]<<9),y=0,U=0;{for(y=0;y<8;++y)S.write_shift(1,ie[y]);for(y=0;y<8;++y)S.write_shift(2,0);for(S.write_shift(2,62),S.write_shift(2,3),S.write_shift(2,65534),S.write_shift(2,9),S.write_shift(2,6),y=0;y<3;++y)S.write_shift(2,0);for(S.write_shift(4,0),S.write_shift(4,T[2]),S.write_shift(4,T[0]+T[1]+T[2]+T[3]-1),S.write_shift(4,0),S.write_shift(4,4096),S.write_shift(4,T[3]?T[0]+T[1]+T[2]-1:J),S.write_shift(4,T[3]),S.write_shift(-4,T[1]?T[0]-1:J),S.write_shift(4,T[1]),y=0;y<109;++y)S.write_shift(-4,y<T[2]?T[1]+y:-1)}if(T[1])for(U=0;U<T[1];++U){for(;y<236+U*127;++y)S.write_shift(-4,y<T[2]?T[1]+y:-1);S.write_shift(-4,U===T[1]-1?J:U+1)}var K=function(he){for(U+=he;y<U-1;++y)S.write_shift(-4,y+1);he&&(++y,S.write_shift(-4,J))};for(U=y=0,U+=T[1];y<U;++y)S.write_shift(-4,ce.DIFSECT);for(U+=T[2];y<U;++y)S.write_shift(-4,ce.FATSECT);K(T[3]),K(T[4]);for(var B=0,W=0,H=g.FileIndex[0];B<g.FileIndex.length;++B)H=g.FileIndex[B],H.content&&(W=H.content.length,!(W<4096)&&(H.start=U,K(W+511>>9)));for(K(T[6]+7>>3);S.l&511;)S.write_shift(-4,ce.ENDOFCHAIN);for(U=y=0,B=0;B<g.FileIndex.length;++B)H=g.FileIndex[B],H.content&&(W=H.content.length,!(!W||W>=4096)&&(H.start=U,K(W+63>>6)));for(;S.l&511;)S.write_shift(-4,ce.ENDOFCHAIN);for(y=0;y<T[4]<<2;++y){var q=g.FullPaths[y];if(!q||q.length===0){for(B=0;B<17;++B)S.write_shift(4,0);for(B=0;B<3;++B)S.write_shift(4,-1);for(B=0;B<12;++B)S.write_shift(4,0);continue}H=g.FileIndex[y],y===0&&(H.start=H.size?H.start-1:J);var se=y===0&&w.root||H.name;if(W=2*(se.length+1),S.write_shift(64,se,"utf16le"),S.write_shift(2,W),S.write_shift(1,H.type),S.write_shift(1,H.color),S.write_shift(-4,H.L),S.write_shift(-4,H.R),S.write_shift(-4,H.C),H.clsid)S.write_shift(16,H.clsid,"hex");else for(B=0;B<4;++B)S.write_shift(4,0);S.write_shift(4,H.state||0),S.write_shift(4,0),S.write_shift(4,0),S.write_shift(4,0),S.write_shift(4,0),S.write_shift(4,H.start),S.write_shift(4,H.size),S.write_shift(4,0)}for(y=1;y<g.FileIndex.length;++y)if(H=g.FileIndex[y],H.size>=4096)if(S.l=H.start+1<<9,Se&&Buffer.isBuffer(H.content))H.content.copy(S,S.l,0,H.size),S.l+=H.size+511&-512;else{for(B=0;B<H.size;++B)S.write_shift(1,H.content[B]);for(;B&511;++B)S.write_shift(1,0)}for(y=1;y<g.FileIndex.length;++y)if(H=g.FileIndex[y],H.size>0&&H.size<4096)if(Se&&Buffer.isBuffer(H.content))H.content.copy(S,S.l,0,H.size),S.l+=H.size+63&-64;else{for(B=0;B<H.size;++B)S.write_shift(1,H.content[B]);for(;B&63;++B)S.write_shift(1,0)}if(Se)S.l=S.length;else for(;S.l<S.length;)S.write_shift(1,0);return S}function X(g,E){var w=g.FullPaths.map(function(B){return B.toUpperCase()}),T=w.map(function(B){var W=B.split("/");return W[W.length-(B.slice(-1)=="/"?2:1)]}),S=!1;E.charCodeAt(0)===47?(S=!0,E=w[0].slice(0,-1)+E):S=E.indexOf("/")!==-1;var y=E.toUpperCase(),U=S===!0?w.indexOf(y):T.indexOf(y);if(U!==-1)return g.FileIndex[U];var K=!y.match(Ta);for(y=y.replace(wr,""),K&&(y=y.replace(Ta,"!")),U=0;U<w.length;++U)if((K?w[U].replace(Ta,"!"):w[U]).replace(wr,"")==y||(K?T[U].replace(Ta,"!"):T[U]).replace(wr,"")==y)return g.FileIndex[U];return null}var b=64,J=-2,le="d0cf11e0a1b11ae1",ie=[208,207,17,224,161,177,26,225],ue="00000000000000000000000000000000",ce={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:J,FREESECT:-1,HEADER_SIGNATURE:le,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:ue,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function Le(g,E,w){o();var T=z(g,w);c.writeFileSync(E,T)}function V(g){for(var E=new Array(g.length),w=0;w<g.length;++w)E[w]=String.fromCharCode(g[w]);return E.join("")}function pe(g,E){var w=z(g,E);switch(E&&E.type||"buffer"){case"file":return o(),c.writeFileSync(E.filename,w),w;case"binary":return typeof w=="string"?w:V(w);case"base64":return Na(typeof w=="string"?w:V(w));case"buffer":if(Se)return Buffer.isBuffer(w)?w:dt(w);case"array":return typeof w=="string"?Rr(w):w}return w}var _e;function O(g){try{var E=g.InflateRaw,w=new E;if(w._processChunk(new Uint8Array([3,0]),w._finishFlushFlag),w.bytesRead)_e=g;else throw new Error("zlib does not expose bytesRead")}catch(T){console.error("cannot use native zlib: "+(T.message||T))}}function L(g,E){if(!_e)return as(g,E);var w=_e.InflateRaw,T=new w,S=T._processChunk(g.slice(g.l),T._finishFlushFlag);return g.l+=T.bytesRead,S}function F(g){return _e?_e.deflateRawSync(g):ye(g)}var N=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],Y=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],re=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577];function te(g){var E=(g<<1|g<<11)&139536|(g<<5|g<<15)&558144;return(E>>16|E>>8|E)&255}for(var Q=typeof Uint8Array<"u",Z=Q?new Uint8Array(256):[],Ee=0;Ee<256;++Ee)Z[Ee]=te(Ee);function C(g,E){var w=Z[g&255];return E<=8?w>>>8-E:(w=w<<8|Z[g>>8&255],E<=16?w>>>16-E:(w=w<<8|Z[g>>16&255],w>>>24-E))}function Ue(g,E){var w=E&7,T=E>>>3;return(g[T]|(w<=6?0:g[T+1]<<8))>>>w&3}function Oe(g,E){var w=E&7,T=E>>>3;return(g[T]|(w<=5?0:g[T+1]<<8))>>>w&7}function Me(g,E){var w=E&7,T=E>>>3;return(g[T]|(w<=4?0:g[T+1]<<8))>>>w&15}function Ae(g,E){var w=E&7,T=E>>>3;return(g[T]|(w<=3?0:g[T+1]<<8))>>>w&31}function fe(g,E){var w=E&7,T=E>>>3;return(g[T]|(w<=1?0:g[T+1]<<8))>>>w&127}function Je(g,E,w){var T=E&7,S=E>>>3,y=(1<<w)-1,U=g[S]>>>T;return w<8-T||(U|=g[S+1]<<8-T,w<16-T)||(U|=g[S+2]<<16-T,w<24-T)||(U|=g[S+3]<<24-T),U&y}function br(g,E,w){var T=E&7,S=E>>>3;return T<=5?g[S]|=(w&7)<<T:(g[S]|=w<<T&255,g[S+1]=(w&7)>>8-T),E+3}function Zr(g,E,w){var T=E&7,S=E>>>3;return w=(w&1)<<T,g[S]|=w,E+1}function ft(g,E,w){var T=E&7,S=E>>>3;return w<<=T,g[S]|=w&255,w>>>=8,g[S+1]=w,E+8}function va(g,E,w){var T=E&7,S=E>>>3;return w<<=T,g[S]|=w&255,w>>>=8,g[S+1]=w&255,g[S+2]=w>>>8,E+16}function mt(g,E){var w=g.length,T=2*w>E?2*w:E+5,S=0;if(w>=E)return g;if(Se){var y=ss(T);if(g.copy)g.copy(y);else for(;S<g.length;++S)y[S]=g[S];return y}else if(Q){var U=new Uint8Array(T);if(U.set)U.set(g);else for(;S<w;++S)U[S]=g[S];return U}return g.length=T,g}function Ir(g){for(var E=new Array(g),w=0;w<g;++w)E[w]=0;return E}function ct(g,E,w){var T=1,S=0,y=0,U=0,K=0,B=g.length,W=Q?new Uint16Array(32):Ir(32);for(y=0;y<32;++y)W[y]=0;for(y=B;y<w;++y)g[y]=0;B=g.length;var H=Q?new Uint16Array(B):Ir(B);for(y=0;y<B;++y)W[S=g[y]]++,T<S&&(T=S),H[y]=0;for(W[0]=0,y=1;y<=T;++y)W[y+16]=K=K+W[y-1]<<1;for(y=0;y<B;++y)K=g[y],K!=0&&(H[y]=W[K+16]++);var q=0;for(y=0;y<B;++y)if(q=g[y],q!=0)for(K=C(H[y],T)>>T-q,U=(1<<T+4-q)-1;U>=0;--U)E[K|U<<q]=q&15|y<<4;return T}var gt=Q?new Uint16Array(512):Ir(512),pa=Q?new Uint16Array(32):Ir(32);if(!Q){for(var Sr=0;Sr<512;++Sr)gt[Sr]=0;for(Sr=0;Sr<32;++Sr)pa[Sr]=0}(function(){for(var g=[],E=0;E<32;E++)g.push(5);ct(g,pa,32);var w=[];for(E=0;E<=143;E++)w.push(8);for(;E<=255;E++)w.push(9);for(;E<=279;E++)w.push(7);for(;E<=287;E++)w.push(8);ct(w,gt,288)})();var qr=function(){for(var E=Q?new Uint8Array(32768):[],w=0,T=0;w<re.length-1;++w)for(;T<re[w+1];++T)E[T]=w;for(;T<32768;++T)E[T]=29;var S=Q?new Uint8Array(259):[];for(w=0,T=0;w<Y.length-1;++w)for(;T<Y[w+1];++T)S[T]=w;function y(K,B){for(var W=0;W<K.length;){var H=Math.min(65535,K.length-W),q=W+H==K.length;for(B.write_shift(1,+q),B.write_shift(2,H),B.write_shift(2,~H&65535);H-- >0;)B[B.l++]=K[W++]}return B.l}function U(K,B){for(var W=0,H=0,q=Q?new Uint16Array(32768):[];H<K.length;){var se=Math.min(65535,K.length-H);if(se<10){for(W=br(B,W,+(H+se==K.length)),W&7&&(W+=8-(W&7)),B.l=W/8|0,B.write_shift(2,se),B.write_shift(2,~se&65535);se-- >0;)B[B.l++]=K[H++];W=B.l*8;continue}W=br(B,W,+(H+se==K.length)+2);for(var he=0;se-- >0;){var ee=K[H];he=(he<<5^ee)&32767;var oe=-1,we=0;if((oe=q[he])&&(oe|=H&-32768,oe>H&&(oe-=32768),oe<H))for(;K[oe+we]==K[H+we]&&we<250;)++we;if(we>2){ee=S[we],ee<=22?W=ft(B,W,Z[ee+1]>>1)-1:(ft(B,W,3),W+=5,ft(B,W,Z[ee-23]>>5),W+=3);var Xe=ee<8?0:ee-4>>2;Xe>0&&(va(B,W,we-Y[ee]),W+=Xe),ee=E[H-oe],W=ft(B,W,Z[ee]>>3),W-=3;var Ge=ee<4?0:ee-2>>1;Ge>0&&(va(B,W,H-oe-re[ee]),W+=Ge);for(var dr=0;dr<we;++dr)q[he]=H&32767,he=(he<<5^K[H])&32767,++H;se-=we-1}else ee<=143?ee=ee+48:W=Zr(B,W,1),W=ft(B,W,Z[ee]),q[he]=H&32767,++H}W=ft(B,W,0)-1}return B.l=(W+7)/8|0,B.l}return function(B,W){return B.length<8?y(B,W):U(B,W)}}();function ye(g){var E=G(50+Math.floor(g.length*1.1)),w=qr(g,E);return E.slice(0,w)}var Ze=Q?new Uint16Array(32768):Ir(32768),Mr=Q?new Uint16Array(32768):Ir(32768),ar=Q?new Uint16Array(128):Ir(128),Ft=1,ts=1;function Oo(g,E){var w=Ae(g,E)+257;E+=5;var T=Ae(g,E)+1;E+=5;var S=Me(g,E)+4;E+=4;for(var y=0,U=Q?new Uint8Array(19):Ir(19),K=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],B=1,W=Q?new Uint8Array(8):Ir(8),H=Q?new Uint8Array(8):Ir(8),q=U.length,se=0;se<S;++se)U[N[se]]=y=Oe(g,E),B<y&&(B=y),W[y]++,E+=3;var he=0;for(W[0]=0,se=1;se<=B;++se)H[se]=he=he+W[se-1]<<1;for(se=0;se<q;++se)(he=U[se])!=0&&(K[se]=H[he]++);var ee=0;for(se=0;se<q;++se)if(ee=U[se],ee!=0){he=Z[K[se]]>>8-ee;for(var oe=(1<<7-ee)-1;oe>=0;--oe)ar[he|oe<<ee]=ee&7|se<<3}var we=[];for(B=1;we.length<w+T;)switch(he=ar[fe(g,E)],E+=he&7,he>>>=3){case 16:for(y=3+Ue(g,E),E+=2,he=we[we.length-1];y-- >0;)we.push(he);break;case 17:for(y=3+Oe(g,E),E+=3;y-- >0;)we.push(0);break;case 18:for(y=11+fe(g,E),E+=7;y-- >0;)we.push(0);break;default:we.push(he),B<he&&(B=he);break}var Xe=we.slice(0,w),Ge=we.slice(w);for(se=w;se<286;++se)Xe[se]=0;for(se=T;se<30;++se)Ge[se]=0;return Ft=ct(Xe,Ze,286),ts=ct(Ge,Mr,30),E}function Io(g,E){if(g[0]==3&&!(g[1]&3))return[St(E),2];for(var w=0,T=0,S=ss(E||1<<18),y=0,U=S.length>>>0,K=0,B=0;(T&1)==0;){if(T=Oe(g,w),w+=3,T>>>1)T>>1==1?(K=9,B=5):(w=Oo(g,w),K=Ft,B=ts);else{w&7&&(w+=8-(w&7));var W=g[w>>>3]|g[(w>>>3)+1]<<8;if(w+=32,W>0)for(!E&&U<y+W&&(S=mt(S,y+W),U=S.length);W-- >0;)S[y++]=g[w>>>3],w+=8;continue}for(;;){!E&&U<y+32767&&(S=mt(S,y+32767),U=S.length);var H=Je(g,w,K),q=T>>>1==1?gt[H]:Ze[H];if(w+=q&15,q>>>=4,(q>>>8&255)===0)S[y++]=q;else{if(q==256)break;q-=257;var se=q<8?0:q-4>>2;se>5&&(se=0);var he=y+Y[q];se>0&&(he+=Je(g,w,se),w+=se),H=Je(g,w,B),q=T>>>1==1?pa[H]:Mr[H],w+=q&15,q>>>=4;var ee=q<4?0:q-2>>1,oe=re[q];for(ee>0&&(oe+=Je(g,w,ee),w+=ee),!E&&U<he&&(S=mt(S,he+100),U=S.length);y<he;)S[y]=S[y-oe],++y}}}return E?[S,w+7>>>3]:[S.slice(0,y),w+7>>>3]}function as(g,E){var w=g.slice(g.l||0),T=Io(w,E);return g.l+=T[1],T[0]}function ns(g,E){if(g)typeof console<"u"&&console.error(E);else throw new Error(E)}function is(g,E){var w=g;hr(w,0);var T=[],S=[],y={FileIndex:T,FullPaths:S};x(y,{root:E.root});for(var U=w.length-4;(w[U]!=80||w[U+1]!=75||w[U+2]!=5||w[U+3]!=6)&&U>=0;)--U;w.l=U+4,w.l+=4;var K=w.read_shift(2);w.l+=6;var B=w.read_shift(4);for(w.l=B,U=0;U<K;++U){w.l+=20;var W=w.read_shift(4),H=w.read_shift(4),q=w.read_shift(2),se=w.read_shift(2),he=w.read_shift(2);w.l+=8;var ee=w.read_shift(4),oe=f(w.slice(w.l+q,w.l+q+se));w.l+=q+se+he;var we=w.l;w.l=ee+4,Fo(w,W,H,y,oe),w.l=we}return y}function Fo(g,E,w,T,S){g.l+=2;var y=g.read_shift(2),U=g.read_shift(2),K=s(g);if(y&8257)throw new Error("Unsupported ZIP encryption");for(var B=g.read_shift(4),W=g.read_shift(4),H=g.read_shift(4),q=g.read_shift(2),se=g.read_shift(2),he="",ee=0;ee<q;++ee)he+=String.fromCharCode(g[g.l++]);if(se){var oe=f(g.slice(g.l,g.l+se));(oe[21589]||{}).mt&&(K=oe[21589].mt),((S||{})[21589]||{}).mt&&(K=S[21589].mt)}g.l+=se;var we=g.slice(g.l,g.l+W);switch(U){case 8:we=L(g,H);break;case 0:break;default:throw new Error("Unsupported ZIP Compression method "+U)}var Xe=!1;y&8&&(B=g.read_shift(4),B==134695760&&(B=g.read_shift(4),Xe=!0),W=g.read_shift(4),H=g.read_shift(4)),W!=E&&ns(Xe,"Bad compressed size: "+E+" != "+W),H!=w&&ns(Xe,"Bad uncompressed size: "+w+" != "+H),bn(T,he,we,{unsafe:!0,mt:K})}function Ro(g,E){var w=E||{},T=[],S=[],y=G(1),U=w.compression?8:0,K=0,B=0,W=0,H=0,q=0,se=g.FullPaths[0],he=se,ee=g.FileIndex[0],oe=[],we=0;for(B=1;B<g.FullPaths.length;++B)if(he=g.FullPaths[B].slice(se.length),ee=g.FileIndex[B],!(!ee.size||!ee.content||he=="Sh33tJ5")){var Xe=H,Ge=G(he.length);for(W=0;W<he.length;++W)Ge.write_shift(1,he.charCodeAt(W)&127);Ge=Ge.slice(0,Ge.l),oe[q]=kl.buf(ee.content,0);var dr=ee.content;U==8&&(dr=F(dr)),y=G(30),y.write_shift(4,67324752),y.write_shift(2,20),y.write_shift(2,K),y.write_shift(2,U),ee.mt?i(y,ee.mt):y.write_shift(4,0),y.write_shift(-4,oe[q]),y.write_shift(4,dr.length),y.write_shift(4,ee.content.length),y.write_shift(2,Ge.length),y.write_shift(2,0),H+=y.length,T.push(y),H+=Ge.length,T.push(Ge),H+=dr.length,T.push(dr),y=G(46),y.write_shift(4,33639248),y.write_shift(2,0),y.write_shift(2,20),y.write_shift(2,K),y.write_shift(2,U),y.write_shift(4,0),y.write_shift(-4,oe[q]),y.write_shift(4,dr.length),y.write_shift(4,ee.content.length),y.write_shift(2,Ge.length),y.write_shift(2,0),y.write_shift(2,0),y.write_shift(2,0),y.write_shift(2,0),y.write_shift(4,0),y.write_shift(4,Xe),we+=y.l,S.push(y),we+=Ge.length,S.push(Ge),++q}return y=G(22),y.write_shift(4,101010256),y.write_shift(2,0),y.write_shift(2,0),y.write_shift(2,q),y.write_shift(2,q),y.write_shift(4,we),y.write_shift(4,H),y.write_shift(2,0),cr([cr(T),cr(S),y])}var en={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function No(g,E){if(g.ctype)return g.ctype;var w=g.name||"",T=w.match(/\.([^\.]+)$/);return T&&en[T[1]]||E&&(T=(w=E).match(/[\.\\]([^\.\\])+$/),T&&en[T[1]])?en[T[1]]:"application/octet-stream"}function Po(g){for(var E=Na(g),w=[],T=0;T<E.length;T+=76)w.push(E.slice(T,T+76));return w.join(`\r
`)+`\r
`}function Do(g){var E=g.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,function(W){var H=W.charCodeAt(0).toString(16).toUpperCase();return"="+(H.length==1?"0"+H:H)});E=E.replace(/ $/mg,"=20").replace(/\t$/mg,"=09"),E.charAt(0)==`
`&&(E="=0D"+E.slice(1)),E=E.replace(/\r(?!\n)/mg,"=0D").replace(/\n\n/mg,`
=0A`).replace(/([^\r\n])\n/mg,"$1=0A");for(var w=[],T=E.split(`\r
`),S=0;S<T.length;++S){var y=T[S];if(y.length==0){w.push("");continue}for(var U=0;U<y.length;){var K=76,B=y.slice(U,U+K);B.charAt(K-1)=="="?K--:B.charAt(K-2)=="="?K-=2:B.charAt(K-3)=="="&&(K-=3),B=y.slice(U,U+K),U+=K,U<y.length&&(B+="="),w.push(B)}}return w.join(`\r
`)}function Lo(g){for(var E=[],w=0;w<g.length;++w){for(var T=g[w];w<=g.length&&T.charAt(T.length-1)=="=";)T=T.slice(0,T.length-1)+g[++w];E.push(T)}for(var S=0;S<E.length;++S)E[S]=E[S].replace(/[=][0-9A-Fa-f]{2}/g,function(y){return String.fromCharCode(parseInt(y.slice(1),16))});return Rr(E.join(`\r
`))}function bo(g,E,w){for(var T="",S="",y="",U,K=0;K<10;++K){var B=E[K];if(!B||B.match(/^\s*$/))break;var W=B.match(/^(.*?):\s*([^\s].*)$/);if(W)switch(W[1].toLowerCase()){case"content-location":T=W[2].trim();break;case"content-type":y=W[2].trim();break;case"content-transfer-encoding":S=W[2].trim();break}}switch(++K,S.toLowerCase()){case"base64":U=Rr(Dr(E.slice(K).join("")));break;case"quoted-printable":U=Lo(E.slice(K));break;default:throw new Error("Unsupported Content-Transfer-Encoding "+S)}var H=bn(g,T.slice(w.length),U,{unsafe:!0});y&&(H.ctype=y)}function Mo(g,E){if(V(g.slice(0,13)).toLowerCase()!="mime-version:")throw new Error("Unsupported MAD header");var w=E&&E.root||"",T=(Se&&Buffer.isBuffer(g)?g.toString("binary"):V(g)).split(`\r
`),S=0,y="";for(S=0;S<T.length;++S)if(y=T[S],!!/^Content-Location:/i.test(y)&&(y=y.slice(y.indexOf("file")),w||(w=y.slice(0,y.lastIndexOf("/")+1)),y.slice(0,w.length)!=w))for(;w.length>0&&(w=w.slice(0,w.length-1),w=w.slice(0,w.lastIndexOf("/")+1),y.slice(0,w.length)!=w););var U=(T[1]||"").match(/boundary="(.*?)"/);if(!U)throw new Error("MAD cannot find boundary");var K="--"+(U[1]||""),B=[],W=[],H={FileIndex:B,FullPaths:W};x(H);var q,se=0;for(S=0;S<T.length;++S){var he=T[S];he!==K&&he!==K+"--"||(se++&&bo(H,T.slice(q,S),w),q=S)}return H}function Bo(g,E){var w=E||{},T=w.boundary||"SheetJS";T="------="+T;for(var S=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+T.slice(2)+'"',"","",""],y=g.FullPaths[0],U=y,K=g.FileIndex[0],B=1;B<g.FullPaths.length;++B)if(U=g.FullPaths[B].slice(y.length),K=g.FileIndex[B],!(!K.size||!K.content||U=="Sh33tJ5")){U=U.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,function(we){return"_x"+we.charCodeAt(0).toString(16)+"_"}).replace(/[\u0080-\uFFFF]/g,function(we){return"_u"+we.charCodeAt(0).toString(16)+"_"});for(var W=K.content,H=Se&&Buffer.isBuffer(W)?W.toString("binary"):V(W),q=0,se=Math.min(1024,H.length),he=0,ee=0;ee<=se;++ee)(he=H.charCodeAt(ee))>=32&&he<128&&++q;var oe=q>=se*4/5;S.push(T),S.push("Content-Location: "+(w.root||"file:///C:/SheetJS/")+U),S.push("Content-Transfer-Encoding: "+(oe?"quoted-printable":"base64")),S.push("Content-Type: "+No(K,U)),S.push(""),S.push(oe?Do(H):Po(H))}return S.push(T+`--\r
`),S.join(`\r
`)}function Uo(g){var E={};return x(E,g),E}function bn(g,E,w,T){var S=T&&T.unsafe;S||x(g);var y=!S&&de.find(g,E);if(!y){var U=g.FullPaths[0];E.slice(0,U.length)==U?U=E:(U.slice(-1)!="/"&&(U+="/"),U=(U+E).replace("//","/")),y={name:n(E),type:2},g.FileIndex.push(y),g.FullPaths.push(U),S||de.utils.cfb_gc(g)}return y.content=w,y.size=w?w.length:0,T&&(T.CLSID&&(y.clsid=T.CLSID),T.mt&&(y.mt=T.mt),T.ct&&(y.ct=T.ct)),y}function Wo(g,E){x(g);var w=de.find(g,E);if(w){for(var T=0;T<g.FileIndex.length;++T)if(g.FileIndex[T]==w)return g.FileIndex.splice(T,1),g.FullPaths.splice(T,1),!0}return!1}function Ho(g,E,w){x(g);var T=de.find(g,E);if(T){for(var S=0;S<g.FileIndex.length;++S)if(g.FileIndex[S]==T)return g.FileIndex[S].name=n(w),g.FullPaths[S]=w,!0}return!1}function Vo(g){R(g,!0)}return t.find=X,t.read=P,t.parse=l,t.write=pe,t.writeFile=Le,t.utils={cfb_new:Uo,cfb_add:bn,cfb_del:Wo,cfb_mov:Ho,cfb_gc:Vo,ReadShift:Sa,CheckField:Xf,prep_blob:hr,bconcat:cr,use_zlib:O,_deflateRaw:ye,_inflateRaw:as,consts:ce},t}();let Et;function W_(e){Et=e}function Tl(e){return typeof e=="string"?za(e):Array.isArray(e)?$o(e):e}function Ka(e,t,r){if(typeof Et<"u"&&Et.writeFileSync)return r?Et.writeFileSync(e,t,r):Et.writeFileSync(e,t);if(typeof Deno<"u"){if(r&&typeof t=="string")switch(r){case"utf8":t=new TextEncoder(r).encode(t);break;case"binary":t=za(t);break;default:throw new Error("Unsupported encoding "+r)}return Deno.writeFileSync(e,t)}var a=r=="utf8"?tt(t):t;if(typeof IE_SaveFile<"u")return IE_SaveFile(a,e);if(typeof Blob<"u"){var n=new Blob([Tl(a)],{type:"application/octet-stream"});if(typeof navigator<"u"&&navigator.msSaveBlob)return navigator.msSaveBlob(n,e);if(typeof saveAs<"u")return saveAs(n,e);if(typeof URL<"u"&&typeof document<"u"&&document.createElement&&URL.createObjectURL){var i=URL.createObjectURL(n);if(typeof chrome=="object"&&typeof(chrome.downloads||{}).download=="function")return URL.revokeObjectURL&&typeof setTimeout<"u"&&setTimeout(function(){URL.revokeObjectURL(i)},6e4),chrome.downloads.download({url:i,filename:e,saveAs:!0});var s=document.createElement("a");if(s.download!=null)return s.download=e,s.href=i,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL&&typeof setTimeout<"u"&&setTimeout(function(){URL.revokeObjectURL(i)},6e4),i}}if(typeof $<"u"&&typeof File<"u"&&typeof Folder<"u")try{var f=File(e);return f.open("w"),f.encoding="binary",Array.isArray(t)&&(t=Ct(t)),f.write(t),f.close(),t}catch(c){if(!c.message||!c.message.match(/onstruct/))throw c}throw new Error("cannot save file "+e)}function El(e){if(typeof Et<"u")return Et.readFileSync(e);if(typeof Deno<"u")return Deno.readFileSync(e);if(typeof $<"u"&&typeof File<"u"&&typeof Folder<"u")try{var t=File(e);t.open("r"),t.encoding="binary";var r=t.read();return t.close(),r}catch(a){if(!a.message||!a.message.match(/onstruct/))throw a}throw new Error("Cannot access file "+e)}function je(e){for(var t=Object.keys(e),r=[],a=0;a<t.length;++a)Object.prototype.hasOwnProperty.call(e,t[a])&&r.push(t[a]);return r}function vs(e,t){for(var r=[],a=je(e),n=0;n!==a.length;++n)r[e[a[n]][t]]==null&&(r[e[a[n]][t]]=a[n]);return r}function Cn(e){for(var t=[],r=je(e),a=0;a!==r.length;++a)t[e[r[a]]]=r[a];return t}function On(e){for(var t=[],r=je(e),a=0;a!==r.length;++a)t[e[r[a]]]=parseInt(r[a],10);return t}function Sl(e){for(var t=[],r=je(e),a=0;a!==r.length;++a)t[e[r[a]]]==null&&(t[e[r[a]]]=[]),t[e[r[a]]].push(r[a]);return t}var mn=new Date(1899,11,30,0,0,0);function ir(e,t){var r=e.getTime(),a=mn.getTime()+(e.getTimezoneOffset()-mn.getTimezoneOffset())*6e4;return(r-a)/(24*60*60*1e3)}var Af=new Date,yl=mn.getTime()+(Af.getTimezoneOffset()-mn.getTimezoneOffset())*6e4,ps=Af.getTimezoneOffset();function In(e){var t=new Date;return t.setTime(e*24*60*60*1e3+yl),t.getTimezoneOffset()!==ps&&t.setTime(t.getTime()+(t.getTimezoneOffset()-ps)*6e4),t}function xl(e){var t=0,r=0,a=!1,n=e.match(/P([0-9\.]+Y)?([0-9\.]+M)?([0-9\.]+D)?T([0-9\.]+H)?([0-9\.]+M)?([0-9\.]+S)?/);if(!n)throw new Error("|"+e+"| is not an ISO8601 Duration");for(var i=1;i!=n.length;++i)if(n[i]){switch(r=1,i>3&&(a=!0),n[i].slice(n[i].length-1)){case"Y":throw new Error("Unsupported ISO Duration Field: "+n[i].slice(n[i].length-1));case"D":r*=24;case"H":r*=60;case"M":if(a)r*=60;else throw new Error("Unsupported ISO Duration Field: M")}t+=r*parseInt(n[i],10)}return t}var ms=new Date("2017-02-19T19:06:09.000Z"),Cf=isNaN(ms.getFullYear())?new Date("2/19/17"):ms,Al=Cf.getFullYear()==2017;function Ve(e,t){var r=new Date(e);if(Al)return t>0?r.setTime(r.getTime()+r.getTimezoneOffset()*60*1e3):t<0&&r.setTime(r.getTime()-r.getTimezoneOffset()*60*1e3),r;if(e instanceof Date)return e;if(Cf.getFullYear()==1917&&!isNaN(r.getFullYear())){var a=r.getFullYear();return e.indexOf(""+a)>-1||r.setFullYear(r.getFullYear()+100),r}var n=e.match(/\d+/g)||["2017","2","19","0","0","0"],i=new Date(+n[0],+n[1]-1,+n[2],+n[3]||0,+n[4]||0,+n[5]||0);return e.indexOf("Z")>-1&&(i=new Date(i.getTime()-i.getTimezoneOffset()*60*1e3)),i}function Ut(e,t){if(Se&&Buffer.isBuffer(e)){if(t){if(e[0]==255&&e[1]==254)return tt(e.slice(2).toString("utf16le"));if(e[1]==254&&e[2]==255)return tt(vf(e.slice(2).toString("binary")))}return e.toString("binary")}if(typeof TextDecoder<"u")try{if(t){if(e[0]==255&&e[1]==254)return tt(new TextDecoder("utf-16le").decode(e.slice(2)));if(e[0]==254&&e[1]==255)return tt(new TextDecoder("utf-16be").decode(e.slice(2)))}var r={"€":"","‚":"",ƒ:"","„":"","…":"","†":"","‡":"","ˆ":"","‰":"",Š:"","‹":"",Œ:"",Ž:"","‘":"","’":"","“":"","”":"","•":"","–":"","—":"","˜":"","™":"",š:"","›":"",œ:"",ž:"",Ÿ:""};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,function(i){return r[i]||i})}catch{}for(var a=[],n=0;n!=e.length;++n)a.push(String.fromCharCode(e[n]));return a.join("")}function Be(e){if(typeof JSON<"u"&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if(typeof e!="object"||e==null)return e;if(e instanceof Date)return new Date(e.getTime());var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=Be(e[r]));return t}function $e(e,t){for(var r="";r.length<t;)r+=e;return r}function jr(e){var t=Number(e);if(!isNaN(t))return isFinite(t)?t:NaN;if(!/\d/.test(e))return t;var r=1,a=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,function(){return r*=100,""});return!isNaN(t=Number(a))||(a=a.replace(/[(](.*)[)]/,function(n,i){return r=-r,i}),!isNaN(t=Number(a)))?t/r:t}var Cl=["january","february","march","april","may","june","july","august","september","october","november","december"];function fa(e){var t=new Date(e),r=new Date(NaN),a=t.getYear(),n=t.getMonth(),i=t.getDate();if(isNaN(i))return r;var s=e.toLowerCase();if(s.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if(s=s.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,""),s.length>3&&Cl.indexOf(s)==-1)return r}else if(s.match(/[a-z]/))return r;return a<0||a>8099?r:(n>0||i>1)&&a!=101?t:e.match(/[^-0-9:,\/\\]/)?r:t}var Ol=function(){var e="abacaba".split(/(:?b)/i).length==5;return function(r,a,n){if(e||typeof a=="string")return r.split(a);for(var i=r.split(a),s=[i[0]],f=1;f<i.length;++f)s.push(n),s.push(i[f]);return s}}();function Of(e){return e?e.content&&e.type?Ut(e.content,!0):e.data?ra(e.data):e.asNodeBuffer&&Se?ra(e.asNodeBuffer().toString("binary")):e.asBinary?ra(e.asBinary()):e._data&&e._data.getContent?ra(Ut(Array.prototype.slice.call(e._data.getContent(),0))):null:null}function If(e){if(!e)return null;if(e.data)return un(e.data);if(e.asNodeBuffer&&Se)return e.asNodeBuffer();if(e._data&&e._data.getContent){var t=e._data.getContent();return typeof t=="string"?un(t):Array.prototype.slice.call(t)}return e.content&&e.type?e.content:null}function Il(e){return e&&e.name.slice(-4)===".bin"?If(e):Of(e)}function Vr(e,t){for(var r=e.FullPaths||je(e.files),a=t.toLowerCase().replace(/[\/]/g,"\\"),n=a.replace(/\\/g,"/"),i=0;i<r.length;++i){var s=r[i].replace(/^Root Entry[\/]/,"").toLowerCase();if(a==s||n==s)return e.files?e.files[r[i]]:e.FileIndex[i]}return null}function pi(e,t){var r=Vr(e,t);if(r==null)throw new Error("Cannot find file "+t+" in zip");return r}function rr(e,t,r){if(!r)return Il(pi(e,t));if(!t)return null;try{return rr(e,t)}catch{return null}}function Nr(e,t,r){if(!r)return Of(pi(e,t));if(!t)return null;try{return Nr(e,t)}catch{return null}}function Fl(e,t,r){return If(pi(e,t))}function gs(e){for(var t=e.FullPaths||je(e.files),r=[],a=0;a<t.length;++a)t[a].slice(-1)!="/"&&r.push(t[a].replace(/^Root Entry[\/]/,""));return r.sort()}function Te(e,t,r){if(e.FullPaths){if(typeof r=="string"){var a;return Se?a=dt(r):a=Ko(r),de.utils.cfb_add(e,t,a)}de.utils.cfb_add(e,t,r)}else e.file(t,r)}function mi(){return de.utils.cfb_new()}function Ff(e,t){switch(t.type){case"base64":return de.read(e,{type:"base64"});case"binary":return de.read(e,{type:"binary"});case"buffer":case"array":return de.read(e,{type:"buffer"})}throw new Error("Unrecognized type "+t.type)}function Ea(e,t){if(e.charAt(0)=="/")return e.slice(1);var r=t.split("/");t.slice(-1)!="/"&&r.pop();for(var a=e.split("/");a.length!==0;){var n=a.shift();n===".."?r.pop():n!=="."&&r.push(n)}return r.join("/")}var Qe=`<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r
`,Rl=/([^"\s?>\/]+)\s*=\s*((?:")([^"]*)(?:")|(?:')([^']*)(?:')|([^'">\s]+))/g,_s=/<[\/\?]?[a-zA-Z0-9:_-]+(?:\s+[^"\s?>\/]+\s*=\s*(?:"[^"]*"|'[^']*'|[^'">\s=]+))*\s*[\/\?]?>/mg,Nl=/<[^>]*>/g,Er=Qe.match(_s)?_s:Nl,Pl=/<\w*:/,Dl=/<(\/?)\w+:/;function ge(e,t,r){for(var a={},n=0,i=0;n!==e.length&&!((i=e.charCodeAt(n))===32||i===10||i===13);++n);if(t||(a[0]=e.slice(0,n)),n===e.length)return a;var s=e.match(Rl),f=0,c="",o=0,l="",h="",d=1;if(s)for(o=0;o!=s.length;++o){for(h=s[o],i=0;i!=h.length&&h.charCodeAt(i)!==61;++i);for(l=h.slice(0,i).trim();h.charCodeAt(i+1)==32;)++i;for(d=(n=h.charCodeAt(i+1))==34||n==39?1:0,c=h.slice(i+1+d,h.length-d),f=0;f!=l.length&&l.charCodeAt(f)!==58;++f);if(f===l.length)l.indexOf("_")>0&&(l=l.slice(0,l.indexOf("_"))),a[l]=c,a[l.toLowerCase()]=c;else{var v=(f===5&&l.slice(0,5)==="xmlns"?"xmlns":"")+l.slice(f+1);if(a[v]&&l.slice(f-3,f)=="ext")continue;a[v]=c,a[v.toLowerCase()]=c}}return a}function it(e){return e.replace(Dl,"<$1")}var Rf={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"},gi=Cn(Rf),Fe=function(){var e=/&(?:quot|apos|gt|lt|amp|#x?([\da-fA-F]+));/ig,t=/_x([\da-fA-F]{4})_/ig;return function r(a){var n=a+"",i=n.indexOf("<![CDATA[");if(i==-1)return n.replace(e,function(f,c){return Rf[f]||String.fromCharCode(parseInt(c,f.indexOf("x")>-1?16:10))||f}).replace(t,function(f,c){return String.fromCharCode(parseInt(c,16))});var s=n.indexOf("]]>");return r(n.slice(0,i))+n.slice(i+9,s)+r(n.slice(s+3))}}(),_i=/[&<>'"]/g,Ll=/[\u0000-\u0008\u000b-\u001f]/g;function De(e){var t=e+"";return t.replace(_i,function(r){return gi[r]}).replace(Ll,function(r){return"_x"+("000"+r.charCodeAt(0).toString(16)).slice(-4)+"_"})}function ws(e){return De(e).replace(/ /g,"_x0020_")}var Nf=/[\u0000-\u001f]/g;function wi(e){var t=e+"";return t.replace(_i,function(r){return gi[r]}).replace(/\n/g,"<br/>").replace(Nf,function(r){return"&#x"+("000"+r.charCodeAt(0).toString(16)).slice(-4)+";"})}function bl(e){var t=e+"";return t.replace(_i,function(r){return gi[r]}).replace(Nf,function(r){return"&#x"+r.charCodeAt(0).toString(16).toUpperCase()+";"})}var ks=function(){var e=/&#(\d+);/g;function t(r,a){return String.fromCharCode(parseInt(a,10))}return function(a){return a.replace(e,t)}}();function Ml(e){return e.replace(/(\r\n|[\r\n])/g,"&#10;")}function We(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}function Un(e){for(var t="",r=0,a=0,n=0,i=0,s=0,f=0;r<e.length;){if(a=e.charCodeAt(r++),a<128){t+=String.fromCharCode(a);continue}if(n=e.charCodeAt(r++),a>191&&a<224){s=(a&31)<<6,s|=n&63,t+=String.fromCharCode(s);continue}if(i=e.charCodeAt(r++),a<240){t+=String.fromCharCode((a&15)<<12|(n&63)<<6|i&63);continue}s=e.charCodeAt(r++),f=((a&7)<<18|(n&63)<<12|(i&63)<<6|s&63)-65536,t+=String.fromCharCode(55296+(f>>>10&1023)),t+=String.fromCharCode(56320+(f&1023))}return t}function Ts(e){var t=St(2*e.length),r,a,n=1,i=0,s=0,f;for(a=0;a<e.length;a+=n)n=1,(f=e.charCodeAt(a))<128?r=f:f<224?(r=(f&31)*64+(e.charCodeAt(a+1)&63),n=2):f<240?(r=(f&15)*4096+(e.charCodeAt(a+1)&63)*64+(e.charCodeAt(a+2)&63),n=3):(n=4,r=(f&7)*262144+(e.charCodeAt(a+1)&63)*4096+(e.charCodeAt(a+2)&63)*64+(e.charCodeAt(a+3)&63),r-=65536,s=55296+(r>>>10&1023),r=56320+(r&1023)),s!==0&&(t[i++]=s&255,t[i++]=s>>>8,s=0),t[i++]=r%256,t[i++]=r>>>8;return t.slice(0,i).toString("ucs2")}function Es(e){return dt(e,"binary").toString("utf8")}var rn="foo bar bazâð£",be=Se&&(Es(rn)==Un(rn)&&Es||Ts(rn)==Un(rn)&&Ts)||Un,tt=Se?function(e){return dt(e,"utf8").toString("binary")}:function(e){for(var t=[],r=0,a=0,n=0;r<e.length;)switch(a=e.charCodeAt(r++),!0){case a<128:t.push(String.fromCharCode(a));break;case a<2048:t.push(String.fromCharCode(192+(a>>6))),t.push(String.fromCharCode(128+(a&63)));break;case(a>=55296&&a<57344):a-=55296,n=e.charCodeAt(r++)-56320+(a<<10),t.push(String.fromCharCode(240+(n>>18&7))),t.push(String.fromCharCode(144+(n>>12&63))),t.push(String.fromCharCode(128+(n>>6&63))),t.push(String.fromCharCode(128+(n&63)));break;default:t.push(String.fromCharCode(224+(a>>12))),t.push(String.fromCharCode(128+(a>>6&63))),t.push(String.fromCharCode(128+(a&63)))}return t.join("")},Da=function(){var e={};return function(r,a){var n=r+"|"+(a||"");return e[n]?e[n]:e[n]=new RegExp("<(?:\\w+:)?"+r+'(?: xml:space="preserve")?(?:[^>]*)>([\\s\\S]*?)</(?:\\w+:)?'+r+">",a||"")}}(),Pf=function(){var e=[["nbsp"," "],["middot","·"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map(function(t){return[new RegExp("&"+t[0]+";","ig"),t[1]]});return function(r){for(var a=r.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,`
`).replace(/<[^>]*>/g,""),n=0;n<e.length;++n)a=a.replace(e[n][0],e[n][1]);return a}}(),Bl=function(){var e={};return function(r){return e[r]!==void 0?e[r]:e[r]=new RegExp("<(?:vt:)?"+r+">([\\s\\S]*?)</(?:vt:)?"+r+">","g")}}(),Ul=/<\/?(?:vt:)?variant>/g,Wl=/<(?:vt:)([^>]*)>([\s\S]*)</;function Ss(e,t){var r=ge(e),a=e.match(Bl(r.baseType))||[],n=[];if(a.length!=r.size){if(t.WTF)throw new Error("unexpected vector length "+a.length+" != "+r.size);return n}return a.forEach(function(i){var s=i.replace(Ul,"").match(Wl);s&&n.push({v:be(s[2]),t:s[1]})}),n}var Df=/(^\s|\s$|\n)/;function ur(e,t){return"<"+e+(t.match(Df)?' xml:space="preserve"':"")+">"+t+"</"+e+">"}function La(e){return je(e).map(function(t){return" "+t+'="'+e[t]+'"'}).join("")}function ae(e,t,r){return"<"+e+(r!=null?La(r):"")+(t!=null?(t.match(Df)?' xml:space="preserve"':"")+">"+t+"</"+e:"/")+">"}function Qn(e,t){try{return e.toISOString().replace(/\.\d*/,"")}catch(r){if(t)throw r}return""}function Hl(e,t){switch(typeof e){case"string":var r=ae("vt:lpwstr",De(e));return r=r.replace(/&quot;/g,"_x0022_"),r;case"number":return ae((e|0)==e?"vt:i4":"vt:r8",De(String(e)));case"boolean":return ae("vt:bool",e?"true":"false")}if(e instanceof Date)return ae("vt:filetime",Qn(e));throw new Error("Unable to serialize "+e)}function ki(e){if(Se&&Buffer.isBuffer(e))return e.toString("utf8");if(typeof e=="string")return e;if(typeof Uint8Array<"u"&&e instanceof Uint8Array)return be(Ct(ui(e)));throw new Error("Bad input format: expected Buffer or string")}var ba=/<(\/?)([^\s?><!\/:]*:|)([^\s?<>:\/]+)(?:[\s?:\/][^>]*)?>/mg,nr={CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/metadata/core-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/custom-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/extended-properties",CT:"http://schemas.openxmlformats.org/package/2006/content-types",RELS:"http://schemas.openxmlformats.org/package/2006/relationships",TCMNT:"http://schemas.microsoft.com/office/spreadsheetml/2018/threadedcomments",dc:"http://purl.org/dc/elements/1.1/",dcterms:"http://purl.org/dc/terms/",dcmitype:"http://purl.org/dc/dcmitype/",r:"http://schemas.openxmlformats.org/officeDocument/2006/relationships",vt:"http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes",xsi:"http://www.w3.org/2001/XMLSchema-instance",xsd:"http://www.w3.org/2001/XMLSchema"},zt=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"],Fr={o:"urn:schemas-microsoft-com:office:office",x:"urn:schemas-microsoft-com:office:excel",ss:"urn:schemas-microsoft-com:office:spreadsheet",dt:"uuid:C2F41010-65B3-11d1-A29F-00AA00C14882",mv:"http://macVmlSchemaUri",v:"urn:schemas-microsoft-com:vml",html:"http://www.w3.org/TR/REC-html40"};function Vl(e,t){for(var r=1-2*(e[t+7]>>>7),a=((e[t+7]&127)<<4)+(e[t+6]>>>4&15),n=e[t+6]&15,i=5;i>=0;--i)n=n*256+e[t+i];return a==2047?n==0?r*(1/0):NaN:(a==0?a=-1022:(a-=1023,n+=Math.pow(2,52)),r*Math.pow(2,a-52)*n)}function Xl(e,t,r){var a=(t<0||1/t==-1/0?1:0)<<7,n=0,i=0,s=a?-t:t;isFinite(s)?s==0?n=i=0:(n=Math.floor(Math.log(s)/Math.LN2),i=s*Math.pow(2,52-n),n<=-1023&&(!isFinite(i)||i<Math.pow(2,52))?n=-1022:(i-=Math.pow(2,52),n+=1023)):(n=2047,i=isNaN(t)?26985:0);for(var f=0;f<=5;++f,i/=256)e[r+f]=i&255;e[r+6]=(n&15)<<4|i&15,e[r+7]=n>>4|a}var ys=function(e){for(var t=[],r=10240,a=0;a<e[0].length;++a)if(e[0][a])for(var n=0,i=e[0][a].length;n<i;n+=r)t.push.apply(t,e[0][a].slice(n,n+r));return t},xs=Se?function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0].map(function(t){return Buffer.isBuffer(t)?t:dt(t)})):ys(e)}:ys,As=function(e,t,r){for(var a=[],n=t;n<r;n+=2)a.push(String.fromCharCode(lt(e,n)));return a.join("").replace(wr,"")},Fn=Se?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf16le",t,r).replace(wr,""):As(e,t,r)}:As,Cs=function(e,t,r){for(var a=[],n=t;n<t+r;++n)a.push(("0"+e[n].toString(16)).slice(-2));return a.join("")},Lf=Se?function(e,t,r){return Buffer.isBuffer(e)?e.toString("hex",t,t+r):Cs(e,t,r)}:Cs,Os=function(e,t,r){for(var a=[],n=t;n<r;n++)a.push(String.fromCharCode(ea(e,n)));return a.join("")},ha=Se?function(t,r,a){return Buffer.isBuffer(t)?t.toString("utf8",r,a):Os(t,r,a)}:Os,bf=function(e,t){var r=fr(e,t);return r>0?ha(e,t+4,t+4+r-1):""},Ti=bf,Mf=function(e,t){var r=fr(e,t);return r>0?ha(e,t+4,t+4+r-1):""},Ei=Mf,Bf=function(e,t){var r=2*fr(e,t);return r>0?ha(e,t+4,t+4+r-1):""},Si=Bf,Uf=function(t,r){var a=fr(t,r);return a>0?Fn(t,r+4,r+4+a):""},yi=Uf,Wf=function(e,t){var r=fr(e,t);return r>0?ha(e,t+4,t+4+r):""},xi=Wf,Hf=function(e,t){return Vl(e,t)},gn=Hf,Ai=function(t){return Array.isArray(t)||typeof Uint8Array<"u"&&t instanceof Uint8Array};Se&&(Ti=function(t,r){if(!Buffer.isBuffer(t))return bf(t,r);var a=t.readUInt32LE(r);return a>0?t.toString("utf8",r+4,r+4+a-1):""},Ei=function(t,r){if(!Buffer.isBuffer(t))return Mf(t,r);var a=t.readUInt32LE(r);return a>0?t.toString("utf8",r+4,r+4+a-1):""},Si=function(t,r){if(!Buffer.isBuffer(t))return Bf(t,r);var a=2*t.readUInt32LE(r);return t.toString("utf16le",r+4,r+4+a-1)},yi=function(t,r){if(!Buffer.isBuffer(t))return Uf(t,r);var a=t.readUInt32LE(r);return t.toString("utf16le",r+4,r+4+a)},xi=function(t,r){if(!Buffer.isBuffer(t))return Wf(t,r);var a=t.readUInt32LE(r);return t.toString("utf8",r+4,r+4+a)},gn=function(t,r){return Buffer.isBuffer(t)?t.readDoubleLE(r):Hf(t,r)},Ai=function(t){return Buffer.isBuffer(t)||Array.isArray(t)||typeof Uint8Array<"u"&&t instanceof Uint8Array});function Vf(){Fn=function(e,t,r){return Ie.utils.decode(1200,e.slice(t,r)).replace(wr,"")},ha=function(e,t,r){return Ie.utils.decode(65001,e.slice(t,r))},Ti=function(e,t){var r=fr(e,t);return r>0?Ie.utils.decode(Mt,e.slice(t+4,t+4+r-1)):""},Ei=function(e,t){var r=fr(e,t);return r>0?Ie.utils.decode(Pr,e.slice(t+4,t+4+r-1)):""},Si=function(e,t){var r=2*fr(e,t);return r>0?Ie.utils.decode(1200,e.slice(t+4,t+4+r-1)):""},yi=function(e,t){var r=fr(e,t);return r>0?Ie.utils.decode(1200,e.slice(t+4,t+4+r)):""},xi=function(e,t){var r=fr(e,t);return r>0?Ie.utils.decode(65001,e.slice(t+4,t+4+r)):""}}typeof Ie<"u"&&Vf();var ea=function(e,t){return e[t]},lt=function(e,t){return e[t+1]*256+e[t]},Gl=function(e,t){var r=e[t+1]*256+e[t];return r<32768?r:(65535-r+1)*-1},fr=function(e,t){return e[t+3]*(1<<24)+(e[t+2]<<16)+(e[t+1]<<8)+e[t]},Nt=function(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]},zl=function(e,t){return e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3]};function Sa(e,t){var r="",a,n,i=[],s,f,c,o;switch(t){case"dbcs":if(o=this.l,Se&&Buffer.isBuffer(this))r=this.slice(this.l,this.l+2*e).toString("utf16le");else for(c=0;c<e;++c)r+=String.fromCharCode(lt(this,o)),o+=2;e*=2;break;case"utf8":r=ha(this,this.l,this.l+e);break;case"utf16le":e*=2,r=Fn(this,this.l,this.l+e);break;case"wstr":if(typeof Ie<"u")r=Ie.utils.decode(Pr,this.slice(this.l,this.l+2*e));else return Sa.call(this,e,"dbcs");e=2*e;break;case"lpstr-ansi":r=Ti(this,this.l),e=4+fr(this,this.l);break;case"lpstr-cp":r=Ei(this,this.l),e=4+fr(this,this.l);break;case"lpwstr":r=Si(this,this.l),e=4+2*fr(this,this.l);break;case"lpp4":e=4+fr(this,this.l),r=yi(this,this.l),e&2&&(e+=2);break;case"8lpp4":e=4+fr(this,this.l),r=xi(this,this.l),e&3&&(e+=4-(e&3));break;case"cstr":for(e=0,r="";(s=ea(this,this.l+e++))!==0;)i.push(ka(s));r=i.join("");break;case"_wstr":for(e=0,r="";(s=lt(this,this.l+e))!==0;)i.push(ka(s)),e+=2;e+=2,r=i.join("");break;case"dbcs-cont":for(r="",o=this.l,c=0;c<e;++c){if(this.lens&&this.lens.indexOf(o)!==-1)return s=ea(this,o),this.l=o+1,f=Sa.call(this,e-c,s?"dbcs-cont":"sbcs-cont"),i.join("")+f;i.push(ka(lt(this,o))),o+=2}r=i.join(""),e*=2;break;case"cpstr":if(typeof Ie<"u"){r=Ie.utils.decode(Pr,this.slice(this.l,this.l+e));break}case"sbcs-cont":for(r="",o=this.l,c=0;c!=e;++c){if(this.lens&&this.lens.indexOf(o)!==-1)return s=ea(this,o),this.l=o+1,f=Sa.call(this,e-c,s?"dbcs-cont":"sbcs-cont"),i.join("")+f;i.push(ka(ea(this,o))),o+=1}r=i.join("");break;default:switch(e){case 1:return a=ea(this,this.l),this.l++,a;case 2:return a=(t==="i"?Gl:lt)(this,this.l),this.l+=2,a;case 4:case-4:return t==="i"||(this[this.l+3]&128)===0?(a=(e>0?Nt:zl)(this,this.l),this.l+=4,a):(n=fr(this,this.l),this.l+=4,n);case 8:case-8:if(t==="f")return e==8?n=gn(this,this.l):n=gn([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,n;e=8;case 16:r=Lf(this,this.l,e);break}}return this.l+=e,r}var $l=function(e,t,r){e[r]=t&255,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24&255},Kl=function(e,t,r){e[r]=t&255,e[r+1]=t>>8&255,e[r+2]=t>>16&255,e[r+3]=t>>24&255},Yl=function(e,t,r){e[r]=t&255,e[r+1]=t>>>8&255};function jl(e,t,r){var a=0,n=0;if(r==="dbcs"){for(n=0;n!=t.length;++n)Yl(this,t.charCodeAt(n),this.l+2*n);a=2*t.length}else if(r==="sbcs"){if(typeof Ie<"u"&&Mt==874)for(n=0;n!=t.length;++n){var i=Ie.utils.encode(Mt,t.charAt(n));this[this.l+n]=i[0]}else for(t=t.replace(/[^\x00-\x7F]/g,"_"),n=0;n!=t.length;++n)this[this.l+n]=t.charCodeAt(n)&255;a=t.length}else if(r==="hex"){for(;n<e;++n)this[this.l++]=parseInt(t.slice(2*n,2*n+2),16)||0;return this}else if(r==="utf16le"){var s=Math.min(this.l+e,this.length);for(n=0;n<Math.min(t.length,e);++n){var f=t.charCodeAt(n);this[this.l++]=f&255,this[this.l++]=f>>8}for(;this.l<s;)this[this.l++]=0;return this}else switch(e){case 1:a=1,this[this.l]=t&255;break;case 2:a=2,this[this.l]=t&255,t>>>=8,this[this.l+1]=t&255;break;case 3:a=3,this[this.l]=t&255,t>>>=8,this[this.l+1]=t&255,t>>>=8,this[this.l+2]=t&255;break;case 4:a=4,$l(this,t,this.l);break;case 8:if(a=8,r==="f"){Xl(this,t,this.l);break}case 16:break;case-4:a=4,Kl(this,t,this.l);break}return this.l+=a,this}function Xf(e,t){var r=Lf(this,this.l,e.length>>1);if(r!==e)throw new Error(t+"Expected "+e+" saw "+r);this.l+=e.length>>1}function hr(e,t){e.l=t,e.read_shift=Sa,e.chk=Xf,e.write_shift=jl}function Tr(e,t){e.l+=t}function G(e){var t=St(e);return hr(t,0),t}function vt(e,t,r){if(e){var a,n,i;hr(e,e.l||0);for(var s=e.length,f=0,c=0;e.l<s;){f=e.read_shift(1),f&128&&(f=(f&127)+((e.read_shift(1)&127)<<7));var o=Va[f]||Va[65535];for(a=e.read_shift(1),i=a&127,n=1;n<4&&a&128;++n)i+=((a=e.read_shift(1))&127)<<7*n;c=e.l+i;var l=o.f&&o.f(e,i,r);if(e.l=c,t(l,o,f))return}}}function Or(){var e=[],t=Se?256:2048,r=function(o){var l=G(o);return hr(l,0),l},a=r(t),n=function(){a&&(a.length>a.l&&(a=a.slice(0,a.l),a.l=a.length),a.length>0&&e.push(a),a=null)},i=function(o){return a&&o<a.length-a.l?a:(n(),a=r(Math.max(o+1,t)))},s=function(){return n(),cr(e)},f=function(o){n(),a=o,a.l==null&&(a.l=a.length),i(t)};return{next:i,push:f,end:s,_bufs:e}}function j(e,t,r,a){var n=+t,i;if(!isNaN(n)){a||(a=Va[n].p||(r||[]).length||0),i=1+(n>=128?1:0)+1,a>=128&&++i,a>=16384&&++i,a>=2097152&&++i;var s=e.next(i);n<=127?s.write_shift(1,n):(s.write_shift(1,(n&127)+128),s.write_shift(1,n>>7));for(var f=0;f!=4;++f)if(a>=128)s.write_shift(1,(a&127)+128),a>>=7;else{s.write_shift(1,a);break}a>0&&Ai(r)&&e.push(r)}}function ya(e,t,r){var a=Be(e);if(t.s?(a.cRel&&(a.c+=t.s.c),a.rRel&&(a.r+=t.s.r)):(a.cRel&&(a.c+=t.c),a.rRel&&(a.r+=t.r)),!r||r.biff<12){for(;a.c>=256;)a.c-=256;for(;a.r>=65536;)a.r-=65536}return a}function Is(e,t,r){var a=Be(e);return a.s=ya(a.s,t.s,r),a.e=ya(a.e,t.s,r),a}function xa(e,t){if(e.cRel&&e.c<0)for(e=Be(e);e.c<0;)e.c+=t>8?16384:256;if(e.rRel&&e.r<0)for(e=Be(e);e.r<0;)e.r+=t>8?1048576:t>5?65536:16384;var r=me(e);return!e.cRel&&e.cRel!=null&&(r=ql(r)),!e.rRel&&e.rRel!=null&&(r=Jl(r)),r}function Wn(e,t){return e.s.r==0&&!e.s.rRel&&e.e.r==(t.biff>=12?1048575:t.biff>=8?65536:16384)&&!e.e.rRel?(e.s.cRel?"":"$")+He(e.s.c)+":"+(e.e.cRel?"":"$")+He(e.e.c):e.s.c==0&&!e.s.cRel&&e.e.c==(t.biff>=12?16383:255)&&!e.e.cRel?(e.s.rRel?"":"$")+Ke(e.s.r)+":"+(e.e.rRel?"":"$")+Ke(e.e.r):xa(e.s,t.biff)+":"+xa(e.e,t.biff)}function Ci(e){return parseInt(Zl(e),10)-1}function Ke(e){return""+(e+1)}function Jl(e){return e.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")}function Zl(e){return e.replace(/\$(\d+)$/,"$1")}function Oi(e){for(var t=Ql(e),r=0,a=0;a!==t.length;++a)r=26*r+t.charCodeAt(a)-64;return r-1}function He(e){if(e<0)throw new Error("invalid column "+e);var t="";for(++e;e;e=Math.floor((e-1)/26))t=String.fromCharCode((e-1)%26+65)+t;return t}function ql(e){return e.replace(/^([A-Z])/,"$$$1")}function Ql(e){return e.replace(/^\$([A-Z])/,"$1")}function e1(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")}function ze(e){for(var t=0,r=0,a=0;a<e.length;++a){var n=e.charCodeAt(a);n>=48&&n<=57?t=10*t+(n-48):n>=65&&n<=90&&(r=26*r+(n-64))}return{c:r-1,r:t-1}}function me(e){for(var t=e.c+1,r="";t;t=(t-1)/26|0)r=String.fromCharCode((t-1)%26+65)+r;return r+(e.r+1)}function Ar(e){var t=e.indexOf(":");return t==-1?{s:ze(e),e:ze(e)}:{s:ze(e.slice(0,t)),e:ze(e.slice(t+1))}}function ke(e,t){return typeof t>"u"||typeof t=="number"?ke(e.s,e.e):(typeof e!="string"&&(e=me(e)),typeof t!="string"&&(t=me(t)),e==t?e:e+":"+t)}function Ce(e){var t={s:{c:0,r:0},e:{c:0,r:0}},r=0,a=0,n=0,i=e.length;for(r=0;a<i&&!((n=e.charCodeAt(a)-64)<1||n>26);++a)r=26*r+n;for(t.s.c=--r,r=0;a<i&&!((n=e.charCodeAt(a)-48)<0||n>9);++a)r=10*r+n;if(t.s.r=--r,a===i||n!=10)return t.e.c=t.s.c,t.e.r=t.s.r,t;for(++a,r=0;a!=i&&!((n=e.charCodeAt(a)-64)<1||n>26);++a)r=26*r+n;for(t.e.c=--r,r=0;a!=i&&!((n=e.charCodeAt(a)-48)<0||n>9);++a)r=10*r+n;return t.e.r=--r,t}function Fs(e,t){var r=e.t=="d"&&t instanceof Date;if(e.z!=null)try{return e.w=Lr(e.z,r?ir(t):t)}catch{}try{return e.w=Lr((e.XF||{}).numFmtId||(r?14:0),r?ir(t):t)}catch{return""+t}}function nt(e,t,r){return e==null||e.t==null||e.t=="z"?"":e.w!==void 0?e.w:(e.t=="d"&&!e.z&&r&&r.dateNF&&(e.z=r.dateNF),e.t=="e"?pt[e.v]||e.v:t==null?Fs(e,e.v):Fs(e,t))}function Ot(e,t){var r=t&&t.sheet?t.sheet:"Sheet1",a={};return a[r]=e,{SheetNames:[r],Sheets:a}}function Gf(e,t,r){var a=r||{},n=e?Array.isArray(e):a.dense,i=e||(n?[]:{}),s=0,f=0;if(i&&a.origin!=null){if(typeof a.origin=="number")s=a.origin;else{var c=typeof a.origin=="string"?ze(a.origin):a.origin;s=c.r,f=c.c}i["!ref"]||(i["!ref"]="A1:A1")}var o={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(i["!ref"]){var l=Ce(i["!ref"]);o.s.c=l.s.c,o.s.r=l.s.r,o.e.c=Math.max(o.e.c,l.e.c),o.e.r=Math.max(o.e.r,l.e.r),s==-1&&(o.e.r=s=l.e.r+1)}for(var h=0;h!=t.length;++h)if(t[h]){if(!Array.isArray(t[h]))throw new Error("aoa_to_sheet expects an array of arrays");for(var d=0;d!=t[h].length;++d)if(!(typeof t[h][d]>"u")){var v={v:t[h][d]},p=s+h,u=f+d;if(o.s.r>p&&(o.s.r=p),o.s.c>u&&(o.s.c=u),o.e.r<p&&(o.e.r=p),o.e.c<u&&(o.e.c=u),t[h][d]&&typeof t[h][d]=="object"&&!Array.isArray(t[h][d])&&!(t[h][d]instanceof Date))v=t[h][d];else if(Array.isArray(v.v)&&(v.f=t[h][d][1],v.v=v.v[0]),v.v===null)if(v.f)v.t="n";else if(a.nullError)v.t="e",v.v=0;else if(a.sheetStubs)v.t="z";else continue;else typeof v.v=="number"?v.t="n":typeof v.v=="boolean"?v.t="b":v.v instanceof Date?(v.z=a.dateNF||ve[14],a.cellDates?(v.t="d",v.w=Lr(v.z,ir(v.v))):(v.t="n",v.v=ir(v.v),v.w=Lr(v.z,v.v))):v.t="s";if(n)i[p]||(i[p]=[]),i[p][u]&&i[p][u].z&&(v.z=i[p][u].z),i[p][u]=v;else{var m=me({c:u,r:p});i[m]&&i[m].z&&(v.z=i[m].z),i[m]=v}}}return o.s.c<1e7&&(i["!ref"]=ke(o)),i}function ua(e,t){return Gf(null,e,t)}function r1(e){return e.read_shift(4,"i")}function Jr(e,t){return t||(t=G(4)),t.write_shift(4,e),t}function kr(e){var t=e.read_shift(4);return t===0?"":e.read_shift(t,"dbcs")}function or(e,t){var r=!1;return t==null&&(r=!0,t=G(4+2*e.length)),t.write_shift(4,e.length),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}function t1(e){return{ich:e.read_shift(2),ifnt:e.read_shift(2)}}function a1(e,t){return t||(t=G(4)),t.write_shift(2,0),t.write_shift(2,0),t}function Ii(e,t){var r=e.l,a=e.read_shift(1),n=kr(e),i=[],s={t:n,h:n};if((a&1)!==0){for(var f=e.read_shift(4),c=0;c!=f;++c)i.push(t1(e));s.r=i}else s.r=[{ich:0,ifnt:0}];return e.l=r+t,s}function n1(e,t){var r=!1;return t==null&&(r=!0,t=G(15+4*e.t.length)),t.write_shift(1,0),or(e.t,t),r?t.slice(0,t.l):t}var i1=Ii;function s1(e,t){var r=!1;return t==null&&(r=!0,t=G(23+4*e.t.length)),t.write_shift(1,1),or(e.t,t),t.write_shift(4,1),a1({},t),r?t.slice(0,t.l):t}function $r(e){var t=e.read_shift(4),r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:t,iStyleRef:r}}function $t(e,t){return t==null&&(t=G(8)),t.write_shift(-4,e.c),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}function Kt(e){var t=e.read_shift(2);return t+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:t}}function Yt(e,t){return t==null&&(t=G(4)),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}var f1=kr,zf=or;function Fi(e){var t=e.read_shift(4);return t===0||t===4294967295?"":e.read_shift(t,"dbcs")}function _n(e,t){var r=!1;return t==null&&(r=!0,t=G(127)),t.write_shift(4,e.length>0?e.length:4294967295),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}var c1=kr,ei=Fi,Ri=_n;function Ni(e){var t=e.slice(e.l,e.l+4),r=t[0]&1,a=t[0]&2;e.l+=4;var n=a===0?gn([0,0,0,0,t[0]&252,t[1],t[2],t[3]],0):Nt(t,0)>>2;return r?n/100:n}function $f(e,t){t==null&&(t=G(4));var r=0,a=0,n=e*100;if(e==(e|0)&&e>=-536870912&&e<1<<29?a=1:n==(n|0)&&n>=-536870912&&n<1<<29&&(a=1,r=1),a)t.write_shift(-4,((r?n:e)<<2)+(r+2));else throw new Error("unsupported RkNumber "+e)}function Kf(e){var t={s:{},e:{}};return t.s.r=e.read_shift(4),t.e.r=e.read_shift(4),t.s.c=e.read_shift(4),t.e.c=e.read_shift(4),t}function o1(e,t){return t||(t=G(16)),t.write_shift(4,e.s.r),t.write_shift(4,e.e.r),t.write_shift(4,e.s.c),t.write_shift(4,e.e.c),t}var jt=Kf,da=o1;function _r(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function Wt(e,t){return(t||G(8)).write_shift(8,e,"f")}function l1(e){var t={},r=e.read_shift(1),a=r>>>1,n=e.read_shift(1),i=e.read_shift(2,"i"),s=e.read_shift(1),f=e.read_shift(1),c=e.read_shift(1);switch(e.l++,a){case 0:t.auto=1;break;case 1:t.index=n;var o=Pt[n];o&&(t.rgb=Ba(o));break;case 2:t.rgb=Ba([s,f,c]);break;case 3:t.theme=n;break}return i!=0&&(t.tint=i>0?i/32767:i/32768),t}function wn(e,t){if(t||(t=G(8)),!e||e.auto)return t.write_shift(4,0),t.write_shift(4,0),t;e.index!=null?(t.write_shift(1,2),t.write_shift(1,e.index)):e.theme!=null?(t.write_shift(1,6),t.write_shift(1,e.theme)):(t.write_shift(1,5),t.write_shift(1,0));var r=e.tint||0;if(r>0?r*=32767:r<0&&(r*=32768),t.write_shift(2,r),!e.rgb||e.theme!=null)t.write_shift(2,0),t.write_shift(1,0),t.write_shift(1,0);else{var a=e.rgb||"FFFFFF";typeof a=="number"&&(a=("000000"+a.toString(16)).slice(-6)),t.write_shift(1,parseInt(a.slice(0,2),16)),t.write_shift(1,parseInt(a.slice(2,4),16)),t.write_shift(1,parseInt(a.slice(4,6),16)),t.write_shift(1,255)}return t}function h1(e){var t=e.read_shift(1);e.l++;var r={fBold:t&1,fItalic:t&2,fUnderline:t&4,fStrikeout:t&8,fOutline:t&16,fShadow:t&32,fCondense:t&64,fExtend:t&128};return r}function u1(e,t){t||(t=G(2));var r=(e.italic?2:0)|(e.strike?8:0)|(e.outline?16:0)|(e.shadow?32:0)|(e.condense?64:0)|(e.extend?128:0);return t.write_shift(1,r),t.write_shift(1,0),t}function Yf(e,t){var r={2:"BITMAP",3:"METAFILEPICT",8:"DIB",14:"ENHMETAFILE"},a=e.read_shift(4);switch(a){case 0:return"";case 4294967295:case 4294967294:return r[e.read_shift(4)]||""}if(a>400)throw new Error("Unsupported Clipboard: "+a.toString(16));return e.l-=4,e.read_shift(0,t==1?"lpstr":"lpwstr")}function d1(e){return Yf(e,1)}function v1(e){return Yf(e,2)}var Pi=2,Cr=3,tn=11,Rs=12,kn=19,an=64,p1=65,m1=71,g1=4108,_1=4126,sr=80,jf=81,w1=[sr,jf],ri={1:{n:"CodePage",t:Pi},2:{n:"Category",t:sr},3:{n:"PresentationFormat",t:sr},4:{n:"ByteCount",t:Cr},5:{n:"LineCount",t:Cr},6:{n:"ParagraphCount",t:Cr},7:{n:"SlideCount",t:Cr},8:{n:"NoteCount",t:Cr},9:{n:"HiddenCount",t:Cr},10:{n:"MultimediaClipCount",t:Cr},11:{n:"ScaleCrop",t:tn},12:{n:"HeadingPairs",t:g1},13:{n:"TitlesOfParts",t:_1},14:{n:"Manager",t:sr},15:{n:"Company",t:sr},16:{n:"LinksUpToDate",t:tn},17:{n:"CharacterCount",t:Cr},19:{n:"SharedDoc",t:tn},22:{n:"HyperlinksChanged",t:tn},23:{n:"AppVersion",t:Cr,p:"version"},24:{n:"DigSig",t:p1},26:{n:"ContentType",t:sr},27:{n:"ContentStatus",t:sr},28:{n:"Language",t:sr},29:{n:"Version",t:sr},255:{},2147483648:{n:"Locale",t:kn},2147483651:{n:"Behavior",t:kn},1919054434:{}},ti={1:{n:"CodePage",t:Pi},2:{n:"Title",t:sr},3:{n:"Subject",t:sr},4:{n:"Author",t:sr},5:{n:"Keywords",t:sr},6:{n:"Comments",t:sr},7:{n:"Template",t:sr},8:{n:"LastAuthor",t:sr},9:{n:"RevNumber",t:sr},10:{n:"EditTime",t:an},11:{n:"LastPrinted",t:an},12:{n:"CreatedDate",t:an},13:{n:"ModifiedDate",t:an},14:{n:"PageCount",t:Cr},15:{n:"WordCount",t:Cr},16:{n:"CharCount",t:Cr},17:{n:"Thumbnail",t:m1},18:{n:"Application",t:sr},19:{n:"DocSecurity",t:Cr},255:{},2147483648:{n:"Locale",t:kn},2147483651:{n:"Behavior",t:kn},1919054434:{}},Ns={1:"US",2:"CA",3:"",7:"RU",20:"EG",30:"GR",31:"NL",32:"BE",33:"FR",34:"ES",36:"HU",39:"IT",41:"CH",43:"AT",44:"GB",45:"DK",46:"SE",47:"NO",48:"PL",49:"DE",52:"MX",55:"BR",61:"AU",64:"NZ",66:"TH",81:"JP",82:"KR",84:"VN",86:"CN",90:"TR",105:"JS",213:"DZ",216:"MA",218:"LY",351:"PT",354:"IS",358:"FI",420:"CZ",886:"TW",961:"LB",962:"JO",963:"SY",964:"IQ",965:"KW",966:"SA",971:"AE",972:"IL",974:"QA",981:"IR",65535:"US"},k1=[null,"solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"];function T1(e){return e.map(function(t){return[t>>16&255,t>>8&255,t&255]})}var E1=T1([0,16777215,16711680,65280,255,16776960,16711935,65535,0,16777215,16711680,65280,255,16776960,16711935,65535,8388608,32768,128,8421376,8388736,32896,12632256,8421504,10066431,10040166,16777164,13434879,6684774,16744576,26316,13421823,128,16711935,16776960,65535,8388736,8388608,32896,255,52479,13434879,13434828,16777113,10079487,16751052,13408767,16764057,3368703,3394764,10079232,16763904,16750848,16737792,6710937,9868950,13158,3381606,13056,3355392,10040064,10040166,3355545,3355443,16777215,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]),Pt=Be(E1),pt={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},Jf={"#NULL!":0,"#DIV/0!":7,"#VALUE!":15,"#REF!":23,"#NAME?":29,"#NUM!":36,"#N/A":42,"#GETTING_DATA":43,"#WTF?":255},ai={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"},nn={workbooks:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.main+xml",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.main",xlam:"application/vnd.ms-excel.addin.macroEnabled.main+xml",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml"},strs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml",xlsb:"application/vnd.ms-excel.sharedStrings"},comments:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml",xlsb:"application/vnd.ms-excel.comments"},sheets:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml",xlsb:"application/vnd.ms-excel.worksheet"},charts:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml",xlsb:"application/vnd.ms-excel.chartsheet"},dialogs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml",xlsb:"application/vnd.ms-excel.dialogsheet"},macros:{xlsx:"application/vnd.ms-excel.macrosheet+xml",xlsb:"application/vnd.ms-excel.macrosheet"},metadata:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml",xlsb:"application/vnd.ms-excel.sheetMetadata"},styles:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml",xlsb:"application/vnd.ms-excel.styles"}};function Di(){return{workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""}}function S1(e){var t=Di();if(!e||!e.match)return t;var r={};if((e.match(Er)||[]).forEach(function(a){var n=ge(a);switch(n[0].replace(Pl,"<")){case"<?xml":break;case"<Types":t.xmlns=n["xmlns"+(n[0].match(/<(\w+):/)||["",""])[1]];break;case"<Default":r[n.Extension]=n.ContentType;break;case"<Override":t[ai[n.ContentType]]!==void 0&&t[ai[n.ContentType]].push(n.PartName);break}}),t.xmlns!==nr.CT)throw new Error("Unknown Namespace: "+t.xmlns);return t.calcchain=t.calcchains.length>0?t.calcchains[0]:"",t.sst=t.strs.length>0?t.strs[0]:"",t.style=t.styles.length>0?t.styles[0]:"",t.defaults=r,delete t.calcchains,t}function Zf(e,t){var r=Sl(ai),a=[],n;a[a.length]=Qe,a[a.length]=ae("Types",null,{xmlns:nr.CT,"xmlns:xsd":nr.xsd,"xmlns:xsi":nr.xsi}),a=a.concat([["xml","application/xml"],["bin","application/vnd.ms-excel.sheet.binary.macroEnabled.main"],["vml","application/vnd.openxmlformats-officedocument.vmlDrawing"],["data","application/vnd.openxmlformats-officedocument.model+data"],["bmp","image/bmp"],["png","image/png"],["gif","image/gif"],["emf","image/x-emf"],["wmf","image/x-wmf"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["tif","image/tiff"],["tiff","image/tiff"],["pdf","application/pdf"],["rels","application/vnd.openxmlformats-package.relationships+xml"]].map(function(c){return ae("Default",null,{Extension:c[0],ContentType:c[1]})}));var i=function(c){e[c]&&e[c].length>0&&(n=e[c][0],a[a.length]=ae("Override",null,{PartName:(n[0]=="/"?"":"/")+n,ContentType:nn[c][t.bookType]||nn[c].xlsx}))},s=function(c){(e[c]||[]).forEach(function(o){a[a.length]=ae("Override",null,{PartName:(o[0]=="/"?"":"/")+o,ContentType:nn[c][t.bookType]||nn[c].xlsx})})},f=function(c){(e[c]||[]).forEach(function(o){a[a.length]=ae("Override",null,{PartName:(o[0]=="/"?"":"/")+o,ContentType:r[c][0]})})};return i("workbooks"),s("sheets"),s("charts"),f("themes"),["strs","styles"].forEach(i),["coreprops","extprops","custprops"].forEach(f),f("vba"),f("comments"),f("threadedcomments"),f("drawings"),s("metadata"),f("people"),a.length>2&&(a[a.length]="</Types>",a[1]=a[1].replace("/>",">")),a.join("")}var xe={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",SHEET:"http://sheetjs.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",XLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLink",CXML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXml",CXMLP:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXmlProps",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",CHART:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chart",CHARTEX:"http://schemas.microsoft.com/office/2014/relationships/chartEx",CS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chartsheet",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/dialogsheet",MS:"http://schemas.microsoft.com/office/2006/relationships/xlMacrosheet",IMG:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function Ma(e){var t=e.lastIndexOf("/");return e.slice(0,t+1)+"_rels/"+e.slice(t+1)+".rels"}function Aa(e,t){var r={"!id":{}};if(!e)return r;t.charAt(0)!=="/"&&(t="/"+t);var a={};return(e.match(Er)||[]).forEach(function(n){var i=ge(n);if(i[0]==="<Relationship"){var s={};s.Type=i.Type,s.Target=i.Target,s.Id=i.Id,i.TargetMode&&(s.TargetMode=i.TargetMode);var f=i.TargetMode==="External"?i.Target:Ea(i.Target,t);r[f]=s,a[i.Id]=s}}),r["!id"]=a,r}function na(e){var t=[Qe,ae("Relationships",null,{xmlns:nr.RELS})];return je(e["!id"]).forEach(function(r){t[t.length]=ae("Relationship",null,e["!id"][r])}),t.length>2&&(t[t.length]="</Relationships>",t[1]=t[1].replace("/>",">")),t.join("")}function Pe(e,t,r,a,n,i){if(n||(n={}),e["!id"]||(e["!id"]={}),e["!idx"]||(e["!idx"]=1),t<0)for(t=e["!idx"];e["!id"]["rId"+t];++t);if(e["!idx"]=t+1,n.Id="rId"+t,n.Type=a,n.Target=r,[xe.HLINK,xe.XPATH,xe.XMISS].indexOf(n.Type)>-1&&(n.TargetMode="External"),e["!id"][n.Id])throw new Error("Cannot rewrite rId "+t);return e["!id"][n.Id]=n,e[("/"+n.Target).replace("//","/")]=n,t}var y1="application/vnd.oasis.opendocument.spreadsheet";function x1(e,t){for(var r=ki(e),a,n;a=ba.exec(r);)switch(a[3]){case"manifest":break;case"file-entry":if(n=ge(a[0],!1),n.path=="/"&&n.type!==y1)throw new Error("This OpenDocument is not a spreadsheet");break;case"encryption-data":case"algorithm":case"start-key-generation":case"key-derivation":throw new Error("Unsupported ODS Encryption");default:if(t&&t.WTF)throw a}}function A1(e){var t=[Qe];t.push(`<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.2">
`),t.push(`  <manifest:file-entry manifest:full-path="/" manifest:version="1.2" manifest:media-type="application/vnd.oasis.opendocument.spreadsheet"/>
`);for(var r=0;r<e.length;++r)t.push('  <manifest:file-entry manifest:full-path="'+e[r][0]+'" manifest:media-type="'+e[r][1]+`"/>
`);return t.push("</manifest:manifest>"),t.join("")}function Ps(e,t,r){return['  <rdf:Description rdf:about="'+e+`">
`,'    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/'+(r||"odf")+"#"+t+`"/>
`,`  </rdf:Description>
`].join("")}function C1(e,t){return['  <rdf:Description rdf:about="'+e+`">
`,'    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="'+t+`"/>
`,`  </rdf:Description>
`].join("")}function O1(e){var t=[Qe];t.push(`<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
`);for(var r=0;r!=e.length;++r)t.push(Ps(e[r][0],e[r][1])),t.push(C1("",e[r][0]));return t.push(Ps("","Document","pkg")),t.push("</rdf:RDF>"),t.join("")}function qf(){return'<office:document-meta xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xlink="http://www.w3.org/1999/xlink" office:version="1.2"><office:meta><meta:generator>SheetJS '+Ra.version+"</meta:generator></office:meta></office:document-meta>"}var zr=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]],I1=function(){for(var e=new Array(zr.length),t=0;t<zr.length;++t){var r=zr[t],a="(?:"+r[0].slice(0,r[0].indexOf(":"))+":)"+r[0].slice(r[0].indexOf(":")+1);e[t]=new RegExp("<"+a+"[^>]*>([\\s\\S]*?)</"+a+">")}return e}();function Qf(e){var t={};e=be(e);for(var r=0;r<zr.length;++r){var a=zr[r],n=e.match(I1[r]);n!=null&&n.length>0&&(t[a[1]]=Fe(n[1])),a[2]==="date"&&t[a[1]]&&(t[a[1]]=Ve(t[a[1]]))}return t}function Hn(e,t,r,a,n){n[e]!=null||t==null||t===""||(n[e]=t,t=De(t),a[a.length]=r?ae(e,t,r):ur(e,t))}function ec(e,t){var r=t||{},a=[Qe,ae("cp:coreProperties",null,{"xmlns:cp":nr.CORE_PROPS,"xmlns:dc":nr.dc,"xmlns:dcterms":nr.dcterms,"xmlns:dcmitype":nr.dcmitype,"xmlns:xsi":nr.xsi})],n={};if(!e&&!r.Props)return a.join("");e&&(e.CreatedDate!=null&&Hn("dcterms:created",typeof e.CreatedDate=="string"?e.CreatedDate:Qn(e.CreatedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},a,n),e.ModifiedDate!=null&&Hn("dcterms:modified",typeof e.ModifiedDate=="string"?e.ModifiedDate:Qn(e.ModifiedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},a,n));for(var i=0;i!=zr.length;++i){var s=zr[i],f=r.Props&&r.Props[s[1]]!=null?r.Props[s[1]]:e?e[s[1]]:null;f===!0?f="1":f===!1?f="0":typeof f=="number"&&(f=String(f)),f!=null&&Hn(s[0],f,null,a,n)}return a.length>2&&(a[a.length]="</cp:coreProperties>",a[1]=a[1].replace("/>",">")),a.join("")}var Dt=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]],rc=["Worksheets","SheetNames","NamedRanges","DefinedNames","Chartsheets","ChartNames"];function tc(e,t,r,a){var n=[];if(typeof e=="string")n=Ss(e,a);else for(var i=0;i<e.length;++i)n=n.concat(e[i].map(function(l){return{v:l}}));var s=typeof t=="string"?Ss(t,a).map(function(l){return l.v}):t,f=0,c=0;if(s.length>0)for(var o=0;o!==n.length;o+=2){switch(c=+n[o+1].v,n[o].v){case"Worksheets":case"工作表":case"Листы":case"أوراق العمل":case"ワークシート":case"גליונות עבודה":case"Arbeitsblätter":case"Çalışma Sayfaları":case"Feuilles de calcul":case"Fogli di lavoro":case"Folhas de cálculo":case"Planilhas":case"Regneark":case"Hojas de cálculo":case"Werkbladen":r.Worksheets=c,r.SheetNames=s.slice(f,f+c);break;case"Named Ranges":case"Rangos con nombre":case"名前付き一覧":case"Benannte Bereiche":case"Navngivne områder":r.NamedRanges=c,r.DefinedNames=s.slice(f,f+c);break;case"Charts":case"Diagramme":r.Chartsheets=c,r.ChartNames=s.slice(f,f+c);break}f+=c}}function F1(e,t,r){var a={};return t||(t={}),e=be(e),Dt.forEach(function(n){var i=(e.match(Da(n[0]))||[])[1];switch(n[2]){case"string":i&&(t[n[1]]=Fe(i));break;case"bool":t[n[1]]=i==="true";break;case"raw":var s=e.match(new RegExp("<"+n[0]+"[^>]*>([\\s\\S]*?)</"+n[0]+">"));s&&s.length>0&&(a[n[1]]=s[1]);break}}),a.HeadingPairs&&a.TitlesOfParts&&tc(a.HeadingPairs,a.TitlesOfParts,t,r),t}function ac(e){var t=[],r=ae;return e||(e={}),e.Application="SheetJS",t[t.length]=Qe,t[t.length]=ae("Properties",null,{xmlns:nr.EXT_PROPS,"xmlns:vt":nr.vt}),Dt.forEach(function(a){if(e[a[1]]!==void 0){var n;switch(a[2]){case"string":n=De(String(e[a[1]]));break;case"bool":n=e[a[1]]?"true":"false";break}n!==void 0&&(t[t.length]=r(a[0],n))}}),t[t.length]=r("HeadingPairs",r("vt:vector",r("vt:variant","<vt:lpstr>Worksheets</vt:lpstr>")+r("vt:variant",r("vt:i4",String(e.Worksheets))),{size:2,baseType:"variant"})),t[t.length]=r("TitlesOfParts",r("vt:vector",e.SheetNames.map(function(a){return"<vt:lpstr>"+De(a)+"</vt:lpstr>"}).join(""),{size:e.Worksheets,baseType:"lpstr"})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var R1=/<[^>]+>[^<]*/g;function N1(e,t){var r={},a="",n=e.match(R1);if(n)for(var i=0;i!=n.length;++i){var s=n[i],f=ge(s);switch(f[0]){case"<?xml":break;case"<Properties":break;case"<property":a=Fe(f.name);break;case"</property>":a=null;break;default:if(s.indexOf("<vt:")===0){var c=s.split(">"),o=c[0].slice(4),l=c[1];switch(o){case"lpstr":case"bstr":case"lpwstr":r[a]=Fe(l);break;case"bool":r[a]=We(l);break;case"i1":case"i2":case"i4":case"i8":case"int":case"uint":r[a]=parseInt(l,10);break;case"r4":case"r8":case"decimal":r[a]=parseFloat(l);break;case"filetime":case"date":r[a]=Ve(l);break;case"cy":case"error":r[a]=Fe(l);break;default:if(o.slice(-1)=="/")break;t.WTF&&typeof console<"u"&&console.warn("Unexpected",s,o,c)}}else if(s.slice(0,2)!=="</"){if(t.WTF)throw new Error(s)}}}return r}function nc(e){var t=[Qe,ae("Properties",null,{xmlns:nr.CUST_PROPS,"xmlns:vt":nr.vt})];if(!e)return t.join("");var r=1;return je(e).forEach(function(n){++r,t[t.length]=ae("property",Hl(e[n]),{fmtid:"{D5CDD505-2E9C-101B-9397-08002B2CF9AE}",pid:r,name:De(n)})}),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var ni={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"},Vn;function P1(e,t,r){Vn||(Vn=Cn(ni)),t=Vn[t]||t,e[t]=r}function D1(e,t){var r=[];return je(ni).map(function(a){for(var n=0;n<zr.length;++n)if(zr[n][1]==a)return zr[n];for(n=0;n<Dt.length;++n)if(Dt[n][1]==a)return Dt[n];throw a}).forEach(function(a){if(e[a[1]]!=null){var n=t&&t.Props&&t.Props[a[1]]!=null?t.Props[a[1]]:e[a[1]];switch(a[2]){case"date":n=new Date(n).toISOString().replace(/\.\d*Z/,"Z");break}typeof n=="number"?n=String(n):n===!0||n===!1?n=n?"1":"0":n instanceof Date&&(n=new Date(n).toISOString().replace(/\.\d*Z/,"")),r.push(ur(ni[a[1]]||a[1],n))}}),ae("DocumentProperties",r.join(""),{xmlns:Fr.o})}function L1(e,t){var r=["Worksheets","SheetNames"],a="CustomDocumentProperties",n=[];return e&&je(e).forEach(function(i){if(Object.prototype.hasOwnProperty.call(e,i)){for(var s=0;s<zr.length;++s)if(i==zr[s][1])return;for(s=0;s<Dt.length;++s)if(i==Dt[s][1])return;for(s=0;s<r.length;++s)if(i==r[s])return;var f=e[i],c="string";typeof f=="number"?(c="float",f=String(f)):f===!0||f===!1?(c="boolean",f=f?"1":"0"):f=String(f),n.push(ae(ws(i),f,{"dt:dt":c}))}}),t&&je(t).forEach(function(i){if(Object.prototype.hasOwnProperty.call(t,i)&&!(e&&Object.prototype.hasOwnProperty.call(e,i))){var s=t[i],f="string";typeof s=="number"?(f="float",s=String(s)):s===!0||s===!1?(f="boolean",s=s?"1":"0"):s instanceof Date?(f="dateTime.tz",s=s.toISOString()):s=String(s),n.push(ae(ws(i),s,{"dt:dt":f}))}}),"<"+a+' xmlns="'+Fr.o+'">'+n.join("")+"</"+a+">"}function Li(e){var t=e.read_shift(4),r=e.read_shift(4);return new Date((r/1e7*Math.pow(2,32)+t/1e7-11644473600)*1e3).toISOString().replace(/\.000/,"")}function b1(e){var t=typeof e=="string"?new Date(Date.parse(e)):e,r=t.getTime()/1e3+11644473600,a=r%Math.pow(2,32),n=(r-a)/Math.pow(2,32);a*=1e7,n*=1e7;var i=a/Math.pow(2,32)|0;i>0&&(a=a%Math.pow(2,32),n+=i);var s=G(8);return s.write_shift(4,a),s.write_shift(4,n),s}function ic(e,t,r){var a=e.l,n=e.read_shift(0,"lpstr-cp");if(r)for(;e.l-a&3;)++e.l;return n}function sc(e,t,r){var a=e.read_shift(0,"lpwstr");return a}function fc(e,t,r){return t===31?sc(e):ic(e,t,r)}function ii(e,t,r){return fc(e,t,r===!1?0:4)}function M1(e,t){if(!t)throw new Error("VtUnalignedString must have positive length");return fc(e,t,0)}function B1(e){for(var t=e.read_shift(4),r=[],a=0;a!=t;++a){var n=e.l;r[a]=e.read_shift(0,"lpwstr").replace(wr,""),e.l-n&2&&(e.l+=2)}return r}function U1(e){for(var t=e.read_shift(4),r=[],a=0;a!=t;++a)r[a]=e.read_shift(0,"lpstr-cp").replace(wr,"");return r}function W1(e){var t=e.l,r=Tn(e,jf);e[e.l]==0&&e[e.l+1]==0&&e.l-t&2&&(e.l+=2);var a=Tn(e,Cr);return[r,a]}function H1(e){for(var t=e.read_shift(4),r=[],a=0;a<t/2;++a)r.push(W1(e));return r}function Ds(e,t){for(var r=e.read_shift(4),a={},n=0;n!=r;++n){var i=e.read_shift(4),s=e.read_shift(4);a[i]=e.read_shift(s,t===1200?"utf16le":"utf8").replace(wr,"").replace(Ta,"!"),t===1200&&s%2&&(e.l+=2)}return e.l&3&&(e.l=e.l>>3<<2),a}function cc(e){var t=e.read_shift(4),r=e.slice(e.l,e.l+t);return e.l+=t,(t&3)>0&&(e.l+=4-(t&3)&3),r}function V1(e){var t={};return t.Size=e.read_shift(4),e.l+=t.Size+3-(t.Size-1)%4,t}function Tn(e,t,r){var a=e.read_shift(2),n,i=r||{};if(e.l+=2,t!==Rs&&a!==t&&w1.indexOf(t)===-1&&!((t&65534)==4126&&(a&65534)==4126))throw new Error("Expected type "+t+" saw "+a);switch(t===Rs?a:t){case 2:return n=e.read_shift(2,"i"),i.raw||(e.l+=2),n;case 3:return n=e.read_shift(4,"i"),n;case 11:return e.read_shift(4)!==0;case 19:return n=e.read_shift(4),n;case 30:return ic(e,a,4).replace(wr,"");case 31:return sc(e);case 64:return Li(e);case 65:return cc(e);case 71:return V1(e);case 80:return ii(e,a,!i.raw).replace(wr,"");case 81:return M1(e,a).replace(wr,"");case 4108:return H1(e);case 4126:case 4127:return a==4127?B1(e):U1(e);default:throw new Error("TypedPropertyValue unrecognized type "+t+" "+a)}}function Ls(e,t){var r=G(4),a=G(4);switch(r.write_shift(4,e==80?31:e),e){case 3:a.write_shift(-4,t);break;case 5:a=G(8),a.write_shift(8,t,"f");break;case 11:a.write_shift(4,t?1:0);break;case 64:a=b1(t);break;case 31:case 80:for(a=G(4+2*(t.length+1)+(t.length%2?0:2)),a.write_shift(4,t.length+1),a.write_shift(0,t,"dbcs");a.l!=a.length;)a.write_shift(1,0);break;default:throw new Error("TypedPropertyValue unrecognized type "+e+" "+t)}return cr([r,a])}function bs(e,t){var r=e.l,a=e.read_shift(4),n=e.read_shift(4),i=[],s=0,f=0,c=-1,o={};for(s=0;s!=n;++s){var l=e.read_shift(4),h=e.read_shift(4);i[s]=[l,h+r]}i.sort(function(A,_){return A[1]-_[1]});var d={};for(s=0;s!=n;++s){if(e.l!==i[s][1]){var v=!0;if(s>0&&t)switch(t[i[s-1][0]].t){case 2:e.l+2===i[s][1]&&(e.l+=2,v=!1);break;case 80:e.l<=i[s][1]&&(e.l=i[s][1],v=!1);break;case 4108:e.l<=i[s][1]&&(e.l=i[s][1],v=!1);break}if((!t||s==0)&&e.l<=i[s][1]&&(v=!1,e.l=i[s][1]),v)throw new Error("Read Error: Expected address "+i[s][1]+" at "+e.l+" :"+s)}if(t){var p=t[i[s][0]];if(d[p.n]=Tn(e,p.t,{raw:!0}),p.p==="version"&&(d[p.n]=String(d[p.n]>>16)+"."+("0000"+String(d[p.n]&65535)).slice(-4)),p.n=="CodePage")switch(d[p.n]){case 0:d[p.n]=1252;case 874:case 932:case 936:case 949:case 950:case 1250:case 1251:case 1253:case 1254:case 1255:case 1256:case 1257:case 1258:case 1e4:case 1200:case 1201:case 1252:case 65e3:case-536:case 65001:case-535:Gr(f=d[p.n]>>>0&65535);break;default:throw new Error("Unsupported CodePage: "+d[p.n])}}else if(i[s][0]===1){if(f=d.CodePage=Tn(e,Pi),Gr(f),c!==-1){var u=e.l;e.l=i[c][1],o=Ds(e,f),e.l=u}}else if(i[s][0]===0){if(f===0){c=s,e.l=i[s+1][1];continue}o=Ds(e,f)}else{var m=o[i[s][0]],k;switch(e[e.l]){case 65:e.l+=4,k=cc(e);break;case 30:e.l+=4,k=ii(e,e[e.l-4]).replace(/\u0000+$/,"");break;case 31:e.l+=4,k=ii(e,e[e.l-4]).replace(/\u0000+$/,"");break;case 3:e.l+=4,k=e.read_shift(4,"i");break;case 19:e.l+=4,k=e.read_shift(4);break;case 5:e.l+=4,k=e.read_shift(8,"f");break;case 11:e.l+=4,k=qe(e,4);break;case 64:e.l+=4,k=Ve(Li(e));break;default:throw new Error("unparsed value: "+e[e.l])}d[m]=k}}return e.l=r+a,d}var oc=["CodePage","Thumbnail","_PID_LINKBASE","_PID_HLINKS","SystemIdentifier","FMTID"];function X1(e){switch(typeof e){case"boolean":return 11;case"number":return(e|0)==e?3:5;case"string":return 31;case"object":if(e instanceof Date)return 64;break}return-1}function Ms(e,t,r){var a=G(8),n=[],i=[],s=8,f=0,c=G(8),o=G(8);if(c.write_shift(4,2),c.write_shift(4,1200),o.write_shift(4,1),i.push(c),n.push(o),s+=8+c.length,!t){o=G(8),o.write_shift(4,0),n.unshift(o);var l=[G(4)];for(l[0].write_shift(4,e.length),f=0;f<e.length;++f){var h=e[f][0];for(c=G(8+2*(h.length+1)+(h.length%2?0:2)),c.write_shift(4,f+2),c.write_shift(4,h.length+1),c.write_shift(0,h,"dbcs");c.l!=c.length;)c.write_shift(1,0);l.push(c)}c=cr(l),i.unshift(c),s+=8+c.length}for(f=0;f<e.length;++f)if(!(t&&!t[e[f][0]])&&!(oc.indexOf(e[f][0])>-1||rc.indexOf(e[f][0])>-1)&&e[f][1]!=null){var d=e[f][1],v=0;if(t){v=+t[e[f][0]];var p=r[v];if(p.p=="version"&&typeof d=="string"){var u=d.split(".");d=(+u[0]<<16)+(+u[1]||0)}c=Ls(p.t,d)}else{var m=X1(d);m==-1&&(m=31,d=String(d)),c=Ls(m,d)}i.push(c),o=G(8),o.write_shift(4,t?v:2+f),n.push(o),s+=8+c.length}var k=8*(i.length+1);for(f=0;f<i.length;++f)n[f].write_shift(4,k),k+=i[f].length;return a.write_shift(4,s),a.write_shift(4,i.length),cr([a].concat(n).concat(i))}function Bs(e,t,r){var a=e.content;if(!a)return{};hr(a,0);var n,i,s,f,c=0;a.chk("feff","Byte Order: "),a.read_shift(2);var o=a.read_shift(4),l=a.read_shift(16);if(l!==de.utils.consts.HEADER_CLSID&&l!==r)throw new Error("Bad PropertySet CLSID "+l);if(n=a.read_shift(4),n!==1&&n!==2)throw new Error("Unrecognized #Sets: "+n);if(i=a.read_shift(16),f=a.read_shift(4),n===1&&f!==a.l)throw new Error("Length mismatch: "+f+" !== "+a.l);n===2&&(s=a.read_shift(16),c=a.read_shift(4));var h=bs(a,t),d={SystemIdentifier:o};for(var v in h)d[v]=h[v];if(d.FMTID=i,n===1)return d;if(c-a.l==2&&(a.l+=2),a.l!==c)throw new Error("Length mismatch 2: "+a.l+" !== "+c);var p;try{p=bs(a,null)}catch{}for(v in p)d[v]=p[v];return d.FMTID=[i,s],d}function Us(e,t,r,a,n,i){var s=G(n?68:48),f=[s];s.write_shift(2,65534),s.write_shift(2,0),s.write_shift(4,842412599),s.write_shift(16,de.utils.consts.HEADER_CLSID,"hex"),s.write_shift(4,n?2:1),s.write_shift(16,t,"hex"),s.write_shift(4,n?68:48);var c=Ms(e,r,a);if(f.push(c),n){var o=Ms(n,null,null);s.write_shift(16,i,"hex"),s.write_shift(4,68+c.length),f.push(o)}return cr(f)}function _t(e,t){return e.read_shift(t),null}function G1(e,t){t||(t=G(e));for(var r=0;r<e;++r)t.write_shift(1,0);return t}function z1(e,t,r){for(var a=[],n=e.l+t;e.l<n;)a.push(r(e,n-e.l));if(n!==e.l)throw new Error("Slurp error");return a}function qe(e,t){return e.read_shift(t)===1}function xr(e,t){return t||(t=G(2)),t.write_shift(2,+!!e),t}function tr(e){return e.read_shift(2,"u")}function Xr(e,t){return t||(t=G(2)),t.write_shift(2,e),t}function lc(e,t){return z1(e,t,tr)}function $1(e){var t=e.read_shift(1),r=e.read_shift(1);return r===1?t:t===1}function hc(e,t,r){return r||(r=G(2)),r.write_shift(1,t=="e"?+e:+!!e),r.write_shift(1,t=="e"?1:0),r}function Ya(e,t,r){var a=e.read_shift(r&&r.biff>=12?2:1),n="sbcs-cont",i=Pr;if(r&&r.biff>=8&&(Pr=1200),!r||r.biff==8){var s=e.read_shift(1);s&&(n="dbcs-cont")}else r.biff==12&&(n="wstr");r.biff>=2&&r.biff<=5&&(n="cpstr");var f=a?e.read_shift(a,n):"";return Pr=i,f}function K1(e){var t=Pr;Pr=1200;var r=e.read_shift(2),a=e.read_shift(1),n=a&4,i=a&8,s=1+(a&1),f=0,c,o={};i&&(f=e.read_shift(2)),n&&(c=e.read_shift(4));var l=s==2?"dbcs-cont":"sbcs-cont",h=r===0?"":e.read_shift(r,l);return i&&(e.l+=4*f),n&&(e.l+=c),o.t=h,i||(o.raw="<t>"+o.t+"</t>",o.r=o.t),Pr=t,o}function Y1(e){var t=e.t||"",r=G(3);r.write_shift(2,t.length),r.write_shift(1,1);var a=G(2*t.length);a.write_shift(2*t.length,t,"utf16le");var n=[r,a];return cr(n)}function Ht(e,t,r){var a;if(r){if(r.biff>=2&&r.biff<=5)return e.read_shift(t,"cpstr");if(r.biff>=12)return e.read_shift(t,"dbcs-cont")}var n=e.read_shift(1);return n===0?a=e.read_shift(t,"sbcs-cont"):a=e.read_shift(t,"dbcs-cont"),a}function ja(e,t,r){var a=e.read_shift(r&&r.biff==2?1:2);return a===0?(e.l++,""):Ht(e,a,r)}function Jt(e,t,r){if(r.biff>5)return ja(e,t,r);var a=e.read_shift(1);return a===0?(e.l++,""):e.read_shift(a,r.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function uc(e,t,r){return r||(r=G(3+2*e.length)),r.write_shift(2,e.length),r.write_shift(1,1),r.write_shift(31,e,"utf16le"),r}function j1(e){var t=e.read_shift(1);e.l++;var r=e.read_shift(2);return e.l+=2,[t,r]}function J1(e){var t=e.read_shift(4),r=e.l,a=!1;t>24&&(e.l+=t-24,e.read_shift(16)==="795881f43b1d7f48af2c825dc4852763"&&(a=!0),e.l=r);var n=e.read_shift((a?t-24:t)>>1,"utf16le").replace(wr,"");return a&&(e.l+=24),n}function Z1(e){for(var t=e.read_shift(2),r="";t-- >0;)r+="../";var a=e.read_shift(0,"lpstr-ansi");if(e.l+=2,e.read_shift(2)!=57005)throw new Error("Bad FileMoniker");var n=e.read_shift(4);if(n===0)return r+a.replace(/\\/g,"/");var i=e.read_shift(4);if(e.read_shift(2)!=3)throw new Error("Bad FileMoniker");var s=e.read_shift(i>>1,"utf16le").replace(wr,"");return r+s}function q1(e,t){var r=e.read_shift(16);switch(r){case"e0c9ea79f9bace118c8200aa004ba90b":return J1(e);case"0303000000000000c000000000000046":return Z1(e);default:throw new Error("Unsupported Moniker "+r)}}function sn(e){var t=e.read_shift(4),r=t>0?e.read_shift(t,"utf16le").replace(wr,""):"";return r}function Ws(e,t){t||(t=G(6+e.length*2)),t.write_shift(4,1+e.length);for(var r=0;r<e.length;++r)t.write_shift(2,e.charCodeAt(r));return t.write_shift(2,0),t}function Q1(e,t){var r=e.l+t,a=e.read_shift(4);if(a!==2)throw new Error("Unrecognized streamVersion: "+a);var n=e.read_shift(2);e.l+=2;var i,s,f,c,o="",l,h;n&16&&(i=sn(e,r-e.l)),n&128&&(s=sn(e,r-e.l)),(n&257)===257&&(f=sn(e,r-e.l)),(n&257)===1&&(c=q1(e,r-e.l)),n&8&&(o=sn(e,r-e.l)),n&32&&(l=e.read_shift(16)),n&64&&(h=Li(e)),e.l=r;var d=s||f||c||"";d&&o&&(d+="#"+o),d||(d="#"+o),n&2&&d.charAt(0)=="/"&&d.charAt(1)!="/"&&(d="file://"+d);var v={Target:d};return l&&(v.guid=l),h&&(v.time=h),i&&(v.Tooltip=i),v}function eh(e){var t=G(512),r=0,a=e.Target;a.slice(0,7)=="file://"&&(a=a.slice(7));var n=a.indexOf("#"),i=n>-1?31:23;switch(a.charAt(0)){case"#":i=28;break;case".":i&=-3;break}t.write_shift(4,2),t.write_shift(4,i);var s=[8,6815827,6619237,4849780,83];for(r=0;r<s.length;++r)t.write_shift(4,s[r]);if(i==28)a=a.slice(1),Ws(a,t);else if(i&2){for(s="e0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),r=0;r<s.length;++r)t.write_shift(1,parseInt(s[r],16));var f=n>-1?a.slice(0,n):a;for(t.write_shift(4,2*(f.length+1)),r=0;r<f.length;++r)t.write_shift(2,f.charCodeAt(r));t.write_shift(2,0),i&8&&Ws(n>-1?a.slice(n+1):"",t)}else{for(s="03 03 00 00 00 00 00 00 c0 00 00 00 00 00 00 46".split(" "),r=0;r<s.length;++r)t.write_shift(1,parseInt(s[r],16));for(var c=0;a.slice(c*3,c*3+3)=="../"||a.slice(c*3,c*3+3)=="..\\";)++c;for(t.write_shift(2,c),t.write_shift(4,a.length-3*c+1),r=0;r<a.length-3*c;++r)t.write_shift(1,a.charCodeAt(r+3*c)&255);for(t.write_shift(1,0),t.write_shift(2,65535),t.write_shift(2,57005),r=0;r<6;++r)t.write_shift(4,0)}return t.slice(0,t.l)}function dc(e){var t=e.read_shift(1),r=e.read_shift(1),a=e.read_shift(1),n=e.read_shift(1);return[t,r,a,n]}function vc(e,t){var r=dc(e);return r[3]=0,r}function st(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(2);return{r:t,c:r,ixfe:a}}function Vt(e,t,r,a){return a||(a=G(6)),a.write_shift(2,e),a.write_shift(2,t),a.write_shift(2,r||0),a}function rh(e){var t=e.read_shift(2),r=e.read_shift(2);return e.l+=8,{type:t,flags:r}}function th(e,t,r){return t===0?"":Jt(e,t,r)}function ah(e,t,r){var a=r.biff>8?4:2,n=e.read_shift(a),i=e.read_shift(a,"i"),s=e.read_shift(a,"i");return[n,i,s]}function pc(e){var t=e.read_shift(2),r=Ni(e);return[t,r]}function nh(e,t,r){e.l+=4,t-=4;var a=e.l+t,n=Ya(e,t,r),i=e.read_shift(2);if(a-=e.l,i!==a)throw new Error("Malformed AddinUdf: padding = "+a+" != "+i);return e.l+=i,n}function Rn(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(2),n=e.read_shift(2);return{s:{c:a,r:t},e:{c:n,r}}}function mc(e,t){return t||(t=G(8)),t.write_shift(2,e.s.r),t.write_shift(2,e.e.r),t.write_shift(2,e.s.c),t.write_shift(2,e.e.c),t}function gc(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(1),n=e.read_shift(1);return{s:{c:a,r:t},e:{c:n,r}}}var ih=gc;function _c(e){e.l+=4;var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(2);return e.l+=12,[r,t,a]}function sh(e){var t={};return e.l+=4,e.l+=16,t.fSharedNote=e.read_shift(2),e.l+=4,t}function fh(e){var t={};return e.l+=4,e.cf=e.read_shift(2),t}function vr(e){e.l+=2,e.l+=e.read_shift(2)}var ch={0:vr,4:vr,5:vr,6:vr,7:fh,8:vr,9:vr,10:vr,11:vr,12:vr,13:sh,14:vr,15:vr,16:vr,17:vr,18:vr,19:vr,20:vr,21:_c};function oh(e,t){for(var r=e.l+t,a=[];e.l<r;){var n=e.read_shift(2);e.l-=2;try{a.push(ch[n](e,r-e.l))}catch{return e.l=r,a}}return e.l!=r&&(e.l=r),a}function fn(e,t){var r={BIFFVer:0,dt:0};switch(r.BIFFVer=e.read_shift(2),t-=2,t>=2&&(r.dt=e.read_shift(2),e.l-=2),r.BIFFVer){case 1536:case 1280:case 1024:case 768:case 512:case 2:case 7:break;default:if(t>6)throw new Error("Unexpected BIFF Ver "+r.BIFFVer)}return e.read_shift(t),r}function bi(e,t,r){var a=1536,n=16;switch(r.bookType){case"biff8":break;case"biff5":a=1280,n=8;break;case"biff4":a=4,n=6;break;case"biff3":a=3,n=6;break;case"biff2":a=2,n=4;break;case"xla":break;default:throw new Error("unsupported BIFF version")}var i=G(n);return i.write_shift(2,a),i.write_shift(2,t),n>4&&i.write_shift(2,29282),n>6&&i.write_shift(2,1997),n>8&&(i.write_shift(2,49161),i.write_shift(2,1),i.write_shift(2,1798),i.write_shift(2,0)),i}function lh(e,t){return t===0||e.read_shift(2),1200}function hh(e,t,r){if(r.enc)return e.l+=t,"";var a=e.l,n=Jt(e,0,r);return e.read_shift(t+a-e.l),n}function uh(e,t){var r=!t||t.biff==8,a=G(r?112:54);for(a.write_shift(t.biff==8?2:1,7),r&&a.write_shift(1,0),a.write_shift(4,859007059),a.write_shift(4,5458548|(r?0:536870912));a.l<a.length;)a.write_shift(1,r?0:32);return a}function dh(e,t,r){var a=r&&r.biff==8||t==2?e.read_shift(2):(e.l+=t,0);return{fDialog:a&16,fBelow:a&64,fRight:a&128}}function vh(e,t,r){var a=e.read_shift(4),n=e.read_shift(1)&3,i=e.read_shift(1);switch(i){case 0:i="Worksheet";break;case 1:i="Macrosheet";break;case 2:i="Chartsheet";break;case 6:i="VBAModule";break}var s=Ya(e,0,r);return s.length===0&&(s="Sheet1"),{pos:a,hs:n,dt:i,name:s}}function ph(e,t){var r=!t||t.biff>=8?2:1,a=G(8+r*e.name.length);a.write_shift(4,e.pos),a.write_shift(1,e.hs||0),a.write_shift(1,e.dt),a.write_shift(1,e.name.length),t.biff>=8&&a.write_shift(1,1),a.write_shift(r*e.name.length,e.name,t.biff<8?"sbcs":"utf16le");var n=a.slice(0,a.l);return n.l=a.l,n}function mh(e,t){for(var r=e.l+t,a=e.read_shift(4),n=e.read_shift(4),i=[],s=0;s!=n&&e.l<r;++s)i.push(K1(e));return i.Count=a,i.Unique=n,i}function gh(e,t){var r=G(8);r.write_shift(4,e.Count),r.write_shift(4,e.Unique);for(var a=[],n=0;n<e.length;++n)a[n]=Y1(e[n]);var i=cr([r].concat(a));return i.parts=[r.length].concat(a.map(function(s){return s.length})),i}function _h(e,t){var r={};return r.dsst=e.read_shift(2),e.l+=t-2,r}function wh(e){var t={};t.r=e.read_shift(2),t.c=e.read_shift(2),t.cnt=e.read_shift(2)-t.c;var r=e.read_shift(2);e.l+=4;var a=e.read_shift(1);return e.l+=3,a&7&&(t.level=a&7),a&32&&(t.hidden=!0),a&64&&(t.hpt=r/20),t}function kh(e){var t=rh(e);if(t.type!=2211)throw new Error("Invalid Future Record "+t.type);var r=e.read_shift(4);return r!==0}function Th(e){return e.read_shift(2),e.read_shift(4)}function Hs(e,t,r){var a=0;r&&r.biff==2||(a=e.read_shift(2));var n=e.read_shift(2);r&&r.biff==2&&(a=1-(n>>15),n&=32767);var i={Unsynced:a&1,DyZero:(a&2)>>1,ExAsc:(a&4)>>2,ExDsc:(a&8)>>3};return[i,n]}function Eh(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(2),n=e.read_shift(2),i=e.read_shift(2),s=e.read_shift(2),f=e.read_shift(2),c=e.read_shift(2),o=e.read_shift(2);return{Pos:[t,r],Dim:[a,n],Flags:i,CurTab:s,FirstTab:f,Selected:c,TabRatio:o}}function Sh(){var e=G(18);return e.write_shift(2,0),e.write_shift(2,0),e.write_shift(2,29280),e.write_shift(2,17600),e.write_shift(2,56),e.write_shift(2,0),e.write_shift(2,0),e.write_shift(2,1),e.write_shift(2,500),e}function yh(e,t,r){if(r&&r.biff>=2&&r.biff<5)return{};var a=e.read_shift(2);return{RTL:a&64}}function xh(e){var t=G(18),r=1718;return e&&e.RTL&&(r|=64),t.write_shift(2,r),t.write_shift(4,0),t.write_shift(4,64),t.write_shift(4,0),t.write_shift(4,0),t}function Ah(){}function Ch(e,t,r){var a={dyHeight:e.read_shift(2),fl:e.read_shift(2)};switch(r&&r.biff||8){case 2:break;case 3:case 4:e.l+=2;break;default:e.l+=10;break}return a.name=Ya(e,0,r),a}function Oh(e,t){var r=e.name||"Arial",a=t&&t.biff==5,n=a?15+r.length:16+2*r.length,i=G(n);return i.write_shift(2,e.sz*20),i.write_shift(4,0),i.write_shift(2,400),i.write_shift(4,0),i.write_shift(2,0),i.write_shift(1,r.length),a||i.write_shift(1,1),i.write_shift((a?1:2)*r.length,r,a?"sbcs":"utf16le"),i}function Ih(e){var t=st(e);return t.isst=e.read_shift(4),t}function Fh(e,t,r,a){var n=G(10);return Vt(e,t,a,n),n.write_shift(4,r),n}function Rh(e,t,r){r.biffguess&&r.biff==2&&(r.biff=5);var a=e.l+t,n=st(e);r.biff==2&&e.l++;var i=ja(e,a-e.l,r);return n.val=i,n}function Nh(e,t,r,a,n){var i=!n||n.biff==8,s=G(8+ +i+(1+i)*r.length);return Vt(e,t,a,s),s.write_shift(2,r.length),i&&s.write_shift(1,1),s.write_shift((1+i)*r.length,r,i?"utf16le":"sbcs"),s}function Ph(e,t,r){var a=e.read_shift(2),n=Jt(e,0,r);return[a,n]}function Dh(e,t,r,a){var n=r&&r.biff==5;a||(a=G(n?3+t.length:5+2*t.length)),a.write_shift(2,e),a.write_shift(n?1:2,t.length),n||a.write_shift(1,1),a.write_shift((n?1:2)*t.length,t,n?"sbcs":"utf16le");var i=a.length>a.l?a.slice(0,a.l):a;return i.l==null&&(i.l=i.length),i}var Lh=Jt;function Vs(e,t,r){var a=e.l+t,n=r.biff==8||!r.biff?4:2,i=e.read_shift(n),s=e.read_shift(n),f=e.read_shift(2),c=e.read_shift(2);return e.l=a,{s:{r:i,c:f},e:{r:s,c}}}function bh(e,t){var r=t.biff==8||!t.biff?4:2,a=G(2*r+6);return a.write_shift(r,e.s.r),a.write_shift(r,e.e.r+1),a.write_shift(2,e.s.c),a.write_shift(2,e.e.c+1),a.write_shift(2,0),a}function Mh(e){var t=e.read_shift(2),r=e.read_shift(2),a=pc(e);return{r:t,c:r,ixfe:a[0],rknum:a[1]}}function Bh(e,t){for(var r=e.l+t-2,a=e.read_shift(2),n=e.read_shift(2),i=[];e.l<r;)i.push(pc(e));if(e.l!==r)throw new Error("MulRK read error");var s=e.read_shift(2);if(i.length!=s-n+1)throw new Error("MulRK length mismatch");return{r:a,c:n,C:s,rkrec:i}}function Uh(e,t){for(var r=e.l+t-2,a=e.read_shift(2),n=e.read_shift(2),i=[];e.l<r;)i.push(e.read_shift(2));if(e.l!==r)throw new Error("MulBlank read error");var s=e.read_shift(2);if(i.length!=s-n+1)throw new Error("MulBlank length mismatch");return{r:a,c:n,C:s,ixfe:i}}function Wh(e,t,r,a){var n={},i=e.read_shift(4),s=e.read_shift(4),f=e.read_shift(4),c=e.read_shift(2);return n.patternType=k1[f>>26],a.cellStyles&&(n.alc=i&7,n.fWrap=i>>3&1,n.alcV=i>>4&7,n.fJustLast=i>>7&1,n.trot=i>>8&255,n.cIndent=i>>16&15,n.fShrinkToFit=i>>20&1,n.iReadOrder=i>>22&2,n.fAtrNum=i>>26&1,n.fAtrFnt=i>>27&1,n.fAtrAlc=i>>28&1,n.fAtrBdr=i>>29&1,n.fAtrPat=i>>30&1,n.fAtrProt=i>>31&1,n.dgLeft=s&15,n.dgRight=s>>4&15,n.dgTop=s>>8&15,n.dgBottom=s>>12&15,n.icvLeft=s>>16&127,n.icvRight=s>>23&127,n.grbitDiag=s>>30&3,n.icvTop=f&127,n.icvBottom=f>>7&127,n.icvDiag=f>>14&127,n.dgDiag=f>>21&15,n.icvFore=c&127,n.icvBack=c>>7&127,n.fsxButton=c>>14&1),n}function Hh(e,t,r){var a={};return a.ifnt=e.read_shift(2),a.numFmtId=e.read_shift(2),a.flags=e.read_shift(2),a.fStyle=a.flags>>2&1,t-=6,a.data=Wh(e,t,a.fStyle,r),a}function Xs(e,t,r,a){var n=r&&r.biff==5;a||(a=G(n?16:20)),a.write_shift(2,0),e.style?(a.write_shift(2,e.numFmtId||0),a.write_shift(2,65524)):(a.write_shift(2,e.numFmtId||0),a.write_shift(2,t<<4));var i=0;return e.numFmtId>0&&n&&(i|=1024),a.write_shift(4,i),a.write_shift(4,0),n||a.write_shift(4,0),a.write_shift(2,0),a}function Vh(e){e.l+=4;var t=[e.read_shift(2),e.read_shift(2)];if(t[0]!==0&&t[0]--,t[1]!==0&&t[1]--,t[0]>7||t[1]>7)throw new Error("Bad Gutters: "+t.join("|"));return t}function Xh(e){var t=G(8);return t.write_shift(4,0),t.write_shift(2,0),t.write_shift(2,0),t}function Gs(e,t,r){var a=st(e);(r.biff==2||t==9)&&++e.l;var n=$1(e);return a.val=n,a.t=n===!0||n===!1?"b":"e",a}function Gh(e,t,r,a,n,i){var s=G(8);return Vt(e,t,a,s),hc(r,i,s),s}function zh(e,t,r){r.biffguess&&r.biff==2&&(r.biff=5);var a=st(e),n=_r(e);return a.val=n,a}function $h(e,t,r,a){var n=G(14);return Vt(e,t,a,n),Wt(r,n),n}var zs=th;function Kh(e,t,r){var a=e.l+t,n=e.read_shift(2),i=e.read_shift(2);if(r.sbcch=i,i==1025||i==14849)return[i,n];if(i<1||i>255)throw new Error("Unexpected SupBook type: "+i);for(var s=Ht(e,i),f=[];a>e.l;)f.push(ja(e));return[i,n,s,f]}function $s(e,t,r){var a=e.read_shift(2),n,i={fBuiltIn:a&1,fWantAdvise:a>>>1&1,fWantPict:a>>>2&1,fOle:a>>>3&1,fOleLink:a>>>4&1,cf:a>>>5&1023,fIcon:a>>>15&1};return r.sbcch===14849&&(n=nh(e,t-2,r)),i.body=n||e.read_shift(t-2),typeof n=="string"&&(i.Name=n),i}var Yh=["_xlnm.Consolidate_Area","_xlnm.Auto_Open","_xlnm.Auto_Close","_xlnm.Extract","_xlnm.Database","_xlnm.Criteria","_xlnm.Print_Area","_xlnm.Print_Titles","_xlnm.Recorder","_xlnm.Data_Form","_xlnm.Auto_Activate","_xlnm.Auto_Deactivate","_xlnm.Sheet_Title","_xlnm._FilterDatabase"];function Ks(e,t,r){var a=e.l+t,n=e.read_shift(2),i=e.read_shift(1),s=e.read_shift(1),f=e.read_shift(r&&r.biff==2?1:2),c=0;(!r||r.biff>=5)&&(r.biff!=5&&(e.l+=2),c=e.read_shift(2),r.biff==5&&(e.l+=2),e.l+=4);var o=Ht(e,s,r);n&32&&(o=Yh[o.charCodeAt(0)]);var l=a-e.l;r&&r.biff==2&&--l;var h=a==e.l||f===0||!(l>0)?[]:ov(e,l,r,f);return{chKey:i,Name:o,itab:c,rgce:h}}function wc(e,t,r){if(r.biff<8)return jh(e,t,r);for(var a=[],n=e.l+t,i=e.read_shift(r.biff>8?4:2);i--!==0;)a.push(ah(e,r.biff>8?12:6,r));if(e.l!=n)throw new Error("Bad ExternSheet: "+e.l+" != "+n);return a}function jh(e,t,r){e[e.l+1]==3&&e[e.l]++;var a=Ya(e,t,r);return a.charCodeAt(0)==3?a.slice(1):a}function Jh(e,t,r){if(r.biff<8){e.l+=t;return}var a=e.read_shift(2),n=e.read_shift(2),i=Ht(e,a,r),s=Ht(e,n,r);return[i,s]}function Zh(e,t,r){var a=gc(e);e.l++;var n=e.read_shift(1);return t-=8,[lv(e,t,r),n,a]}function Ys(e,t,r){var a=ih(e);switch(r.biff){case 2:e.l++,t-=7;break;case 3:case 4:e.l+=2,t-=8;break;default:e.l+=6,t-=12}return[a,fv(e,t,r)]}function qh(e){var t=e.read_shift(4)!==0,r=e.read_shift(4)!==0,a=e.read_shift(4);return[t,r,a]}function Qh(e,t,r){if(!(r.biff<8)){var a=e.read_shift(2),n=e.read_shift(2),i=e.read_shift(2),s=e.read_shift(2),f=Jt(e,0,r);return r.biff<8&&e.read_shift(1),[{r:a,c:n},f,s,i]}}function eu(e,t,r){return Qh(e,t,r)}function ru(e,t){for(var r=[],a=e.read_shift(2);a--;)r.push(Rn(e));return r}function tu(e){var t=G(2+e.length*8);t.write_shift(2,e.length);for(var r=0;r<e.length;++r)mc(e[r],t);return t}function au(e,t,r){if(r&&r.biff<8)return iu(e,t,r);var a=_c(e),n=oh(e,t-22,a[1]);return{cmo:a,ft:n}}var nu={8:function(e,t){var r=e.l+t;e.l+=10;var a=e.read_shift(2);e.l+=4,e.l+=2,e.l+=2,e.l+=2,e.l+=4;var n=e.read_shift(1);return e.l+=n,e.l=r,{fmt:a}}};function iu(e,t,r){e.l+=4;var a=e.read_shift(2),n=e.read_shift(2),i=e.read_shift(2);e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=6,t-=36;var s=[];return s.push((nu[a]||Tr)(e,t,r)),{cmo:[n,a,i],ft:s}}function su(e,t,r){var a=e.l,n="";try{e.l+=4;var i=(r.lastobj||{cmo:[0,0]}).cmo[1],s;[0,5,7,11,12,14].indexOf(i)==-1?e.l+=6:s=j1(e,6,r);var f=e.read_shift(2);e.read_shift(2),tr(e,2);var c=e.read_shift(2);e.l+=c;for(var o=1;o<e.lens.length-1;++o){if(e.l-a!=e.lens[o])throw new Error("TxO: bad continue record");var l=e[e.l],h=Ht(e,e.lens[o+1]-e.lens[o]-1);if(n+=h,n.length>=(l?f:2*f))break}if(n.length!==f&&n.length!==f*2)throw new Error("cchText: "+f+" != "+n.length);return e.l=a+t,{t:n}}catch{return e.l=a+t,{t:n}}}function fu(e,t){var r=Rn(e);e.l+=16;var a=Q1(e,t-24);return[r,a]}function cu(e){var t=G(24),r=ze(e[0]);t.write_shift(2,r.r),t.write_shift(2,r.r),t.write_shift(2,r.c),t.write_shift(2,r.c);for(var a="d0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),n=0;n<16;++n)t.write_shift(1,parseInt(a[n],16));return cr([t,eh(e[1])])}function ou(e,t){e.read_shift(2);var r=Rn(e),a=e.read_shift((t-10)/2,"dbcs-cont");return a=a.replace(wr,""),[r,a]}function lu(e){var t=e[1].Tooltip,r=G(10+2*(t.length+1));r.write_shift(2,2048);var a=ze(e[0]);r.write_shift(2,a.r),r.write_shift(2,a.r),r.write_shift(2,a.c),r.write_shift(2,a.c);for(var n=0;n<t.length;++n)r.write_shift(2,t.charCodeAt(n));return r.write_shift(2,0),r}function hu(e){var t=[0,0],r;return r=e.read_shift(2),t[0]=Ns[r]||r,r=e.read_shift(2),t[1]=Ns[r]||r,t}function uu(e){return e||(e=G(4)),e.write_shift(2,1),e.write_shift(2,1),e}function du(e){for(var t=e.read_shift(2),r=[];t-- >0;)r.push(vc(e));return r}function vu(e){for(var t=e.read_shift(2),r=[];t-- >0;)r.push(vc(e));return r}function pu(e){e.l+=2;var t={cxfs:0,crc:0};return t.cxfs=e.read_shift(2),t.crc=e.read_shift(4),t}function kc(e,t,r){if(!r.cellStyles)return Tr(e,t);var a=r&&r.biff>=12?4:2,n=e.read_shift(a),i=e.read_shift(a),s=e.read_shift(a),f=e.read_shift(a),c=e.read_shift(2);a==2&&(e.l+=2);var o={s:n,e:i,w:s,ixfe:f,flags:c};return(r.biff>=5||!r.biff)&&(o.level=c>>8&7),o}function mu(e,t){var r=G(12);r.write_shift(2,t),r.write_shift(2,t),r.write_shift(2,e.width*256),r.write_shift(2,0);var a=0;return e.hidden&&(a|=1),r.write_shift(1,a),a=e.level||0,r.write_shift(1,a),r.write_shift(2,0),r}function gu(e,t){var r={};return t<32||(e.l+=16,r.header=_r(e),r.footer=_r(e),e.l+=2),r}function _u(e,t,r){var a={area:!1};if(r.biff!=5)return e.l+=t,a;var n=e.read_shift(1);return e.l+=3,n&16&&(a.area=!0),a}function wu(e){for(var t=G(2*e),r=0;r<e;++r)t.write_shift(2,r+1);return t}var ku=st,Tu=lc,Eu=ja;function Su(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(4),n={fmt:t,env:r,len:a,data:e.slice(e.l,e.l+a)};return e.l+=a,n}function yu(e,t,r){r.biffguess&&r.biff==5&&(r.biff=2);var a=st(e);++e.l;var n=Jt(e,t-7,r);return a.t="str",a.val=n,a}function xu(e){var t=st(e);++e.l;var r=_r(e);return t.t="n",t.val=r,t}function Au(e,t,r){var a=G(15);return Qa(a,e,t),a.write_shift(8,r,"f"),a}function Cu(e){var t=st(e);++e.l;var r=e.read_shift(2);return t.t="n",t.val=r,t}function Ou(e,t,r){var a=G(9);return Qa(a,e,t),a.write_shift(2,r),a}function Iu(e){var t=e.read_shift(1);return t===0?(e.l++,""):e.read_shift(t,"sbcs-cont")}function Fu(e,t){e.l+=6,e.l+=2,e.l+=1,e.l+=3,e.l+=1,e.l+=t-13}function Ru(e,t,r){var a=e.l+t,n=st(e),i=e.read_shift(2),s=Ht(e,i,r);return e.l=a,n.t="str",n.val=s,n}var Nu=[2,3,48,49,131,139,140,245],si=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},t=Cn({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function r(f,c){var o=[],l=St(1);switch(c.type){case"base64":l=Rr(Dr(f));break;case"binary":l=Rr(f);break;case"buffer":case"array":l=f;break}hr(l,0);var h=l.read_shift(1),d=!!(h&136),v=!1,p=!1;switch(h){case 2:break;case 3:break;case 48:v=!0,d=!0;break;case 49:v=!0,d=!0;break;case 131:break;case 139:break;case 140:p=!0;break;case 245:break;default:throw new Error("DBF Unsupported Version: "+h.toString(16))}var u=0,m=521;h==2&&(u=l.read_shift(2)),l.l+=3,h!=2&&(u=l.read_shift(4)),u>1048576&&(u=1e6),h!=2&&(m=l.read_shift(2));var k=l.read_shift(2),A=c.codepage||1252;h!=2&&(l.l+=16,l.read_shift(1),l[l.l]!==0&&(A=e[l[l.l]]),l.l+=1,l.l+=2),p&&(l.l+=36);for(var _=[],I={},D=Math.min(l.length,h==2?521:m-10-(v?264:0)),P=p?32:11;l.l<D&&l[l.l]!=13;)switch(I={},I.name=Ie.utils.decode(A,l.slice(l.l,l.l+P)).replace(/[\u0000\r\n].*$/g,""),l.l+=P,I.type=String.fromCharCode(l.read_shift(1)),h!=2&&!p&&(I.offset=l.read_shift(4)),I.len=l.read_shift(1),h==2&&(I.offset=l.read_shift(2)),I.dec=l.read_shift(1),I.name.length&&_.push(I),h!=2&&(l.l+=p?13:14),I.type){case"B":(!v||I.len!=8)&&c.WTF&&console.log("Skipping "+I.name+":"+I.type);break;case"G":case"P":c.WTF&&console.log("Skipping "+I.name+":"+I.type);break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw new Error("Unknown Field Type: "+I.type)}if(l[l.l]!==13&&(l.l=m-1),l.read_shift(1)!==13)throw new Error("DBF Terminator not found "+l.l+" "+l[l.l]);l.l=m;var x=0,M=0;for(o[0]=[],M=0;M!=_.length;++M)o[0][M]=_[M].name;for(;u-- >0;){if(l[l.l]===42){l.l+=k;continue}for(++l.l,o[++x]=[],M=0,M=0;M!=_.length;++M){var R=l.slice(l.l,l.l+_[M].len);l.l+=_[M].len,hr(R,0);var z=Ie.utils.decode(A,R);switch(_[M].type){case"C":z.trim().length&&(o[x][M]=z.replace(/\s+$/,""));break;case"D":z.length===8?o[x][M]=new Date(+z.slice(0,4),+z.slice(4,6)-1,+z.slice(6,8)):o[x][M]=z;break;case"F":o[x][M]=parseFloat(z.trim());break;case"+":case"I":o[x][M]=p?R.read_shift(-4,"i")^2147483648:R.read_shift(4,"i");break;case"L":switch(z.trim().toUpperCase()){case"Y":case"T":o[x][M]=!0;break;case"N":case"F":o[x][M]=!1;break;case"":case"?":break;default:throw new Error("DBF Unrecognized L:|"+z+"|")}break;case"M":if(!d)throw new Error("DBF Unexpected MEMO for type "+h.toString(16));o[x][M]="##MEMO##"+(p?parseInt(z.trim(),10):R.read_shift(4));break;case"N":z=z.replace(/\u0000/g,"").trim(),z&&z!="."&&(o[x][M]=+z||0);break;case"@":o[x][M]=new Date(R.read_shift(-8,"f")-621356832e5);break;case"T":o[x][M]=new Date((R.read_shift(4)-2440588)*864e5+R.read_shift(4));break;case"Y":o[x][M]=R.read_shift(4,"i")/1e4+R.read_shift(4,"i")/1e4*Math.pow(2,32);break;case"O":o[x][M]=-R.read_shift(-8,"f");break;case"B":if(v&&_[M].len==8){o[x][M]=R.read_shift(8,"f");break}case"G":case"P":R.l+=_[M].len;break;case"0":if(_[M].name==="_NullFlags")break;default:throw new Error("DBF Unsupported data type "+_[M].type)}}}if(h!=2&&l.l<l.length&&l[l.l++]!=26)throw new Error("DBF EOF Marker missing "+(l.l-1)+" of "+l.length+" "+l[l.l-1].toString(16));return c&&c.sheetRows&&(o=o.slice(0,c.sheetRows)),c.DBF=_,o}function a(f,c){var o=c||{};o.dateNF||(o.dateNF="yyyymmdd");var l=ua(r(f,o),o);return l["!cols"]=o.DBF.map(function(h){return{wch:h.len,DBF:h}}),delete o.DBF,l}function n(f,c){try{return Ot(a(f,c),c)}catch(o){if(c&&c.WTF)throw o}return{SheetNames:[],Sheets:{}}}var i={B:8,C:250,L:1,D:8,"?":0,"":0};function s(f,c){var o=c||{};if(+o.codepage>=0&&Gr(+o.codepage),o.type=="string")throw new Error("Cannot write DBF to JS string");var l=Or(),h=yn(f,{header:1,raw:!0,cellDates:!0}),d=h[0],v=h.slice(1),p=f["!cols"]||[],u=0,m=0,k=0,A=1;for(u=0;u<d.length;++u){if(((p[u]||{}).DBF||{}).name){d[u]=p[u].DBF.name,++k;continue}if(d[u]!=null){if(++k,typeof d[u]=="number"&&(d[u]=d[u].toString(10)),typeof d[u]!="string")throw new Error("DBF Invalid column name "+d[u]+" |"+typeof d[u]+"|");if(d.indexOf(d[u])!==u){for(m=0;m<1024;++m)if(d.indexOf(d[u]+"_"+m)==-1){d[u]+="_"+m;break}}}}var _=Ce(f["!ref"]),I=[],D=[],P=[];for(u=0;u<=_.e.c-_.s.c;++u){var x="",M="",R=0,z=[];for(m=0;m<v.length;++m)v[m][u]!=null&&z.push(v[m][u]);if(z.length==0||d[u]==null){I[u]="?";continue}for(m=0;m<z.length;++m){switch(typeof z[m]){case"number":M="B";break;case"string":M="C";break;case"boolean":M="L";break;case"object":M=z[m]instanceof Date?"D":"C";break;default:M="C"}R=Math.max(R,String(z[m]).length),x=x&&x!=M?"C":M}R>250&&(R=250),M=((p[u]||{}).DBF||{}).type,M=="C"&&p[u].DBF.len>R&&(R=p[u].DBF.len),x=="B"&&M=="N"&&(x="N",P[u]=p[u].DBF.dec,R=p[u].DBF.len),D[u]=x=="C"||M=="N"?R:i[x]||0,A+=D[u],I[u]=x}var X=l.next(32);for(X.write_shift(4,318902576),X.write_shift(4,v.length),X.write_shift(2,296+32*k),X.write_shift(2,A),u=0;u<4;++u)X.write_shift(4,0);for(X.write_shift(4,0|(+t[Mt]||3)<<8),u=0,m=0;u<d.length;++u)if(d[u]!=null){var b=l.next(32),J=(d[u].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);b.write_shift(1,J,"sbcs"),b.write_shift(1,I[u]=="?"?"C":I[u],"sbcs"),b.write_shift(4,m),b.write_shift(1,D[u]||i[I[u]]||0),b.write_shift(1,P[u]||0),b.write_shift(1,2),b.write_shift(4,0),b.write_shift(1,0),b.write_shift(4,0),b.write_shift(4,0),m+=D[u]||i[I[u]]||0}var le=l.next(264);for(le.write_shift(4,13),u=0;u<65;++u)le.write_shift(4,0);for(u=0;u<v.length;++u){var ie=l.next(A);for(ie.write_shift(1,0),m=0;m<d.length;++m)if(d[m]!=null)switch(I[m]){case"L":ie.write_shift(1,v[u][m]==null?63:v[u][m]?84:70);break;case"B":ie.write_shift(8,v[u][m]||0,"f");break;case"N":var ue="0";for(typeof v[u][m]=="number"&&(ue=v[u][m].toFixed(P[m]||0)),k=0;k<D[m]-ue.length;++k)ie.write_shift(1,32);ie.write_shift(1,ue,"sbcs");break;case"D":v[u][m]?(ie.write_shift(4,("0000"+v[u][m].getFullYear()).slice(-4),"sbcs"),ie.write_shift(2,("00"+(v[u][m].getMonth()+1)).slice(-2),"sbcs"),ie.write_shift(2,("00"+v[u][m].getDate()).slice(-2),"sbcs")):ie.write_shift(8,"00000000","sbcs");break;case"C":var ce=String(v[u][m]!=null?v[u][m]:"").slice(0,D[m]);for(ie.write_shift(1,ce,"sbcs"),k=0;k<D[m]-ce.length;++k)ie.write_shift(1,32);break}}return l.next(1).write_shift(1,26),l.end()}return{to_workbook:n,to_sheet:a,from_sheet:s}}(),Tc=function(){var e={AA:"À",BA:"Á",CA:"Â",DA:195,HA:"Ä",JA:197,AE:"È",BE:"É",CE:"Ê",HE:"Ë",AI:"Ì",BI:"Í",CI:"Î",HI:"Ï",AO:"Ò",BO:"Ó",CO:"Ô",DO:213,HO:"Ö",AU:"Ù",BU:"Ú",CU:"Û",HU:"Ü",Aa:"à",Ba:"á",Ca:"â",Da:227,Ha:"ä",Ja:229,Ae:"è",Be:"é",Ce:"ê",He:"ë",Ai:"ì",Bi:"í",Ci:"î",Hi:"ï",Ao:"ò",Bo:"ó",Co:"ô",Do:245,Ho:"ö",Au:"ù",Bu:"ú",Cu:"û",Hu:"ü",KC:"Ç",Kc:"ç",q:"æ",z:"œ",a:"Æ",j:"Œ",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},t=new RegExp("\x1BN("+je(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),r=function(d,v){var p=e[v];return typeof p=="number"?qn(p):p},a=function(d,v,p){var u=v.charCodeAt(0)-32<<4|p.charCodeAt(0)-48;return u==59?d:qn(u)};e["|"]=254;function n(d,v){switch(v.type){case"base64":return i(Dr(d),v);case"binary":return i(d,v);case"buffer":return i(Se&&Buffer.isBuffer(d)?d.toString("binary"):Ct(d),v);case"array":return i(Ut(d),v)}throw new Error("Unrecognized type "+v.type)}function i(d,v){var p=d.split(/[\n\r]+/),u=-1,m=-1,k=0,A=0,_=[],I=[],D=null,P={},x=[],M=[],R=[],z=0,X;for(+v.codepage>=0&&Gr(+v.codepage);k!==p.length;++k){z=0;var b=p[k].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,a).replace(t,r),J=b.replace(/;;/g,"\0").split(";").map(function(N){return N.replace(/\u0000/g,";")}),le=J[0],ie;if(b.length>0)switch(le){case"ID":break;case"E":break;case"B":break;case"O":break;case"W":break;case"P":J[1].charAt(0)=="P"&&I.push(b.slice(3).replace(/;;/g,";"));break;case"C":var ue=!1,ce=!1,Le=!1,V=!1,pe=-1,_e=-1;for(A=1;A<J.length;++A)switch(J[A].charAt(0)){case"A":break;case"X":m=parseInt(J[A].slice(1))-1,ce=!0;break;case"Y":for(u=parseInt(J[A].slice(1))-1,ce||(m=0),X=_.length;X<=u;++X)_[X]=[];break;case"K":ie=J[A].slice(1),ie.charAt(0)==='"'?ie=ie.slice(1,ie.length-1):ie==="TRUE"?ie=!0:ie==="FALSE"?ie=!1:isNaN(jr(ie))?isNaN(fa(ie).getDate())||(ie=Ve(ie)):(ie=jr(ie),D!==null&&Gt(D)&&(ie=In(ie))),typeof Ie<"u"&&typeof ie=="string"&&(v||{}).type!="string"&&(v||{}).codepage&&(ie=Ie.utils.decode(v.codepage,ie)),ue=!0;break;case"E":V=!0;var O=ia(J[A].slice(1),{r:u,c:m});_[u][m]=[_[u][m],O];break;case"S":Le=!0,_[u][m]=[_[u][m],"S5S"];break;case"G":break;case"R":pe=parseInt(J[A].slice(1))-1;break;case"C":_e=parseInt(J[A].slice(1))-1;break;default:if(v&&v.WTF)throw new Error("SYLK bad record "+b)}if(ue&&(_[u][m]&&_[u][m].length==2?_[u][m][0]=ie:_[u][m]=ie,D=null),Le){if(V)throw new Error("SYLK shared formula cannot have own formula");var L=pe>-1&&_[pe][_e];if(!L||!L[1])throw new Error("SYLK shared formula cannot find base");_[u][m][1]=Uc(L[1],{r:u-pe,c:m-_e})}break;case"F":var F=0;for(A=1;A<J.length;++A)switch(J[A].charAt(0)){case"X":m=parseInt(J[A].slice(1))-1,++F;break;case"Y":for(u=parseInt(J[A].slice(1))-1,X=_.length;X<=u;++X)_[X]=[];break;case"M":z=parseInt(J[A].slice(1))/20;break;case"F":break;case"G":break;case"P":D=I[parseInt(J[A].slice(1))];break;case"S":break;case"D":break;case"N":break;case"W":for(R=J[A].slice(1).split(" "),X=parseInt(R[0],10);X<=parseInt(R[1],10);++X)z=parseInt(R[2],10),M[X-1]=z===0?{hidden:!0}:{wch:z},yt(M[X-1]);break;case"C":m=parseInt(J[A].slice(1))-1,M[m]||(M[m]={});break;case"R":u=parseInt(J[A].slice(1))-1,x[u]||(x[u]={}),z>0?(x[u].hpt=z,x[u].hpx=oa(z)):z===0&&(x[u].hidden=!0);break;default:if(v&&v.WTF)throw new Error("SYLK bad record "+b)}F<1&&(D=null);break;default:if(v&&v.WTF)throw new Error("SYLK bad record "+b)}}return x.length>0&&(P["!rows"]=x),M.length>0&&(P["!cols"]=M),v&&v.sheetRows&&(_=_.slice(0,v.sheetRows)),[_,P]}function s(d,v){var p=n(d,v),u=p[0],m=p[1],k=ua(u,v);return je(m).forEach(function(A){k[A]=m[A]}),k}function f(d,v){return Ot(s(d,v),v)}function c(d,v,p,u){var m="C;Y"+(p+1)+";X"+(u+1)+";K";switch(d.t){case"n":m+=d.v||0,d.f&&!d.F&&(m+=";E"+Vi(d.f,{r:p,c:u}));break;case"b":m+=d.v?"TRUE":"FALSE";break;case"e":m+=d.w||d.v;break;case"d":m+='"'+(d.w||d.v)+'"';break;case"s":m+='"'+d.v.replace(/"/g,"").replace(/;/g,";;")+'"';break}return m}function o(d,v){v.forEach(function(p,u){var m="F;W"+(u+1)+" "+(u+1)+" ";p.hidden?m+="0":(typeof p.width=="number"&&!p.wpx&&(p.wpx=Ua(p.width)),typeof p.wpx=="number"&&!p.wch&&(p.wch=Wa(p.wpx)),typeof p.wch=="number"&&(m+=Math.round(p.wch))),m.charAt(m.length-1)!=" "&&d.push(m)})}function l(d,v){v.forEach(function(p,u){var m="F;";p.hidden?m+="M0;":p.hpt?m+="M"+20*p.hpt+";":p.hpx&&(m+="M"+20*Ha(p.hpx)+";"),m.length>2&&d.push(m+"R"+(u+1))})}function h(d,v){var p=["ID;PWXL;N;E"],u=[],m=Ce(d["!ref"]),k,A=Array.isArray(d),_=`\r
`;p.push("P;PGeneral"),p.push("F;P0;DG0G8;M255"),d["!cols"]&&o(p,d["!cols"]),d["!rows"]&&l(p,d["!rows"]),p.push("B;Y"+(m.e.r-m.s.r+1)+";X"+(m.e.c-m.s.c+1)+";D"+[m.s.c,m.s.r,m.e.c,m.e.r].join(" "));for(var I=m.s.r;I<=m.e.r;++I)for(var D=m.s.c;D<=m.e.c;++D){var P=me({r:I,c:D});k=A?(d[I]||[])[D]:d[P],!(!k||k.v==null&&(!k.f||k.F))&&u.push(c(k,d,I,D))}return p.join(_)+_+u.join(_)+_+"E"+_}return{to_workbook:f,to_sheet:s,from_sheet:h}}(),Ec=function(){function e(i,s){switch(s.type){case"base64":return t(Dr(i),s);case"binary":return t(i,s);case"buffer":return t(Se&&Buffer.isBuffer(i)?i.toString("binary"):Ct(i),s);case"array":return t(Ut(i),s)}throw new Error("Unrecognized type "+s.type)}function t(i,s){for(var f=i.split(`
`),c=-1,o=-1,l=0,h=[];l!==f.length;++l){if(f[l].trim()==="BOT"){h[++c]=[],o=0;continue}if(!(c<0)){var d=f[l].trim().split(","),v=d[0],p=d[1];++l;for(var u=f[l]||"";(u.match(/["]/g)||[]).length&1&&l<f.length-1;)u+=`
`+f[++l];switch(u=u.trim(),+v){case-1:if(u==="BOT"){h[++c]=[],o=0;continue}else if(u!=="EOD")throw new Error("Unrecognized DIF special command "+u);break;case 0:u==="TRUE"?h[c][o]=!0:u==="FALSE"?h[c][o]=!1:isNaN(jr(p))?isNaN(fa(p).getDate())?h[c][o]=p:h[c][o]=Ve(p):h[c][o]=jr(p),++o;break;case 1:u=u.slice(1,u.length-1),u=u.replace(/""/g,'"'),u&&u.match(/^=".*"$/)&&(u=u.slice(2,-1)),h[c][o++]=u!==""?u:null;break}if(u==="EOD")break}}return s&&s.sheetRows&&(h=h.slice(0,s.sheetRows)),h}function r(i,s){return ua(e(i,s),s)}function a(i,s){return Ot(r(i,s),s)}var n=function(){var i=function(c,o,l,h,d){c.push(o),c.push(l+","+h),c.push('"'+d.replace(/"/g,'""')+'"')},s=function(c,o,l,h){c.push(o+","+l),c.push(o==1?'"'+h.replace(/"/g,'""')+'"':h)};return function(c){var o=[],l=Ce(c["!ref"]),h,d=Array.isArray(c);i(o,"TABLE",0,1,"sheetjs"),i(o,"VECTORS",0,l.e.r-l.s.r+1,""),i(o,"TUPLES",0,l.e.c-l.s.c+1,""),i(o,"DATA",0,0,"");for(var v=l.s.r;v<=l.e.r;++v){s(o,-1,0,"BOT");for(var p=l.s.c;p<=l.e.c;++p){var u=me({r:v,c:p});if(h=d?(c[v]||[])[p]:c[u],!h){s(o,1,0,"");continue}switch(h.t){case"n":var m=h.w;!m&&h.v!=null&&(m=h.v),m==null?h.f&&!h.F?s(o,1,0,"="+h.f):s(o,1,0,""):s(o,0,m,"V");break;case"b":s(o,0,h.v?1:0,h.v?"TRUE":"FALSE");break;case"s":s(o,1,0,isNaN(h.v)?h.v:'="'+h.v+'"');break;case"d":h.w||(h.w=Lr(h.z||ve[14],ir(Ve(h.v)))),s(o,0,h.w,"V");break;default:s(o,1,0,"")}}}s(o,-1,0,"EOD");var k=`\r
`,A=o.join(k);return A}}();return{to_workbook:a,to_sheet:r,from_sheet:n}}(),Sc=function(){function e(h){return h.replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,`
`)}function t(h){return h.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function r(h,d){for(var v=h.split(`
`),p=-1,u=-1,m=0,k=[];m!==v.length;++m){var A=v[m].trim().split(":");if(A[0]==="cell"){var _=ze(A[1]);if(k.length<=_.r)for(p=k.length;p<=_.r;++p)k[p]||(k[p]=[]);switch(p=_.r,u=_.c,A[2]){case"t":k[p][u]=e(A[3]);break;case"v":k[p][u]=+A[3];break;case"vtf":var I=A[A.length-1];case"vtc":switch(A[3]){case"nl":k[p][u]=!!+A[4];break;default:k[p][u]=+A[4];break}A[2]=="vtf"&&(k[p][u]=[k[p][u],I])}}}return d&&d.sheetRows&&(k=k.slice(0,d.sheetRows)),k}function a(h,d){return ua(r(h,d),d)}function n(h,d){return Ot(a(h,d),d)}var i=["socialcalc:version:1.5","MIME-Version: 1.0","Content-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave"].join(`
`),s=["--SocialCalcSpreadsheetControlSave","Content-type: text/plain; charset=UTF-8"].join(`
`)+`
`,f=["# SocialCalc Spreadsheet Control Save","part:sheet"].join(`
`),c="--SocialCalcSpreadsheetControlSave--";function o(h){if(!h||!h["!ref"])return"";for(var d=[],v=[],p,u="",m=Ar(h["!ref"]),k=Array.isArray(h),A=m.s.r;A<=m.e.r;++A)for(var _=m.s.c;_<=m.e.c;++_)if(u=me({r:A,c:_}),p=k?(h[A]||[])[_]:h[u],!(!p||p.v==null||p.t==="z")){switch(v=["cell",u,"t"],p.t){case"s":case"str":v.push(t(p.v));break;case"n":p.f?(v[2]="vtf",v[3]="n",v[4]=p.v,v[5]=t(p.f)):(v[2]="v",v[3]=p.v);break;case"b":v[2]="vt"+(p.f?"f":"c"),v[3]="nl",v[4]=p.v?"1":"0",v[5]=t(p.f||(p.v?"TRUE":"FALSE"));break;case"d":var I=ir(Ve(p.v));v[2]="vtc",v[3]="nd",v[4]=""+I,v[5]=p.w||Lr(p.z||ve[14],I);break;case"e":continue}d.push(v.join(":"))}return d.push("sheet:c:"+(m.e.c-m.s.c+1)+":r:"+(m.e.r-m.s.r+1)+":tvf:1"),d.push("valueformat:1:text-wiki"),d.join(`
`)}function l(h){return[i,s,f,s,o(h),c].join(`
`)}return{to_workbook:n,to_sheet:a,from_sheet:l}}(),ca=function(){function e(l,h,d,v,p){p.raw?h[d][v]=l:l===""||(l==="TRUE"?h[d][v]=!0:l==="FALSE"?h[d][v]=!1:isNaN(jr(l))?isNaN(fa(l).getDate())?h[d][v]=l:h[d][v]=Ve(l):h[d][v]=jr(l))}function t(l,h){var d=h||{},v=[];if(!l||l.length===0)return v;for(var p=l.split(/[\r\n]/),u=p.length-1;u>=0&&p[u].length===0;)--u;for(var m=10,k=0,A=0;A<=u;++A)k=p[A].indexOf(" "),k==-1?k=p[A].length:k++,m=Math.max(m,k);for(A=0;A<=u;++A){v[A]=[];var _=0;for(e(p[A].slice(0,m).trim(),v,A,_,d),_=1;_<=(p[A].length-m)/10+1;++_)e(p[A].slice(m+(_-1)*10,m+_*10).trim(),v,A,_,d)}return d.sheetRows&&(v=v.slice(0,d.sheetRows)),v}var r={44:",",9:"	",59:";",124:"|"},a={44:3,9:2,59:1,124:0};function n(l){for(var h={},d=!1,v=0,p=0;v<l.length;++v)(p=l.charCodeAt(v))==34?d=!d:!d&&p in r&&(h[p]=(h[p]||0)+1);p=[];for(v in h)Object.prototype.hasOwnProperty.call(h,v)&&p.push([h[v],v]);if(!p.length){h=a;for(v in h)Object.prototype.hasOwnProperty.call(h,v)&&p.push([h[v],v])}return p.sort(function(u,m){return u[0]-m[0]||a[u[1]]-a[m[1]]}),r[p.pop()[1]]||44}function i(l,h){var d=h||{},v="",p=d.dense?[]:{},u={s:{c:0,r:0},e:{c:0,r:0}};l.slice(0,4)=="sep="?l.charCodeAt(5)==13&&l.charCodeAt(6)==10?(v=l.charAt(4),l=l.slice(7)):l.charCodeAt(5)==13||l.charCodeAt(5)==10?(v=l.charAt(4),l=l.slice(6)):v=n(l.slice(0,1024)):d&&d.FS?v=d.FS:v=n(l.slice(0,1024));var m=0,k=0,A=0,_=0,I=0,D=v.charCodeAt(0),P=!1,x=0,M=l.charCodeAt(0);l=l.replace(/\r\n/mg,`
`);var R=d.dateNF!=null?_l(d.dateNF):null;function z(){var X=l.slice(_,I),b={};if(X.charAt(0)=='"'&&X.charAt(X.length-1)=='"'&&(X=X.slice(1,-1).replace(/""/g,'"')),X.length===0)b.t="z";else if(d.raw)b.t="s",b.v=X;else if(X.trim().length===0)b.t="s",b.v=X;else if(X.charCodeAt(0)==61)X.charCodeAt(1)==34&&X.charCodeAt(X.length-1)==34?(b.t="s",b.v=X.slice(2,-1).replace(/""/g,'"')):K2(X)?(b.t="n",b.f=X.slice(1)):(b.t="s",b.v=X);else if(X=="TRUE")b.t="b",b.v=!0;else if(X=="FALSE")b.t="b",b.v=!1;else if(!isNaN(A=jr(X)))b.t="n",d.cellText!==!1&&(b.w=X),b.v=A;else if(!isNaN(fa(X).getDate())||R&&X.match(R)){b.z=d.dateNF||ve[14];var J=0;R&&X.match(R)&&(X=wl(X,d.dateNF,X.match(R)||[]),J=1),d.cellDates?(b.t="d",b.v=Ve(X,J)):(b.t="n",b.v=ir(Ve(X,J))),d.cellText!==!1&&(b.w=Lr(b.z,b.v instanceof Date?ir(b.v):b.v)),d.cellNF||delete b.z}else b.t="s",b.v=X;if(b.t=="z"||(d.dense?(p[m]||(p[m]=[]),p[m][k]=b):p[me({c:k,r:m})]=b),_=I+1,M=l.charCodeAt(_),u.e.c<k&&(u.e.c=k),u.e.r<m&&(u.e.r=m),x==D)++k;else if(k=0,++m,d.sheetRows&&d.sheetRows<=m)return!0}e:for(;I<l.length;++I)switch(x=l.charCodeAt(I)){case 34:M===34&&(P=!P);break;case D:case 10:case 13:if(!P&&z())break e;break}return I-_>0&&z(),p["!ref"]=ke(u),p}function s(l,h){return!(h&&h.PRN)||h.FS||l.slice(0,4)=="sep="||l.indexOf("	")>=0||l.indexOf(",")>=0||l.indexOf(";")>=0?i(l,h):ua(t(l,h),h)}function f(l,h){var d="",v=h.type=="string"?[0,0,0,0]:Ji(l,h);switch(h.type){case"base64":d=Dr(l);break;case"binary":d=l;break;case"buffer":h.codepage==65001?d=l.toString("utf8"):h.codepage&&typeof Ie<"u"?d=Ie.utils.decode(h.codepage,l):d=Se&&Buffer.isBuffer(l)?l.toString("binary"):Ct(l);break;case"array":d=Ut(l);break;case"string":d=l;break;default:throw new Error("Unrecognized type "+h.type)}return v[0]==239&&v[1]==187&&v[2]==191?d=be(d.slice(3)):h.type!="string"&&h.type!="buffer"&&h.codepage==65001?d=be(d):h.type=="binary"&&typeof Ie<"u"&&h.codepage&&(d=Ie.utils.decode(h.codepage,Ie.utils.encode(28591,d))),d.slice(0,19)=="socialcalc:version:"?Sc.to_sheet(h.type=="string"?d:be(d),h):s(d,h)}function c(l,h){return Ot(f(l,h),h)}function o(l){for(var h=[],d=Ce(l["!ref"]),v,p=Array.isArray(l),u=d.s.r;u<=d.e.r;++u){for(var m=[],k=d.s.c;k<=d.e.c;++k){var A=me({r:u,c:k});if(v=p?(l[u]||[])[k]:l[A],!v||v.v==null){m.push("          ");continue}for(var _=(v.w||(nt(v),v.w)||"").slice(0,10);_.length<10;)_+=" ";m.push(_+(k===0?" ":""))}h.push(m.join(""))}return h.join(`
`)}return{to_workbook:c,to_sheet:f,from_sheet:o}}();function Pu(e,t){var r=t||{},a=!!r.WTF;r.WTF=!0;try{var n=Tc.to_workbook(e,r);return r.WTF=a,n}catch(i){if(r.WTF=a,!i.message.match(/SYLK bad record ID/)&&a)throw i;return ca.to_workbook(e,t)}}var Lt=function(){function e(O,L,F){if(O){hr(O,O.l||0);for(var N=F.Enum||pe;O.l<O.length;){var Y=O.read_shift(2),re=N[Y]||N[65535],te=O.read_shift(2),Q=O.l+te,Z=re.f&&re.f(O,te,F);if(O.l=Q,L(Z,re,Y))return}}}function t(O,L){switch(L.type){case"base64":return r(Rr(Dr(O)),L);case"binary":return r(Rr(O),L);case"buffer":case"array":return r(O,L)}throw"Unsupported type "+L.type}function r(O,L){if(!O)return O;var F=L||{},N=F.dense?[]:{},Y="Sheet1",re="",te=0,Q={},Z=[],Ee=[],C={s:{r:0,c:0},e:{r:0,c:0}},Ue=F.sheetRows||0;if(O[2]==0&&(O[3]==8||O[3]==9)&&O.length>=16&&O[14]==5&&O[15]===108)throw new Error("Unsupported Works 3 for Mac file");if(O[2]==2)F.Enum=pe,e(O,function(fe,Je,br){switch(br){case 0:F.vers=fe,fe>=4096&&(F.qpro=!0);break;case 6:C=fe;break;case 204:fe&&(re=fe);break;case 222:re=fe;break;case 15:case 51:F.qpro||(fe[1].v=fe[1].v.slice(1));case 13:case 14:case 16:br==14&&(fe[2]&112)==112&&(fe[2]&15)>1&&(fe[2]&15)<15&&(fe[1].z=F.dateNF||ve[14],F.cellDates&&(fe[1].t="d",fe[1].v=In(fe[1].v))),F.qpro&&fe[3]>te&&(N["!ref"]=ke(C),Q[Y]=N,Z.push(Y),N=F.dense?[]:{},C={s:{r:0,c:0},e:{r:0,c:0}},te=fe[3],Y=re||"Sheet"+(te+1),re="");var Zr=F.dense?(N[fe[0].r]||[])[fe[0].c]:N[me(fe[0])];if(Zr){Zr.t=fe[1].t,Zr.v=fe[1].v,fe[1].z!=null&&(Zr.z=fe[1].z),fe[1].f!=null&&(Zr.f=fe[1].f);break}F.dense?(N[fe[0].r]||(N[fe[0].r]=[]),N[fe[0].r][fe[0].c]=fe[1]):N[me(fe[0])]=fe[1];break}},F);else if(O[2]==26||O[2]==14)F.Enum=_e,O[2]==14&&(F.qpro=!0,O.l=0),e(O,function(fe,Je,br){switch(br){case 204:Y=fe;break;case 22:fe[1].v=fe[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(fe[3]>te&&(N["!ref"]=ke(C),Q[Y]=N,Z.push(Y),N=F.dense?[]:{},C={s:{r:0,c:0},e:{r:0,c:0}},te=fe[3],Y="Sheet"+(te+1)),Ue>0&&fe[0].r>=Ue)break;F.dense?(N[fe[0].r]||(N[fe[0].r]=[]),N[fe[0].r][fe[0].c]=fe[1]):N[me(fe[0])]=fe[1],C.e.c<fe[0].c&&(C.e.c=fe[0].c),C.e.r<fe[0].r&&(C.e.r=fe[0].r);break;case 27:fe[14e3]&&(Ee[fe[14e3][0]]=fe[14e3][1]);break;case 1537:Ee[fe[0]]=fe[1],fe[0]==te&&(Y=fe[1]);break}},F);else throw new Error("Unrecognized LOTUS BOF "+O[2]);if(N["!ref"]=ke(C),Q[re||Y]=N,Z.push(re||Y),!Ee.length)return{SheetNames:Z,Sheets:Q};for(var Oe={},Me=[],Ae=0;Ae<Ee.length;++Ae)Q[Z[Ae]]?(Me.push(Ee[Ae]||Z[Ae]),Oe[Ee[Ae]]=Q[Ee[Ae]]||Q[Z[Ae]]):(Me.push(Ee[Ae]),Oe[Ee[Ae]]={"!ref":"A1"});return{SheetNames:Me,Sheets:Oe}}function a(O,L){var F=L||{};if(+F.codepage>=0&&Gr(+F.codepage),F.type=="string")throw new Error("Cannot write WK1 to JS string");var N=Or(),Y=Ce(O["!ref"]),re=Array.isArray(O),te=[];ne(N,0,i(1030)),ne(N,6,c(Y));for(var Q=Math.min(Y.e.r,8191),Z=Y.s.r;Z<=Q;++Z)for(var Ee=Ke(Z),C=Y.s.c;C<=Y.e.c;++C){Z===Y.s.r&&(te[C]=He(C));var Ue=te[C]+Ee,Oe=re?(O[Z]||[])[C]:O[Ue];if(!(!Oe||Oe.t=="z"))if(Oe.t=="n")(Oe.v|0)==Oe.v&&Oe.v>=-32768&&Oe.v<=32767?ne(N,13,v(Z,C,Oe.v)):ne(N,14,u(Z,C,Oe.v));else{var Me=nt(Oe);ne(N,15,h(Z,C,Me.slice(0,239)))}}return ne(N,1),N.end()}function n(O,L){var F=L||{};if(+F.codepage>=0&&Gr(+F.codepage),F.type=="string")throw new Error("Cannot write WK3 to JS string");var N=Or();ne(N,0,s(O));for(var Y=0,re=0;Y<O.SheetNames.length;++Y)(O.Sheets[O.SheetNames[Y]]||{})["!ref"]&&ne(N,27,V(O.SheetNames[Y],re++));var te=0;for(Y=0;Y<O.SheetNames.length;++Y){var Q=O.Sheets[O.SheetNames[Y]];if(!(!Q||!Q["!ref"])){for(var Z=Ce(Q["!ref"]),Ee=Array.isArray(Q),C=[],Ue=Math.min(Z.e.r,8191),Oe=Z.s.r;Oe<=Ue;++Oe)for(var Me=Ke(Oe),Ae=Z.s.c;Ae<=Z.e.c;++Ae){Oe===Z.s.r&&(C[Ae]=He(Ae));var fe=C[Ae]+Me,Je=Ee?(Q[Oe]||[])[Ae]:Q[fe];if(!(!Je||Je.t=="z"))if(Je.t=="n")ne(N,23,z(Oe,Ae,te,Je.v));else{var br=nt(Je);ne(N,22,x(Oe,Ae,te,br.slice(0,239)))}}++te}}return ne(N,1),N.end()}function i(O){var L=G(2);return L.write_shift(2,O),L}function s(O){var L=G(26);L.write_shift(2,4096),L.write_shift(2,4),L.write_shift(4,0);for(var F=0,N=0,Y=0,re=0;re<O.SheetNames.length;++re){var te=O.SheetNames[re],Q=O.Sheets[te];if(!(!Q||!Q["!ref"])){++Y;var Z=Ar(Q["!ref"]);F<Z.e.r&&(F=Z.e.r),N<Z.e.c&&(N=Z.e.c)}}return F>8191&&(F=8191),L.write_shift(2,F),L.write_shift(1,Y),L.write_shift(1,N),L.write_shift(2,0),L.write_shift(2,0),L.write_shift(1,1),L.write_shift(1,2),L.write_shift(4,0),L.write_shift(4,0),L}function f(O,L,F){var N={s:{c:0,r:0},e:{c:0,r:0}};return L==8&&F.qpro?(N.s.c=O.read_shift(1),O.l++,N.s.r=O.read_shift(2),N.e.c=O.read_shift(1),O.l++,N.e.r=O.read_shift(2),N):(N.s.c=O.read_shift(2),N.s.r=O.read_shift(2),L==12&&F.qpro&&(O.l+=2),N.e.c=O.read_shift(2),N.e.r=O.read_shift(2),L==12&&F.qpro&&(O.l+=2),N.s.c==65535&&(N.s.c=N.e.c=N.s.r=N.e.r=0),N)}function c(O){var L=G(8);return L.write_shift(2,O.s.c),L.write_shift(2,O.s.r),L.write_shift(2,O.e.c),L.write_shift(2,O.e.r),L}function o(O,L,F){var N=[{c:0,r:0},{t:"n",v:0},0,0];return F.qpro&&F.vers!=20768?(N[0].c=O.read_shift(1),N[3]=O.read_shift(1),N[0].r=O.read_shift(2),O.l+=2):(N[2]=O.read_shift(1),N[0].c=O.read_shift(2),N[0].r=O.read_shift(2)),N}function l(O,L,F){var N=O.l+L,Y=o(O,L,F);if(Y[1].t="s",F.vers==20768){O.l++;var re=O.read_shift(1);return Y[1].v=O.read_shift(re,"utf8"),Y}return F.qpro&&O.l++,Y[1].v=O.read_shift(N-O.l,"cstr"),Y}function h(O,L,F){var N=G(7+F.length);N.write_shift(1,255),N.write_shift(2,L),N.write_shift(2,O),N.write_shift(1,39);for(var Y=0;Y<N.length;++Y){var re=F.charCodeAt(Y);N.write_shift(1,re>=128?95:re)}return N.write_shift(1,0),N}function d(O,L,F){var N=o(O,L,F);return N[1].v=O.read_shift(2,"i"),N}function v(O,L,F){var N=G(7);return N.write_shift(1,255),N.write_shift(2,L),N.write_shift(2,O),N.write_shift(2,F,"i"),N}function p(O,L,F){var N=o(O,L,F);return N[1].v=O.read_shift(8,"f"),N}function u(O,L,F){var N=G(13);return N.write_shift(1,255),N.write_shift(2,L),N.write_shift(2,O),N.write_shift(8,F,"f"),N}function m(O,L,F){var N=O.l+L,Y=o(O,L,F);if(Y[1].v=O.read_shift(8,"f"),F.qpro)O.l=N;else{var re=O.read_shift(2);I(O.slice(O.l,O.l+re),Y),O.l+=re}return Y}function k(O,L,F){var N=L&32768;return L&=-32769,L=(N?O:0)+(L>=8192?L-16384:L),(N?"":"$")+(F?He(L):Ke(L))}var A={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},_=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function I(O,L){hr(O,0);for(var F=[],N=0,Y="",re="",te="",Q="";O.l<O.length;){var Z=O[O.l++];switch(Z){case 0:F.push(O.read_shift(8,"f"));break;case 1:re=k(L[0].c,O.read_shift(2),!0),Y=k(L[0].r,O.read_shift(2),!1),F.push(re+Y);break;case 2:{var Ee=k(L[0].c,O.read_shift(2),!0),C=k(L[0].r,O.read_shift(2),!1);re=k(L[0].c,O.read_shift(2),!0),Y=k(L[0].r,O.read_shift(2),!1),F.push(Ee+C+":"+re+Y)}break;case 3:if(O.l<O.length){console.error("WK1 premature formula end");return}break;case 4:F.push("("+F.pop()+")");break;case 5:F.push(O.read_shift(2));break;case 6:{for(var Ue="";Z=O[O.l++];)Ue+=String.fromCharCode(Z);F.push('"'+Ue.replace(/"/g,'""')+'"')}break;case 8:F.push("-"+F.pop());break;case 23:F.push("+"+F.pop());break;case 22:F.push("NOT("+F.pop()+")");break;case 20:case 21:Q=F.pop(),te=F.pop(),F.push(["AND","OR"][Z-20]+"("+te+","+Q+")");break;default:if(Z<32&&_[Z])Q=F.pop(),te=F.pop(),F.push(te+_[Z]+Q);else if(A[Z]){if(N=A[Z][1],N==69&&(N=O[O.l++]),N>F.length){console.error("WK1 bad formula parse 0x"+Z.toString(16)+":|"+F.join("|")+"|");return}var Oe=F.slice(-N);F.length-=N,F.push(A[Z][0]+"("+Oe.join(",")+")")}else return Z<=7?console.error("WK1 invalid opcode "+Z.toString(16)):Z<=24?console.error("WK1 unsupported op "+Z.toString(16)):Z<=30?console.error("WK1 invalid opcode "+Z.toString(16)):Z<=115?console.error("WK1 unsupported function opcode "+Z.toString(16)):console.error("WK1 unrecognized opcode "+Z.toString(16))}}F.length==1?L[1].f=""+F[0]:console.error("WK1 bad formula parse |"+F.join("|")+"|")}function D(O){var L=[{c:0,r:0},{t:"n",v:0},0];return L[0].r=O.read_shift(2),L[3]=O[O.l++],L[0].c=O[O.l++],L}function P(O,L){var F=D(O);return F[1].t="s",F[1].v=O.read_shift(L-4,"cstr"),F}function x(O,L,F,N){var Y=G(6+N.length);Y.write_shift(2,O),Y.write_shift(1,F),Y.write_shift(1,L),Y.write_shift(1,39);for(var re=0;re<N.length;++re){var te=N.charCodeAt(re);Y.write_shift(1,te>=128?95:te)}return Y.write_shift(1,0),Y}function M(O,L){var F=D(O);F[1].v=O.read_shift(2);var N=F[1].v>>1;if(F[1].v&1)switch(N&7){case 0:N=(N>>3)*5e3;break;case 1:N=(N>>3)*500;break;case 2:N=(N>>3)/20;break;case 3:N=(N>>3)/200;break;case 4:N=(N>>3)/2e3;break;case 5:N=(N>>3)/2e4;break;case 6:N=(N>>3)/16;break;case 7:N=(N>>3)/64;break}return F[1].v=N,F}function R(O,L){var F=D(O),N=O.read_shift(4),Y=O.read_shift(4),re=O.read_shift(2);if(re==65535)return N===0&&Y===3221225472?(F[1].t="e",F[1].v=15):N===0&&Y===3489660928?(F[1].t="e",F[1].v=42):F[1].v=0,F;var te=re&32768;return re=(re&32767)-16446,F[1].v=(1-te*2)*(Y*Math.pow(2,re+32)+N*Math.pow(2,re)),F}function z(O,L,F,N){var Y=G(14);if(Y.write_shift(2,O),Y.write_shift(1,F),Y.write_shift(1,L),N==0)return Y.write_shift(4,0),Y.write_shift(4,0),Y.write_shift(2,65535),Y;var re=0,te=0,Q=0,Z=0;return N<0&&(re=1,N=-N),te=Math.log2(N)|0,N/=Math.pow(2,te-31),Z=N>>>0,(Z&2147483648)==0&&(N/=2,++te,Z=N>>>0),N-=Z,Z|=2147483648,Z>>>=0,N*=Math.pow(2,32),Q=N>>>0,Y.write_shift(4,Q),Y.write_shift(4,Z),te+=16383+(re?32768:0),Y.write_shift(2,te),Y}function X(O,L){var F=R(O);return O.l+=L-14,F}function b(O,L){var F=D(O),N=O.read_shift(4);return F[1].v=N>>6,F}function J(O,L){var F=D(O),N=O.read_shift(8,"f");return F[1].v=N,F}function le(O,L){var F=J(O);return O.l+=L-10,F}function ie(O,L){return O[O.l+L-1]==0?O.read_shift(L,"cstr"):""}function ue(O,L){var F=O[O.l++];F>L-1&&(F=L-1);for(var N="";N.length<F;)N+=String.fromCharCode(O[O.l++]);return N}function ce(O,L,F){if(!(!F.qpro||L<21)){var N=O.read_shift(1);O.l+=17,O.l+=1,O.l+=2;var Y=O.read_shift(L-21,"cstr");return[N,Y]}}function Le(O,L){for(var F={},N=O.l+L;O.l<N;){var Y=O.read_shift(2);if(Y==14e3){for(F[Y]=[0,""],F[Y][0]=O.read_shift(2);O[O.l];)F[Y][1]+=String.fromCharCode(O[O.l]),O.l++;O.l++}}return F}function V(O,L){var F=G(5+O.length);F.write_shift(2,14e3),F.write_shift(2,L);for(var N=0;N<O.length;++N){var Y=O.charCodeAt(N);F[F.l++]=Y>127?95:Y}return F[F.l++]=0,F}var pe={0:{n:"BOF",f:tr},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:d},14:{n:"NUMBER",f:p},15:{n:"LABEL",f:l},16:{n:"FORMULA",f:m},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:l},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:ie},222:{n:"SHEETNAMELP",f:ue},65535:{n:""}},_e={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:P},23:{n:"NUMBER17",f:R},24:{n:"NUMBER18",f:M},25:{n:"FORMULA19",f:X},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:Le},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:b},38:{n:"??"},39:{n:"NUMBER27",f:J},40:{n:"FORMULA28",f:le},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:ie},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:ce},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:a,book_to_wk3:n,to_workbook:t}}();function Du(e){var t={},r=e.match(Er),a=0,n=!1;if(r)for(;a!=r.length;++a){var i=ge(r[a]);switch(i[0].replace(/\w*:/g,"")){case"<condense":break;case"<extend":break;case"<shadow":if(!i.val)break;case"<shadow>":case"<shadow/>":t.shadow=1;break;case"</shadow>":break;case"<charset":if(i.val=="1")break;t.cp=hi[parseInt(i.val,10)];break;case"<outline":if(!i.val)break;case"<outline>":case"<outline/>":t.outline=1;break;case"</outline>":break;case"<rFont":t.name=i.val;break;case"<sz":t.sz=i.val;break;case"<strike":if(!i.val)break;case"<strike>":case"<strike/>":t.strike=1;break;case"</strike>":break;case"<u":if(!i.val)break;switch(i.val){case"double":t.uval="double";break;case"singleAccounting":t.uval="single-accounting";break;case"doubleAccounting":t.uval="double-accounting";break}case"<u>":case"<u/>":t.u=1;break;case"</u>":break;case"<b":if(i.val=="0")break;case"<b>":case"<b/>":t.b=1;break;case"</b>":break;case"<i":if(i.val=="0")break;case"<i>":case"<i/>":t.i=1;break;case"</i>":break;case"<color":i.rgb&&(t.color=i.rgb.slice(2,8));break;case"<color>":case"<color/>":case"</color>":break;case"<family":t.family=i.val;break;case"<family>":case"<family/>":case"</family>":break;case"<vertAlign":t.valign=i.val;break;case"<vertAlign>":case"<vertAlign/>":case"</vertAlign>":break;case"<scheme":break;case"<scheme>":case"<scheme/>":case"</scheme>":break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":n=!0;break;case"</ext>":n=!1;break;default:if(i[0].charCodeAt(1)!==47&&!n)throw new Error("Unrecognized rich format "+i[0])}}return t}var Lu=function(){var e=Da("t"),t=Da("rPr");function r(i){var s=i.match(e);if(!s)return{t:"s",v:""};var f={t:"s",v:Fe(s[1])},c=i.match(t);return c&&(f.s=Du(c[1])),f}var a=/<(?:\w+:)?r>/g,n=/<\/(?:\w+:)?r>/;return function(s){return s.replace(a,"").split(n).map(r).filter(function(f){return f.v})}}(),bu=function(){var t=/(\r\n|\n)/g;function r(n,i,s){var f=[];n.u&&f.push("text-decoration: underline;"),n.uval&&f.push("text-underline-style:"+n.uval+";"),n.sz&&f.push("font-size:"+n.sz+"pt;"),n.outline&&f.push("text-effect: outline;"),n.shadow&&f.push("text-shadow: auto;"),i.push('<span style="'+f.join("")+'">'),n.b&&(i.push("<b>"),s.push("</b>")),n.i&&(i.push("<i>"),s.push("</i>")),n.strike&&(i.push("<s>"),s.push("</s>"));var c=n.valign||"";return c=="superscript"||c=="super"?c="sup":c=="subscript"&&(c="sub"),c!=""&&(i.push("<"+c+">"),s.push("</"+c+">")),s.push("</span>"),n}function a(n){var i=[[],n.v,[]];return n.v?(n.s&&r(n.s,i[0],i[2]),i[0].join("")+i[1].replace(t,"<br/>")+i[2].join("")):""}return function(i){return i.map(a).join("")}}(),Mu=/<(?:\w+:)?t[^>]*>([^<]*)<\/(?:\w+:)?t>/g,Bu=/<(?:\w+:)?r>/,Uu=/<(?:\w+:)?rPh.*?>([\s\S]*?)<\/(?:\w+:)?rPh>/g;function Mi(e,t){var r=t?t.cellHTML:!0,a={};return e?(e.match(/^\s*<(?:\w+:)?t[^>]*>/)?(a.t=Fe(be(e.slice(e.indexOf(">")+1).split(/<\/(?:\w+:)?t>/)[0]||"")),a.r=be(e),r&&(a.h=wi(a.t))):e.match(Bu)&&(a.r=be(e),a.t=Fe(be((e.replace(Uu,"").match(Mu)||[]).join("").replace(Er,""))),r&&(a.h=bu(Lu(a.r)))),a):{t:""}}var Wu=/<(?:\w+:)?sst([^>]*)>([\s\S]*)<\/(?:\w+:)?sst>/,Hu=/<(?:\w+:)?(?:si|sstItem)>/g,Vu=/<\/(?:\w+:)?(?:si|sstItem)>/;function Xu(e,t){var r=[],a="";if(!e)return r;var n=e.match(Wu);if(n){a=n[2].replace(Hu,"").split(Vu);for(var i=0;i!=a.length;++i){var s=Mi(a[i].trim(),t);s!=null&&(r[r.length]=s)}n=ge(n[1]),r.Count=n.count,r.Unique=n.uniqueCount}return r}var Gu=/^\s|\s$|[\t\n\r]/;function yc(e,t){if(!t.bookSST)return"";var r=[Qe];r[r.length]=ae("sst",null,{xmlns:zt[0],count:e.Count,uniqueCount:e.Unique});for(var a=0;a!=e.length;++a)if(e[a]!=null){var n=e[a],i="<si>";n.r?i+=n.r:(i+="<t",n.t||(n.t=""),n.t.match(Gu)&&(i+=' xml:space="preserve"'),i+=">"+De(n.t)+"</t>"),i+="</si>",r[r.length]=i}return r.length>2&&(r[r.length]="</sst>",r[1]=r[1].replace("/>",">")),r.join("")}function zu(e){return[e.read_shift(4),e.read_shift(4)]}function $u(e,t){var r=[],a=!1;return vt(e,function(i,s,f){switch(f){case 159:r.Count=i[0],r.Unique=i[1];break;case 19:r.push(i);break;case 160:return!0;case 35:a=!0;break;case 36:a=!1;break;default:if(s.T,!a||t.WTF)throw new Error("Unexpected record 0x"+f.toString(16))}}),r}function Ku(e,t){return t||(t=G(8)),t.write_shift(4,e.Count),t.write_shift(4,e.Unique),t}var Yu=n1;function ju(e){var t=Or();j(t,159,Ku(e));for(var r=0;r<e.length;++r)j(t,19,Yu(e[r]));return j(t,160),t.end()}function xc(e){if(typeof Ie<"u")return Ie.utils.encode(Mt,e);for(var t=[],r=e.split(""),a=0;a<r.length;++a)t[a]=r[a].charCodeAt(0);return t}function ut(e,t){var r={};return r.Major=e.read_shift(2),r.Minor=e.read_shift(2),t>=4&&(e.l+=t-4),r}function Ju(e){var t={};return t.id=e.read_shift(0,"lpp4"),t.R=ut(e,4),t.U=ut(e,4),t.W=ut(e,4),t}function Zu(e){for(var t=e.read_shift(4),r=e.l+t-4,a={},n=e.read_shift(4),i=[];n-- >0;)i.push({t:e.read_shift(4),v:e.read_shift(0,"lpp4")});if(a.name=e.read_shift(0,"lpp4"),a.comps=i,e.l!=r)throw new Error("Bad DataSpaceMapEntry: "+e.l+" != "+r);return a}function qu(e){var t=[];e.l+=4;for(var r=e.read_shift(4);r-- >0;)t.push(Zu(e));return t}function Qu(e){var t=[];e.l+=4;for(var r=e.read_shift(4);r-- >0;)t.push(e.read_shift(0,"lpp4"));return t}function e0(e){var t={};return e.read_shift(4),e.l+=4,t.id=e.read_shift(0,"lpp4"),t.name=e.read_shift(0,"lpp4"),t.R=ut(e,4),t.U=ut(e,4),t.W=ut(e,4),t}function r0(e){var t=e0(e);if(t.ename=e.read_shift(0,"8lpp4"),t.blksz=e.read_shift(4),t.cmode=e.read_shift(4),e.read_shift(4)!=4)throw new Error("Bad !Primary record");return t}function Ac(e,t){var r=e.l+t,a={};a.Flags=e.read_shift(4)&63,e.l+=4,a.AlgID=e.read_shift(4);var n=!1;switch(a.AlgID){case 26126:case 26127:case 26128:n=a.Flags==36;break;case 26625:n=a.Flags==4;break;case 0:n=a.Flags==16||a.Flags==4||a.Flags==36;break;default:throw"Unrecognized encryption algorithm: "+a.AlgID}if(!n)throw new Error("Encryption Flags/AlgID mismatch");return a.AlgIDHash=e.read_shift(4),a.KeySize=e.read_shift(4),a.ProviderType=e.read_shift(4),e.l+=8,a.CSPName=e.read_shift(r-e.l>>1,"utf16le"),e.l=r,a}function Cc(e,t){var r={},a=e.l+t;return e.l+=4,r.Salt=e.slice(e.l,e.l+16),e.l+=16,r.Verifier=e.slice(e.l,e.l+16),e.l+=16,e.read_shift(4),r.VerifierHash=e.slice(e.l,a),e.l=a,r}function t0(e){var t=ut(e);switch(t.Minor){case 2:return[t.Minor,a0(e)];case 3:return[t.Minor,n0()];case 4:return[t.Minor,i0(e)]}throw new Error("ECMA-376 Encrypted file unrecognized Version: "+t.Minor)}function a0(e){var t=e.read_shift(4);if((t&63)!=36)throw new Error("EncryptionInfo mismatch");var r=e.read_shift(4),a=Ac(e,r),n=Cc(e,e.length-e.l);return{t:"Std",h:a,v:n}}function n0(){throw new Error("File is password-protected: ECMA-376 Extensible")}function i0(e){var t=["saltSize","blockSize","keyBits","hashSize","cipherAlgorithm","cipherChaining","hashAlgorithm","saltValue"];e.l+=4;var r=e.read_shift(e.length-e.l,"utf8"),a={};return r.replace(Er,function(i){var s=ge(i);switch(it(s[0])){case"<?xml":break;case"<encryption":case"</encryption>":break;case"<keyData":t.forEach(function(f){a[f]=s[f]});break;case"<dataIntegrity":a.encryptedHmacKey=s.encryptedHmacKey,a.encryptedHmacValue=s.encryptedHmacValue;break;case"<keyEncryptors>":case"<keyEncryptors":a.encs=[];break;case"</keyEncryptors>":break;case"<keyEncryptor":a.uri=s.uri;break;case"</keyEncryptor>":break;case"<encryptedKey":a.encs.push(s);break;default:throw s[0]}}),a}function s0(e,t){var r={},a=r.EncryptionVersionInfo=ut(e,4);if(t-=4,a.Minor!=2)throw new Error("unrecognized minor version code: "+a.Minor);if(a.Major>4||a.Major<2)throw new Error("unrecognized major version code: "+a.Major);r.Flags=e.read_shift(4),t-=4;var n=e.read_shift(4);return t-=4,r.EncryptionHeader=Ac(e,n),t-=n,r.EncryptionVerifier=Cc(e,t),r}function f0(e){var t={},r=t.EncryptionVersionInfo=ut(e,4);if(r.Major!=1||r.Minor!=1)throw"unrecognized version code "+r.Major+" : "+r.Minor;return t.Salt=e.read_shift(16),t.EncryptedVerifier=e.read_shift(16),t.EncryptedVerifierHash=e.read_shift(16),t}function Bi(e){var t=0,r,a=xc(e),n=a.length+1,i,s,f,c,o;for(r=St(n),r[0]=a.length,i=1;i!=n;++i)r[i]=a[i-1];for(i=n-1;i>=0;--i)s=r[i],f=(t&16384)===0?0:1,c=t<<1&32767,o=f|c,t=o^s;return t^52811}var Oc=function(){var e=[187,255,255,186,255,255,185,128,0,190,15,0,191,15,0],t=[57840,7439,52380,33984,4364,3600,61902,12606,6258,57657,54287,34041,10252,43370,20163],r=[44796,19929,39858,10053,20106,40212,10761,31585,63170,64933,60267,50935,40399,11199,17763,35526,1453,2906,5812,11624,23248,885,1770,3540,7080,14160,28320,56640,55369,41139,20807,41614,21821,43642,17621,28485,56970,44341,19019,38038,14605,29210,60195,50791,40175,10751,21502,43004,24537,18387,36774,3949,7898,15796,31592,63184,47201,24803,49606,37805,14203,28406,56812,17824,35648,1697,3394,6788,13576,27152,43601,17539,35078,557,1114,2228,4456,30388,60776,51953,34243,7079,14158,28316,14128,28256,56512,43425,17251,34502,7597,13105,26210,52420,35241,883,1766,3532,4129,8258,16516,33032,4657,9314,18628],a=function(s){return(s/2|s*128)&255},n=function(s,f){return a(s^f)},i=function(s){for(var f=t[s.length-1],c=104,o=s.length-1;o>=0;--o)for(var l=s[o],h=0;h!=7;++h)l&64&&(f^=r[c]),l*=2,--c;return f};return function(s){for(var f=xc(s),c=i(f),o=f.length,l=St(16),h=0;h!=16;++h)l[h]=0;var d,v,p;for((o&1)===1&&(d=c>>8,l[o]=n(e[0],d),--o,d=c&255,v=f[f.length-1],l[o]=n(v,d));o>0;)--o,d=c>>8,l[o]=n(f[o],d),--o,d=c&255,l[o]=n(f[o],d);for(o=15,p=15-f.length;p>0;)d=c>>8,l[o]=n(e[p],d),--o,--p,d=c&255,l[o]=n(f[o],d),--o,--p;return l}}(),c0=function(e,t,r,a,n){n||(n=t),a||(a=Oc(e));var i,s;for(i=0;i!=t.length;++i)s=t[i],s^=a[r],s=(s>>5|s<<3)&255,n[i]=s,++r;return[n,r,a]},o0=function(e){var t=0,r=Oc(e);return function(a){var n=c0("",a,t,r);return t=n[1],n[0]}};function l0(e,t,r,a){var n={key:tr(e),verificationBytes:tr(e)};return r.password&&(n.verifier=Bi(r.password)),a.valid=n.verificationBytes===n.verifier,a.valid&&(a.insitu=o0(r.password)),n}function h0(e,t,r){var a=r||{};return a.Info=e.read_shift(2),e.l-=2,a.Info===1?a.Data=f0(e):a.Data=s0(e,t),a}function u0(e,t,r){var a={Type:r.biff>=8?e.read_shift(2):0};return a.Type?h0(e,t-2,a):l0(e,r.biff>=8?t:t-2,r,a),a}var Ic=function(){function e(n,i){switch(i.type){case"base64":return t(Dr(n),i);case"binary":return t(n,i);case"buffer":return t(Se&&Buffer.isBuffer(n)?n.toString("binary"):Ct(n),i);case"array":return t(Ut(n),i)}throw new Error("Unrecognized type "+i.type)}function t(n,i){var s=i||{},f=s.dense?[]:{},c=n.match(/\\trowd.*?\\row\b/g);if(!c.length)throw new Error("RTF missing table");var o={s:{c:0,r:0},e:{c:0,r:c.length-1}};return c.forEach(function(l,h){Array.isArray(f)&&(f[h]=[]);for(var d=/\\\w+\b/g,v=0,p,u=-1;p=d.exec(l);){switch(p[0]){case"\\cell":var m=l.slice(v,d.lastIndex-p[0].length);if(m[0]==" "&&(m=m.slice(1)),++u,m.length){var k={v:m,t:"s"};Array.isArray(f)?f[h][u]=k:f[me({r:h,c:u})]=k}break}v=d.lastIndex}u>o.e.c&&(o.e.c=u)}),f["!ref"]=ke(o),f}function r(n,i){return Ot(e(n,i),i)}function a(n){for(var i=["{\\rtf1\\ansi"],s=Ce(n["!ref"]),f,c=Array.isArray(n),o=s.s.r;o<=s.e.r;++o){i.push("\\trowd\\trautofit1");for(var l=s.s.c;l<=s.e.c;++l)i.push("\\cellx"+(l+1));for(i.push("\\pard\\intbl"),l=s.s.c;l<=s.e.c;++l){var h=me({r:o,c:l});f=c?(n[o]||[])[l]:n[h],!(!f||f.v==null&&(!f.f||f.F))&&(i.push(" "+(f.w||(nt(f),f.w))),i.push("\\cell"))}i.push("\\pard\\intbl\\row")}return i.join("")+"}"}return{to_workbook:r,to_sheet:e,from_sheet:a}}();function d0(e){var t=e.slice(e[0]==="#"?1:0).slice(0,6);return[parseInt(t.slice(0,2),16),parseInt(t.slice(2,4),16),parseInt(t.slice(4,6),16)]}function Ba(e){for(var t=0,r=1;t!=3;++t)r=r*256+(e[t]>255?255:e[t]<0?0:e[t]);return r.toString(16).toUpperCase().slice(1)}function v0(e){var t=e[0]/255,r=e[1]/255,a=e[2]/255,n=Math.max(t,r,a),i=Math.min(t,r,a),s=n-i;if(s===0)return[0,0,t];var f=0,c=0,o=n+i;switch(c=s/(o>1?2-o:o),n){case t:f=((r-a)/s+6)%6;break;case r:f=(a-t)/s+2;break;case a:f=(t-r)/s+4;break}return[f/6,c,o/2]}function p0(e){var t=e[0],r=e[1],a=e[2],n=r*2*(a<.5?a:1-a),i=a-n/2,s=[i,i,i],f=6*t,c;if(r!==0)switch(f|0){case 0:case 6:c=n*f,s[0]+=n,s[1]+=c;break;case 1:c=n*(2-f),s[0]+=c,s[1]+=n;break;case 2:c=n*(f-2),s[1]+=n,s[2]+=c;break;case 3:c=n*(4-f),s[1]+=c,s[2]+=n;break;case 4:c=n*(f-4),s[2]+=n,s[0]+=c;break;case 5:c=n*(6-f),s[2]+=c,s[0]+=n;break}for(var o=0;o!=3;++o)s[o]=Math.round(s[o]*255);return s}function En(e,t){if(t===0)return e;var r=v0(d0(e));return t<0?r[2]=r[2]*(1+t):r[2]=1-(1-r[2])*(1-t),Ba(p0(r))}var Fc=6,m0=15,g0=1,gr=Fc;function Ua(e){return Math.floor((e+Math.round(128/gr)/256)*gr)}function Wa(e){return Math.floor((e-5)/gr*100+.5)/100}function Sn(e){return Math.round((e*gr+5)/gr*256)/256}function Xn(e){return Sn(Wa(Ua(e)))}function Ui(e){var t=Math.abs(e-Xn(e)),r=gr;if(t>.005)for(gr=g0;gr<m0;++gr)Math.abs(e-Xn(e))<=t&&(t=Math.abs(e-Xn(e)),r=gr);gr=r}function yt(e){e.width?(e.wpx=Ua(e.width),e.wch=Wa(e.wpx),e.MDW=gr):e.wpx?(e.wch=Wa(e.wpx),e.width=Sn(e.wch),e.MDW=gr):typeof e.wch=="number"&&(e.width=Sn(e.wch),e.wpx=Ua(e.width),e.MDW=gr),e.customWidth&&delete e.customWidth}var _0=96,Rc=_0;function Ha(e){return e*96/Rc}function oa(e){return e*Rc/96}var w0={None:"none",Solid:"solid",Gray50:"mediumGray",Gray75:"darkGray",Gray25:"lightGray",HorzStripe:"darkHorizontal",VertStripe:"darkVertical",ReverseDiagStripe:"darkDown",DiagStripe:"darkUp",DiagCross:"darkGrid",ThickDiagCross:"darkTrellis",ThinHorzStripe:"lightHorizontal",ThinVertStripe:"lightVertical",ThinReverseDiagStripe:"lightDown",ThinHorzCross:"lightGrid"};function k0(e,t,r,a){t.Borders=[];var n={},i=!1;(e[0].match(Er)||[]).forEach(function(s){var f=ge(s);switch(it(f[0])){case"<borders":case"<borders>":case"</borders>":break;case"<border":case"<border>":case"<border/>":n={},f.diagonalUp&&(n.diagonalUp=We(f.diagonalUp)),f.diagonalDown&&(n.diagonalDown=We(f.diagonalDown)),t.Borders.push(n);break;case"</border>":break;case"<left/>":break;case"<left":case"<left>":break;case"</left>":break;case"<right/>":break;case"<right":case"<right>":break;case"</right>":break;case"<top/>":break;case"<top":case"<top>":break;case"</top>":break;case"<bottom/>":break;case"<bottom":case"<bottom>":break;case"</bottom>":break;case"<diagonal":case"<diagonal>":case"<diagonal/>":break;case"</diagonal>":break;case"<horizontal":case"<horizontal>":case"<horizontal/>":break;case"</horizontal>":break;case"<vertical":case"<vertical>":case"<vertical/>":break;case"</vertical>":break;case"<start":case"<start>":case"<start/>":break;case"</start>":break;case"<end":case"<end>":case"<end/>":break;case"</end>":break;case"<color":case"<color>":break;case"<color/>":case"</color>":break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":i=!0;break;case"</ext>":i=!1;break;default:if(a&&a.WTF&&!i)throw new Error("unrecognized "+f[0]+" in borders")}})}function T0(e,t,r,a){t.Fills=[];var n={},i=!1;(e[0].match(Er)||[]).forEach(function(s){var f=ge(s);switch(it(f[0])){case"<fills":case"<fills>":case"</fills>":break;case"<fill>":case"<fill":case"<fill/>":n={},t.Fills.push(n);break;case"</fill>":break;case"<gradientFill>":break;case"<gradientFill":case"</gradientFill>":t.Fills.push(n),n={};break;case"<patternFill":case"<patternFill>":f.patternType&&(n.patternType=f.patternType);break;case"<patternFill/>":case"</patternFill>":break;case"<bgColor":n.bgColor||(n.bgColor={}),f.indexed&&(n.bgColor.indexed=parseInt(f.indexed,10)),f.theme&&(n.bgColor.theme=parseInt(f.theme,10)),f.tint&&(n.bgColor.tint=parseFloat(f.tint)),f.rgb&&(n.bgColor.rgb=f.rgb.slice(-6));break;case"<bgColor/>":case"</bgColor>":break;case"<fgColor":n.fgColor||(n.fgColor={}),f.theme&&(n.fgColor.theme=parseInt(f.theme,10)),f.tint&&(n.fgColor.tint=parseFloat(f.tint)),f.rgb!=null&&(n.fgColor.rgb=f.rgb.slice(-6));break;case"<fgColor/>":case"</fgColor>":break;case"<stop":case"<stop/>":break;case"</stop>":break;case"<color":case"<color/>":break;case"</color>":break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":i=!0;break;case"</ext>":i=!1;break;default:if(a&&a.WTF&&!i)throw new Error("unrecognized "+f[0]+" in fills")}})}function E0(e,t,r,a){t.Fonts=[];var n={},i=!1;(e[0].match(Er)||[]).forEach(function(s){var f=ge(s);switch(it(f[0])){case"<fonts":case"<fonts>":case"</fonts>":break;case"<font":case"<font>":break;case"</font>":case"<font/>":t.Fonts.push(n),n={};break;case"<name":f.val&&(n.name=be(f.val));break;case"<name/>":case"</name>":break;case"<b":n.bold=f.val?We(f.val):1;break;case"<b/>":n.bold=1;break;case"<i":n.italic=f.val?We(f.val):1;break;case"<i/>":n.italic=1;break;case"<u":switch(f.val){case"none":n.underline=0;break;case"single":n.underline=1;break;case"double":n.underline=2;break;case"singleAccounting":n.underline=33;break;case"doubleAccounting":n.underline=34;break}break;case"<u/>":n.underline=1;break;case"<strike":n.strike=f.val?We(f.val):1;break;case"<strike/>":n.strike=1;break;case"<outline":n.outline=f.val?We(f.val):1;break;case"<outline/>":n.outline=1;break;case"<shadow":n.shadow=f.val?We(f.val):1;break;case"<shadow/>":n.shadow=1;break;case"<condense":n.condense=f.val?We(f.val):1;break;case"<condense/>":n.condense=1;break;case"<extend":n.extend=f.val?We(f.val):1;break;case"<extend/>":n.extend=1;break;case"<sz":f.val&&(n.sz=+f.val);break;case"<sz/>":case"</sz>":break;case"<vertAlign":f.val&&(n.vertAlign=f.val);break;case"<vertAlign/>":case"</vertAlign>":break;case"<family":f.val&&(n.family=parseInt(f.val,10));break;case"<family/>":case"</family>":break;case"<scheme":f.val&&(n.scheme=f.val);break;case"<scheme/>":case"</scheme>":break;case"<charset":if(f.val=="1")break;f.codepage=hi[parseInt(f.val,10)];break;case"<color":if(n.color||(n.color={}),f.auto&&(n.color.auto=We(f.auto)),f.rgb)n.color.rgb=f.rgb.slice(-6);else if(f.indexed){n.color.index=parseInt(f.indexed,10);var c=Pt[n.color.index];n.color.index==81&&(c=Pt[1]),c||(c=Pt[1]),n.color.rgb=c[0].toString(16)+c[1].toString(16)+c[2].toString(16)}else f.theme&&(n.color.theme=parseInt(f.theme,10),f.tint&&(n.color.tint=parseFloat(f.tint)),f.theme&&r.themeElements&&r.themeElements.clrScheme&&(n.color.rgb=En(r.themeElements.clrScheme[n.color.theme].rgb,n.color.tint||0)));break;case"<color/>":case"</color>":break;case"<AlternateContent":i=!0;break;case"</AlternateContent>":i=!1;break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":i=!0;break;case"</ext>":i=!1;break;default:if(a&&a.WTF&&!i)throw new Error("unrecognized "+f[0]+" in fonts")}})}function S0(e,t,r){t.NumberFmt=[];for(var a=je(ve),n=0;n<a.length;++n)t.NumberFmt[a[n]]=ve[a[n]];var i=e[0].match(Er);if(i)for(n=0;n<i.length;++n){var s=ge(i[n]);switch(it(s[0])){case"<numFmts":case"</numFmts>":case"<numFmts/>":case"<numFmts>":break;case"<numFmt":{var f=Fe(be(s.formatCode)),c=parseInt(s.numFmtId,10);if(t.NumberFmt[c]=f,c>0){if(c>392){for(c=392;c>60&&t.NumberFmt[c]!=null;--c);t.NumberFmt[c]=f}at(f,c)}}break;case"</numFmt>":break;default:if(r.WTF)throw new Error("unrecognized "+s[0]+" in numFmts")}}}function y0(e){var t=["<numFmts>"];return[[5,8],[23,26],[41,44],[50,392]].forEach(function(r){for(var a=r[0];a<=r[1];++a)e[a]!=null&&(t[t.length]=ae("numFmt",null,{numFmtId:a,formatCode:De(e[a])}))}),t.length===1?"":(t[t.length]="</numFmts>",t[0]=ae("numFmts",null,{count:t.length-2}).replace("/>",">"),t.join(""))}var cn=["numFmtId","fillId","fontId","borderId","xfId"],on=["applyAlignment","applyBorder","applyFill","applyFont","applyNumberFormat","applyProtection","pivotButton","quotePrefix"];function x0(e,t,r){t.CellXf=[];var a,n=!1;(e[0].match(Er)||[]).forEach(function(i){var s=ge(i),f=0;switch(it(s[0])){case"<cellXfs":case"<cellXfs>":case"<cellXfs/>":case"</cellXfs>":break;case"<xf":case"<xf/>":for(a=s,delete a[0],f=0;f<cn.length;++f)a[cn[f]]&&(a[cn[f]]=parseInt(a[cn[f]],10));for(f=0;f<on.length;++f)a[on[f]]&&(a[on[f]]=We(a[on[f]]));if(t.NumberFmt&&a.numFmtId>392){for(f=392;f>60;--f)if(t.NumberFmt[a.numFmtId]==t.NumberFmt[f]){a.numFmtId=f;break}}t.CellXf.push(a);break;case"</xf>":break;case"<alignment":case"<alignment/>":var c={};s.vertical&&(c.vertical=s.vertical),s.horizontal&&(c.horizontal=s.horizontal),s.textRotation!=null&&(c.textRotation=s.textRotation),s.indent&&(c.indent=s.indent),s.wrapText&&(c.wrapText=We(s.wrapText)),a.alignment=c;break;case"</alignment>":break;case"<protection":break;case"</protection>":case"<protection/>":break;case"<AlternateContent":n=!0;break;case"</AlternateContent>":n=!1;break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":n=!0;break;case"</ext>":n=!1;break;default:if(r&&r.WTF&&!n)throw new Error("unrecognized "+s[0]+" in cellXfs")}})}function A0(e){var t=[];return t[t.length]=ae("cellXfs",null),e.forEach(function(r){t[t.length]=ae("xf",null,r)}),t[t.length]="</cellXfs>",t.length===2?"":(t[0]=ae("cellXfs",null,{count:t.length-2}).replace("/>",">"),t.join(""))}var C0=function(){var t=/<(?:\w+:)?numFmts([^>]*)>[\S\s]*?<\/(?:\w+:)?numFmts>/,r=/<(?:\w+:)?cellXfs([^>]*)>[\S\s]*?<\/(?:\w+:)?cellXfs>/,a=/<(?:\w+:)?fills([^>]*)>[\S\s]*?<\/(?:\w+:)?fills>/,n=/<(?:\w+:)?fonts([^>]*)>[\S\s]*?<\/(?:\w+:)?fonts>/,i=/<(?:\w+:)?borders([^>]*)>[\S\s]*?<\/(?:\w+:)?borders>/;return function(f,c,o){var l={};if(!f)return l;f=f.replace(/<!--([\s\S]*?)-->/mg,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");var h;return(h=f.match(t))&&S0(h,l,o),(h=f.match(n))&&E0(h,l,c,o),(h=f.match(a))&&T0(h,l,c,o),(h=f.match(i))&&k0(h,l,c,o),(h=f.match(r))&&x0(h,l,o),l}}();function Nc(e,t){var r=[Qe,ae("styleSheet",null,{xmlns:zt[0],"xmlns:vt":nr.vt})],a;return e.SSF&&(a=y0(e.SSF))!=null&&(r[r.length]=a),r[r.length]='<fonts count="1"><font><sz val="12"/><color theme="1"/><name val="Calibri"/><family val="2"/><scheme val="minor"/></font></fonts>',r[r.length]='<fills count="2"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="gray125"/></fill></fills>',r[r.length]='<borders count="1"><border><left/><right/><top/><bottom/><diagonal/></border></borders>',r[r.length]='<cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0"/></cellStyleXfs>',(a=A0(t.cellXfs))&&(r[r.length]=a),r[r.length]='<cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0"/></cellStyles>',r[r.length]='<dxfs count="0"/>',r[r.length]='<tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4"/>',r.length>2&&(r[r.length]="</styleSheet>",r[1]=r[1].replace("/>",">")),r.join("")}function O0(e,t){var r=e.read_shift(2),a=kr(e);return[r,a]}function I0(e,t,r){r||(r=G(6+4*t.length)),r.write_shift(2,e),or(t,r);var a=r.length>r.l?r.slice(0,r.l):r;return r.l==null&&(r.l=r.length),a}function F0(e,t,r){var a={};a.sz=e.read_shift(2)/20;var n=h1(e);n.fItalic&&(a.italic=1),n.fCondense&&(a.condense=1),n.fExtend&&(a.extend=1),n.fShadow&&(a.shadow=1),n.fOutline&&(a.outline=1),n.fStrikeout&&(a.strike=1);var i=e.read_shift(2);switch(i===700&&(a.bold=1),e.read_shift(2)){case 1:a.vertAlign="superscript";break;case 2:a.vertAlign="subscript";break}var s=e.read_shift(1);s!=0&&(a.underline=s);var f=e.read_shift(1);f>0&&(a.family=f);var c=e.read_shift(1);switch(c>0&&(a.charset=c),e.l++,a.color=l1(e),e.read_shift(1)){case 1:a.scheme="major";break;case 2:a.scheme="minor";break}return a.name=kr(e),a}function R0(e,t){t||(t=G(25+4*32)),t.write_shift(2,e.sz*20),u1(e,t),t.write_shift(2,e.bold?700:400);var r=0;e.vertAlign=="superscript"?r=1:e.vertAlign=="subscript"&&(r=2),t.write_shift(2,r),t.write_shift(1,e.underline||0),t.write_shift(1,e.family||0),t.write_shift(1,e.charset||0),t.write_shift(1,0),wn(e.color,t);var a=0;return a=2,t.write_shift(1,a),or(e.name,t),t.length>t.l?t.slice(0,t.l):t}var N0=["none","solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"],Gn,P0=Tr;function js(e,t){t||(t=G(4*3+8*7+16*1)),Gn||(Gn=Cn(N0));var r=Gn[e.patternType];r==null&&(r=40),t.write_shift(4,r);var a=0;if(r!=40)for(wn({auto:1},t),wn({auto:1},t);a<12;++a)t.write_shift(4,0);else{for(;a<4;++a)t.write_shift(4,0);for(;a<12;++a)t.write_shift(4,0)}return t.length>t.l?t.slice(0,t.l):t}function D0(e,t){var r=e.l+t,a=e.read_shift(2),n=e.read_shift(2);return e.l=r,{ixfe:a,numFmtId:n}}function Pc(e,t,r){r||(r=G(16)),r.write_shift(2,t||0),r.write_shift(2,e.numFmtId||0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(1,0),r.write_shift(1,0);var a=0;return r.write_shift(1,a),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r}function wa(e,t){return t||(t=G(10)),t.write_shift(1,0),t.write_shift(1,0),t.write_shift(4,0),t.write_shift(4,0),t}var L0=Tr;function b0(e,t){return t||(t=G(51)),t.write_shift(1,0),wa(null,t),wa(null,t),wa(null,t),wa(null,t),wa(null,t),t.length>t.l?t.slice(0,t.l):t}function M0(e,t){return t||(t=G(12+4*10)),t.write_shift(4,e.xfId),t.write_shift(2,1),t.write_shift(1,0),t.write_shift(1,0),_n(e.name||"",t),t.length>t.l?t.slice(0,t.l):t}function B0(e,t,r){var a=G(2052);return a.write_shift(4,e),_n(t,a),_n(r,a),a.length>a.l?a.slice(0,a.l):a}function U0(e,t,r){var a={};a.NumberFmt=[];for(var n in ve)a.NumberFmt[n]=ve[n];a.CellXf=[],a.Fonts=[];var i=[],s=!1;return vt(e,function(c,o,l){switch(l){case 44:a.NumberFmt[c[0]]=c[1],at(c[1],c[0]);break;case 43:a.Fonts.push(c),c.color.theme!=null&&t&&t.themeElements&&t.themeElements.clrScheme&&(c.color.rgb=En(t.themeElements.clrScheme[c.color.theme].rgb,c.color.tint||0));break;case 1025:break;case 45:break;case 46:break;case 47:i[i.length-1]==617&&a.CellXf.push(c);break;case 48:case 507:case 572:case 475:break;case 1171:case 2102:case 1130:case 512:case 2095:case 3072:break;case 35:s=!0;break;case 36:s=!1;break;case 37:i.push(l),s=!0;break;case 38:i.pop(),s=!1;break;default:if(o.T>0)i.push(l);else if(o.T<0)i.pop();else if(!s||r.WTF&&i[i.length-1]!=37)throw new Error("Unexpected record 0x"+l.toString(16))}}),a}function W0(e,t){if(t){var r=0;[[5,8],[23,26],[41,44],[50,392]].forEach(function(a){for(var n=a[0];n<=a[1];++n)t[n]!=null&&++r}),r!=0&&(j(e,615,Jr(r)),[[5,8],[23,26],[41,44],[50,392]].forEach(function(a){for(var n=a[0];n<=a[1];++n)t[n]!=null&&j(e,44,I0(n,t[n]))}),j(e,616))}}function H0(e){var t=1;j(e,611,Jr(t)),j(e,43,R0({sz:12,color:{theme:1},name:"Calibri",family:2})),j(e,612)}function V0(e){var t=2;j(e,603,Jr(t)),j(e,45,js({patternType:"none"})),j(e,45,js({patternType:"gray125"})),j(e,604)}function X0(e){var t=1;j(e,613,Jr(t)),j(e,46,b0()),j(e,614)}function G0(e){var t=1;j(e,626,Jr(t)),j(e,47,Pc({numFmtId:0},65535)),j(e,627)}function z0(e,t){j(e,617,Jr(t.length)),t.forEach(function(r){j(e,47,Pc(r,0))}),j(e,618)}function $0(e){var t=1;j(e,619,Jr(t)),j(e,48,M0({xfId:0,name:"Normal"})),j(e,620)}function K0(e){var t=0;j(e,505,Jr(t)),j(e,506)}function Y0(e){var t=0;j(e,508,B0(t,"TableStyleMedium9","PivotStyleMedium4")),j(e,509)}function j0(e,t){var r=Or();return j(r,278),W0(r,e.SSF),H0(r),V0(r),X0(r),G0(r),z0(r,t.cellXfs),$0(r),K0(r),Y0(r),j(r,279),r.end()}var J0=["</a:lt1>","</a:dk1>","</a:lt2>","</a:dk2>","</a:accent1>","</a:accent2>","</a:accent3>","</a:accent4>","</a:accent5>","</a:accent6>","</a:hlink>","</a:folHlink>"];function Z0(e,t,r){t.themeElements.clrScheme=[];var a={};(e[0].match(Er)||[]).forEach(function(n){var i=ge(n);switch(i[0]){case"<a:clrScheme":case"</a:clrScheme>":break;case"<a:srgbClr":a.rgb=i.val;break;case"<a:sysClr":a.rgb=i.lastClr;break;case"<a:dk1>":case"</a:dk1>":case"<a:lt1>":case"</a:lt1>":case"<a:dk2>":case"</a:dk2>":case"<a:lt2>":case"</a:lt2>":case"<a:accent1>":case"</a:accent1>":case"<a:accent2>":case"</a:accent2>":case"<a:accent3>":case"</a:accent3>":case"<a:accent4>":case"</a:accent4>":case"<a:accent5>":case"</a:accent5>":case"<a:accent6>":case"</a:accent6>":case"<a:hlink>":case"</a:hlink>":case"<a:folHlink>":case"</a:folHlink>":i[0].charAt(1)==="/"?(t.themeElements.clrScheme[J0.indexOf(i[0])]=a,a={}):a.name=i[0].slice(3,i[0].length-1);break;default:if(r&&r.WTF)throw new Error("Unrecognized "+i[0]+" in clrScheme")}})}function q0(){}function Q0(){}var e2=/<a:clrScheme([^>]*)>[\s\S]*<\/a:clrScheme>/,r2=/<a:fontScheme([^>]*)>[\s\S]*<\/a:fontScheme>/,t2=/<a:fmtScheme([^>]*)>[\s\S]*<\/a:fmtScheme>/;function a2(e,t,r){t.themeElements={};var a;[["clrScheme",e2,Z0],["fontScheme",r2,q0],["fmtScheme",t2,Q0]].forEach(function(n){if(!(a=e.match(n[1])))throw new Error(n[0]+" not found in themeElements");n[2](a,t,r)})}var n2=/<a:themeElements([^>]*)>[\s\S]*<\/a:themeElements>/;function Dc(e,t){(!e||e.length===0)&&(e=Wi());var r,a={};if(!(r=e.match(n2)))throw new Error("themeElements not found in theme");return a2(r[0],a,t),a.raw=e,a}function Wi(e,t){if(t&&t.themeXLSX)return t.themeXLSX;if(e&&typeof e.raw=="string")return e.raw;var r=[Qe];return r[r.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',r[r.length]="<a:themeElements>",r[r.length]='<a:clrScheme name="Office">',r[r.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',r[r.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',r[r.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',r[r.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',r[r.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',r[r.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',r[r.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',r[r.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',r[r.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',r[r.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',r[r.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',r[r.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',r[r.length]="</a:clrScheme>",r[r.length]='<a:fontScheme name="Office">',r[r.length]="<a:majorFont>",r[r.length]='<a:latin typeface="Cambria"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Times New Roman"/>',r[r.length]='<a:font script="Hebr" typeface="Times New Roman"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="MoolBoran"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Times New Roman"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:majorFont>",r[r.length]="<a:minorFont>",r[r.length]='<a:latin typeface="Calibri"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Arial"/>',r[r.length]='<a:font script="Hebr" typeface="Arial"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="DaunPenh"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Arial"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:minorFont>",r[r.length]="</a:fontScheme>",r[r.length]='<a:fmtScheme name="Office">',r[r.length]="<a:fillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="1"/>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="0"/>',r[r.length]="</a:gradFill>",r[r.length]="</a:fillStyleLst>",r[r.length]="<a:lnStyleLst>",r[r.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]="</a:lnStyleLst>",r[r.length]="<a:effectStyleLst>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',r[r.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',r[r.length]="</a:effectStyle>",r[r.length]="</a:effectStyleLst>",r[r.length]="<a:bgFillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]="</a:bgFillStyleLst>",r[r.length]="</a:fmtScheme>",r[r.length]="</a:themeElements>",r[r.length]="<a:objectDefaults>",r[r.length]="<a:spDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',r[r.length]="</a:spDef>",r[r.length]="<a:lnDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',r[r.length]="</a:lnDef>",r[r.length]="</a:objectDefaults>",r[r.length]="<a:extraClrSchemeLst/>",r[r.length]="</a:theme>",r.join("")}function i2(e,t,r){var a=e.l+t,n=e.read_shift(4);if(n!==124226){if(!r.cellStyles){e.l=a;return}var i=e.slice(e.l);e.l=a;var s;try{s=Ff(i,{type:"array"})}catch{return}var f=Nr(s,"theme/theme/theme1.xml",!0);if(f)return Dc(f,r)}}function s2(e){return e.read_shift(4)}function f2(e){var t={};switch(t.xclrType=e.read_shift(2),t.nTintShade=e.read_shift(2),t.xclrType){case 0:e.l+=4;break;case 1:t.xclrValue=c2(e,4);break;case 2:t.xclrValue=dc(e);break;case 3:t.xclrValue=s2(e);break;case 4:e.l+=4;break}return e.l+=8,t}function c2(e,t){return Tr(e,t)}function o2(e,t){return Tr(e,t)}function l2(e){var t=e.read_shift(2),r=e.read_shift(2)-4,a=[t];switch(t){case 4:case 5:case 7:case 8:case 9:case 10:case 11:case 13:a[1]=f2(e);break;case 6:a[1]=o2(e,r);break;case 14:case 15:a[1]=e.read_shift(r===1?1:2);break;default:throw new Error("Unrecognized ExtProp type: "+t+" "+r)}return a}function h2(e,t){var r=e.l+t;e.l+=2;var a=e.read_shift(2);e.l+=2;for(var n=e.read_shift(2),i=[];n-- >0;)i.push(l2(e,r-e.l));return{ixfe:a,ext:i}}function u2(e,t){t.forEach(function(r){r[0]})}function d2(e,t){return{flags:e.read_shift(4),version:e.read_shift(4),name:kr(e)}}function v2(e){var t=G(12+2*e.name.length);return t.write_shift(4,e.flags),t.write_shift(4,e.version),or(e.name,t),t.slice(0,t.l)}function p2(e){for(var t=[],r=e.read_shift(4);r-- >0;)t.push([e.read_shift(4),e.read_shift(4)]);return t}function m2(e){var t=G(4+8*e.length);t.write_shift(4,e.length);for(var r=0;r<e.length;++r)t.write_shift(4,e[r][0]),t.write_shift(4,e[r][1]);return t}function g2(e,t){var r=G(8+2*t.length);return r.write_shift(4,e),or(t,r),r.slice(0,r.l)}function _2(e){return e.l+=4,e.read_shift(4)!=0}function w2(e,t){var r=G(8);return r.write_shift(4,e),r.write_shift(4,1),r}function k2(e,t,r){var a={Types:[],Cell:[],Value:[]},n=r||{},i=[],s=!1,f=2;return vt(e,function(c,o,l){switch(l){case 335:a.Types.push({name:c.name});break;case 51:c.forEach(function(h){f==1?a.Cell.push({type:a.Types[h[0]-1].name,index:h[1]}):f==0&&a.Value.push({type:a.Types[h[0]-1].name,index:h[1]})});break;case 337:f=c?1:0;break;case 338:f=2;break;case 35:i.push(l),s=!0;break;case 36:i.pop(),s=!1;break;default:if(!o.T){if(!s||n.WTF&&i[i.length-1]!=35)throw new Error("Unexpected record 0x"+l.toString(16))}}}),a}function T2(){var e=Or();return j(e,332),j(e,334,Jr(1)),j(e,335,v2({name:"XLDAPR",version:12e4,flags:3496657072})),j(e,336),j(e,339,g2(1,"XLDAPR")),j(e,52),j(e,35,Jr(514)),j(e,4096,Jr(0)),j(e,4097,Xr(1)),j(e,36),j(e,53),j(e,340),j(e,337,w2(1)),j(e,51,m2([[1,0]])),j(e,338),j(e,333),e.end()}function E2(e,t,r){var a={Types:[],Cell:[],Value:[]};if(!e)return a;var n=!1,i=2,s;return e.replace(Er,function(f){var c=ge(f);switch(it(c[0])){case"<?xml":break;case"<metadata":case"</metadata>":break;case"<metadataTypes":case"</metadataTypes>":break;case"<metadataType":a.Types.push({name:c.name});break;case"</metadataType>":break;case"<futureMetadata":for(var o=0;o<a.Types.length;++o)a.Types[o].name==c.name&&(s=a.Types[o]);break;case"</futureMetadata>":break;case"<bk>":break;case"</bk>":break;case"<rc":i==1?a.Cell.push({type:a.Types[c.t-1].name,index:+c.v}):i==0&&a.Value.push({type:a.Types[c.t-1].name,index:+c.v});break;case"</rc>":break;case"<cellMetadata":i=1;break;case"</cellMetadata>":i=2;break;case"<valueMetadata":i=0;break;case"</valueMetadata>":i=2;break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":n=!0;break;case"</ext>":n=!1;break;case"<rvb":if(!s)break;s.offsets||(s.offsets=[]),s.offsets.push(+c.i);break;default:if(!n&&r.WTF)throw new Error("unrecognized "+c[0]+" in metadata")}return f}),a}function Lc(){var e=[Qe];return e.push(`<metadata xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:xlrd="http://schemas.microsoft.com/office/spreadsheetml/2017/richdata" xmlns:xda="http://schemas.microsoft.com/office/spreadsheetml/2017/dynamicarray">
  <metadataTypes count="1">
    <metadataType name="XLDAPR" minSupportedVersion="120000" copy="1" pasteAll="1" pasteValues="1" merge="1" splitFirst="1" rowColShift="1" clearFormats="1" clearComments="1" assign="1" coerce="1" cellMeta="1"/>
  </metadataTypes>
  <futureMetadata name="XLDAPR" count="1">
    <bk>
      <extLst>
        <ext uri="{bdbb8cdc-fa1e-496e-a857-3c3f30c029c3}">
          <xda:dynamicArrayProperties fDynamic="1" fCollapsed="0"/>
        </ext>
      </extLst>
    </bk>
  </futureMetadata>
  <cellMetadata count="1">
    <bk>
      <rc t="1" v="0"/>
    </bk>
  </cellMetadata>
</metadata>`),e.join("")}function S2(e){var t=[];if(!e)return t;var r=1;return(e.match(Er)||[]).forEach(function(a){var n=ge(a);switch(n[0]){case"<?xml":break;case"<calcChain":case"<calcChain>":case"</calcChain>":break;case"<c":delete n[0],n.i?r=n.i:n.i=r,t.push(n);break}}),t}function y2(e){var t={};t.i=e.read_shift(4);var r={};r.r=e.read_shift(4),r.c=e.read_shift(4),t.r=me(r);var a=e.read_shift(1);return a&2&&(t.l="1"),a&8&&(t.a="1"),t}function x2(e,t,r){var a=[];return vt(e,function(i,s,f){switch(f){case 63:a.push(i);break;default:if(!s.T)throw new Error("Unexpected record 0x"+f.toString(16))}}),a}function A2(e,t,r,a){if(!e)return e;var n=a||{},i=!1;vt(e,function(f,c,o){switch(o){case 359:case 363:case 364:case 366:case 367:case 368:case 369:case 370:case 371:case 472:case 577:case 578:case 579:case 580:case 581:case 582:case 583:case 584:case 585:case 586:case 587:break;case 35:i=!0;break;case 36:i=!1;break;default:if(!c.T){if(!i||n.WTF)throw new Error("Unexpected record 0x"+o.toString(16))}}},n)}function C2(e,t){if(!e)return"??";var r=(e.match(/<c:chart [^>]*r:id="([^"]*)"/)||["",""])[1];return t["!id"][r].Target}var ta=1024;function bc(e,t){for(var r=[21600,21600],a=["m0,0l0",r[1],r[0],r[1],r[0],"0xe"].join(","),n=[ae("xml",null,{"xmlns:v":Fr.v,"xmlns:o":Fr.o,"xmlns:x":Fr.x,"xmlns:mv":Fr.mv}).replace(/\/>/,">"),ae("o:shapelayout",ae("o:idmap",null,{"v:ext":"edit",data:e}),{"v:ext":"edit"}),ae("v:shapetype",[ae("v:stroke",null,{joinstyle:"miter"}),ae("v:path",null,{gradientshapeok:"t","o:connecttype":"rect"})].join(""),{id:"_x0000_t202","o:spt":202,coordsize:r.join(","),path:a})];ta<e*1e3;)ta+=1e3;return t.forEach(function(i){var s=ze(i[0]),f={color2:"#BEFF82",type:"gradient"};f.type=="gradient"&&(f.angle="-180");var c=f.type=="gradient"?ae("o:fill",null,{type:"gradientUnscaled","v:ext":"view"}):null,o=ae("v:fill",c,f),l={on:"t",obscured:"t"};++ta,n=n.concat(["<v:shape"+La({id:"_x0000_s"+ta,type:"#_x0000_t202",style:"position:absolute; margin-left:80pt;margin-top:5pt;width:104pt;height:64pt;z-index:10"+(i[1].hidden?";visibility:hidden":""),fillcolor:"#ECFAD4",strokecolor:"#edeaa1"})+">",o,ae("v:shadow",null,l),ae("v:path",null,{"o:connecttype":"none"}),'<v:textbox><div style="text-align:left"></div></v:textbox>','<x:ClientData ObjectType="Note">',"<x:MoveWithCells/>","<x:SizeWithCells/>",ur("x:Anchor",[s.c+1,0,s.r+1,0,s.c+3,20,s.r+5,20].join(",")),ur("x:AutoFill","False"),ur("x:Row",String(s.r)),ur("x:Column",String(s.c)),i[1].hidden?"":"<x:Visible/>","</x:ClientData>","</v:shape>"])}),n.push("</xml>"),n.join("")}function Js(e,t,r,a){var n=Array.isArray(e),i;t.forEach(function(s){var f=ze(s.ref);if(n?(e[f.r]||(e[f.r]=[]),i=e[f.r][f.c]):i=e[s.ref],!i){i={t:"z"},n?e[f.r][f.c]=i:e[s.ref]=i;var c=Ce(e["!ref"]||"BDWGO1000001:A1");c.s.r>f.r&&(c.s.r=f.r),c.e.r<f.r&&(c.e.r=f.r),c.s.c>f.c&&(c.s.c=f.c),c.e.c<f.c&&(c.e.c=f.c);var o=ke(c);o!==e["!ref"]&&(e["!ref"]=o)}i.c||(i.c=[]);var l={a:s.author,t:s.t,r:s.r,T:r};s.h&&(l.h=s.h);for(var h=i.c.length-1;h>=0;--h){if(!r&&i.c[h].T)return;r&&!i.c[h].T&&i.c.splice(h,1)}if(r&&a){for(h=0;h<a.length;++h)if(l.a==a[h].id){l.a=a[h].name||l.a;break}}i.c.push(l)})}function O2(e,t){if(e.match(/<(?:\w+:)?comments *\/>/))return[];var r=[],a=[],n=e.match(/<(?:\w+:)?authors>([\s\S]*)<\/(?:\w+:)?authors>/);n&&n[1]&&n[1].split(/<\/\w*:?author>/).forEach(function(s){if(!(s===""||s.trim()==="")){var f=s.match(/<(?:\w+:)?author[^>]*>(.*)/);f&&r.push(f[1])}});var i=e.match(/<(?:\w+:)?commentList>([\s\S]*)<\/(?:\w+:)?commentList>/);return i&&i[1]&&i[1].split(/<\/\w*:?comment>/).forEach(function(s){if(!(s===""||s.trim()==="")){var f=s.match(/<(?:\w+:)?comment[^>]*>/);if(f){var c=ge(f[0]),o={author:c.authorId&&r[c.authorId]||"sheetjsghost",ref:c.ref,guid:c.guid},l=ze(c.ref);if(!(t.sheetRows&&t.sheetRows<=l.r)){var h=s.match(/<(?:\w+:)?text>([\s\S]*)<\/(?:\w+:)?text>/),d=!!h&&!!h[1]&&Mi(h[1])||{r:"",t:"",h:""};o.r=d.r,d.r=="<t></t>"&&(d.t=d.h=""),o.t=(d.t||"").replace(/\r\n/g,`
`).replace(/\r/g,`
`),t.cellHTML&&(o.h=d.h),a.push(o)}}}}),a}function Mc(e){var t=[Qe,ae("comments",null,{xmlns:zt[0]})],r=[];return t.push("<authors>"),e.forEach(function(a){a[1].forEach(function(n){var i=De(n.a);r.indexOf(i)==-1&&(r.push(i),t.push("<author>"+i+"</author>")),n.T&&n.ID&&r.indexOf("tc="+n.ID)==-1&&(r.push("tc="+n.ID),t.push("<author>tc="+n.ID+"</author>"))})}),r.length==0&&(r.push("SheetJ5"),t.push("<author>SheetJ5</author>")),t.push("</authors>"),t.push("<commentList>"),e.forEach(function(a){var n=0,i=[];if(a[1][0]&&a[1][0].T&&a[1][0].ID?n=r.indexOf("tc="+a[1][0].ID):a[1].forEach(function(c){c.a&&(n=r.indexOf(De(c.a))),i.push(c.t||"")}),t.push('<comment ref="'+a[0]+'" authorId="'+n+'"><text>'),i.length<=1)t.push(ur("t",De(i[0]||"")));else{for(var s=`Comment:
    `+i[0]+`
`,f=1;f<i.length;++f)s+=`Reply:
    `+i[f]+`
`;t.push(ur("t",De(s)))}t.push("</text></comment>")}),t.push("</commentList>"),t.length>2&&(t[t.length]="</comments>",t[1]=t[1].replace("/>",">")),t.join("")}function I2(e,t){var r=[],a=!1,n={},i=0;return e.replace(Er,function(f,c){var o=ge(f);switch(it(o[0])){case"<?xml":break;case"<ThreadedComments":break;case"</ThreadedComments>":break;case"<threadedComment":n={author:o.personId,guid:o.id,ref:o.ref,T:1};break;case"</threadedComment>":n.t!=null&&r.push(n);break;case"<text>":case"<text":i=c+f.length;break;case"</text>":n.t=e.slice(i,c).replace(/\r\n/g,`
`).replace(/\r/g,`
`);break;case"<mentions":case"<mentions>":a=!0;break;case"</mentions>":a=!1;break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":a=!0;break;case"</ext>":a=!1;break;default:if(!a&&t.WTF)throw new Error("unrecognized "+o[0]+" in threaded comments")}return f}),r}function F2(e,t,r){var a=[Qe,ae("ThreadedComments",null,{xmlns:nr.TCMNT}).replace(/[\/]>/,">")];return e.forEach(function(n){var i="";(n[1]||[]).forEach(function(s,f){if(!s.T){delete s.ID;return}s.a&&t.indexOf(s.a)==-1&&t.push(s.a);var c={ref:n[0],id:"{54EE7951-**************-"+("000000000000"+r.tcid++).slice(-12)+"}"};f==0?i=c.id:c.parentId=i,s.ID=c.id,s.a&&(c.personId="{54EE7950-**************-"+("000000000000"+t.indexOf(s.a)).slice(-12)+"}"),a.push(ae("threadedComment",ur("text",s.t||""),c))})}),a.push("</ThreadedComments>"),a.join("")}function R2(e,t){var r=[],a=!1;return e.replace(Er,function(i){var s=ge(i);switch(it(s[0])){case"<?xml":break;case"<personList":break;case"</personList>":break;case"<person":r.push({name:s.displayname,id:s.id});break;case"</person>":break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":a=!0;break;case"</ext>":a=!1;break;default:if(!a&&t.WTF)throw new Error("unrecognized "+s[0]+" in threaded comments")}return i}),r}function N2(e){var t=[Qe,ae("personList",null,{xmlns:nr.TCMNT,"xmlns:x":zt[0]}).replace(/[\/]>/,">")];return e.forEach(function(r,a){t.push(ae("person",null,{displayName:r,id:"{54EE7950-**************-"+("000000000000"+a).slice(-12)+"}",userId:r,providerId:"None"}))}),t.push("</personList>"),t.join("")}function P2(e){var t={};t.iauthor=e.read_shift(4);var r=jt(e);return t.rfx=r.s,t.ref=me(r.s),e.l+=16,t}function D2(e,t){return t==null&&(t=G(36)),t.write_shift(4,e[1].iauthor),da(e[0],t),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t}var L2=kr;function b2(e){return or(e.slice(0,54))}function M2(e,t){var r=[],a=[],n={},i=!1;return vt(e,function(f,c,o){switch(o){case 632:a.push(f);break;case 635:n=f;break;case 637:n.t=f.t,n.h=f.h,n.r=f.r;break;case 636:if(n.author=a[n.iauthor],delete n.iauthor,t.sheetRows&&n.rfx&&t.sheetRows<=n.rfx.r)break;n.t||(n.t=""),delete n.rfx,r.push(n);break;case 3072:break;case 35:i=!0;break;case 36:i=!1;break;case 37:break;case 38:break;default:if(!c.T){if(!i||t.WTF)throw new Error("Unexpected record 0x"+o.toString(16))}}}),r}function B2(e){var t=Or(),r=[];return j(t,628),j(t,630),e.forEach(function(a){a[1].forEach(function(n){r.indexOf(n.a)>-1||(r.push(n.a.slice(0,54)),j(t,632,b2(n.a)))})}),j(t,631),j(t,633),e.forEach(function(a){a[1].forEach(function(n){n.iauthor=r.indexOf(n.a);var i={s:ze(a[0]),e:ze(a[0])};j(t,635,D2([i,n])),n.t&&n.t.length>0&&j(t,637,s1(n)),j(t,636),delete n.iauthor})}),j(t,634),j(t,629),t.end()}var U2="application/vnd.ms-office.vbaProject";function W2(e){var t=de.utils.cfb_new({root:"R"});return e.FullPaths.forEach(function(r,a){if(!(r.slice(-1)==="/"||!r.match(/_VBA_PROJECT_CUR/))){var n=r.replace(/^[^\/]*/,"R").replace(/\/_VBA_PROJECT_CUR\u0000*/,"");de.utils.cfb_add(t,n,e.FileIndex[a].content)}}),de.write(t)}function H2(e,t){t.FullPaths.forEach(function(r,a){if(a!=0){var n=r.replace(/[^\/]*[\/]/,"/_VBA_PROJECT_CUR/");n.slice(-1)!=="/"&&de.utils.cfb_add(e,n,t.FileIndex[a].content)}})}var Bc=["xlsb","xlsm","xlam","biff8","xla"];function V2(){return{"!type":"dialog"}}function X2(){return{"!type":"dialog"}}function G2(){return{"!type":"macro"}}function z2(){return{"!type":"macro"}}var ia=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,t={r:0,c:0};function r(a,n,i,s){var f=!1,c=!1;i.length==0?c=!0:i.charAt(0)=="["&&(c=!0,i=i.slice(1,-1)),s.length==0?f=!0:s.charAt(0)=="["&&(f=!0,s=s.slice(1,-1));var o=i.length>0?parseInt(i,10)|0:0,l=s.length>0?parseInt(s,10)|0:0;return f?l+=t.c:--l,c?o+=t.r:--o,n+(f?"":"$")+He(l)+(c?"":"$")+Ke(o)}return function(n,i){return t=i,n.replace(e,r)}}(),Hi=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,Vi=function(){return function(t,r){return t.replace(Hi,function(a,n,i,s,f,c){var o=Oi(s)-(i?0:r.c),l=Ci(c)-(f?0:r.r),h=l==0?"":f?l+1:"["+l+"]",d=o==0?"":i?o+1:"["+o+"]";return n+"R"+h+"C"+d})}}();function Uc(e,t){return e.replace(Hi,function(r,a,n,i,s,f){return a+(n=="$"?n+i:He(Oi(i)+t.c))+(s=="$"?s+f:Ke(Ci(f)+t.r))})}function $2(e,t,r){var a=Ar(t),n=a.s,i=ze(r),s={r:i.r-n.r,c:i.c-n.c};return Uc(e,s)}function K2(e){return e.length!=1}function Zs(e){return e.replace(/_xlfn\./g,"")}function er(e){e.l+=1}function xt(e,t){var r=e.read_shift(2);return[r&16383,r>>14&1,r>>15&1]}function Wc(e,t,r){var a=2;if(r){if(r.biff>=2&&r.biff<=5)return Hc(e);r.biff==12&&(a=4)}var n=e.read_shift(a),i=e.read_shift(a),s=xt(e),f=xt(e);return{s:{r:n,c:s[0],cRel:s[1],rRel:s[2]},e:{r:i,c:f[0],cRel:f[1],rRel:f[2]}}}function Hc(e){var t=xt(e),r=xt(e),a=e.read_shift(1),n=e.read_shift(1);return{s:{r:t[0],c:a,cRel:t[1],rRel:t[2]},e:{r:r[0],c:n,cRel:r[1],rRel:r[2]}}}function Y2(e,t,r){if(r.biff<8)return Hc(e);var a=e.read_shift(r.biff==12?4:2),n=e.read_shift(r.biff==12?4:2),i=xt(e),s=xt(e);return{s:{r:a,c:i[0],cRel:i[1],rRel:i[2]},e:{r:n,c:s[0],cRel:s[1],rRel:s[2]}}}function Vc(e,t,r){if(r&&r.biff>=2&&r.biff<=5)return j2(e);var a=e.read_shift(r&&r.biff==12?4:2),n=xt(e);return{r:a,c:n[0],cRel:n[1],rRel:n[2]}}function j2(e){var t=xt(e),r=e.read_shift(1);return{r:t[0],c:r,cRel:t[1],rRel:t[2]}}function J2(e){var t=e.read_shift(2),r=e.read_shift(2);return{r:t,c:r&255,fQuoted:!!(r&16384),cRel:r>>15,rRel:r>>15}}function Z2(e,t,r){var a=r&&r.biff?r.biff:8;if(a>=2&&a<=5)return q2(e);var n=e.read_shift(a>=12?4:2),i=e.read_shift(2),s=(i&16384)>>14,f=(i&32768)>>15;if(i&=16383,f==1)for(;n>524287;)n-=1048576;if(s==1)for(;i>8191;)i=i-16384;return{r:n,c:i,cRel:s,rRel:f}}function q2(e){var t=e.read_shift(2),r=e.read_shift(1),a=(t&32768)>>15,n=(t&16384)>>14;return t&=16383,a==1&&t>=8192&&(t=t-16384),n==1&&r>=128&&(r=r-256),{r:t,c:r,cRel:n,rRel:a}}function Q2(e,t,r){var a=(e[e.l++]&96)>>5,n=Wc(e,r.biff>=2&&r.biff<=5?6:8,r);return[a,n]}function ed(e,t,r){var a=(e[e.l++]&96)>>5,n=e.read_shift(2,"i"),i=8;if(r)switch(r.biff){case 5:e.l+=12,i=6;break;case 12:i=12;break}var s=Wc(e,i,r);return[a,n,s]}function rd(e,t,r){var a=(e[e.l++]&96)>>5;return e.l+=r&&r.biff>8?12:r.biff<8?6:8,[a]}function td(e,t,r){var a=(e[e.l++]&96)>>5,n=e.read_shift(2),i=8;if(r)switch(r.biff){case 5:e.l+=12,i=6;break;case 12:i=12;break}return e.l+=i,[a,n]}function ad(e,t,r){var a=(e[e.l++]&96)>>5,n=Y2(e,t-1,r);return[a,n]}function nd(e,t,r){var a=(e[e.l++]&96)>>5;return e.l+=r.biff==2?6:r.biff==12?14:7,[a]}function qs(e){var t=e[e.l+1]&1,r=1;return e.l+=4,[t,r]}function id(e,t,r){e.l+=2;for(var a=e.read_shift(r&&r.biff==2?1:2),n=[],i=0;i<=a;++i)n.push(e.read_shift(r&&r.biff==2?1:2));return n}function sd(e,t,r){var a=e[e.l+1]&255?1:0;return e.l+=2,[a,e.read_shift(r&&r.biff==2?1:2)]}function fd(e,t,r){var a=e[e.l+1]&255?1:0;return e.l+=2,[a,e.read_shift(r&&r.biff==2?1:2)]}function cd(e){var t=e[e.l+1]&255?1:0;return e.l+=2,[t,e.read_shift(2)]}function od(e,t,r){var a=e[e.l+1]&255?1:0;return e.l+=r&&r.biff==2?3:4,[a]}function Xc(e){var t=e.read_shift(1),r=e.read_shift(1);return[t,r]}function ld(e){return e.read_shift(2),Xc(e)}function hd(e){return e.read_shift(2),Xc(e)}function ud(e,t,r){var a=(e[e.l]&96)>>5;e.l+=1;var n=Vc(e,0,r);return[a,n]}function dd(e,t,r){var a=(e[e.l]&96)>>5;e.l+=1;var n=Z2(e,0,r);return[a,n]}function vd(e,t,r){var a=(e[e.l]&96)>>5;e.l+=1;var n=e.read_shift(2);r&&r.biff==5&&(e.l+=12);var i=Vc(e,0,r);return[a,n,i]}function pd(e,t,r){var a=(e[e.l]&96)>>5;e.l+=1;var n=e.read_shift(r&&r.biff<=3?1:2);return[_v[n],$c[n],a]}function md(e,t,r){var a=e[e.l++],n=e.read_shift(1),i=r&&r.biff<=3?[a==88?-1:0,e.read_shift(1)]:gd(e);return[n,(i[0]===0?$c:gv)[i[1]]]}function gd(e){return[e[e.l+1]>>7,e.read_shift(2)&32767]}function _d(e,t,r){e.l+=r&&r.biff==2?3:4}function wd(e,t,r){if(e.l++,r&&r.biff==12)return[e.read_shift(4,"i"),0];var a=e.read_shift(2),n=e.read_shift(r&&r.biff==2?1:2);return[a,n]}function kd(e){return e.l++,pt[e.read_shift(1)]}function Td(e){return e.l++,e.read_shift(2)}function Ed(e){return e.l++,e.read_shift(1)!==0}function Sd(e){return e.l++,_r(e)}function yd(e,t,r){return e.l++,Ya(e,t-1,r)}function xd(e,t){var r=[e.read_shift(1)];if(t==12)switch(r[0]){case 2:r[0]=4;break;case 4:r[0]=16;break;case 0:r[0]=1;break;case 1:r[0]=2;break}switch(r[0]){case 4:r[1]=qe(e,1)?"TRUE":"FALSE",t!=12&&(e.l+=7);break;case 37:case 16:r[1]=pt[e[e.l]],e.l+=t==12?4:8;break;case 0:e.l+=8;break;case 1:r[1]=_r(e);break;case 2:r[1]=Jt(e,0,{biff:t>0&&t<8?2:t});break;default:throw new Error("Bad SerAr: "+r[0])}return r}function Ad(e,t,r){for(var a=e.read_shift(r.biff==12?4:2),n=[],i=0;i!=a;++i)n.push((r.biff==12?jt:Rn)(e));return n}function Cd(e,t,r){var a=0,n=0;r.biff==12?(a=e.read_shift(4),n=e.read_shift(4)):(n=1+e.read_shift(1),a=1+e.read_shift(2)),r.biff>=2&&r.biff<8&&(--a,--n==0&&(n=256));for(var i=0,s=[];i!=a&&(s[i]=[]);++i)for(var f=0;f!=n;++f)s[i][f]=xd(e,r.biff);return s}function Od(e,t,r){var a=e.read_shift(1)>>>5&3,n=!r||r.biff>=8?4:2,i=e.read_shift(n);switch(r.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12;break}return[a,0,i]}function Id(e,t,r){if(r.biff==5)return Fd(e);var a=e.read_shift(1)>>>5&3,n=e.read_shift(2),i=e.read_shift(4);return[a,n,i]}function Fd(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2,"i");e.l+=8;var a=e.read_shift(2);return e.l+=12,[t,r,a]}function Rd(e,t,r){var a=e.read_shift(1)>>>5&3;e.l+=r&&r.biff==2?3:4;var n=e.read_shift(r&&r.biff==2?1:2);return[a,n]}function Nd(e,t,r){var a=e.read_shift(1)>>>5&3,n=e.read_shift(r&&r.biff==2?1:2);return[a,n]}function Pd(e,t,r){var a=e.read_shift(1)>>>5&3;return e.l+=4,r.biff<8&&e.l--,r.biff==12&&(e.l+=2),[a]}function Dd(e,t,r){var a=(e[e.l++]&96)>>5,n=e.read_shift(2),i=4;if(r)switch(r.biff){case 5:i=15;break;case 12:i=6;break}return e.l+=i,[a,n]}var Ld=Tr,bd=Tr,Md=Tr;function Ja(e,t,r){return e.l+=2,[J2(e)]}function Xi(e){return e.l+=6,[]}var Bd=Ja,Ud=Xi,Wd=Xi,Hd=Ja;function Gc(e){return e.l+=2,[tr(e),e.read_shift(2)&1]}var Vd=Ja,Xd=Gc,Gd=Xi,zd=Ja,$d=Ja,Kd=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"];function Yd(e){e.l+=2;var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(4),n=e.read_shift(2),i=e.read_shift(2),s=Kd[r>>2&31];return{ixti:t,coltype:r&3,rt:s,idx:a,c:n,C:i}}function jd(e){return e.l+=2,[e.read_shift(4)]}function Jd(e,t,r){return e.l+=5,e.l+=2,e.l+=r.biff==2?1:4,["PTGSHEET"]}function Zd(e,t,r){return e.l+=r.biff==2?4:5,["PTGENDSHEET"]}function qd(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[t,r]}function Qd(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[t,r]}function ev(e){return e.l+=4,[0,0]}var Qs={1:{n:"PtgExp",f:wd},2:{n:"PtgTbl",f:Md},3:{n:"PtgAdd",f:er},4:{n:"PtgSub",f:er},5:{n:"PtgMul",f:er},6:{n:"PtgDiv",f:er},7:{n:"PtgPower",f:er},8:{n:"PtgConcat",f:er},9:{n:"PtgLt",f:er},10:{n:"PtgLe",f:er},11:{n:"PtgEq",f:er},12:{n:"PtgGe",f:er},13:{n:"PtgGt",f:er},14:{n:"PtgNe",f:er},15:{n:"PtgIsect",f:er},16:{n:"PtgUnion",f:er},17:{n:"PtgRange",f:er},18:{n:"PtgUplus",f:er},19:{n:"PtgUminus",f:er},20:{n:"PtgPercent",f:er},21:{n:"PtgParen",f:er},22:{n:"PtgMissArg",f:er},23:{n:"PtgStr",f:yd},26:{n:"PtgSheet",f:Jd},27:{n:"PtgEndSheet",f:Zd},28:{n:"PtgErr",f:kd},29:{n:"PtgBool",f:Ed},30:{n:"PtgInt",f:Td},31:{n:"PtgNum",f:Sd},32:{n:"PtgArray",f:nd},33:{n:"PtgFunc",f:pd},34:{n:"PtgFuncVar",f:md},35:{n:"PtgName",f:Od},36:{n:"PtgRef",f:ud},37:{n:"PtgArea",f:Q2},38:{n:"PtgMemArea",f:Rd},39:{n:"PtgMemErr",f:Ld},40:{n:"PtgMemNoMem",f:bd},41:{n:"PtgMemFunc",f:Nd},42:{n:"PtgRefErr",f:Pd},43:{n:"PtgAreaErr",f:rd},44:{n:"PtgRefN",f:dd},45:{n:"PtgAreaN",f:ad},46:{n:"PtgMemAreaN",f:qd},47:{n:"PtgMemNoMemN",f:Qd},57:{n:"PtgNameX",f:Id},58:{n:"PtgRef3d",f:vd},59:{n:"PtgArea3d",f:ed},60:{n:"PtgRefErr3d",f:Dd},61:{n:"PtgAreaErr3d",f:td},255:{}},rv={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},tv={1:{n:"PtgElfLel",f:Gc},2:{n:"PtgElfRw",f:zd},3:{n:"PtgElfCol",f:Bd},6:{n:"PtgElfRwV",f:$d},7:{n:"PtgElfColV",f:Hd},10:{n:"PtgElfRadical",f:Vd},11:{n:"PtgElfRadicalS",f:Gd},13:{n:"PtgElfColS",f:Ud},15:{n:"PtgElfColSV",f:Wd},16:{n:"PtgElfRadicalLel",f:Xd},25:{n:"PtgList",f:Yd},29:{n:"PtgSxName",f:jd},255:{}},av={0:{n:"PtgAttrNoop",f:ev},1:{n:"PtgAttrSemi",f:od},2:{n:"PtgAttrIf",f:fd},4:{n:"PtgAttrChoose",f:id},8:{n:"PtgAttrGoto",f:sd},16:{n:"PtgAttrSum",f:_d},32:{n:"PtgAttrBaxcel",f:qs},33:{n:"PtgAttrBaxcel",f:qs},64:{n:"PtgAttrSpace",f:ld},65:{n:"PtgAttrSpaceSemi",f:hd},128:{n:"PtgAttrIfError",f:cd},255:{}};function Za(e,t,r,a){if(a.biff<8)return Tr(e,t);for(var n=e.l+t,i=[],s=0;s!==r.length;++s)switch(r[s][0]){case"PtgArray":r[s][1]=Cd(e,0,a),i.push(r[s][1]);break;case"PtgMemArea":r[s][2]=Ad(e,r[s][1],a),i.push(r[s][2]);break;case"PtgExp":a&&a.biff==12&&(r[s][1][1]=e.read_shift(4),i.push(r[s][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+r[s][0]}return t=n-e.l,t!==0&&i.push(Tr(e,t)),i}function qa(e,t,r){for(var a=e.l+t,n,i,s=[];a!=e.l;)t=a-e.l,i=e[e.l],n=Qs[i]||Qs[rv[i]],(i===24||i===25)&&(n=(i===24?tv:av)[e[e.l+1]]),!n||!n.f?Tr(e,t):s.push([n.n,n.f(e,t,r)]);return s}function nv(e){for(var t=[],r=0;r<e.length;++r){for(var a=e[r],n=[],i=0;i<a.length;++i){var s=a[i];if(s)switch(s[0]){case 2:n.push('"'+s[1].replace(/"/g,'""')+'"');break;default:n.push(s[1])}else n.push("")}t.push(n.join(","))}return t.join(";")}var iv={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function sv(e,t){if(!e&&!(t&&t.biff<=5&&t.biff>=2))throw new Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e+"'":e}function zc(e,t,r){if(!e)return"SH33TJSERR0";if(r.biff>8&&(!e.XTI||!e.XTI[t]))return e.SheetNames[t];if(!e.XTI)return"SH33TJSERR6";var a=e.XTI[t];if(r.biff<8)return t>1e4&&(t-=65536),t<0&&(t=-t),t==0?"":e.XTI[t-1];if(!a)return"SH33TJSERR1";var n="";if(r.biff>8)switch(e[a[0]][0]){case 357:return n=a[1]==-1?"#REF":e.SheetNames[a[1]],a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 358:return r.SID!=null?e.SheetNames[r.SID]:"SH33TJSSAME"+e[a[0]][0];case 355:default:return"SH33TJSSRC"+e[a[0]][0]}switch(e[a[0]][0][0]){case 1025:return n=a[1]==-1?"#REF":e.SheetNames[a[1]]||"SH33TJSERR3",a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 14849:return e[a[0]].slice(1).map(function(i){return i.Name}).join(";;");default:return e[a[0]][0][3]?(n=a[1]==-1?"#REF":e[a[0]][0][3][a[1]]||"SH33TJSERR4",a[1]==a[2]?n:n+":"+e[a[0]][0][3][a[2]]):"SH33TJSERR2"}}function ef(e,t,r){var a=zc(e,t,r);return a=="#REF"?a:sv(a,r)}function mr(e,t,r,a,n){var i=n&&n.biff||8,s={s:{c:0,r:0}},f=[],c,o,l,h=0,d=0,v,p="";if(!e[0]||!e[0][0])return"";for(var u=-1,m="",k=0,A=e[0].length;k<A;++k){var _=e[0][k];switch(_[0]){case"PtgUminus":f.push("-"+f.pop());break;case"PtgUplus":f.push("+"+f.pop());break;case"PtgPercent":f.push(f.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(c=f.pop(),o=f.pop(),u>=0){switch(e[0][u][1][0]){case 0:m=$e(" ",e[0][u][1][1]);break;case 1:m=$e("\r",e[0][u][1][1]);break;default:if(m="",n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][u][1][0])}o=o+m,u=-1}f.push(o+iv[_[0]]+c);break;case"PtgIsect":c=f.pop(),o=f.pop(),f.push(o+" "+c);break;case"PtgUnion":c=f.pop(),o=f.pop(),f.push(o+","+c);break;case"PtgRange":c=f.pop(),o=f.pop(),f.push(o+":"+c);break;case"PtgAttrChoose":break;case"PtgAttrGoto":break;case"PtgAttrIf":break;case"PtgAttrIfError":break;case"PtgRef":l=ya(_[1][1],s,n),f.push(xa(l,i));break;case"PtgRefN":l=r?ya(_[1][1],r,n):_[1][1],f.push(xa(l,i));break;case"PtgRef3d":h=_[1][1],l=ya(_[1][2],s,n),p=ef(a,h,n),f.push(p+"!"+xa(l,i));break;case"PtgFunc":case"PtgFuncVar":var I=_[1][0],D=_[1][1];I||(I=0),I&=127;var P=I==0?[]:f.slice(-I);f.length-=I,D==="User"&&(D=P.shift()),f.push(D+"("+P.join(",")+")");break;case"PtgBool":f.push(_[1]?"TRUE":"FALSE");break;case"PtgInt":f.push(_[1]);break;case"PtgNum":f.push(String(_[1]));break;case"PtgStr":f.push('"'+_[1].replace(/"/g,'""')+'"');break;case"PtgErr":f.push(_[1]);break;case"PtgAreaN":v=Is(_[1][1],r?{s:r}:s,n),f.push(Wn(v,n));break;case"PtgArea":v=Is(_[1][1],s,n),f.push(Wn(v,n));break;case"PtgArea3d":h=_[1][1],v=_[1][2],p=ef(a,h,n),f.push(p+"!"+Wn(v,n));break;case"PtgAttrSum":f.push("SUM("+f.pop()+")");break;case"PtgAttrBaxcel":case"PtgAttrSemi":break;case"PtgName":d=_[1][2];var x=(a.names||[])[d-1]||(a[0]||[])[d],M=x?x.Name:"SH33TJSNAME"+String(d);M&&M.slice(0,6)=="_xlfn."&&!n.xlfn&&(M=M.slice(6)),f.push(M);break;case"PtgNameX":var R=_[1][1];d=_[1][2];var z;if(n.biff<=5)R<0&&(R=-R),a[R]&&(z=a[R][d]);else{var X="";if(((a[R]||[])[0]||[])[0]==14849||(((a[R]||[])[0]||[])[0]==1025?a[R][d]&&a[R][d].itab>0&&(X=a.SheetNames[a[R][d].itab-1]+"!"):X=a.SheetNames[d-1]+"!"),a[R]&&a[R][d])X+=a[R][d].Name;else if(a[0]&&a[0][d])X+=a[0][d].Name;else{var b=(zc(a,R,n)||"").split(";;");b[d-1]?X=b[d-1]:X+="SH33TJSERRX"}f.push(X);break}z||(z={Name:"SH33TJSERRY"}),f.push(z.Name);break;case"PtgParen":var J="(",le=")";if(u>=0){switch(m="",e[0][u][1][0]){case 2:J=$e(" ",e[0][u][1][1])+J;break;case 3:J=$e("\r",e[0][u][1][1])+J;break;case 4:le=$e(" ",e[0][u][1][1])+le;break;case 5:le=$e("\r",e[0][u][1][1])+le;break;default:if(n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][u][1][0])}u=-1}f.push(J+f.pop()+le);break;case"PtgRefErr":f.push("#REF!");break;case"PtgRefErr3d":f.push("#REF!");break;case"PtgExp":l={c:_[1][1],r:_[1][0]};var ie={c:r.c,r:r.r};if(a.sharedf[me(l)]){var ue=a.sharedf[me(l)];f.push(mr(ue,s,ie,a,n))}else{var ce=!1;for(c=0;c!=a.arrayf.length;++c)if(o=a.arrayf[c],!(l.c<o[0].s.c||l.c>o[0].e.c)&&!(l.r<o[0].s.r||l.r>o[0].e.r)){f.push(mr(o[1],s,ie,a,n)),ce=!0;break}ce||f.push(_[1])}break;case"PtgArray":f.push("{"+nv(_[1])+"}");break;case"PtgMemArea":break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":u=k;break;case"PtgTbl":break;case"PtgMemErr":break;case"PtgMissArg":f.push("");break;case"PtgAreaErr":f.push("#REF!");break;case"PtgAreaErr3d":f.push("#REF!");break;case"PtgList":f.push("Table"+_[1].idx+"[#"+_[1].rt+"]");break;case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":break;case"PtgMemFunc":break;case"PtgMemNoMem":break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw new Error("Unsupported ELFs");case"PtgSxName":throw new Error("Unrecognized Formula Token: "+String(_));default:throw new Error("Unrecognized Formula Token: "+String(_))}var Le=["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"];if(n.biff!=3&&u>=0&&Le.indexOf(e[0][k][0])==-1){_=e[0][u];var V=!0;switch(_[1][0]){case 4:V=!1;case 0:m=$e(" ",_[1][1]);break;case 5:V=!1;case 1:m=$e("\r",_[1][1]);break;default:if(m="",n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+_[1][0])}f.push((V?m:"")+f.pop()+(V?"":m)),u=-1}}if(f.length>1&&n.WTF)throw new Error("bad formula stack");return f[0]}function fv(e,t,r){var a=e.l+t,n=r.biff==2?1:2,i,s=e.read_shift(n);if(s==65535)return[[],Tr(e,t-2)];var f=qa(e,s,r);return t!==s+n&&(i=Za(e,t-s-n,f,r)),e.l=a,[f,i]}function cv(e,t,r){var a=e.l+t,n=r.biff==2?1:2,i,s=e.read_shift(n);if(s==65535)return[[],Tr(e,t-2)];var f=qa(e,s,r);return t!==s+n&&(i=Za(e,t-s-n,f,r)),e.l=a,[f,i]}function ov(e,t,r,a){var n=e.l+t,i=qa(e,a,r),s;return n!==e.l&&(s=Za(e,n-e.l,i,r)),[i,s]}function lv(e,t,r){var a=e.l+t,n,i=e.read_shift(2),s=qa(e,i,r);return i==65535?[[],Tr(e,t-2)]:(t!==i+2&&(n=Za(e,a-i-2,s,r)),[s,n])}function hv(e){var t;if(lt(e,e.l+6)!==65535)return[_r(e),"n"];switch(e[e.l]){case 0:return e.l+=8,["String","s"];case 1:return t=e[e.l+2]===1,e.l+=8,[t,"b"];case 2:return t=e[e.l+2],e.l+=8,[t,"e"];case 3:return e.l+=8,["","s"]}return[]}function uv(e){if(e==null){var t=G(8);return t.write_shift(1,3),t.write_shift(1,0),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(2,65535),t}else if(typeof e=="number")return Wt(e);return Wt(0)}function zn(e,t,r){var a=e.l+t,n=st(e);r.biff==2&&++e.l;var i=hv(e),s=e.read_shift(1);r.biff!=2&&(e.read_shift(1),r.biff>=5&&e.read_shift(4));var f=cv(e,a-e.l,r);return{cell:n,val:i[0],formula:f,shared:s>>3&1,tt:i[1]}}function dv(e,t,r,a,n){var i=Vt(t,r,n),s=uv(e.v),f=G(6),c=33;f.write_shift(2,c),f.write_shift(4,0);for(var o=G(e.bf.length),l=0;l<e.bf.length;++l)o[l]=e.bf[l];var h=cr([i,s,f,o]);return h}function Nn(e,t,r){var a=e.read_shift(4),n=qa(e,a,r),i=e.read_shift(4),s=i>0?Za(e,i,n,r):null;return[n,s]}var vv=Nn,Pn=Nn,pv=Nn,mv=Nn,gv={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},$c={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},_v={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};function rf(e){return e.slice(0,3)=="of:"&&(e=e.slice(3)),e.charCodeAt(0)==61&&(e=e.slice(1),e.charCodeAt(0)==61&&(e=e.slice(1))),e=e.replace(/COM\.MICROSOFT\./g,""),e=e.replace(/\[((?:\.[A-Z]+[0-9]+)(?::\.[A-Z]+[0-9]+)?)\]/g,function(t,r){return r.replace(/\./g,"")}),e=e.replace(/\[.(#[A-Z]*[?!])\]/g,"$1"),e.replace(/[;~]/g,",").replace(/\|/g,";")}function wv(e){var t="of:="+e.replace(Hi,"$1[.$2$3$4$5]").replace(/\]:\[/g,":");return t.replace(/;/g,"|").replace(/,/g,";")}function $n(e){var t=e.split(":"),r=t[0].split(".")[0];return[r,t[0].split(".")[1]+(t.length>1?":"+(t[1].split(".")[1]||t[1].split(".")[0]):"")]}function kv(e){return e.replace(/\./,"!")}var Ca={},sa={},Oa=typeof Map<"u";function Gi(e,t,r){var a=0,n=e.length;if(r){if(Oa?r.has(t):Object.prototype.hasOwnProperty.call(r,t)){for(var i=Oa?r.get(t):r[t];a<i.length;++a)if(e[i[a]].t===t)return e.Count++,i[a]}}else for(;a<n;++a)if(e[a].t===t)return e.Count++,a;return e[n]={t},e.Count++,e.Unique++,r&&(Oa?(r.has(t)||r.set(t,[]),r.get(t).push(n)):(Object.prototype.hasOwnProperty.call(r,t)||(r[t]=[]),r[t].push(n))),n}function Dn(e,t){var r={min:e+1,max:e+1},a=-1;return t.MDW&&(gr=t.MDW),t.width!=null?r.customWidth=1:t.wpx!=null?a=Wa(t.wpx):t.wch!=null&&(a=t.wch),a>-1?(r.width=Sn(a),r.customWidth=1):t.width!=null&&(r.width=t.width),t.hidden&&(r.hidden=!0),t.level!=null&&(r.outlineLevel=r.level=t.level),r}function bt(e,t){if(e){var r=[.7,.7,.75,.75,.3,.3];t=="xlml"&&(r=[1,1,1,1,.5,.5]),e.left==null&&(e.left=r[0]),e.right==null&&(e.right=r[1]),e.top==null&&(e.top=r[2]),e.bottom==null&&(e.bottom=r[3]),e.header==null&&(e.header=r[4]),e.footer==null&&(e.footer=r[5])}}function It(e,t,r){var a=r.revssf[t.z!=null?t.z:"General"],n=60,i=e.length;if(a==null&&r.ssf){for(;n<392;++n)if(r.ssf[n]==null){at(t.z,n),r.ssf[n]=t.z,r.revssf[t.z]=a=n;break}}for(n=0;n!=i;++n)if(e[n].numFmtId===a)return n;return e[i]={numFmtId:a,fontId:0,fillId:0,borderId:0,xfId:0,applyNumberFormat:1},i}function Kc(e,t,r,a,n,i){try{a.cellNF&&(e.z=ve[t])}catch(f){if(a.WTF)throw f}if(!(e.t==="z"&&!a.cellStyles)){if(e.t==="d"&&typeof e.v=="string"&&(e.v=Ve(e.v)),(!a||a.cellText!==!1)&&e.t!=="z")try{if(ve[t]==null&&at(gl[t]||"General",t),e.t==="e")e.w=e.w||pt[e.v];else if(t===0)if(e.t==="n")(e.v|0)===e.v?e.w=e.v.toString(10):e.w=Pa(e.v);else if(e.t==="d"){var s=ir(e.v);(s|0)===s?e.w=s.toString(10):e.w=Pa(s)}else{if(e.v===void 0)return"";e.w=Bt(e.v,sa)}else e.t==="d"?e.w=Lr(t,ir(e.v),sa):e.w=Lr(t,e.v,sa)}catch(f){if(a.WTF)throw f}if(a.cellStyles&&r!=null)try{e.s=i.Fills[r],e.s.fgColor&&e.s.fgColor.theme&&!e.s.fgColor.rgb&&(e.s.fgColor.rgb=En(n.themeElements.clrScheme[e.s.fgColor.theme].rgb,e.s.fgColor.tint||0),a.WTF&&(e.s.fgColor.raw_rgb=n.themeElements.clrScheme[e.s.fgColor.theme].rgb)),e.s.bgColor&&e.s.bgColor.theme&&(e.s.bgColor.rgb=En(n.themeElements.clrScheme[e.s.bgColor.theme].rgb,e.s.bgColor.tint||0),a.WTF&&(e.s.bgColor.raw_rgb=n.themeElements.clrScheme[e.s.bgColor.theme].rgb))}catch(f){if(a.WTF&&i.Fills)throw f}}}function Tv(e,t,r){if(e&&e["!ref"]){var a=Ce(e["!ref"]);if(a.e.c<a.s.c||a.e.r<a.s.r)throw new Error("Bad range ("+r+"): "+e["!ref"])}}function Ev(e,t){var r=Ce(t);r.s.r<=r.e.r&&r.s.c<=r.e.c&&r.s.r>=0&&r.s.c>=0&&(e["!ref"]=ke(r))}var Sv=/<(?:\w:)?mergeCell ref="[A-Z0-9:]+"\s*[\/]?>/g,yv=/<(?:\w+:)?sheetData[^>]*>([\s\S]*)<\/(?:\w+:)?sheetData>/,xv=/<(?:\w:)?hyperlink [^>]*>/mg,Av=/"(\w*:\w*)"/,Cv=/<(?:\w:)?col\b[^>]*[\/]?>/g,Ov=/<(?:\w:)?autoFilter[^>]*([\/]|>([\s\S]*)<\/(?:\w:)?autoFilter)>/g,Iv=/<(?:\w:)?pageMargins[^>]*\/>/g,Yc=/<(?:\w:)?sheetPr\b(?:[^>a-z][^>]*)?\/>/,Fv=/<(?:\w:)?sheetPr[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetPr)>/,Rv=/<(?:\w:)?sheetViews[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetViews)>/;function Nv(e,t,r,a,n,i,s){if(!e)return e;a||(a={"!id":{}});var f=t.dense?[]:{},c={s:{r:2e6,c:2e6},e:{r:0,c:0}},o="",l="",h=e.match(yv);h?(o=e.slice(0,h.index),l=e.slice(h.index+h[0].length)):o=l=e;var d=o.match(Yc);d?zi(d[0],f,n,r):(d=o.match(Fv))&&Dv(d[0],d[1]||"",f,n,r);var v=(o.match(/<(?:\w*:)?dimension/)||{index:-1}).index;if(v>0){var p=o.slice(v,v+50).match(Av);p&&Ev(f,p[1])}var u=o.match(Rv);u&&u[1]&&Kv(u[1],n);var m=[];if(t.cellStyles){var k=o.match(Cv);k&&Vv(m,k)}h&&Jv(h[1],f,t,c,i,s);var A=l.match(Ov);A&&(f["!autofilter"]=Gv(A[0]));var _=[],I=l.match(Sv);if(I)for(v=0;v!=I.length;++v)_[v]=Ce(I[v].slice(I[v].indexOf('"')+1));var D=l.match(xv);D&&Uv(f,D,a);var P=l.match(Iv);if(P&&(f["!margins"]=Wv(ge(P[0]))),!f["!ref"]&&c.e.c>=c.s.c&&c.e.r>=c.s.r&&(f["!ref"]=ke(c)),t.sheetRows>0&&f["!ref"]){var x=Ce(f["!ref"]);t.sheetRows<=+x.e.r&&(x.e.r=t.sheetRows-1,x.e.r>c.e.r&&(x.e.r=c.e.r),x.e.r<x.s.r&&(x.s.r=x.e.r),x.e.c>c.e.c&&(x.e.c=c.e.c),x.e.c<x.s.c&&(x.s.c=x.e.c),f["!fullref"]=f["!ref"],f["!ref"]=ke(x))}return m.length>0&&(f["!cols"]=m),_.length>0&&(f["!merges"]=_),f}function Pv(e){if(e.length===0)return"";for(var t='<mergeCells count="'+e.length+'">',r=0;r!=e.length;++r)t+='<mergeCell ref="'+ke(e[r])+'"/>';return t+"</mergeCells>"}function zi(e,t,r,a){var n=ge(e);r.Sheets[a]||(r.Sheets[a]={}),n.codeName&&(r.Sheets[a].CodeName=Fe(be(n.codeName)))}function Dv(e,t,r,a,n){zi(e.slice(0,e.indexOf(">")),r,a,n)}function Lv(e,t,r,a,n){var i=!1,s={},f=null;if(a.bookType!=="xlsx"&&t.vbaraw){var c=t.SheetNames[r];try{t.Workbook&&(c=t.Workbook.Sheets[r].CodeName||c)}catch{}i=!0,s.codeName=tt(De(c))}if(e&&e["!outline"]){var o={summaryBelow:1,summaryRight:1};e["!outline"].above&&(o.summaryBelow=0),e["!outline"].left&&(o.summaryRight=0),f=(f||"")+ae("outlinePr",null,o)}!i&&!f||(n[n.length]=ae("sheetPr",f,s))}var bv=["objects","scenarios","selectLockedCells","selectUnlockedCells"],Mv=["formatColumns","formatRows","formatCells","insertColumns","insertRows","insertHyperlinks","deleteColumns","deleteRows","sort","autoFilter","pivotTables"];function Bv(e){var t={sheet:1};return bv.forEach(function(r){e[r]!=null&&e[r]&&(t[r]="1")}),Mv.forEach(function(r){e[r]!=null&&!e[r]&&(t[r]="0")}),e.password&&(t.password=Bi(e.password).toString(16).toUpperCase()),ae("sheetProtection",null,t)}function Uv(e,t,r){for(var a=Array.isArray(e),n=0;n!=t.length;++n){var i=ge(be(t[n]),!0);if(!i.ref)return;var s=((r||{})["!id"]||[])[i.id];s?(i.Target=s.Target,i.location&&(i.Target+="#"+Fe(i.location))):(i.Target="#"+Fe(i.location),s={Target:i.Target,TargetMode:"Internal"}),i.Rel=s,i.tooltip&&(i.Tooltip=i.tooltip,delete i.tooltip);for(var f=Ce(i.ref),c=f.s.r;c<=f.e.r;++c)for(var o=f.s.c;o<=f.e.c;++o){var l=me({c:o,r:c});a?(e[c]||(e[c]=[]),e[c][o]||(e[c][o]={t:"z",v:void 0}),e[c][o].l=i):(e[l]||(e[l]={t:"z",v:void 0}),e[l].l=i)}}}function Wv(e){var t={};return["left","right","top","bottom","header","footer"].forEach(function(r){e[r]&&(t[r]=parseFloat(e[r]))}),t}function Hv(e){return bt(e),ae("pageMargins",null,e)}function Vv(e,t){for(var r=!1,a=0;a!=t.length;++a){var n=ge(t[a],!0);n.hidden&&(n.hidden=We(n.hidden));var i=parseInt(n.min,10)-1,s=parseInt(n.max,10)-1;for(n.outlineLevel&&(n.level=+n.outlineLevel||0),delete n.min,delete n.max,n.width=+n.width,!r&&n.width&&(r=!0,Ui(n.width)),yt(n);i<=s;)e[i++]=Be(n)}}function Xv(e,t){for(var r=["<cols>"],a,n=0;n!=t.length;++n)(a=t[n])&&(r[r.length]=ae("col",null,Dn(n,a)));return r[r.length]="</cols>",r.join("")}function Gv(e){var t={ref:(e.match(/ref="([^"]*)"/)||[])[1]};return t}function zv(e,t,r,a){var n=typeof e.ref=="string"?e.ref:ke(e.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var i=r.Workbook.Names,s=Ar(n);s.s.r==s.e.r&&(s.e.r=Ar(t["!ref"]).e.r,n=ke(s));for(var f=0;f<i.length;++f){var c=i[f];if(c.Name=="_xlnm._FilterDatabase"&&c.Sheet==a){c.Ref="'"+r.SheetNames[a]+"'!"+n;break}}return f==i.length&&i.push({Name:"_xlnm._FilterDatabase",Sheet:a,Ref:"'"+r.SheetNames[a]+"'!"+n}),ae("autoFilter",null,{ref:n})}var $v=/<(?:\w:)?sheetView(?:[^>a-z][^>]*)?\/?>/;function Kv(e,t){t.Views||(t.Views=[{}]),(e.match($v)||[]).forEach(function(r,a){var n=ge(r);t.Views[a]||(t.Views[a]={}),+n.zoomScale&&(t.Views[a].zoom=+n.zoomScale),We(n.rightToLeft)&&(t.Views[a].RTL=!0)})}function Yv(e,t,r,a){var n={workbookViewId:"0"};return(((a||{}).Workbook||{}).Views||[])[0]&&(n.rightToLeft=a.Workbook.Views[0].RTL?"1":"0"),ae("sheetViews",ae("sheetView",null,n),{})}function jv(e,t,r,a){if(e.c&&r["!comments"].push([t,e.c]),e.v===void 0&&typeof e.f!="string"||e.t==="z"&&!e.f)return"";var n="",i=e.t,s=e.v;if(e.t!=="z")switch(e.t){case"b":n=e.v?"1":"0";break;case"n":n=""+e.v;break;case"e":n=pt[e.v];break;case"d":a&&a.cellDates?n=Ve(e.v,-1).toISOString():(e=Be(e),e.t="n",n=""+(e.v=ir(Ve(e.v)))),typeof e.z>"u"&&(e.z=ve[14]);break;default:n=e.v;break}var f=ur("v",De(n)),c={r:t},o=It(a.cellXfs,e,a);switch(o!==0&&(c.s=o),e.t){case"n":break;case"d":c.t="d";break;case"b":c.t="b";break;case"e":c.t="e";break;case"z":break;default:if(e.v==null){delete e.t;break}if(e.v.length>32767)throw new Error("Text length must not exceed 32767 characters");if(a&&a.bookSST){f=ur("v",""+Gi(a.Strings,e.v,a.revStrings)),c.t="s";break}c.t="str";break}if(e.t!=i&&(e.t=i,e.v=s),typeof e.f=="string"&&e.f){var l=e.F&&e.F.slice(0,t.length)==t?{t:"array",ref:e.F}:null;f=ae("f",De(e.f),l)+(e.v!=null?f:"")}return e.l&&r["!links"].push([t,e.l]),e.D&&(c.cm=1),ae("c",f,c)}var Jv=function(){var e=/<(?:\w+:)?c[ \/>]/,t=/<\/(?:\w+:)?row>/,r=/r=["']([^"']*)["']/,a=/<(?:\w+:)?is>([\S\s]*?)<\/(?:\w+:)?is>/,n=/ref=["']([^"']*)["']/,i=Da("v"),s=Da("f");return function(c,o,l,h,d,v){for(var p=0,u="",m=[],k=[],A=0,_=0,I=0,D="",P,x,M=0,R=0,z,X,b=0,J=0,le=Array.isArray(v.CellXf),ie,ue=[],ce=[],Le=Array.isArray(o),V=[],pe={},_e=!1,O=!!l.sheetStubs,L=c.split(t),F=0,N=L.length;F!=N;++F){u=L[F].trim();var Y=u.length;if(Y!==0){var re=0;e:for(p=0;p<Y;++p)switch(u[p]){case">":if(u[p-1]!="/"){++p;break e}if(l&&l.cellStyles){if(x=ge(u.slice(re,p),!0),M=x.r!=null?parseInt(x.r,10):M+1,R=-1,l.sheetRows&&l.sheetRows<M)continue;pe={},_e=!1,x.ht&&(_e=!0,pe.hpt=parseFloat(x.ht),pe.hpx=oa(pe.hpt)),x.hidden=="1"&&(_e=!0,pe.hidden=!0),x.outlineLevel!=null&&(_e=!0,pe.level=+x.outlineLevel),_e&&(V[M-1]=pe)}break;case"<":re=p;break}if(re>=p)break;if(x=ge(u.slice(re,p),!0),M=x.r!=null?parseInt(x.r,10):M+1,R=-1,!(l.sheetRows&&l.sheetRows<M)){h.s.r>M-1&&(h.s.r=M-1),h.e.r<M-1&&(h.e.r=M-1),l&&l.cellStyles&&(pe={},_e=!1,x.ht&&(_e=!0,pe.hpt=parseFloat(x.ht),pe.hpx=oa(pe.hpt)),x.hidden=="1"&&(_e=!0,pe.hidden=!0),x.outlineLevel!=null&&(_e=!0,pe.level=+x.outlineLevel),_e&&(V[M-1]=pe)),m=u.slice(p).split(e);for(var te=0;te!=m.length&&m[te].trim().charAt(0)=="<";++te);for(m=m.slice(te),p=0;p!=m.length;++p)if(u=m[p].trim(),u.length!==0){if(k=u.match(r),A=p,_=0,I=0,u="<c "+(u.slice(0,1)=="<"?">":"")+u,k!=null&&k.length===2){for(A=0,D=k[1],_=0;_!=D.length&&!((I=D.charCodeAt(_)-64)<1||I>26);++_)A=26*A+I;--A,R=A}else++R;for(_=0;_!=u.length&&u.charCodeAt(_)!==62;++_);if(++_,x=ge(u.slice(0,_),!0),x.r||(x.r=me({r:M-1,c:R})),D=u.slice(_),P={t:""},(k=D.match(i))!=null&&k[1]!==""&&(P.v=Fe(k[1])),l.cellFormula){if((k=D.match(s))!=null&&k[1]!==""){if(P.f=Fe(be(k[1])).replace(/\r\n/g,`
`),l.xlfn||(P.f=Zs(P.f)),k[0].indexOf('t="array"')>-1)P.F=(D.match(n)||[])[1],P.F.indexOf(":")>-1&&ue.push([Ce(P.F),P.F]);else if(k[0].indexOf('t="shared"')>-1){X=ge(k[0]);var Q=Fe(be(k[1]));l.xlfn||(Q=Zs(Q)),ce[parseInt(X.si,10)]=[X,Q,x.r]}}else(k=D.match(/<f[^>]*\/>/))&&(X=ge(k[0]),ce[X.si]&&(P.f=$2(ce[X.si][1],ce[X.si][2],x.r)));var Z=ze(x.r);for(_=0;_<ue.length;++_)Z.r>=ue[_][0].s.r&&Z.r<=ue[_][0].e.r&&Z.c>=ue[_][0].s.c&&Z.c<=ue[_][0].e.c&&(P.F=ue[_][1])}if(x.t==null&&P.v===void 0)if(P.f||P.F)P.v=0,P.t="n";else if(O)P.t="z";else continue;else P.t=x.t||"n";switch(h.s.c>R&&(h.s.c=R),h.e.c<R&&(h.e.c=R),P.t){case"n":if(P.v==""||P.v==null){if(!O)continue;P.t="z"}else P.v=parseFloat(P.v);break;case"s":if(typeof P.v>"u"){if(!O)continue;P.t="z"}else z=Ca[parseInt(P.v,10)],P.v=z.t,P.r=z.r,l.cellHTML&&(P.h=z.h);break;case"str":P.t="s",P.v=P.v!=null?be(P.v):"",l.cellHTML&&(P.h=wi(P.v));break;case"inlineStr":k=D.match(a),P.t="s",k!=null&&(z=Mi(k[1]))?(P.v=z.t,l.cellHTML&&(P.h=z.h)):P.v="";break;case"b":P.v=We(P.v);break;case"d":l.cellDates?P.v=Ve(P.v,1):(P.v=ir(Ve(P.v,1)),P.t="n");break;case"e":(!l||l.cellText!==!1)&&(P.w=P.v),P.v=Jf[P.v];break}if(b=J=0,ie=null,le&&x.s!==void 0&&(ie=v.CellXf[x.s],ie!=null&&(ie.numFmtId!=null&&(b=ie.numFmtId),l.cellStyles&&ie.fillId!=null&&(J=ie.fillId))),Kc(P,b,J,l,d,v),l.cellDates&&le&&P.t=="n"&&Gt(ve[b])&&(P.t="d",P.v=In(P.v)),x.cm&&l.xlmeta){var Ee=(l.xlmeta.Cell||[])[+x.cm-1];Ee&&Ee.type=="XLDAPR"&&(P.D=!0)}if(Le){var C=ze(x.r);o[C.r]||(o[C.r]=[]),o[C.r][C.c]=P}else o[x.r]=P}}}}V.length>0&&(o["!rows"]=V)}}();function Zv(e,t,r,a){var n=[],i=[],s=Ce(e["!ref"]),f="",c,o="",l=[],h=0,d=0,v=e["!rows"],p=Array.isArray(e),u={r:o},m,k=-1;for(d=s.s.c;d<=s.e.c;++d)l[d]=He(d);for(h=s.s.r;h<=s.e.r;++h){for(i=[],o=Ke(h),d=s.s.c;d<=s.e.c;++d){c=l[d]+o;var A=p?(e[h]||[])[d]:e[c];A!==void 0&&(f=jv(A,c,e,t))!=null&&i.push(f)}(i.length>0||v&&v[h])&&(u={r:o},v&&v[h]&&(m=v[h],m.hidden&&(u.hidden=1),k=-1,m.hpx?k=Ha(m.hpx):m.hpt&&(k=m.hpt),k>-1&&(u.ht=k,u.customHeight=1),m.level&&(u.outlineLevel=m.level)),n[n.length]=ae("row",i.join(""),u))}if(v)for(;h<v.length;++h)v&&v[h]&&(u={r:h+1},m=v[h],m.hidden&&(u.hidden=1),k=-1,m.hpx?k=Ha(m.hpx):m.hpt&&(k=m.hpt),k>-1&&(u.ht=k,u.customHeight=1),m.level&&(u.outlineLevel=m.level),n[n.length]=ae("row","",u));return n.join("")}function jc(e,t,r,a){var n=[Qe,ae("worksheet",null,{xmlns:zt[0],"xmlns:r":nr.r})],i=r.SheetNames[e],s=0,f="",c=r.Sheets[i];c==null&&(c={});var o=c["!ref"]||"A1",l=Ce(o);if(l.e.c>16383||l.e.r>1048575){if(t.WTF)throw new Error("Range "+o+" exceeds format limit A1:XFD1048576");l.e.c=Math.min(l.e.c,16383),l.e.r=Math.min(l.e.c,1048575),o=ke(l)}a||(a={}),c["!comments"]=[];var h=[];Lv(c,r,e,t,n),n[n.length]=ae("dimension",null,{ref:o}),n[n.length]=Yv(c,t,e,r),t.sheetFormat&&(n[n.length]=ae("sheetFormatPr",null,{defaultRowHeight:t.sheetFormat.defaultRowHeight||"16",baseColWidth:t.sheetFormat.baseColWidth||"10",outlineLevelRow:t.sheetFormat.outlineLevelRow||"7"})),c["!cols"]!=null&&c["!cols"].length>0&&(n[n.length]=Xv(c,c["!cols"])),n[s=n.length]="<sheetData/>",c["!links"]=[],c["!ref"]!=null&&(f=Zv(c,t),f.length>0&&(n[n.length]=f)),n.length>s+1&&(n[n.length]="</sheetData>",n[s]=n[s].replace("/>",">")),c["!protect"]&&(n[n.length]=Bv(c["!protect"])),c["!autofilter"]!=null&&(n[n.length]=zv(c["!autofilter"],c,r,e)),c["!merges"]!=null&&c["!merges"].length>0&&(n[n.length]=Pv(c["!merges"]));var d=-1,v,p=-1;return c["!links"].length>0&&(n[n.length]="<hyperlinks>",c["!links"].forEach(function(u){u[1].Target&&(v={ref:u[0]},u[1].Target.charAt(0)!="#"&&(p=Pe(a,-1,De(u[1].Target).replace(/#.*$/,""),xe.HLINK),v["r:id"]="rId"+p),(d=u[1].Target.indexOf("#"))>-1&&(v.location=De(u[1].Target.slice(d+1))),u[1].Tooltip&&(v.tooltip=De(u[1].Tooltip)),n[n.length]=ae("hyperlink",null,v))}),n[n.length]="</hyperlinks>"),delete c["!links"],c["!margins"]!=null&&(n[n.length]=Hv(c["!margins"])),(!t||t.ignoreEC||t.ignoreEC==null)&&(n[n.length]=ur("ignoredErrors",ae("ignoredError",null,{numberStoredAsText:1,sqref:o}))),h.length>0&&(p=Pe(a,-1,"../drawings/drawing"+(e+1)+".xml",xe.DRAW),n[n.length]=ae("drawing",null,{"r:id":"rId"+p}),c["!drawing"]=h),c["!comments"].length>0&&(p=Pe(a,-1,"../drawings/vmlDrawing"+(e+1)+".vml",xe.VML),n[n.length]=ae("legacyDrawing",null,{"r:id":"rId"+p}),c["!legacy"]=p),n.length>1&&(n[n.length]="</worksheet>",n[1]=n[1].replace("/>",">")),n.join("")}function qv(e,t){var r={},a=e.l+t;r.r=e.read_shift(4),e.l+=4;var n=e.read_shift(2);e.l+=1;var i=e.read_shift(1);return e.l=a,i&7&&(r.level=i&7),i&16&&(r.hidden=!0),i&32&&(r.hpt=n/20),r}function Qv(e,t,r){var a=G(145),n=(r["!rows"]||[])[e]||{};a.write_shift(4,e),a.write_shift(4,0);var i=320;n.hpx?i=Ha(n.hpx)*20:n.hpt&&(i=n.hpt*20),a.write_shift(2,i),a.write_shift(1,0);var s=0;n.level&&(s|=n.level),n.hidden&&(s|=16),(n.hpx||n.hpt)&&(s|=32),a.write_shift(1,s),a.write_shift(1,0);var f=0,c=a.l;a.l+=4;for(var o={r:e,c:0},l=0;l<16;++l)if(!(t.s.c>l+1<<10||t.e.c<l<<10)){for(var h=-1,d=-1,v=l<<10;v<l+1<<10;++v){o.c=v;var p=Array.isArray(r)?(r[o.r]||[])[o.c]:r[me(o)];p&&(h<0&&(h=v),d=v)}h<0||(++f,a.write_shift(4,h),a.write_shift(4,d))}var u=a.l;return a.l=c,a.write_shift(4,f),a.l=u,a.length>a.l?a.slice(0,a.l):a}function ep(e,t,r,a){var n=Qv(a,r,t);(n.length>17||(t["!rows"]||[])[a])&&j(e,0,n)}var rp=jt,tp=da;function ap(){}function np(e,t){var r={},a=e[e.l];return++e.l,r.above=!(a&64),r.left=!(a&128),e.l+=18,r.name=f1(e),r}function ip(e,t,r){r==null&&(r=G(84+4*e.length));var a=192;t&&(t.above&&(a&=-65),t.left&&(a&=-129)),r.write_shift(1,a);for(var n=1;n<3;++n)r.write_shift(1,0);return wn({auto:1},r),r.write_shift(-4,-1),r.write_shift(-4,-1),zf(e,r),r.slice(0,r.l)}function sp(e){var t=$r(e);return[t]}function fp(e,t,r){return r==null&&(r=G(8)),$t(t,r)}function cp(e){var t=Kt(e);return[t]}function op(e,t,r){return r==null&&(r=G(4)),Yt(t,r)}function lp(e){var t=$r(e),r=e.read_shift(1);return[t,r,"b"]}function hp(e,t,r){return r==null&&(r=G(9)),$t(t,r),r.write_shift(1,e.v?1:0),r}function up(e){var t=Kt(e),r=e.read_shift(1);return[t,r,"b"]}function dp(e,t,r){return r==null&&(r=G(5)),Yt(t,r),r.write_shift(1,e.v?1:0),r}function vp(e){var t=$r(e),r=e.read_shift(1);return[t,r,"e"]}function pp(e,t,r){return r==null&&(r=G(9)),$t(t,r),r.write_shift(1,e.v),r}function mp(e){var t=Kt(e),r=e.read_shift(1);return[t,r,"e"]}function gp(e,t,r){return r==null&&(r=G(8)),Yt(t,r),r.write_shift(1,e.v),r.write_shift(2,0),r.write_shift(1,0),r}function _p(e){var t=$r(e),r=e.read_shift(4);return[t,r,"s"]}function wp(e,t,r){return r==null&&(r=G(12)),$t(t,r),r.write_shift(4,t.v),r}function kp(e){var t=Kt(e),r=e.read_shift(4);return[t,r,"s"]}function Tp(e,t,r){return r==null&&(r=G(8)),Yt(t,r),r.write_shift(4,t.v),r}function Ep(e){var t=$r(e),r=_r(e);return[t,r,"n"]}function Sp(e,t,r){return r==null&&(r=G(16)),$t(t,r),Wt(e.v,r),r}function Jc(e){var t=Kt(e),r=_r(e);return[t,r,"n"]}function yp(e,t,r){return r==null&&(r=G(12)),Yt(t,r),Wt(e.v,r),r}function xp(e){var t=$r(e),r=Ni(e);return[t,r,"n"]}function Ap(e,t,r){return r==null&&(r=G(12)),$t(t,r),$f(e.v,r),r}function Cp(e){var t=Kt(e),r=Ni(e);return[t,r,"n"]}function Op(e,t,r){return r==null&&(r=G(8)),Yt(t,r),$f(e.v,r),r}function Ip(e){var t=$r(e),r=Ii(e);return[t,r,"is"]}function Fp(e){var t=$r(e),r=kr(e);return[t,r,"str"]}function Rp(e,t,r){return r==null&&(r=G(12+4*e.v.length)),$t(t,r),or(e.v,r),r.length>r.l?r.slice(0,r.l):r}function Np(e){var t=Kt(e),r=kr(e);return[t,r,"str"]}function Pp(e,t,r){return r==null&&(r=G(8+4*e.v.length)),Yt(t,r),or(e.v,r),r.length>r.l?r.slice(0,r.l):r}function Dp(e,t,r){var a=e.l+t,n=$r(e);n.r=r["!row"];var i=e.read_shift(1),s=[n,i,"b"];if(r.cellFormula){e.l+=2;var f=Pn(e,a-e.l,r);s[3]=mr(f,null,n,r.supbooks,r)}else e.l=a;return s}function Lp(e,t,r){var a=e.l+t,n=$r(e);n.r=r["!row"];var i=e.read_shift(1),s=[n,i,"e"];if(r.cellFormula){e.l+=2;var f=Pn(e,a-e.l,r);s[3]=mr(f,null,n,r.supbooks,r)}else e.l=a;return s}function bp(e,t,r){var a=e.l+t,n=$r(e);n.r=r["!row"];var i=_r(e),s=[n,i,"n"];if(r.cellFormula){e.l+=2;var f=Pn(e,a-e.l,r);s[3]=mr(f,null,n,r.supbooks,r)}else e.l=a;return s}function Mp(e,t,r){var a=e.l+t,n=$r(e);n.r=r["!row"];var i=kr(e),s=[n,i,"str"];if(r.cellFormula){e.l+=2;var f=Pn(e,a-e.l,r);s[3]=mr(f,null,n,r.supbooks,r)}else e.l=a;return s}var Bp=jt,Up=da;function Wp(e,t){return t==null&&(t=G(4)),t.write_shift(4,e),t}function Hp(e,t){var r=e.l+t,a=jt(e),n=Fi(e),i=kr(e),s=kr(e),f=kr(e);e.l=r;var c={rfx:a,relId:n,loc:i,display:f};return s&&(c.Tooltip=s),c}function Vp(e,t){var r=G(50+4*(e[1].Target.length+(e[1].Tooltip||"").length));da({s:ze(e[0]),e:ze(e[0])},r),Ri("rId"+t,r);var a=e[1].Target.indexOf("#"),n=a==-1?"":e[1].Target.slice(a+1);return or(n||"",r),or(e[1].Tooltip||"",r),or("",r),r.slice(0,r.l)}function Xp(){}function Gp(e,t,r){var a=e.l+t,n=Kf(e),i=e.read_shift(1),s=[n];if(s[2]=i,r.cellFormula){var f=vv(e,a-e.l,r);s[1]=f}else e.l=a;return s}function zp(e,t,r){var a=e.l+t,n=jt(e),i=[n];if(r.cellFormula){var s=mv(e,a-e.l,r);i[1]=s,e.l=a}else e.l=a;return i}function $p(e,t,r){r==null&&(r=G(18));var a=Dn(e,t);r.write_shift(-4,e),r.write_shift(-4,e),r.write_shift(4,(a.width||10)*256),r.write_shift(4,0);var n=0;return t.hidden&&(n|=1),typeof a.width=="number"&&(n|=2),t.level&&(n|=t.level<<8),r.write_shift(2,n),r}var Zc=["left","right","top","bottom","header","footer"];function Kp(e){var t={};return Zc.forEach(function(r){t[r]=_r(e)}),t}function Yp(e,t){return t==null&&(t=G(6*8)),bt(e),Zc.forEach(function(r){Wt(e[r],t)}),t}function jp(e){var t=e.read_shift(2);return e.l+=28,{RTL:t&32}}function Jp(e,t,r){r==null&&(r=G(30));var a=924;return(((t||{}).Views||[])[0]||{}).RTL&&(a|=32),r.write_shift(2,a),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(2,0),r.write_shift(2,100),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(4,0),r}function Zp(e){var t=G(24);return t.write_shift(4,4),t.write_shift(4,1),da(e,t),t}function qp(e,t){return t==null&&(t=G(16*4+2)),t.write_shift(2,e.password?Bi(e.password):0),t.write_shift(4,1),[["objects",!1],["scenarios",!1],["formatCells",!0],["formatColumns",!0],["formatRows",!0],["insertColumns",!0],["insertRows",!0],["insertHyperlinks",!0],["deleteColumns",!0],["deleteRows",!0],["selectLockedCells",!1],["sort",!0],["autoFilter",!0],["pivotTables",!0],["selectUnlockedCells",!1]].forEach(function(r){r[1]?t.write_shift(4,e[r[0]]!=null&&!e[r[0]]?1:0):t.write_shift(4,e[r[0]]!=null&&e[r[0]]?0:1)}),t}function Qp(){}function em(){}function rm(e,t,r,a,n,i,s){if(!e)return e;var f=t||{};a||(a={"!id":{}});var c=f.dense?[]:{},o,l={s:{r:2e6,c:2e6},e:{r:0,c:0}},h=!1,d=!1,v,p,u,m,k,A,_,I,D,P=[];f.biff=12,f["!row"]=0;var x=0,M=!1,R=[],z={},X=f.supbooks||n.supbooks||[[]];if(X.sharedf=z,X.arrayf=R,X.SheetNames=n.SheetNames||n.Sheets.map(function(Le){return Le.name}),!f.supbooks&&(f.supbooks=X,n.Names))for(var b=0;b<n.Names.length;++b)X[0][b+1]=n.Names[b];var J=[],le=[],ie=!1;Va[16]={n:"BrtShortReal",f:Jc};var ue;if(vt(e,function(V,pe,_e){if(!d)switch(_e){case 148:o=V;break;case 0:v=V,f.sheetRows&&f.sheetRows<=v.r&&(d=!0),I=Ke(m=v.r),f["!row"]=v.r,(V.hidden||V.hpt||V.level!=null)&&(V.hpt&&(V.hpx=oa(V.hpt)),le[V.r]=V);break;case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 13:case 14:case 15:case 16:case 17:case 18:case 62:switch(p={t:V[2]},V[2]){case"n":p.v=V[1];break;case"s":_=Ca[V[1]],p.v=_.t,p.r=_.r;break;case"b":p.v=!!V[1];break;case"e":p.v=V[1],f.cellText!==!1&&(p.w=pt[p.v]);break;case"str":p.t="s",p.v=V[1];break;case"is":p.t="s",p.v=V[1].t;break}if((u=s.CellXf[V[0].iStyleRef])&&Kc(p,u.numFmtId,null,f,i,s),k=V[0].c==-1?k+1:V[0].c,f.dense?(c[m]||(c[m]=[]),c[m][k]=p):c[He(k)+I]=p,f.cellFormula){for(M=!1,x=0;x<R.length;++x){var O=R[x];v.r>=O[0].s.r&&v.r<=O[0].e.r&&k>=O[0].s.c&&k<=O[0].e.c&&(p.F=ke(O[0]),M=!0)}!M&&V.length>3&&(p.f=V[3])}if(l.s.r>v.r&&(l.s.r=v.r),l.s.c>k&&(l.s.c=k),l.e.r<v.r&&(l.e.r=v.r),l.e.c<k&&(l.e.c=k),f.cellDates&&u&&p.t=="n"&&Gt(ve[u.numFmtId])){var L=kt(p.v);L&&(p.t="d",p.v=new Date(L.y,L.m-1,L.d,L.H,L.M,L.S,L.u))}ue&&(ue.type=="XLDAPR"&&(p.D=!0),ue=void 0);break;case 1:case 12:if(!f.sheetStubs||h)break;p={t:"z",v:void 0},k=V[0].c==-1?k+1:V[0].c,f.dense?(c[m]||(c[m]=[]),c[m][k]=p):c[He(k)+I]=p,l.s.r>v.r&&(l.s.r=v.r),l.s.c>k&&(l.s.c=k),l.e.r<v.r&&(l.e.r=v.r),l.e.c<k&&(l.e.c=k),ue&&(ue.type=="XLDAPR"&&(p.D=!0),ue=void 0);break;case 176:P.push(V);break;case 49:ue=((f.xlmeta||{}).Cell||[])[V-1];break;case 494:var F=a["!id"][V.relId];for(F?(V.Target=F.Target,V.loc&&(V.Target+="#"+V.loc),V.Rel=F):V.relId==""&&(V.Target="#"+V.loc),m=V.rfx.s.r;m<=V.rfx.e.r;++m)for(k=V.rfx.s.c;k<=V.rfx.e.c;++k)f.dense?(c[m]||(c[m]=[]),c[m][k]||(c[m][k]={t:"z",v:void 0}),c[m][k].l=V):(A=me({c:k,r:m}),c[A]||(c[A]={t:"z",v:void 0}),c[A].l=V);break;case 426:if(!f.cellFormula)break;R.push(V),D=f.dense?c[m][k]:c[He(k)+I],D.f=mr(V[1],l,{r:v.r,c:k},X,f),D.F=ke(V[0]);break;case 427:if(!f.cellFormula)break;z[me(V[0].s)]=V[1],D=f.dense?c[m][k]:c[He(k)+I],D.f=mr(V[1],l,{r:v.r,c:k},X,f);break;case 60:if(!f.cellStyles)break;for(;V.e>=V.s;)J[V.e--]={width:V.w/256,hidden:!!(V.flags&1),level:V.level},ie||(ie=!0,Ui(V.w/256)),yt(J[V.e+1]);break;case 161:c["!autofilter"]={ref:ke(V)};break;case 476:c["!margins"]=V;break;case 147:n.Sheets[r]||(n.Sheets[r]={}),V.name&&(n.Sheets[r].CodeName=V.name),(V.above||V.left)&&(c["!outline"]={above:V.above,left:V.left});break;case 137:n.Views||(n.Views=[{}]),n.Views[0]||(n.Views[0]={}),V.RTL&&(n.Views[0].RTL=!0);break;case 485:break;case 64:case 1053:break;case 151:break;case 152:case 175:case 644:case 625:case 562:case 396:case 1112:case 1146:case 471:case 1050:case 649:case 1105:case 589:case 607:case 564:case 1055:case 168:case 174:case 1180:case 499:case 507:case 550:case 171:case 167:case 1177:case 169:case 1181:case 551:case 552:case 661:case 639:case 478:case 537:case 477:case 536:case 1103:case 680:case 1104:case 1024:case 663:case 535:case 678:case 504:case 1043:case 428:case 170:case 3072:case 50:case 2070:case 1045:break;case 35:h=!0;break;case 36:h=!1;break;case 37:h=!0;break;case 38:h=!1;break;default:if(!pe.T){if(!h||f.WTF)throw new Error("Unexpected record 0x"+_e.toString(16))}}},f),delete f.supbooks,delete f["!row"],!c["!ref"]&&(l.s.r<2e6||o&&(o.e.r>0||o.e.c>0||o.s.r>0||o.s.c>0))&&(c["!ref"]=ke(o||l)),f.sheetRows&&c["!ref"]){var ce=Ce(c["!ref"]);f.sheetRows<=+ce.e.r&&(ce.e.r=f.sheetRows-1,ce.e.r>l.e.r&&(ce.e.r=l.e.r),ce.e.r<ce.s.r&&(ce.s.r=ce.e.r),ce.e.c>l.e.c&&(ce.e.c=l.e.c),ce.e.c<ce.s.c&&(ce.s.c=ce.e.c),c["!fullref"]=c["!ref"],c["!ref"]=ke(ce))}return P.length>0&&(c["!merges"]=P),J.length>0&&(c["!cols"]=J),le.length>0&&(c["!rows"]=le),c}function tm(e,t,r,a,n,i,s){if(t.v===void 0)return!1;var f="";switch(t.t){case"b":f=t.v?"1":"0";break;case"d":t=Be(t),t.z=t.z||ve[14],t.v=ir(Ve(t.v)),t.t="n";break;case"n":case"e":f=""+t.v;break;default:f=t.v;break}var c={r,c:a};switch(c.s=It(n.cellXfs,t,n),t.l&&i["!links"].push([me(c),t.l]),t.c&&i["!comments"].push([me(c),t.c]),t.t){case"s":case"str":return n.bookSST?(f=Gi(n.Strings,t.v,n.revStrings),c.t="s",c.v=f,s?j(e,18,Tp(t,c)):j(e,7,wp(t,c))):(c.t="str",s?j(e,17,Pp(t,c)):j(e,6,Rp(t,c))),!0;case"n":return t.v==(t.v|0)&&t.v>-1e3&&t.v<1e3?s?j(e,13,Op(t,c)):j(e,2,Ap(t,c)):s?j(e,16,yp(t,c)):j(e,5,Sp(t,c)),!0;case"b":return c.t="b",s?j(e,15,dp(t,c)):j(e,4,hp(t,c)),!0;case"e":return c.t="e",s?j(e,14,gp(t,c)):j(e,3,pp(t,c)),!0}return s?j(e,12,op(t,c)):j(e,1,fp(t,c)),!0}function am(e,t,r,a){var n=Ce(t["!ref"]||"A1"),i,s="",f=[];j(e,145);var c=Array.isArray(t),o=n.e.r;t["!rows"]&&(o=Math.max(n.e.r,t["!rows"].length-1));for(var l=n.s.r;l<=o;++l){s=Ke(l),ep(e,t,n,l);var h=!1;if(l<=n.e.r)for(var d=n.s.c;d<=n.e.c;++d){l===n.s.r&&(f[d]=He(d)),i=f[d]+s;var v=c?(t[l]||[])[d]:t[i];if(!v){h=!1;continue}h=tm(e,v,l,d,a,t,h)}}j(e,146)}function nm(e,t){!t||!t["!merges"]||(j(e,177,Wp(t["!merges"].length)),t["!merges"].forEach(function(r){j(e,176,Up(r))}),j(e,178))}function im(e,t){!t||!t["!cols"]||(j(e,390),t["!cols"].forEach(function(r,a){r&&j(e,60,$p(a,r))}),j(e,391))}function sm(e,t){!t||!t["!ref"]||(j(e,648),j(e,649,Zp(Ce(t["!ref"]))),j(e,650))}function fm(e,t,r){t["!links"].forEach(function(a){if(a[1].Target){var n=Pe(r,-1,a[1].Target.replace(/#.*$/,""),xe.HLINK);j(e,494,Vp(a,n))}}),delete t["!links"]}function cm(e,t,r,a){if(t["!comments"].length>0){var n=Pe(a,-1,"../drawings/vmlDrawing"+(r+1)+".vml",xe.VML);j(e,551,Ri("rId"+n)),t["!legacy"]=n}}function om(e,t,r,a){if(t["!autofilter"]){var n=t["!autofilter"],i=typeof n.ref=="string"?n.ref:ke(n.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var s=r.Workbook.Names,f=Ar(i);f.s.r==f.e.r&&(f.e.r=Ar(t["!ref"]).e.r,i=ke(f));for(var c=0;c<s.length;++c){var o=s[c];if(o.Name=="_xlnm._FilterDatabase"&&o.Sheet==a){o.Ref="'"+r.SheetNames[a]+"'!"+i;break}}c==s.length&&s.push({Name:"_xlnm._FilterDatabase",Sheet:a,Ref:"'"+r.SheetNames[a]+"'!"+i}),j(e,161,da(Ce(i))),j(e,162)}}function lm(e,t,r){j(e,133),j(e,137,Jp(t,r)),j(e,138),j(e,134)}function hm(e,t){t["!protect"]&&j(e,535,qp(t["!protect"]))}function um(e,t,r,a){var n=Or(),i=r.SheetNames[e],s=r.Sheets[i]||{},f=i;try{r&&r.Workbook&&(f=r.Workbook.Sheets[e].CodeName||f)}catch{}var c=Ce(s["!ref"]||"A1");if(c.e.c>16383||c.e.r>1048575){if(t.WTF)throw new Error("Range "+(s["!ref"]||"A1")+" exceeds format limit A1:XFD1048576");c.e.c=Math.min(c.e.c,16383),c.e.r=Math.min(c.e.c,1048575)}return s["!links"]=[],s["!comments"]=[],j(n,129),(r.vbaraw||s["!outline"])&&j(n,147,ip(f,s["!outline"])),j(n,148,tp(c)),lm(n,s,r.Workbook),im(n,s),am(n,s,e,t),hm(n,s),om(n,s,r,e),nm(n,s),fm(n,s,a),s["!margins"]&&j(n,476,Yp(s["!margins"])),(!t||t.ignoreEC||t.ignoreEC==null)&&sm(n,s),cm(n,s,e,a),j(n,130),n.end()}function dm(e){var t=[],r=e.match(/^<c:numCache>/),a;(e.match(/<c:pt idx="(\d*)">(.*?)<\/c:pt>/mg)||[]).forEach(function(i){var s=i.match(/<c:pt idx="(\d*?)"><c:v>(.*)<\/c:v><\/c:pt>/);s&&(t[+s[1]]=r?+s[2]:s[2])});var n=Fe((e.match(/<c:formatCode>([\s\S]*?)<\/c:formatCode>/)||["","General"])[1]);return(e.match(/<c:f>(.*?)<\/c:f>/mg)||[]).forEach(function(i){a=i.replace(/<.*?>/g,"")}),[t,n,a]}function vm(e,t,r,a,n,i){var s=i||{"!type":"chart"};if(!e)return i;var f=0,c=0,o="A",l={s:{r:2e6,c:2e6},e:{r:0,c:0}};return(e.match(/<c:numCache>[\s\S]*?<\/c:numCache>/gm)||[]).forEach(function(h){var d=dm(h);l.s.r=l.s.c=0,l.e.c=f,o=He(f),d[0].forEach(function(v,p){s[o+Ke(p)]={t:"n",v,z:d[1]},c=p}),l.e.r<c&&(l.e.r=c),++f}),f>0&&(s["!ref"]=ke(l)),s}function pm(e,t,r,a,n){if(!e)return e;a||(a={"!id":{}});var i={"!type":"chart","!drawel":null,"!rel":""},s,f=e.match(Yc);return f&&zi(f[0],i,n,r),(s=e.match(/drawing r:id="(.*?)"/))&&(i["!rel"]=s[1]),a["!id"][i["!rel"]]&&(i["!drawel"]=a["!id"][i["!rel"]]),i}function mm(e,t){e.l+=10;var r=kr(e);return{name:r}}function gm(e,t,r,a,n){if(!e)return e;a||(a={"!id":{}});var i={"!type":"chart","!drawel":null,"!rel":""},s=!1;return vt(e,function(c,o,l){switch(l){case 550:i["!rel"]=c;break;case 651:n.Sheets[r]||(n.Sheets[r]={}),c.name&&(n.Sheets[r].CodeName=c.name);break;case 562:case 652:case 669:case 679:case 551:case 552:case 476:case 3072:break;case 35:s=!0;break;case 36:s=!1;break;case 37:break;case 38:break;default:if(!(o.T>0)){if(!(o.T<0)){if(!s||t.WTF)throw new Error("Unexpected record 0x"+l.toString(16))}}}},t),a["!id"][i["!rel"]]&&(i["!drawel"]=a["!id"][i["!rel"]]),i}var $i=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]],_m=[["activeTab",0,"int"],["autoFilterDateGrouping",!0,"bool"],["firstSheet",0,"int"],["minimized",!1,"bool"],["showHorizontalScroll",!0,"bool"],["showSheetTabs",!0,"bool"],["showVerticalScroll",!0,"bool"],["tabRatio",600,"int"],["visibility","visible"]],wm=[],km=[["calcCompleted","true"],["calcMode","auto"],["calcOnSave","true"],["concurrentCalc","true"],["fullCalcOnLoad","false"],["fullPrecision","true"],["iterate","false"],["iterateCount","100"],["iterateDelta","0.001"],["refMode","A1"]];function tf(e,t){for(var r=0;r!=e.length;++r)for(var a=e[r],n=0;n!=t.length;++n){var i=t[n];if(a[i[0]]==null)a[i[0]]=i[1];else switch(i[2]){case"bool":typeof a[i[0]]=="string"&&(a[i[0]]=We(a[i[0]]));break;case"int":typeof a[i[0]]=="string"&&(a[i[0]]=parseInt(a[i[0]],10));break}}}function af(e,t){for(var r=0;r!=t.length;++r){var a=t[r];if(e[a[0]]==null)e[a[0]]=a[1];else switch(a[2]){case"bool":typeof e[a[0]]=="string"&&(e[a[0]]=We(e[a[0]]));break;case"int":typeof e[a[0]]=="string"&&(e[a[0]]=parseInt(e[a[0]],10));break}}}function qc(e){af(e.WBProps,$i),af(e.CalcPr,km),tf(e.WBView,_m),tf(e.Sheets,wm),sa.date1904=We(e.WBProps.date1904)}function Tm(e){return!e.Workbook||!e.Workbook.WBProps?"false":We(e.Workbook.WBProps.date1904)?"true":"false"}var Em="][*?/\\".split("");function Qc(e,t){if(e.length>31)throw new Error("Sheet names cannot exceed 31 chars");var r=!0;return Em.forEach(function(a){if(e.indexOf(a)!=-1)throw new Error("Sheet name cannot contain : \\ / ? * [ ]")}),r}function Sm(e,t,r){e.forEach(function(a,n){Qc(a);for(var i=0;i<n;++i)if(a==e[i])throw new Error("Duplicate Sheet Name: "+a);if(r){var s=t&&t[n]&&t[n].CodeName||a;if(s.charCodeAt(0)==95&&s.length>22)throw new Error("Bad Code Name: Worksheet"+s)}})}function eo(e){if(!e||!e.SheetNames||!e.Sheets)throw new Error("Invalid Workbook");if(!e.SheetNames.length)throw new Error("Workbook is empty");var t=e.Workbook&&e.Workbook.Sheets||[];Sm(e.SheetNames,t,!!e.vbaraw);for(var r=0;r<e.SheetNames.length;++r)Tv(e.Sheets[e.SheetNames[r]],e.SheetNames[r],r)}var ym=/<\w+:workbook/;function xm(e,t){if(!e)throw new Error("Could not find file");var r={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},Names:[],xmlns:""},a=!1,n="xmlns",i={},s=0;if(e.replace(Er,function(c,o){var l=ge(c);switch(it(l[0])){case"<?xml":break;case"<workbook":c.match(ym)&&(n="xmlns"+c.match(/<(\w+):/)[1]),r.xmlns=l[n];break;case"</workbook>":break;case"<fileVersion":delete l[0],r.AppVersion=l;break;case"<fileVersion/>":case"</fileVersion>":break;case"<fileSharing":break;case"<fileSharing/>":break;case"<workbookPr":case"<workbookPr/>":$i.forEach(function(h){if(l[h[0]]!=null)switch(h[2]){case"bool":r.WBProps[h[0]]=We(l[h[0]]);break;case"int":r.WBProps[h[0]]=parseInt(l[h[0]],10);break;default:r.WBProps[h[0]]=l[h[0]]}}),l.codeName&&(r.WBProps.CodeName=be(l.codeName));break;case"</workbookPr>":break;case"<workbookProtection":break;case"<workbookProtection/>":break;case"<bookViews":case"<bookViews>":case"</bookViews>":break;case"<workbookView":case"<workbookView/>":delete l[0],r.WBView.push(l);break;case"</workbookView>":break;case"<sheets":case"<sheets>":case"</sheets>":break;case"<sheet":switch(l.state){case"hidden":l.Hidden=1;break;case"veryHidden":l.Hidden=2;break;default:l.Hidden=0}delete l.state,l.name=Fe(be(l.name)),delete l[0],r.Sheets.push(l);break;case"</sheet>":break;case"<functionGroups":case"<functionGroups/>":break;case"<functionGroup":break;case"<externalReferences":case"</externalReferences>":case"<externalReferences>":break;case"<externalReference":break;case"<definedNames/>":break;case"<definedNames>":case"<definedNames":a=!0;break;case"</definedNames>":a=!1;break;case"<definedName":i={},i.Name=be(l.name),l.comment&&(i.Comment=l.comment),l.localSheetId&&(i.Sheet=+l.localSheetId),We(l.hidden||"0")&&(i.Hidden=!0),s=o+c.length;break;case"</definedName>":i.Ref=Fe(be(e.slice(s,o))),r.Names.push(i);break;case"<definedName/>":break;case"<calcPr":delete l[0],r.CalcPr=l;break;case"<calcPr/>":delete l[0],r.CalcPr=l;break;case"</calcPr>":break;case"<oleSize":break;case"<customWorkbookViews>":case"</customWorkbookViews>":case"<customWorkbookViews":break;case"<customWorkbookView":case"</customWorkbookView>":break;case"<pivotCaches>":case"</pivotCaches>":case"<pivotCaches":break;case"<pivotCache":break;case"<smartTagPr":case"<smartTagPr/>":break;case"<smartTagTypes":case"<smartTagTypes>":case"</smartTagTypes>":break;case"<smartTagType":break;case"<webPublishing":case"<webPublishing/>":break;case"<fileRecoveryPr":case"<fileRecoveryPr/>":break;case"<webPublishObjects>":case"<webPublishObjects":case"</webPublishObjects>":break;case"<webPublishObject":break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":a=!0;break;case"</ext>":a=!1;break;case"<ArchID":break;case"<AlternateContent":case"<AlternateContent>":a=!0;break;case"</AlternateContent>":a=!1;break;case"<revisionPtr":break;default:if(!a&&t.WTF)throw new Error("unrecognized "+l[0]+" in workbook")}return c}),zt.indexOf(r.xmlns)===-1)throw new Error("Unknown Namespace: "+r.xmlns);return qc(r),r}function ro(e){var t=[Qe];t[t.length]=ae("workbook",null,{xmlns:zt[0],"xmlns:r":nr.r});var r=e.Workbook&&(e.Workbook.Names||[]).length>0,a={codeName:"ThisWorkbook"};e.Workbook&&e.Workbook.WBProps&&($i.forEach(function(f){e.Workbook.WBProps[f[0]]!=null&&e.Workbook.WBProps[f[0]]!=f[1]&&(a[f[0]]=e.Workbook.WBProps[f[0]])}),e.Workbook.WBProps.CodeName&&(a.codeName=e.Workbook.WBProps.CodeName,delete a.CodeName)),t[t.length]=ae("workbookPr",null,a);var n=e.Workbook&&e.Workbook.Sheets||[],i=0;if(n&&n[0]&&n[0].Hidden){for(t[t.length]="<bookViews>",i=0;i!=e.SheetNames.length&&!(!n[i]||!n[i].Hidden);++i);i==e.SheetNames.length&&(i=0),t[t.length]='<workbookView firstSheet="'+i+'" activeTab="'+i+'"/>',t[t.length]="</bookViews>"}for(t[t.length]="<sheets>",i=0;i!=e.SheetNames.length;++i){var s={name:De(e.SheetNames[i].slice(0,31))};if(s.sheetId=""+(i+1),s["r:id"]="rId"+(i+1),n[i])switch(n[i].Hidden){case 1:s.state="hidden";break;case 2:s.state="veryHidden";break}t[t.length]=ae("sheet",null,s)}return t[t.length]="</sheets>",r&&(t[t.length]="<definedNames>",e.Workbook&&e.Workbook.Names&&e.Workbook.Names.forEach(function(f){var c={name:f.Name};f.Comment&&(c.comment=f.Comment),f.Sheet!=null&&(c.localSheetId=""+f.Sheet),f.Hidden&&(c.hidden="1"),f.Ref&&(t[t.length]=ae("definedName",De(f.Ref),c))}),t[t.length]="</definedNames>"),t.length>2&&(t[t.length]="</workbook>",t[1]=t[1].replace("/>",">")),t.join("")}function Am(e,t){var r={};return r.Hidden=e.read_shift(4),r.iTabID=e.read_shift(4),r.strRelID=ei(e),r.name=kr(e),r}function Cm(e,t){return t||(t=G(127)),t.write_shift(4,e.Hidden),t.write_shift(4,e.iTabID),Ri(e.strRelID,t),or(e.name.slice(0,31),t),t.length>t.l?t.slice(0,t.l):t}function Om(e,t){var r={},a=e.read_shift(4);r.defaultThemeVersion=e.read_shift(4);var n=t>8?kr(e):"";return n.length>0&&(r.CodeName=n),r.autoCompressPictures=!!(a&65536),r.backupFile=!!(a&64),r.checkCompatibility=!!(a&4096),r.date1904=!!(a&1),r.filterPrivacy=!!(a&8),r.hidePivotFieldList=!!(a&1024),r.promptedSolutions=!!(a&16),r.publishItems=!!(a&2048),r.refreshAllConnections=!!(a&262144),r.saveExternalLinkValues=!!(a&128),r.showBorderUnselectedTables=!!(a&4),r.showInkAnnotation=!!(a&32),r.showObjects=["all","placeholders","none"][a>>13&3],r.showPivotChartFilter=!!(a&32768),r.updateLinks=["userSet","never","always"][a>>8&3],r}function Im(e,t){t||(t=G(72));var r=0;return e&&e.filterPrivacy&&(r|=8),t.write_shift(4,r),t.write_shift(4,0),zf(e&&e.CodeName||"ThisWorkbook",t),t.slice(0,t.l)}function Fm(e,t){var r={};return e.read_shift(4),r.ArchID=e.read_shift(4),e.l+=t-8,r}function Rm(e,t,r){var a=e.l+t;e.l+=4,e.l+=1;var n=e.read_shift(4),i=c1(e),s=pv(e,0,r),f=Fi(e);e.l=a;var c={Name:i,Ptg:s};return n<268435455&&(c.Sheet=n),f&&(c.Comment=f),c}function Nm(e,t){var r={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},xmlns:""},a=[],n=!1;t||(t={}),t.biff=12;var i=[],s=[[]];return s.SheetNames=[],s.XTI=[],Va[16]={n:"BrtFRTArchID$",f:Fm},vt(e,function(c,o,l){switch(l){case 156:s.SheetNames.push(c.name),r.Sheets.push(c);break;case 153:r.WBProps=c;break;case 39:c.Sheet!=null&&(t.SID=c.Sheet),c.Ref=mr(c.Ptg,null,null,s,t),delete t.SID,delete c.Ptg,i.push(c);break;case 1036:break;case 357:case 358:case 355:case 667:s[0].length?s.push([l,c]):s[0]=[l,c],s[s.length-1].XTI=[];break;case 362:s.length===0&&(s[0]=[],s[0].XTI=[]),s[s.length-1].XTI=s[s.length-1].XTI.concat(c),s.XTI=s.XTI.concat(c);break;case 361:break;case 2071:case 158:case 143:case 664:case 353:break;case 3072:case 3073:case 534:case 677:case 157:case 610:case 2050:case 155:case 548:case 676:case 128:case 665:case 2128:case 2125:case 549:case 2053:case 596:case 2076:case 2075:case 2082:case 397:case 154:case 1117:case 553:case 2091:break;case 35:a.push(l),n=!0;break;case 36:a.pop(),n=!1;break;case 37:a.push(l),n=!0;break;case 38:a.pop(),n=!1;break;case 16:break;default:if(!o.T){if(!n||t.WTF&&a[a.length-1]!=37&&a[a.length-1]!=35)throw new Error("Unexpected record 0x"+l.toString(16))}}},t),qc(r),r.Names=i,r.supbooks=s,r}function Pm(e,t){j(e,143);for(var r=0;r!=t.SheetNames.length;++r){var a=t.Workbook&&t.Workbook.Sheets&&t.Workbook.Sheets[r]&&t.Workbook.Sheets[r].Hidden||0,n={Hidden:a,iTabID:r+1,strRelID:"rId"+(r+1),name:t.SheetNames[r]};j(e,156,Cm(n))}j(e,144)}function Dm(e,t){t||(t=G(127));for(var r=0;r!=4;++r)t.write_shift(4,0);return or("SheetJS",t),or(Ra.version,t),or(Ra.version,t),or("7262",t),t.length>t.l?t.slice(0,t.l):t}function Lm(e,t){t||(t=G(29)),t.write_shift(-4,0),t.write_shift(-4,460),t.write_shift(4,28800),t.write_shift(4,17600),t.write_shift(4,500),t.write_shift(4,e),t.write_shift(4,e);var r=120;return t.write_shift(1,r),t.length>t.l?t.slice(0,t.l):t}function bm(e,t){if(!(!t.Workbook||!t.Workbook.Sheets)){for(var r=t.Workbook.Sheets,a=0,n=-1,i=-1;a<r.length;++a)!r[a]||!r[a].Hidden&&n==-1?n=a:r[a].Hidden==1&&i==-1&&(i=a);i>n||(j(e,135),j(e,158,Lm(n)),j(e,136))}}function Mm(e,t){var r=Or();return j(r,131),j(r,128,Dm()),j(r,153,Im(e.Workbook&&e.Workbook.WBProps||null)),bm(r,e),Pm(r,e),j(r,132),r.end()}function Bm(e,t,r){return t.slice(-4)===".bin"?Nm(e,r):xm(e,r)}function Um(e,t,r,a,n,i,s,f){return t.slice(-4)===".bin"?rm(e,a,r,n,i,s,f):Nv(e,a,r,n,i,s,f)}function Wm(e,t,r,a,n,i,s,f){return t.slice(-4)===".bin"?gm(e,a,r,n,i):pm(e,a,r,n,i)}function Hm(e,t,r,a,n,i,s,f){return t.slice(-4)===".bin"?G2():z2()}function Vm(e,t,r,a,n,i,s,f){return t.slice(-4)===".bin"?V2():X2()}function Xm(e,t,r,a){return t.slice(-4)===".bin"?U0(e,r,a):C0(e,r,a)}function Gm(e,t,r){return Dc(e,r)}function zm(e,t,r){return t.slice(-4)===".bin"?$u(e,r):Xu(e,r)}function $m(e,t,r){return t.slice(-4)===".bin"?M2(e,r):O2(e,r)}function Km(e,t,r){return t.slice(-4)===".bin"?x2(e):S2(e)}function Ym(e,t,r,a){return r.slice(-4)===".bin"?A2(e,t,r,a):void 0}function jm(e,t,r){return t.slice(-4)===".bin"?k2(e,t,r):E2(e,t,r)}function Jm(e,t,r){return(t.slice(-4)===".bin"?Mm:ro)(e)}function Zm(e,t,r,a,n){return(t.slice(-4)===".bin"?um:jc)(e,r,a,n)}function qm(e,t,r){return(t.slice(-4)===".bin"?j0:Nc)(e,r)}function Qm(e,t,r){return(t.slice(-4)===".bin"?ju:yc)(e,r)}function eg(e,t,r){return(t.slice(-4)===".bin"?B2:Mc)(e)}function rg(e){return(e.slice(-4)===".bin"?T2:Lc)()}var to=/([\w:]+)=((?:")([^"]*)(?:")|(?:')([^']*)(?:'))/g,ao=/([\w:]+)=((?:")(?:[^"]*)(?:")|(?:')(?:[^']*)(?:'))/;function Kr(e,t){var r=e.split(/\s+/),a=[];if(a[0]=r[0],r.length===1)return a;var n=e.match(to),i,s,f,c;if(n)for(c=0;c!=n.length;++c)i=n[c].match(ao),(s=i[1].indexOf(":"))===-1?a[i[1]]=i[2].slice(1,i[2].length-1):(i[1].slice(0,6)==="xmlns:"?f="xmlns"+i[1].slice(6):f=i[1].slice(s+1),a[f]=i[2].slice(1,i[2].length-1));return a}function tg(e){var t=e.split(/\s+/),r={};if(t.length===1)return r;var a=e.match(to),n,i,s,f;if(a)for(f=0;f!=a.length;++f)n=a[f].match(ao),(i=n[1].indexOf(":"))===-1?r[n[1]]=n[2].slice(1,n[2].length-1):(n[1].slice(0,6)==="xmlns:"?s="xmlns"+n[1].slice(6):s=n[1].slice(i+1),r[s]=n[2].slice(1,n[2].length-1));return r}var Ia;function ag(e,t){var r=Ia[e]||Fe(e);return r==="General"?Bt(t):Lr(r,t)}function ng(e,t,r,a){var n=a;switch((r[0].match(/dt:dt="([\w.]+)"/)||["",""])[1]){case"boolean":n=We(a);break;case"i2":case"int":n=parseInt(a,10);break;case"r4":case"float":n=parseFloat(a);break;case"date":case"dateTime.tz":n=Ve(a);break;case"i8":case"string":case"fixed":case"uuid":case"bin.base64":break;default:throw new Error("bad custprop:"+r[0])}e[Fe(t)]=n}function ig(e,t,r){if(e.t!=="z"){if(!r||r.cellText!==!1)try{e.t==="e"?e.w=e.w||pt[e.v]:t==="General"?e.t==="n"?(e.v|0)===e.v?e.w=e.v.toString(10):e.w=Pa(e.v):e.w=Bt(e.v):e.w=ag(t||"General",e.v)}catch(i){if(r.WTF)throw i}try{var a=Ia[t]||t||"General";if(r.cellNF&&(e.z=a),r.cellDates&&e.t=="n"&&Gt(a)){var n=kt(e.v);n&&(e.t="d",e.v=new Date(n.y,n.m-1,n.d,n.H,n.M,n.S,n.u))}}catch(i){if(r.WTF)throw i}}}function sg(e,t,r){if(r.cellStyles&&t.Interior){var a=t.Interior;a.Pattern&&(a.patternType=w0[a.Pattern]||a.Pattern)}e[t.ID]=t}function fg(e,t,r,a,n,i,s,f,c,o){var l="General",h=a.StyleID,d={};o=o||{};var v=[],p=0;for(h===void 0&&f&&(h=f.StyleID),h===void 0&&s&&(h=s.StyleID);i[h]!==void 0&&(i[h].nf&&(l=i[h].nf),i[h].Interior&&v.push(i[h].Interior),!!i[h].Parent);)h=i[h].Parent;switch(r.Type){case"Boolean":a.t="b",a.v=We(e);break;case"String":a.t="s",a.r=ks(Fe(e)),a.v=e.indexOf("<")>-1?Fe(t||e).replace(/<.*?>/g,""):a.r;break;case"DateTime":e.slice(-1)!="Z"&&(e+="Z"),a.v=(Ve(e)-new Date(Date.UTC(1899,11,30)))/(24*60*60*1e3),a.v!==a.v?a.v=Fe(e):a.v<60&&(a.v=a.v-1),(!l||l=="General")&&(l="yyyy-mm-dd");case"Number":a.v===void 0&&(a.v=+e),a.t||(a.t="n");break;case"Error":a.t="e",a.v=Jf[e],o.cellText!==!1&&(a.w=e);break;default:e==""&&t==""?a.t="z":(a.t="s",a.v=ks(t||e));break}if(ig(a,l,o),o.cellFormula!==!1)if(a.Formula){var u=Fe(a.Formula);u.charCodeAt(0)==61&&(u=u.slice(1)),a.f=ia(u,n),delete a.Formula,a.ArrayRange=="RC"?a.F=ia("RC:RC",n):a.ArrayRange&&(a.F=ia(a.ArrayRange,n),c.push([Ce(a.F),a.F]))}else for(p=0;p<c.length;++p)n.r>=c[p][0].s.r&&n.r<=c[p][0].e.r&&n.c>=c[p][0].s.c&&n.c<=c[p][0].e.c&&(a.F=c[p][1]);o.cellStyles&&(v.forEach(function(m){!d.patternType&&m.patternType&&(d.patternType=m.patternType)}),a.s=d),a.StyleID!==void 0&&(a.ixfe=a.StyleID)}function cg(e){e.t=e.v||"",e.t=e.t.replace(/\r\n/g,`
`).replace(/\r/g,`
`),e.v=e.w=e.ixfe=void 0}function Kn(e,t){var r=t||{};la();var a=ra(ki(e));(r.type=="binary"||r.type=="array"||r.type=="base64")&&(typeof Ie<"u"?a=Ie.utils.decode(65001,un(a)):a=be(a));var n=a.slice(0,1024).toLowerCase(),i=!1;if(n=n.replace(/".*?"/g,""),(n.indexOf(">")&1023)>Math.min(n.indexOf(",")&1023,n.indexOf(";")&1023)){var s=Be(r);return s.type="string",ca.to_workbook(a,s)}if(n.indexOf("<?xml")==-1&&["html","table","head","meta","script","style","div"].forEach(function(Je){n.indexOf("<"+Je)>=0&&(i=!0)}),i)return Gg(a,r);Ia={"General Number":"General","General Date":ve[22],"Long Date":"dddd, mmmm dd, yyyy","Medium Date":ve[15],"Short Date":ve[14],"Long Time":ve[19],"Medium Time":ve[18],"Short Time":ve[20],Currency:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',Fixed:ve[2],Standard:ve[4],Percent:ve[10],Scientific:ve[11],"Yes/No":'"Yes";"Yes";"No";@',"True/False":'"True";"True";"False";@',"On/Off":'"Yes";"Yes";"No";@'};var f,c=[],o,l={},h=[],d=r.dense?[]:{},v="",p={},u={},m=Kr('<Data ss:Type="String">'),k=0,A=0,_=0,I={s:{r:2e6,c:2e6},e:{r:0,c:0}},D={},P={},x="",M=0,R=[],z={},X={},b=0,J=[],le=[],ie={},ue=[],ce,Le=!1,V=[],pe=[],_e={},O=0,L=0,F={Sheets:[],WBProps:{date1904:!1}},N={};ba.lastIndex=0,a=a.replace(/<!--([\s\S]*?)-->/mg,"");for(var Y="";f=ba.exec(a);)switch(f[3]=(Y=f[3]).toLowerCase()){case"data":if(Y=="data"){if(f[1]==="/"){if((o=c.pop())[0]!==f[3])throw new Error("Bad state: "+o.join("|"))}else f[0].charAt(f[0].length-2)!=="/"&&c.push([f[3],!0]);break}if(c[c.length-1][1])break;f[1]==="/"?fg(a.slice(k,f.index),x,m,c[c.length-1][0]=="comment"?ie:p,{c:A,r:_},D,ue[A],u,V,r):(x="",m=Kr(f[0]),k=f.index+f[0].length);break;case"cell":if(f[1]==="/")if(le.length>0&&(p.c=le),(!r.sheetRows||r.sheetRows>_)&&p.v!==void 0&&(r.dense?(d[_]||(d[_]=[]),d[_][A]=p):d[He(A)+Ke(_)]=p),p.HRef&&(p.l={Target:Fe(p.HRef)},p.HRefScreenTip&&(p.l.Tooltip=p.HRefScreenTip),delete p.HRef,delete p.HRefScreenTip),(p.MergeAcross||p.MergeDown)&&(O=A+(parseInt(p.MergeAcross,10)|0),L=_+(parseInt(p.MergeDown,10)|0),R.push({s:{c:A,r:_},e:{c:O,r:L}})),!r.sheetStubs)p.MergeAcross?A=O+1:++A;else if(p.MergeAcross||p.MergeDown){for(var re=A;re<=O;++re)for(var te=_;te<=L;++te)(re>A||te>_)&&(r.dense?(d[te]||(d[te]=[]),d[te][re]={t:"z"}):d[He(re)+Ke(te)]={t:"z"});A=O+1}else++A;else p=tg(f[0]),p.Index&&(A=+p.Index-1),A<I.s.c&&(I.s.c=A),A>I.e.c&&(I.e.c=A),f[0].slice(-2)==="/>"&&++A,le=[];break;case"row":f[1]==="/"||f[0].slice(-2)==="/>"?(_<I.s.r&&(I.s.r=_),_>I.e.r&&(I.e.r=_),f[0].slice(-2)==="/>"&&(u=Kr(f[0]),u.Index&&(_=+u.Index-1)),A=0,++_):(u=Kr(f[0]),u.Index&&(_=+u.Index-1),_e={},(u.AutoFitHeight=="0"||u.Height)&&(_e.hpx=parseInt(u.Height,10),_e.hpt=Ha(_e.hpx),pe[_]=_e),u.Hidden=="1"&&(_e.hidden=!0,pe[_]=_e));break;case"worksheet":if(f[1]==="/"){if((o=c.pop())[0]!==f[3])throw new Error("Bad state: "+o.join("|"));h.push(v),I.s.r<=I.e.r&&I.s.c<=I.e.c&&(d["!ref"]=ke(I),r.sheetRows&&r.sheetRows<=I.e.r&&(d["!fullref"]=d["!ref"],I.e.r=r.sheetRows-1,d["!ref"]=ke(I))),R.length&&(d["!merges"]=R),ue.length>0&&(d["!cols"]=ue),pe.length>0&&(d["!rows"]=pe),l[v]=d}else I={s:{r:2e6,c:2e6},e:{r:0,c:0}},_=A=0,c.push([f[3],!1]),o=Kr(f[0]),v=Fe(o.Name),d=r.dense?[]:{},R=[],V=[],pe=[],N={name:v,Hidden:0},F.Sheets.push(N);break;case"table":if(f[1]==="/"){if((o=c.pop())[0]!==f[3])throw new Error("Bad state: "+o.join("|"))}else{if(f[0].slice(-2)=="/>")break;c.push([f[3],!1]),ue=[],Le=!1}break;case"style":f[1]==="/"?sg(D,P,r):P=Kr(f[0]);break;case"numberformat":P.nf=Fe(Kr(f[0]).Format||"General"),Ia[P.nf]&&(P.nf=Ia[P.nf]);for(var Q=0;Q!=392&&ve[Q]!=P.nf;++Q);if(Q==392){for(Q=57;Q!=392;++Q)if(ve[Q]==null){at(P.nf,Q);break}}break;case"column":if(c[c.length-1][0]!=="table")break;if(ce=Kr(f[0]),ce.Hidden&&(ce.hidden=!0,delete ce.Hidden),ce.Width&&(ce.wpx=parseInt(ce.Width,10)),!Le&&ce.wpx>10){Le=!0,gr=Fc;for(var Z=0;Z<ue.length;++Z)ue[Z]&&yt(ue[Z])}Le&&yt(ce),ue[ce.Index-1||ue.length]=ce;for(var Ee=0;Ee<+ce.Span;++Ee)ue[ue.length]=Be(ce);break;case"namedrange":if(f[1]==="/")break;F.Names||(F.Names=[]);var C=ge(f[0]),Ue={Name:C.Name,Ref:ia(C.RefersTo.slice(1),{r:0,c:0})};F.Sheets.length>0&&(Ue.Sheet=F.Sheets.length-1),F.Names.push(Ue);break;case"namedcell":break;case"b":break;case"i":break;case"u":break;case"s":break;case"em":break;case"h2":break;case"h3":break;case"sub":break;case"sup":break;case"span":break;case"alignment":break;case"borders":break;case"border":break;case"font":if(f[0].slice(-2)==="/>")break;f[1]==="/"?x+=a.slice(M,f.index):M=f.index+f[0].length;break;case"interior":if(!r.cellStyles)break;P.Interior=Kr(f[0]);break;case"protection":break;case"author":case"title":case"description":case"created":case"keywords":case"subject":case"category":case"company":case"lastauthor":case"lastsaved":case"lastprinted":case"version":case"revision":case"totaltime":case"hyperlinkbase":case"manager":case"contentstatus":case"identifier":case"language":case"appname":if(f[0].slice(-2)==="/>")break;f[1]==="/"?P1(z,Y,a.slice(b,f.index)):b=f.index+f[0].length;break;case"paragraphs":break;case"styles":case"workbook":if(f[1]==="/"){if((o=c.pop())[0]!==f[3])throw new Error("Bad state: "+o.join("|"))}else c.push([f[3],!1]);break;case"comment":if(f[1]==="/"){if((o=c.pop())[0]!==f[3])throw new Error("Bad state: "+o.join("|"));cg(ie),le.push(ie)}else c.push([f[3],!1]),o=Kr(f[0]),ie={a:o.Author};break;case"autofilter":if(f[1]==="/"){if((o=c.pop())[0]!==f[3])throw new Error("Bad state: "+o.join("|"))}else if(f[0].charAt(f[0].length-2)!=="/"){var Oe=Kr(f[0]);d["!autofilter"]={ref:ia(Oe.Range).replace(/\$/g,"")},c.push([f[3],!0])}break;case"name":break;case"datavalidation":if(f[1]==="/"){if((o=c.pop())[0]!==f[3])throw new Error("Bad state: "+o.join("|"))}else f[0].charAt(f[0].length-2)!=="/"&&c.push([f[3],!0]);break;case"pixelsperinch":break;case"componentoptions":case"documentproperties":case"customdocumentproperties":case"officedocumentsettings":case"pivottable":case"pivotcache":case"names":case"mapinfo":case"pagebreaks":case"querytable":case"sorting":case"schema":case"conditionalformatting":case"smarttagtype":case"smarttags":case"excelworkbook":case"workbookoptions":case"worksheetoptions":if(f[1]==="/"){if((o=c.pop())[0]!==f[3])throw new Error("Bad state: "+o.join("|"))}else f[0].charAt(f[0].length-2)!=="/"&&c.push([f[3],!0]);break;case"null":break;default:if(c.length==0&&f[3]=="document"||c.length==0&&f[3]=="uof")return of(a,r);var Me=!0;switch(c[c.length-1][0]){case"officedocumentsettings":switch(f[3]){case"allowpng":break;case"removepersonalinformation":break;case"downloadcomponents":break;case"locationofcomponents":break;case"colors":break;case"color":break;case"index":break;case"rgb":break;case"targetscreensize":break;case"readonlyrecommended":break;default:Me=!1}break;case"componentoptions":switch(f[3]){case"toolbar":break;case"hideofficelogo":break;case"spreadsheetautofit":break;case"label":break;case"caption":break;case"maxheight":break;case"maxwidth":break;case"nextsheetnumber":break;default:Me=!1}break;case"excelworkbook":switch(f[3]){case"date1904":F.WBProps.date1904=!0;break;case"windowheight":break;case"windowwidth":break;case"windowtopx":break;case"windowtopy":break;case"tabratio":break;case"protectstructure":break;case"protectwindow":break;case"protectwindows":break;case"activesheet":break;case"displayinknotes":break;case"firstvisiblesheet":break;case"supbook":break;case"sheetname":break;case"sheetindex":break;case"sheetindexfirst":break;case"sheetindexlast":break;case"dll":break;case"acceptlabelsinformulas":break;case"donotsavelinkvalues":break;case"iteration":break;case"maxiterations":break;case"maxchange":break;case"path":break;case"xct":break;case"count":break;case"selectedsheets":break;case"calculation":break;case"uncalced":break;case"startupprompt":break;case"crn":break;case"externname":break;case"formula":break;case"colfirst":break;case"collast":break;case"wantadvise":break;case"boolean":break;case"error":break;case"text":break;case"ole":break;case"noautorecover":break;case"publishobjects":break;case"donotcalculatebeforesave":break;case"number":break;case"refmoder1c1":break;case"embedsavesmarttags":break;default:Me=!1}break;case"workbookoptions":switch(f[3]){case"owcversion":break;case"height":break;case"width":break;default:Me=!1}break;case"worksheetoptions":switch(f[3]){case"visible":if(f[0].slice(-2)!=="/>")if(f[1]==="/")switch(a.slice(b,f.index)){case"SheetHidden":N.Hidden=1;break;case"SheetVeryHidden":N.Hidden=2;break}else b=f.index+f[0].length;break;case"header":d["!margins"]||bt(d["!margins"]={},"xlml"),isNaN(+ge(f[0]).Margin)||(d["!margins"].header=+ge(f[0]).Margin);break;case"footer":d["!margins"]||bt(d["!margins"]={},"xlml"),isNaN(+ge(f[0]).Margin)||(d["!margins"].footer=+ge(f[0]).Margin);break;case"pagemargins":var Ae=ge(f[0]);d["!margins"]||bt(d["!margins"]={},"xlml"),isNaN(+Ae.Top)||(d["!margins"].top=+Ae.Top),isNaN(+Ae.Left)||(d["!margins"].left=+Ae.Left),isNaN(+Ae.Right)||(d["!margins"].right=+Ae.Right),isNaN(+Ae.Bottom)||(d["!margins"].bottom=+Ae.Bottom);break;case"displayrighttoleft":F.Views||(F.Views=[]),F.Views[0]||(F.Views[0]={}),F.Views[0].RTL=!0;break;case"freezepanes":break;case"frozennosplit":break;case"splithorizontal":case"splitvertical":break;case"donotdisplaygridlines":break;case"activerow":break;case"activecol":break;case"toprowbottompane":break;case"leftcolumnrightpane":break;case"unsynced":break;case"print":break;case"printerrors":break;case"panes":break;case"scale":break;case"pane":break;case"number":break;case"layout":break;case"pagesetup":break;case"selected":break;case"protectobjects":break;case"enableselection":break;case"protectscenarios":break;case"validprinterinfo":break;case"horizontalresolution":break;case"verticalresolution":break;case"numberofcopies":break;case"activepane":break;case"toprowvisible":break;case"leftcolumnvisible":break;case"fittopage":break;case"rangeselection":break;case"papersizeindex":break;case"pagelayoutzoom":break;case"pagebreakzoom":break;case"filteron":break;case"fitwidth":break;case"fitheight":break;case"commentslayout":break;case"zoom":break;case"lefttoright":break;case"gridlines":break;case"allowsort":break;case"allowfilter":break;case"allowinsertrows":break;case"allowdeleterows":break;case"allowinsertcols":break;case"allowdeletecols":break;case"allowinserthyperlinks":break;case"allowformatcells":break;case"allowsizecols":break;case"allowsizerows":break;case"nosummaryrowsbelowdetail":d["!outline"]||(d["!outline"]={}),d["!outline"].above=!0;break;case"tabcolorindex":break;case"donotdisplayheadings":break;case"showpagelayoutzoom":break;case"nosummarycolumnsrightdetail":d["!outline"]||(d["!outline"]={}),d["!outline"].left=!0;break;case"blackandwhite":break;case"donotdisplayzeros":break;case"displaypagebreak":break;case"rowcolheadings":break;case"donotdisplayoutline":break;case"noorientation":break;case"allowusepivottables":break;case"zeroheight":break;case"viewablerange":break;case"selection":break;case"protectcontents":break;default:Me=!1}break;case"pivottable":case"pivotcache":switch(f[3]){case"immediateitemsondrop":break;case"showpagemultipleitemlabel":break;case"compactrowindent":break;case"location":break;case"pivotfield":break;case"orientation":break;case"layoutform":break;case"layoutsubtotallocation":break;case"layoutcompactrow":break;case"position":break;case"pivotitem":break;case"datatype":break;case"datafield":break;case"sourcename":break;case"parentfield":break;case"ptlineitems":break;case"ptlineitem":break;case"countofsameitems":break;case"item":break;case"itemtype":break;case"ptsource":break;case"cacheindex":break;case"consolidationreference":break;case"filename":break;case"reference":break;case"nocolumngrand":break;case"norowgrand":break;case"blanklineafteritems":break;case"hidden":break;case"subtotal":break;case"basefield":break;case"mapchilditems":break;case"function":break;case"refreshonfileopen":break;case"printsettitles":break;case"mergelabels":break;case"defaultversion":break;case"refreshname":break;case"refreshdate":break;case"refreshdatecopy":break;case"versionlastrefresh":break;case"versionlastupdate":break;case"versionupdateablemin":break;case"versionrefreshablemin":break;case"calculation":break;default:Me=!1}break;case"pagebreaks":switch(f[3]){case"colbreaks":break;case"colbreak":break;case"rowbreaks":break;case"rowbreak":break;case"colstart":break;case"colend":break;case"rowend":break;default:Me=!1}break;case"autofilter":switch(f[3]){case"autofiltercolumn":break;case"autofiltercondition":break;case"autofilterand":break;case"autofilteror":break;default:Me=!1}break;case"querytable":switch(f[3]){case"id":break;case"autoformatfont":break;case"autoformatpattern":break;case"querysource":break;case"querytype":break;case"enableredirections":break;case"refreshedinxl9":break;case"urlstring":break;case"htmltables":break;case"connection":break;case"commandtext":break;case"refreshinfo":break;case"notitles":break;case"nextid":break;case"columninfo":break;case"overwritecells":break;case"donotpromptforfile":break;case"textwizardsettings":break;case"source":break;case"number":break;case"decimal":break;case"thousandseparator":break;case"trailingminusnumbers":break;case"formatsettings":break;case"fieldtype":break;case"delimiters":break;case"tab":break;case"comma":break;case"autoformatname":break;case"versionlastedit":break;case"versionlastrefresh":break;default:Me=!1}break;case"datavalidation":switch(f[3]){case"range":break;case"type":break;case"min":break;case"max":break;case"sort":break;case"descending":break;case"order":break;case"casesensitive":break;case"value":break;case"errorstyle":break;case"errormessage":break;case"errortitle":break;case"inputmessage":break;case"inputtitle":break;case"combohide":break;case"inputhide":break;case"condition":break;case"qualifier":break;case"useblank":break;case"value1":break;case"value2":break;case"format":break;case"cellrangelist":break;default:Me=!1}break;case"sorting":case"conditionalformatting":switch(f[3]){case"range":break;case"type":break;case"min":break;case"max":break;case"sort":break;case"descending":break;case"order":break;case"casesensitive":break;case"value":break;case"errorstyle":break;case"errormessage":break;case"errortitle":break;case"cellrangelist":break;case"inputmessage":break;case"inputtitle":break;case"combohide":break;case"inputhide":break;case"condition":break;case"qualifier":break;case"useblank":break;case"value1":break;case"value2":break;case"format":break;default:Me=!1}break;case"mapinfo":case"schema":case"data":switch(f[3]){case"map":break;case"entry":break;case"range":break;case"xpath":break;case"field":break;case"xsdtype":break;case"filteron":break;case"aggregate":break;case"elementtype":break;case"attributetype":break;case"schema":case"element":case"complextype":case"datatype":case"all":case"attribute":case"extends":break;case"row":break;default:Me=!1}break;case"smarttags":break;default:Me=!1;break}if(Me||f[3].match(/!\[CDATA/))break;if(!c[c.length-1][1])throw"Unrecognized tag: "+f[3]+"|"+c.join("|");if(c[c.length-1][0]==="customdocumentproperties"){if(f[0].slice(-2)==="/>")break;f[1]==="/"?ng(X,Y,J,a.slice(b,f.index)):(J=f,b=f.index+f[0].length);break}if(r.WTF)throw"Unrecognized tag: "+f[3]+"|"+c.join("|")}var fe={};return!r.bookSheets&&!r.bookProps&&(fe.Sheets=l),fe.SheetNames=h,fe.Workbook=F,fe.SSF=Be(ve),fe.Props=z,fe.Custprops=X,fe}function fi(e,t){switch(Yi(t=t||{}),t.type||"base64"){case"base64":return Kn(Dr(e),t);case"binary":case"buffer":case"file":return Kn(e,t);case"array":return Kn(Ct(e),t)}}function og(e,t){var r=[];return e.Props&&r.push(D1(e.Props,t)),e.Custprops&&r.push(L1(e.Props,e.Custprops)),r.join("")}function lg(){return""}function hg(e,t){var r=['<Style ss:ID="Default" ss:Name="Normal"><NumberFormat/></Style>'];return t.cellXfs.forEach(function(a,n){var i=[];i.push(ae("NumberFormat",null,{"ss:Format":De(ve[a.numFmtId])}));var s={"ss:ID":"s"+(21+n)};r.push(ae("Style",i.join(""),s))}),ae("Styles",r.join(""))}function no(e){return ae("NamedRange",null,{"ss:Name":e.Name,"ss:RefersTo":"="+Vi(e.Ref,{r:0,c:0})})}function ug(e){if(!((e||{}).Workbook||{}).Names)return"";for(var t=e.Workbook.Names,r=[],a=0;a<t.length;++a){var n=t[a];n.Sheet==null&&(n.Name.match(/^_xlfn\./)||r.push(no(n)))}return ae("Names",r.join(""))}function dg(e,t,r,a){if(!e||!((a||{}).Workbook||{}).Names)return"";for(var n=a.Workbook.Names,i=[],s=0;s<n.length;++s){var f=n[s];f.Sheet==r&&(f.Name.match(/^_xlfn\./)||i.push(no(f)))}return i.join("")}function vg(e,t,r,a){if(!e)return"";var n=[];if(e["!margins"]&&(n.push("<PageSetup>"),e["!margins"].header&&n.push(ae("Header",null,{"x:Margin":e["!margins"].header})),e["!margins"].footer&&n.push(ae("Footer",null,{"x:Margin":e["!margins"].footer})),n.push(ae("PageMargins",null,{"x:Bottom":e["!margins"].bottom||"0.75","x:Left":e["!margins"].left||"0.7","x:Right":e["!margins"].right||"0.7","x:Top":e["!margins"].top||"0.75"})),n.push("</PageSetup>")),a&&a.Workbook&&a.Workbook.Sheets&&a.Workbook.Sheets[r])if(a.Workbook.Sheets[r].Hidden)n.push(ae("Visible",a.Workbook.Sheets[r].Hidden==1?"SheetHidden":"SheetVeryHidden",{}));else{for(var i=0;i<r&&!(a.Workbook.Sheets[i]&&!a.Workbook.Sheets[i].Hidden);++i);i==r&&n.push("<Selected/>")}return((((a||{}).Workbook||{}).Views||[])[0]||{}).RTL&&n.push("<DisplayRightToLeft/>"),e["!protect"]&&(n.push(ur("ProtectContents","True")),e["!protect"].objects&&n.push(ur("ProtectObjects","True")),e["!protect"].scenarios&&n.push(ur("ProtectScenarios","True")),e["!protect"].selectLockedCells!=null&&!e["!protect"].selectLockedCells?n.push(ur("EnableSelection","NoSelection")):e["!protect"].selectUnlockedCells!=null&&!e["!protect"].selectUnlockedCells&&n.push(ur("EnableSelection","UnlockedCells")),[["formatCells","AllowFormatCells"],["formatColumns","AllowSizeCols"],["formatRows","AllowSizeRows"],["insertColumns","AllowInsertCols"],["insertRows","AllowInsertRows"],["insertHyperlinks","AllowInsertHyperlinks"],["deleteColumns","AllowDeleteCols"],["deleteRows","AllowDeleteRows"],["sort","AllowSort"],["autoFilter","AllowFilter"],["pivotTables","AllowUsePivotTables"]].forEach(function(s){e["!protect"][s[0]]&&n.push("<"+s[1]+"/>")})),n.length==0?"":ae("WorksheetOptions",n.join(""),{xmlns:Fr.x})}function pg(e){return e.map(function(t){var r=Ml(t.t||""),a=ae("ss:Data",r,{xmlns:"http://www.w3.org/TR/REC-html40"});return ae("Comment",a,{"ss:Author":t.a})}).join("")}function mg(e,t,r,a,n,i,s){if(!e||e.v==null&&e.f==null)return"";var f={};if(e.f&&(f["ss:Formula"]="="+De(Vi(e.f,s))),e.F&&e.F.slice(0,t.length)==t){var c=ze(e.F.slice(t.length+1));f["ss:ArrayRange"]="RC:R"+(c.r==s.r?"":"["+(c.r-s.r)+"]")+"C"+(c.c==s.c?"":"["+(c.c-s.c)+"]")}if(e.l&&e.l.Target&&(f["ss:HRef"]=De(e.l.Target),e.l.Tooltip&&(f["x:HRefScreenTip"]=De(e.l.Tooltip))),r["!merges"])for(var o=r["!merges"],l=0;l!=o.length;++l)o[l].s.c!=s.c||o[l].s.r!=s.r||(o[l].e.c>o[l].s.c&&(f["ss:MergeAcross"]=o[l].e.c-o[l].s.c),o[l].e.r>o[l].s.r&&(f["ss:MergeDown"]=o[l].e.r-o[l].s.r));var h="",d="";switch(e.t){case"z":if(!a.sheetStubs)return"";break;case"n":h="Number",d=String(e.v);break;case"b":h="Boolean",d=e.v?"1":"0";break;case"e":h="Error",d=pt[e.v];break;case"d":h="DateTime",d=new Date(e.v).toISOString(),e.z==null&&(e.z=e.z||ve[14]);break;case"s":h="String",d=bl(e.v||"");break}var v=It(a.cellXfs,e,a);f["ss:StyleID"]="s"+(21+v),f["ss:Index"]=s.c+1;var p=e.v!=null?d:"",u=e.t=="z"?"":'<Data ss:Type="'+h+'">'+p+"</Data>";return(e.c||[]).length>0&&(u+=pg(e.c)),ae("Cell",u,f)}function gg(e,t){var r='<Row ss:Index="'+(e+1)+'"';return t&&(t.hpt&&!t.hpx&&(t.hpx=oa(t.hpt)),t.hpx&&(r+=' ss:AutoFitHeight="0" ss:Height="'+t.hpx+'"'),t.hidden&&(r+=' ss:Hidden="1"')),r+">"}function _g(e,t,r,a){if(!e["!ref"])return"";var n=Ce(e["!ref"]),i=e["!merges"]||[],s=0,f=[];e["!cols"]&&e["!cols"].forEach(function(m,k){yt(m);var A=!!m.width,_=Dn(k,m),I={"ss:Index":k+1};A&&(I["ss:Width"]=Ua(_.width)),m.hidden&&(I["ss:Hidden"]="1"),f.push(ae("Column",null,I))});for(var c=Array.isArray(e),o=n.s.r;o<=n.e.r;++o){for(var l=[gg(o,(e["!rows"]||[])[o])],h=n.s.c;h<=n.e.c;++h){var d=!1;for(s=0;s!=i.length;++s)if(!(i[s].s.c>h)&&!(i[s].s.r>o)&&!(i[s].e.c<h)&&!(i[s].e.r<o)){(i[s].s.c!=h||i[s].s.r!=o)&&(d=!0);break}if(!d){var v={r:o,c:h},p=me(v),u=c?(e[o]||[])[h]:e[p];l.push(mg(u,p,e,t,r,a,v))}}l.push("</Row>"),l.length>2&&f.push(l.join(""))}return f.join("")}function wg(e,t,r){var a=[],n=r.SheetNames[e],i=r.Sheets[n],s=i?dg(i,t,e,r):"";return s.length>0&&a.push("<Names>"+s+"</Names>"),s=i?_g(i,t,e,r):"",s.length>0&&a.push("<Table>"+s+"</Table>"),a.push(vg(i,t,e,r)),a.join("")}function kg(e,t){t||(t={}),e.SSF||(e.SSF=Be(ve)),e.SSF&&(la(),$a(e.SSF),t.revssf=On(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF,t.cellXfs=[],It(t.cellXfs,{},{revssf:{General:0}}));var r=[];r.push(og(e,t)),r.push(lg()),r.push(""),r.push("");for(var a=0;a<e.SheetNames.length;++a)r.push(ae("Worksheet",wg(a,t,e),{"ss:Name":De(e.SheetNames[a])}));return r[2]=hg(e,t),r[3]=ug(e),Qe+ae("Workbook",r.join(""),{xmlns:Fr.ss,"xmlns:o":Fr.o,"xmlns:x":Fr.x,"xmlns:ss":Fr.ss,"xmlns:dt":Fr.dt,"xmlns:html":Fr.html})}function Tg(e){var t={},r=e.content;if(r.l=28,t.AnsiUserType=r.read_shift(0,"lpstr-ansi"),t.AnsiClipboardFormat=d1(r),r.length-r.l<=4)return t;var a=r.read_shift(4);if(a==0||a>40||(r.l-=4,t.Reserved1=r.read_shift(0,"lpstr-ansi"),r.length-r.l<=4)||(a=r.read_shift(4),a!==1907505652)||(t.UnicodeClipboardFormat=v1(r),a=r.read_shift(4),a==0||a>40))return t;r.l-=4,t.Reserved2=r.read_shift(0,"lpwstr")}var Eg=[60,1084,2066,2165,2175];function Sg(e,t,r,a,n){var i=a,s=[],f=r.slice(r.l,r.l+i);if(n&&n.enc&&n.enc.insitu&&f.length>0)switch(e){case 9:case 521:case 1033:case 2057:case 47:case 405:case 225:case 406:case 312:case 404:case 10:break;case 133:break;default:n.enc.insitu(f)}s.push(f),r.l+=i;for(var c=lt(r,r.l),o=ci[c],l=0;o!=null&&Eg.indexOf(c)>-1;)i=lt(r,r.l+2),l=r.l+4,c==2066?l+=4:(c==2165||c==2175)&&(l+=12),f=r.slice(l,r.l+4+i),s.push(f),r.l+=4+i,o=ci[c=lt(r,r.l)];var h=cr(s);hr(h,0);var d=0;h.lens=[];for(var v=0;v<s.length;++v)h.lens.push(d),d+=s[v].length;if(h.length<a)throw"XLS Record 0x"+e.toString(16)+" Truncated: "+h.length+" < "+a;return t.f(h,h.length,n)}function Qr(e,t,r){if(e.t!=="z"&&e.XF){var a=0;try{a=e.z||e.XF.numFmtId||0,t.cellNF&&(e.z=ve[a])}catch(i){if(t.WTF)throw i}if(!t||t.cellText!==!1)try{e.t==="e"?e.w=e.w||pt[e.v]:a===0||a=="General"?e.t==="n"?(e.v|0)===e.v?e.w=e.v.toString(10):e.w=Pa(e.v):e.w=Bt(e.v):e.w=Lr(a,e.v,{date1904:!!r,dateNF:t&&t.dateNF})}catch(i){if(t.WTF)throw i}if(t.cellDates&&a&&e.t=="n"&&Gt(ve[a]||String(a))){var n=kt(e.v);n&&(e.t="d",e.v=new Date(n.y,n.m-1,n.d,n.H,n.M,n.S,n.u))}}}function ln(e,t,r){return{v:e,ixfe:t,t:r}}function yg(e,t){var r={opts:{}},a={},n=t.dense?[]:{},i={},s={},f=null,c=[],o="",l={},h,d="",v,p,u,m,k={},A=[],_,I,D=[],P=[],x={Sheets:[],WBProps:{date1904:!1},Views:[{}]},M={},R=function(ye){return ye<8?Pt[ye]:ye<64&&P[ye-8]||Pt[ye]},z=function(ye,Ze,Mr){var ar=Ze.XF.data;if(!(!ar||!ar.patternType||!Mr||!Mr.cellStyles)){Ze.s={},Ze.s.patternType=ar.patternType;var Ft;(Ft=Ba(R(ar.icvFore)))&&(Ze.s.fgColor={rgb:Ft}),(Ft=Ba(R(ar.icvBack)))&&(Ze.s.bgColor={rgb:Ft})}},X=function(ye,Ze,Mr){if(!(_e>1)&&!(Mr.sheetRows&&ye.r>=Mr.sheetRows)){if(Mr.cellStyles&&Ze.XF&&Ze.XF.data&&z(ye,Ze,Mr),delete Ze.ixfe,delete Ze.XF,h=ye,d=me(ye),(!s||!s.s||!s.e)&&(s={s:{r:0,c:0},e:{r:0,c:0}}),ye.r<s.s.r&&(s.s.r=ye.r),ye.c<s.s.c&&(s.s.c=ye.c),ye.r+1>s.e.r&&(s.e.r=ye.r+1),ye.c+1>s.e.c&&(s.e.c=ye.c+1),Mr.cellFormula&&Ze.f){for(var ar=0;ar<A.length;++ar)if(!(A[ar][0].s.c>ye.c||A[ar][0].s.r>ye.r)&&!(A[ar][0].e.c<ye.c||A[ar][0].e.r<ye.r)){Ze.F=ke(A[ar][0]),(A[ar][0].s.c!=ye.c||A[ar][0].s.r!=ye.r)&&delete Ze.f,Ze.f&&(Ze.f=""+mr(A[ar][1],s,ye,V,b));break}}Mr.dense?(n[ye.r]||(n[ye.r]=[]),n[ye.r][ye.c]=Ze):n[d]=Ze}},b={enc:!1,sbcch:0,snames:[],sharedf:k,arrayf:A,rrtabid:[],lastuser:"",biff:8,codepage:0,winlocked:0,cellStyles:!!t&&!!t.cellStyles,WTF:!!t&&!!t.wtf};t.password&&(b.password=t.password);var J,le=[],ie=[],ue=[],ce=[],Le=!1,V=[];V.SheetNames=b.snames,V.sharedf=b.sharedf,V.arrayf=b.arrayf,V.names=[],V.XTI=[];var pe=0,_e=0,O=0,L=[],F=[],N;b.codepage=1200,Gr(1200);for(var Y=!1;e.l<e.length-1;){var re=e.l,te=e.read_shift(2);if(te===0&&pe===10)break;var Q=e.l===e.length?0:e.read_shift(2),Z=ci[te];if(Z&&Z.f){if(t.bookSheets&&pe===133&&te!==133)break;if(pe=te,Z.r===2||Z.r==12){var Ee=e.read_shift(2);if(Q-=2,!b.enc&&Ee!==te&&((Ee&255)<<8|Ee>>8)!==te)throw new Error("rt mismatch: "+Ee+"!="+te);Z.r==12&&(e.l+=10,Q-=10)}var C={};if(te===10?C=Z.f(e,Q,b):C=Sg(te,Z,e,Q,b),_e==0&&[9,521,1033,2057].indexOf(pe)===-1)continue;switch(te){case 34:r.opts.Date1904=x.WBProps.date1904=C;break;case 134:r.opts.WriteProtect=!0;break;case 47:if(b.enc||(e.l=0),b.enc=C,!t.password)throw new Error("File is password-protected");if(C.valid==null)throw new Error("Encryption scheme unsupported");if(!C.valid)throw new Error("Password is incorrect");break;case 92:b.lastuser=C;break;case 66:var Ue=Number(C);switch(Ue){case 21010:Ue=1200;break;case 32768:Ue=1e4;break;case 32769:Ue=1252;break}Gr(b.codepage=Ue),Y=!0;break;case 317:b.rrtabid=C;break;case 25:b.winlocked=C;break;case 439:r.opts.RefreshAll=C;break;case 12:r.opts.CalcCount=C;break;case 16:r.opts.CalcDelta=C;break;case 17:r.opts.CalcIter=C;break;case 13:r.opts.CalcMode=C;break;case 14:r.opts.CalcPrecision=C;break;case 95:r.opts.CalcSaveRecalc=C;break;case 15:b.CalcRefMode=C;break;case 2211:r.opts.FullCalc=C;break;case 129:C.fDialog&&(n["!type"]="dialog"),C.fBelow||((n["!outline"]||(n["!outline"]={})).above=!0),C.fRight||((n["!outline"]||(n["!outline"]={})).left=!0);break;case 224:D.push(C);break;case 430:V.push([C]),V[V.length-1].XTI=[];break;case 35:case 547:V[V.length-1].push(C);break;case 24:case 536:N={Name:C.Name,Ref:mr(C.rgce,s,null,V,b)},C.itab>0&&(N.Sheet=C.itab-1),V.names.push(N),V[0]||(V[0]=[],V[0].XTI=[]),V[V.length-1].push(C),C.Name=="_xlnm._FilterDatabase"&&C.itab>0&&C.rgce&&C.rgce[0]&&C.rgce[0][0]&&C.rgce[0][0][0]=="PtgArea3d"&&(F[C.itab-1]={ref:ke(C.rgce[0][0][1][2])});break;case 22:b.ExternCount=C;break;case 23:V.length==0&&(V[0]=[],V[0].XTI=[]),V[V.length-1].XTI=V[V.length-1].XTI.concat(C),V.XTI=V.XTI.concat(C);break;case 2196:if(b.biff<8)break;N!=null&&(N.Comment=C[1]);break;case 18:n["!protect"]=C;break;case 19:C!==0&&b.WTF&&console.error("Password verifier: "+C);break;case 133:i[C.pos]=C,b.snames.push(C.name);break;case 10:{if(--_e)break;if(s.e){if(s.e.r>0&&s.e.c>0){if(s.e.r--,s.e.c--,n["!ref"]=ke(s),t.sheetRows&&t.sheetRows<=s.e.r){var Oe=s.e.r;s.e.r=t.sheetRows-1,n["!fullref"]=n["!ref"],n["!ref"]=ke(s),s.e.r=Oe}s.e.r++,s.e.c++}le.length>0&&(n["!merges"]=le),ie.length>0&&(n["!objects"]=ie),ue.length>0&&(n["!cols"]=ue),ce.length>0&&(n["!rows"]=ce),x.Sheets.push(M)}o===""?l=n:a[o]=n,n=t.dense?[]:{}}break;case 9:case 521:case 1033:case 2057:{if(b.biff===8&&(b.biff={9:2,521:3,1033:4}[te]||{512:2,768:3,1024:4,1280:5,1536:8,2:2,7:2}[C.BIFFVer]||8),b.biffguess=C.BIFFVer==0,C.BIFFVer==0&&C.dt==4096&&(b.biff=5,Y=!0,Gr(b.codepage=28591)),b.biff==8&&C.BIFFVer==0&&C.dt==16&&(b.biff=2),_e++)break;if(n=t.dense?[]:{},b.biff<8&&!Y&&(Y=!0,Gr(b.codepage=t.codepage||1252)),b.biff<5||C.BIFFVer==0&&C.dt==4096){o===""&&(o="Sheet1"),s={s:{r:0,c:0},e:{r:0,c:0}};var Me={pos:e.l-Q,name:o};i[Me.pos]=Me,b.snames.push(o)}else o=(i[re]||{name:""}).name;C.dt==32&&(n["!type"]="chart"),C.dt==64&&(n["!type"]="macro"),le=[],ie=[],b.arrayf=A=[],ue=[],ce=[],Le=!1,M={Hidden:(i[re]||{hs:0}).hs,name:o}}break;case 515:case 3:case 2:n["!type"]=="chart"&&(t.dense?(n[C.r]||[])[C.c]:n[me({c:C.c,r:C.r})])&&++C.c,_={ixfe:C.ixfe,XF:D[C.ixfe]||{},v:C.val,t:"n"},O>0&&(_.z=L[_.ixfe>>8&63]),Qr(_,t,r.opts.Date1904),X({c:C.c,r:C.r},_,t);break;case 5:case 517:_={ixfe:C.ixfe,XF:D[C.ixfe],v:C.val,t:C.t},O>0&&(_.z=L[_.ixfe>>8&63]),Qr(_,t,r.opts.Date1904),X({c:C.c,r:C.r},_,t);break;case 638:_={ixfe:C.ixfe,XF:D[C.ixfe],v:C.rknum,t:"n"},O>0&&(_.z=L[_.ixfe>>8&63]),Qr(_,t,r.opts.Date1904),X({c:C.c,r:C.r},_,t);break;case 189:for(var Ae=C.c;Ae<=C.C;++Ae){var fe=C.rkrec[Ae-C.c][0];_={ixfe:fe,XF:D[fe],v:C.rkrec[Ae-C.c][1],t:"n"},O>0&&(_.z=L[_.ixfe>>8&63]),Qr(_,t,r.opts.Date1904),X({c:Ae,r:C.r},_,t)}break;case 6:case 518:case 1030:{if(C.val=="String"){f=C;break}if(_=ln(C.val,C.cell.ixfe,C.tt),_.XF=D[_.ixfe],t.cellFormula){var Je=C.formula;if(Je&&Je[0]&&Je[0][0]&&Je[0][0][0]=="PtgExp"){var br=Je[0][0][1][0],Zr=Je[0][0][1][1],ft=me({r:br,c:Zr});k[ft]?_.f=""+mr(C.formula,s,C.cell,V,b):_.F=((t.dense?(n[br]||[])[Zr]:n[ft])||{}).F}else _.f=""+mr(C.formula,s,C.cell,V,b)}O>0&&(_.z=L[_.ixfe>>8&63]),Qr(_,t,r.opts.Date1904),X(C.cell,_,t),f=C}break;case 7:case 519:if(f)f.val=C,_=ln(C,f.cell.ixfe,"s"),_.XF=D[_.ixfe],t.cellFormula&&(_.f=""+mr(f.formula,s,f.cell,V,b)),O>0&&(_.z=L[_.ixfe>>8&63]),Qr(_,t,r.opts.Date1904),X(f.cell,_,t),f=null;else throw new Error("String record expects Formula");break;case 33:case 545:{A.push(C);var va=me(C[0].s);if(v=t.dense?(n[C[0].s.r]||[])[C[0].s.c]:n[va],t.cellFormula&&v){if(!f||!va||!v)break;v.f=""+mr(C[1],s,C[0],V,b),v.F=ke(C[0])}}break;case 1212:{if(!t.cellFormula)break;if(d){if(!f)break;k[me(f.cell)]=C[0],v=t.dense?(n[f.cell.r]||[])[f.cell.c]:n[me(f.cell)],(v||{}).f=""+mr(C[0],s,h,V,b)}}break;case 253:_=ln(c[C.isst].t,C.ixfe,"s"),c[C.isst].h&&(_.h=c[C.isst].h),_.XF=D[_.ixfe],O>0&&(_.z=L[_.ixfe>>8&63]),Qr(_,t,r.opts.Date1904),X({c:C.c,r:C.r},_,t);break;case 513:t.sheetStubs&&(_={ixfe:C.ixfe,XF:D[C.ixfe],t:"z"},O>0&&(_.z=L[_.ixfe>>8&63]),Qr(_,t,r.opts.Date1904),X({c:C.c,r:C.r},_,t));break;case 190:if(t.sheetStubs)for(var mt=C.c;mt<=C.C;++mt){var Ir=C.ixfe[mt-C.c];_={ixfe:Ir,XF:D[Ir],t:"z"},O>0&&(_.z=L[_.ixfe>>8&63]),Qr(_,t,r.opts.Date1904),X({c:mt,r:C.r},_,t)}break;case 214:case 516:case 4:_=ln(C.val,C.ixfe,"s"),_.XF=D[_.ixfe],O>0&&(_.z=L[_.ixfe>>8&63]),Qr(_,t,r.opts.Date1904),X({c:C.c,r:C.r},_,t);break;case 0:case 512:_e===1&&(s=C);break;case 252:c=C;break;case 1054:if(b.biff==4){L[O++]=C[1];for(var ct=0;ct<O+163&&ve[ct]!=C[1];++ct);ct>=163&&at(C[1],O+163)}else at(C[1],C[0]);break;case 30:{L[O++]=C;for(var gt=0;gt<O+163&&ve[gt]!=C;++gt);gt>=163&&at(C,O+163)}break;case 229:le=le.concat(C);break;case 93:ie[C.cmo[0]]=b.lastobj=C;break;case 438:b.lastobj.TxO=C;break;case 127:b.lastobj.ImData=C;break;case 440:for(m=C[0].s.r;m<=C[0].e.r;++m)for(u=C[0].s.c;u<=C[0].e.c;++u)v=t.dense?(n[m]||[])[u]:n[me({c:u,r:m})],v&&(v.l=C[1]);break;case 2048:for(m=C[0].s.r;m<=C[0].e.r;++m)for(u=C[0].s.c;u<=C[0].e.c;++u)v=t.dense?(n[m]||[])[u]:n[me({c:u,r:m})],v&&v.l&&(v.l.Tooltip=C[1]);break;case 28:{if(b.biff<=5&&b.biff>=2)break;v=t.dense?(n[C[0].r]||[])[C[0].c]:n[me(C[0])];var pa=ie[C[2]];v||(t.dense?(n[C[0].r]||(n[C[0].r]=[]),v=n[C[0].r][C[0].c]={t:"z"}):v=n[me(C[0])]={t:"z"},s.e.r=Math.max(s.e.r,C[0].r),s.s.r=Math.min(s.s.r,C[0].r),s.e.c=Math.max(s.e.c,C[0].c),s.s.c=Math.min(s.s.c,C[0].c)),v.c||(v.c=[]),p={a:C[1],t:pa.TxO.t},v.c.push(p)}break;case 2173:u2(D[C.ixfe],C.ext);break;case 125:{if(!b.cellStyles)break;for(;C.e>=C.s;)ue[C.e--]={width:C.w/256,level:C.level||0,hidden:!!(C.flags&1)},Le||(Le=!0,Ui(C.w/256)),yt(ue[C.e+1])}break;case 520:{var Sr={};C.level!=null&&(ce[C.r]=Sr,Sr.level=C.level),C.hidden&&(ce[C.r]=Sr,Sr.hidden=!0),C.hpt&&(ce[C.r]=Sr,Sr.hpt=C.hpt,Sr.hpx=oa(C.hpt))}break;case 38:case 39:case 40:case 41:n["!margins"]||bt(n["!margins"]={}),n["!margins"][{38:"left",39:"right",40:"top",41:"bottom"}[te]]=C;break;case 161:n["!margins"]||bt(n["!margins"]={}),n["!margins"].header=C.header,n["!margins"].footer=C.footer;break;case 574:C.RTL&&(x.Views[0].RTL=!0);break;case 146:P=C;break;case 2198:J=C;break;case 140:I=C;break;case 442:o?M.CodeName=C||M.name:x.WBProps.CodeName=C||"ThisWorkbook";break}}else Z||console.error("Missing Info for XLS Record 0x"+te.toString(16)),e.l+=Q}return r.SheetNames=je(i).sort(function(qr,ye){return Number(qr)-Number(ye)}).map(function(qr){return i[qr].name}),t.bookSheets||(r.Sheets=a),!r.SheetNames.length&&l["!ref"]?(r.SheetNames.push("Sheet1"),r.Sheets&&(r.Sheets.Sheet1=l)):r.Preamble=l,r.Sheets&&F.forEach(function(qr,ye){r.Sheets[r.SheetNames[ye]]["!autofilter"]=qr}),r.Strings=c,r.SSF=Be(ve),b.enc&&(r.Encryption=b.enc),J&&(r.Themes=J),r.Metadata={},I!==void 0&&(r.Metadata.Country=I),V.names.length>0&&(x.Names=V.names),r.Workbook=x,r}var Fa={SI:"e0859ff2f94f6810ab9108002b27b3d9",DSI:"02d5cdd59c2e1b10939708002b2cf9ae",UDI:"05d5cdd59c2e1b10939708002b2cf9ae"};function xg(e,t,r){var a=de.find(e,"/!DocumentSummaryInformation");if(a&&a.size>0)try{var n=Bs(a,ri,Fa.DSI);for(var i in n)t[i]=n[i]}catch(o){if(r.WTF)throw o}var s=de.find(e,"/!SummaryInformation");if(s&&s.size>0)try{var f=Bs(s,ti,Fa.SI);for(var c in f)t[c]==null&&(t[c]=f[c])}catch(o){if(r.WTF)throw o}t.HeadingPairs&&t.TitlesOfParts&&(tc(t.HeadingPairs,t.TitlesOfParts,t,r),delete t.HeadingPairs,delete t.TitlesOfParts)}function Ag(e,t){var r=[],a=[],n=[],i=0,s,f=vs(ri,"n"),c=vs(ti,"n");if(e.Props)for(s=je(e.Props),i=0;i<s.length;++i)(Object.prototype.hasOwnProperty.call(f,s[i])?r:Object.prototype.hasOwnProperty.call(c,s[i])?a:n).push([s[i],e.Props[s[i]]]);if(e.Custprops)for(s=je(e.Custprops),i=0;i<s.length;++i)Object.prototype.hasOwnProperty.call(e.Props||{},s[i])||(Object.prototype.hasOwnProperty.call(f,s[i])?r:Object.prototype.hasOwnProperty.call(c,s[i])?a:n).push([s[i],e.Custprops[s[i]]]);var o=[];for(i=0;i<n.length;++i)oc.indexOf(n[i][0])>-1||rc.indexOf(n[i][0])>-1||n[i][1]!=null&&o.push(n[i]);a.length&&de.utils.cfb_add(t,"/SummaryInformation",Us(a,Fa.SI,c,ti)),(r.length||o.length)&&de.utils.cfb_add(t,"/DocumentSummaryInformation",Us(r,Fa.DSI,f,ri,o.length?o:null,Fa.UDI))}function io(e,t){t||(t={}),Yi(t),An(),t.codepage&&xn(t.codepage);var r,a;if(e.FullPaths){if(de.find(e,"/encryption"))throw new Error("File is password-protected");r=de.find(e,"!CompObj"),a=de.find(e,"/Workbook")||de.find(e,"/Book")}else{switch(t.type){case"base64":e=Rr(Dr(e));break;case"binary":e=Rr(e);break;case"buffer":break;case"array":Array.isArray(e)||(e=Array.prototype.slice.call(e));break}hr(e,0),a={content:e}}var n,i;if(r&&Tg(r),t.bookProps&&!t.bookSheets)n={};else{var s=Se?"buffer":"array";if(a&&a.content)n=yg(a.content,t);else if((i=de.find(e,"PerfectOffice_MAIN"))&&i.content)n=Lt.to_workbook(i.content,(t.type=s,t));else if((i=de.find(e,"NativeContent_MAIN"))&&i.content)n=Lt.to_workbook(i.content,(t.type=s,t));else throw(i=de.find(e,"MN0"))&&i.content?new Error("Unsupported Works 4 for Mac file"):new Error("Cannot find Workbook stream");t.bookVBA&&e.FullPaths&&de.find(e,"/_VBA_PROJECT_CUR/VBA/dir")&&(n.vbaraw=W2(e))}var f={};return e.FullPaths&&xg(e,f,t),n.Props=n.Custprops=f,t.bookFiles&&(n.cfb=e),n}function Cg(e,t){var r=t||{},a=de.utils.cfb_new({root:"R"}),n="/Workbook";switch(r.bookType||"xls"){case"xls":r.bookType="biff8";case"xla":r.bookType||(r.bookType="xla");case"biff8":n="/Workbook",r.biff=8;break;case"biff5":n="/Book",r.biff=5;break;default:throw new Error("invalid type "+r.bookType+" for XLS CFB")}return de.utils.cfb_add(a,n,so(e,r)),r.biff==8&&(e.Props||e.Custprops)&&Ag(e,a),r.biff==8&&e.vbaraw&&H2(a,de.read(e.vbaraw,{type:typeof e.vbaraw=="string"?"binary":"buffer"})),a}var Va={0:{f:qv},1:{f:sp},2:{f:xp},3:{f:vp},4:{f:lp},5:{f:Ep},6:{f:Fp},7:{f:_p},8:{f:Mp},9:{f:bp},10:{f:Dp},11:{f:Lp},12:{f:cp},13:{f:Cp},14:{f:mp},15:{f:up},16:{f:Jc},17:{f:Np},18:{f:kp},19:{f:Ii},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:Rm},40:{},42:{},43:{f:F0},44:{f:O0},45:{f:P0},46:{f:L0},47:{f:D0},48:{},49:{f:r1},50:{},51:{f:p2},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:kc},62:{f:Ip},63:{f:y2},64:{f:Qp},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:Tr,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:jp},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:np},148:{f:rp,p:16},151:{f:Xp},152:{},153:{f:Om},154:{},155:{},156:{f:Am},157:{},158:{},159:{T:1,f:zu},160:{T:-1},161:{T:1,f:jt},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:Bp},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:d2},336:{T:-1},337:{f:_2,T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:ei},357:{},358:{},359:{},360:{T:1},361:{},362:{f:wc},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:Gp},427:{f:zp},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:Kp},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:ap},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:Hp},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:ei},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:L2},633:{T:1},634:{T:-1},635:{T:1,f:P2},636:{T:-1},637:{f:i1},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:mm},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:em},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}},ci={6:{f:zn},10:{f:_t},12:{f:tr},13:{f:tr},14:{f:qe},15:{f:qe},16:{f:_r},17:{f:qe},18:{f:qe},19:{f:tr},20:{f:zs},21:{f:zs},23:{f:wc},24:{f:Ks},25:{f:qe},26:{},27:{},28:{f:eu},29:{},34:{f:qe},35:{f:$s},38:{f:_r},39:{f:_r},40:{f:_r},41:{f:_r},42:{f:qe},43:{f:qe},47:{f:u0},49:{f:Ch},51:{f:tr},60:{},61:{f:Eh},64:{f:qe},65:{f:Ah},66:{f:tr},77:{},80:{},81:{},82:{},85:{f:tr},89:{},90:{},91:{},92:{f:hh},93:{f:au},94:{},95:{f:qe},96:{},97:{},99:{f:qe},125:{f:kc},128:{f:Vh},129:{f:dh},130:{f:tr},131:{f:qe},132:{f:qe},133:{f:vh},134:{},140:{f:hu},141:{f:tr},144:{},146:{f:vu},151:{},152:{},153:{},154:{},155:{},156:{f:tr},157:{},158:{},160:{f:Tu},161:{f:gu},174:{},175:{},176:{},177:{},178:{},180:{},181:{},182:{},184:{},185:{},189:{f:Bh},190:{f:Uh},193:{f:_t},197:{},198:{},199:{},200:{},201:{},202:{f:qe},203:{},204:{},205:{},206:{},207:{},208:{},209:{},210:{},211:{},213:{},215:{},216:{},217:{},218:{f:tr},220:{},221:{f:qe},222:{},224:{f:Hh},225:{f:lh},226:{f:_t},227:{},229:{f:ru},233:{},235:{},236:{},237:{},239:{},240:{},241:{},242:{},244:{},245:{},246:{},247:{},248:{},249:{},251:{},252:{f:mh},253:{f:Ih},255:{f:_h},256:{},259:{},290:{},311:{},312:{},315:{},317:{f:lc},318:{},319:{},320:{},330:{},331:{},333:{},334:{},335:{},336:{},337:{},338:{},339:{},340:{},351:{},352:{f:qe},353:{f:_t},401:{},402:{},403:{},404:{},405:{},406:{},407:{},408:{},425:{},426:{},427:{},428:{},429:{},430:{f:Kh},431:{f:qe},432:{},433:{},434:{},437:{},438:{f:su},439:{f:qe},440:{f:fu},441:{},442:{f:ja},443:{},444:{f:tr},445:{},446:{},448:{f:_t},449:{f:Th,r:2},450:{f:_t},512:{f:Vs},513:{f:ku},515:{f:zh},516:{f:Rh},517:{f:Gs},519:{f:Eu},520:{f:wh},523:{},545:{f:Ys},549:{f:Hs},566:{},574:{f:yh},638:{f:Mh},659:{},1048:{},1054:{f:Ph},1084:{},1212:{f:Zh},2048:{f:ou},2049:{},2050:{},2051:{},2052:{},2053:{},2054:{},2055:{},2056:{},2057:{f:fn},2058:{},2059:{},2060:{},2061:{},2062:{},2063:{},2064:{},2066:{},2067:{},2128:{},2129:{},2130:{},2131:{},2132:{},2133:{},2134:{},2135:{},2136:{},2137:{},2138:{},2146:{},2147:{r:12},2148:{},2149:{},2150:{},2151:{f:_t},2152:{},2154:{},2155:{},2156:{},2161:{},2162:{},2164:{},2165:{},2166:{},2167:{},2168:{},2169:{},2170:{},2171:{},2172:{f:pu,r:12},2173:{f:h2,r:12},2174:{},2175:{},2180:{},2181:{},2182:{},2183:{},2184:{},2185:{},2186:{},2187:{},2188:{f:qe,r:12},2189:{},2190:{r:12},2191:{},2192:{},2194:{},2195:{},2196:{f:Jh,r:12},2197:{},2198:{f:i2,r:12},2199:{},2200:{},2201:{},2202:{f:qh,r:12},2203:{f:_t},2204:{},2205:{},2206:{},2207:{},2211:{f:kh},2212:{},2213:{},2214:{},2215:{},4097:{},4098:{},4099:{},4102:{},4103:{},4105:{},4106:{},4107:{},4108:{},4109:{},4116:{},4117:{},4118:{},4119:{},4120:{},4121:{},4122:{},4123:{},4124:{},4125:{},4126:{},4127:{},4128:{},4129:{},4130:{},4132:{},4133:{},4134:{f:tr},4135:{},4146:{},4147:{},4148:{},4149:{},4154:{},4156:{},4157:{},4158:{},4159:{},4160:{},4161:{},4163:{},4164:{f:_u},4165:{},4166:{},4168:{},4170:{},4171:{},4174:{},4175:{},4176:{},4177:{},4187:{},4188:{f:du},4189:{},4191:{},4192:{},4193:{},4194:{},4195:{},4196:{},4197:{},4198:{},4199:{},4200:{},0:{f:Vs},1:{},2:{f:Cu},3:{f:xu},4:{f:yu},5:{f:Gs},7:{f:Iu},8:{},9:{f:fn},11:{},22:{f:tr},30:{f:Lh},31:{},32:{},33:{f:Ys},36:{},37:{f:Hs},50:{f:Fu},62:{},52:{},67:{},68:{f:tr},69:{},86:{},126:{},127:{f:Su},135:{},136:{},137:{},145:{},148:{},149:{},150:{},169:{},171:{},188:{},191:{},192:{},194:{},195:{},214:{f:Ru},223:{},234:{},354:{},421:{},518:{f:zn},521:{f:fn},536:{f:Ks},547:{f:$s},561:{},579:{},1030:{f:zn},1033:{f:fn},1091:{},2157:{},2163:{},2177:{},2240:{},2241:{},2242:{},2243:{},2244:{},2245:{},2246:{},2247:{},2248:{},2249:{},2250:{},2251:{},2262:{r:12},29282:{}};function ne(e,t,r,a){var n=t;if(!isNaN(n)){var i=a||(r||[]).length||0,s=e.next(4);s.write_shift(2,n),s.write_shift(2,i),i>0&&Ai(r)&&e.push(r)}}function Og(e,t,r,a){var n=(r||[]).length||0;if(n<=8224)return ne(e,t,r,n);var i=t;if(!isNaN(i)){for(var s=r.parts||[],f=0,c=0,o=0;o+(s[f]||8224)<=8224;)o+=s[f]||8224,f++;var l=e.next(4);for(l.write_shift(2,i),l.write_shift(2,o),e.push(r.slice(c,c+o)),c+=o;c<n;){for(l=e.next(4),l.write_shift(2,60),o=0;o+(s[f]||8224)<=8224;)o+=s[f]||8224,f++;l.write_shift(2,o),e.push(r.slice(c,c+o)),c+=o}}}function Qa(e,t,r){return e||(e=G(7)),e.write_shift(2,t),e.write_shift(2,r),e.write_shift(2,0),e.write_shift(1,0),e}function Ig(e,t,r,a){var n=G(9);return Qa(n,e,t),hc(r,a||"b",n),n}function Fg(e,t,r){var a=G(8+2*r.length);return Qa(a,e,t),a.write_shift(1,r.length),a.write_shift(r.length,r,"sbcs"),a.l<a.length?a.slice(0,a.l):a}function Rg(e,t,r,a){if(t.v!=null)switch(t.t){case"d":case"n":var n=t.t=="d"?ir(Ve(t.v)):t.v;n==(n|0)&&n>=0&&n<65536?ne(e,2,Ou(r,a,n)):ne(e,3,Au(r,a,n));return;case"b":case"e":ne(e,5,Ig(r,a,t.v,t.t));return;case"s":case"str":ne(e,4,Fg(r,a,(t.v||"").slice(0,255)));return}ne(e,1,Qa(null,r,a))}function Ng(e,t,r,a){var n=Array.isArray(t),i=Ce(t["!ref"]||"A1"),s,f="",c=[];if(i.e.c>255||i.e.r>16383){if(a.WTF)throw new Error("Range "+(t["!ref"]||"A1")+" exceeds format limit A1:IV16384");i.e.c=Math.min(i.e.c,255),i.e.r=Math.min(i.e.c,16383),s=ke(i)}for(var o=i.s.r;o<=i.e.r;++o){f=Ke(o);for(var l=i.s.c;l<=i.e.c;++l){o===i.s.r&&(c[l]=He(l)),s=c[l]+f;var h=n?(t[o]||[])[l]:t[s];h&&Rg(e,h,o,l)}}}function Pg(e,t){for(var r=t||{},a=Or(),n=0,i=0;i<e.SheetNames.length;++i)e.SheetNames[i]==r.sheet&&(n=i);if(n==0&&r.sheet&&e.SheetNames[0]!=r.sheet)throw new Error("Sheet not found: "+r.sheet);return ne(a,r.biff==4?1033:r.biff==3?521:9,bi(e,16,r)),Ng(a,e.Sheets[e.SheetNames[n]],n,r),ne(a,10),a.end()}function Dg(e,t,r){ne(e,49,Oh({sz:12,name:"Arial"},r))}function Lg(e,t,r){t&&[[5,8],[23,26],[41,44],[50,392]].forEach(function(a){for(var n=a[0];n<=a[1];++n)t[n]!=null&&ne(e,1054,Dh(n,t[n],r))})}function bg(e,t){var r=G(19);r.write_shift(4,2151),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,1),r.write_shift(4,0),ne(e,2151,r),r=G(39),r.write_shift(4,2152),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,0),r.write_shift(4,0),r.write_shift(2,1),r.write_shift(4,4),r.write_shift(2,0),mc(Ce(t["!ref"]||"A1"),r),r.write_shift(4,4),ne(e,2152,r)}function Mg(e,t){for(var r=0;r<16;++r)ne(e,224,Xs({numFmtId:0,style:!0},0,t));t.cellXfs.forEach(function(a){ne(e,224,Xs(a,0,t))})}function Bg(e,t){for(var r=0;r<t["!links"].length;++r){var a=t["!links"][r];ne(e,440,cu(a)),a[1].Tooltip&&ne(e,2048,lu(a))}delete t["!links"]}function Ug(e,t){if(t){var r=0;t.forEach(function(a,n){++r<=256&&a&&ne(e,125,mu(Dn(n,a),n))})}}function Wg(e,t,r,a,n){var i=16+It(n.cellXfs,t,n);if(t.v==null&&!t.bf){ne(e,513,Vt(r,a,i));return}if(t.bf)ne(e,6,dv(t,r,a,n,i));else switch(t.t){case"d":case"n":var s=t.t=="d"?ir(Ve(t.v)):t.v;ne(e,515,$h(r,a,s,i));break;case"b":case"e":ne(e,517,Gh(r,a,t.v,i,n,t.t));break;case"s":case"str":if(n.bookSST){var f=Gi(n.Strings,t.v,n.revStrings);ne(e,253,Fh(r,a,f,i))}else ne(e,516,Nh(r,a,(t.v||"").slice(0,255),i,n));break;default:ne(e,513,Vt(r,a,i))}}function Hg(e,t,r){var a=Or(),n=r.SheetNames[e],i=r.Sheets[n]||{},s=(r||{}).Workbook||{},f=(s.Sheets||[])[e]||{},c=Array.isArray(i),o=t.biff==8,l,h="",d=[],v=Ce(i["!ref"]||"A1"),p=o?65536:16384;if(v.e.c>255||v.e.r>=p){if(t.WTF)throw new Error("Range "+(i["!ref"]||"A1")+" exceeds format limit A1:IV16384");v.e.c=Math.min(v.e.c,255),v.e.r=Math.min(v.e.c,p-1)}ne(a,2057,bi(r,16,t)),ne(a,13,Xr(1)),ne(a,12,Xr(100)),ne(a,15,xr(!0)),ne(a,17,xr(!1)),ne(a,16,Wt(.001)),ne(a,95,xr(!0)),ne(a,42,xr(!1)),ne(a,43,xr(!1)),ne(a,130,Xr(1)),ne(a,128,Xh()),ne(a,131,xr(!1)),ne(a,132,xr(!1)),o&&Ug(a,i["!cols"]),ne(a,512,bh(v,t)),o&&(i["!links"]=[]);for(var u=v.s.r;u<=v.e.r;++u){h=Ke(u);for(var m=v.s.c;m<=v.e.c;++m){u===v.s.r&&(d[m]=He(m)),l=d[m]+h;var k=c?(i[u]||[])[m]:i[l];k&&(Wg(a,k,u,m,t),o&&k.l&&i["!links"].push([l,k.l]))}}var A=f.CodeName||f.name||n;return o&&ne(a,574,xh((s.Views||[])[0])),o&&(i["!merges"]||[]).length&&ne(a,229,tu(i["!merges"])),o&&Bg(a,i),ne(a,442,uc(A)),o&&bg(a,i),ne(a,10),a.end()}function Vg(e,t,r){var a=Or(),n=(e||{}).Workbook||{},i=n.Sheets||[],s=n.WBProps||{},f=r.biff==8,c=r.biff==5;if(ne(a,2057,bi(e,5,r)),r.bookType=="xla"&&ne(a,135),ne(a,225,f?Xr(1200):null),ne(a,193,G1(2)),c&&ne(a,191),c&&ne(a,192),ne(a,226),ne(a,92,uh("SheetJS",r)),ne(a,66,Xr(f?1200:1252)),f&&ne(a,353,Xr(0)),f&&ne(a,448),ne(a,317,wu(e.SheetNames.length)),f&&e.vbaraw&&ne(a,211),f&&e.vbaraw){var o=s.CodeName||"ThisWorkbook";ne(a,442,uc(o))}ne(a,156,Xr(17)),ne(a,25,xr(!1)),ne(a,18,xr(!1)),ne(a,19,Xr(0)),f&&ne(a,431,xr(!1)),f&&ne(a,444,Xr(0)),ne(a,61,Sh()),ne(a,64,xr(!1)),ne(a,141,Xr(0)),ne(a,34,xr(Tm(e)=="true")),ne(a,14,xr(!0)),f&&ne(a,439,xr(!1)),ne(a,218,Xr(0)),Dg(a,e,r),Lg(a,e.SSF,r),Mg(a,r),f&&ne(a,352,xr(!1));var l=a.end(),h=Or();f&&ne(h,140,uu()),f&&r.Strings&&Og(h,252,gh(r.Strings)),ne(h,10);var d=h.end(),v=Or(),p=0,u=0;for(u=0;u<e.SheetNames.length;++u)p+=(f?12:11)+(f?2:1)*e.SheetNames[u].length;var m=l.length+p+d.length;for(u=0;u<e.SheetNames.length;++u){var k=i[u]||{};ne(v,133,ph({pos:m,hs:k.Hidden||0,dt:0,name:e.SheetNames[u]},r)),m+=t[u].length}var A=v.end();if(p!=A.length)throw new Error("BS8 "+p+" != "+A.length);var _=[];return l.length&&_.push(l),A.length&&_.push(A),d.length&&_.push(d),cr(_)}function Xg(e,t){var r=t||{},a=[];e&&!e.SSF&&(e.SSF=Be(ve)),e&&e.SSF&&(la(),$a(e.SSF),r.revssf=On(e.SSF),r.revssf[e.SSF[65535]]=0,r.ssf=e.SSF),r.Strings=[],r.Strings.Count=0,r.Strings.Unique=0,ji(r),r.cellXfs=[],It(r.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});for(var n=0;n<e.SheetNames.length;++n)a[a.length]=Hg(n,r,e);return a.unshift(Vg(e,a,r)),cr(a)}function so(e,t){for(var r=0;r<=e.SheetNames.length;++r){var a=e.Sheets[e.SheetNames[r]];if(!(!a||!a["!ref"])){var n=Ar(a["!ref"]);n.e.c>255&&typeof console<"u"&&console.error&&console.error("Worksheet '"+e.SheetNames[r]+"' extends beyond column IV (255).  Data may be lost.")}}var i=t||{};switch(i.biff||2){case 8:case 5:return Xg(e,t);case 4:case 3:case 2:return Pg(e,t)}throw new Error("invalid type "+i.bookType+" for BIFF")}function nf(e,t){var r=t||{},a=r.dense?[]:{};e=e.replace(/<!--.*?-->/g,"");var n=e.match(/<table/i);if(!n)throw new Error("Invalid HTML: could not find <table>");var i=e.match(/<\/table/i),s=n.index,f=i&&i.index||e.length,c=Ol(e.slice(s,f),/(:?<tr[^>]*>)/i,"<tr>"),o=-1,l=0,h=0,d=0,v={s:{r:1e7,c:1e7},e:{r:0,c:0}},p=[];for(s=0;s<c.length;++s){var u=c[s].trim(),m=u.slice(0,3).toLowerCase();if(m=="<tr"){if(++o,r.sheetRows&&r.sheetRows<=o){--o;break}l=0;continue}if(!(m!="<td"&&m!="<th")){var k=u.split(/<\/t[dh]>/i);for(f=0;f<k.length;++f){var A=k[f].trim();if(A.match(/<t[dh]/i)){for(var _=A,I=0;_.charAt(0)=="<"&&(I=_.indexOf(">"))>-1;)_=_.slice(I+1);for(var D=0;D<p.length;++D){var P=p[D];P.s.c==l&&P.s.r<o&&o<=P.e.r&&(l=P.e.c+1,D=-1)}var x=ge(A.slice(0,A.indexOf(">")));d=x.colspan?+x.colspan:1,((h=+x.rowspan)>1||d>1)&&p.push({s:{r:o,c:l},e:{r:o+(h||1)-1,c:l+d-1}});var M=x.t||x["data-t"]||"";if(!_.length){l+=d;continue}if(_=Pf(_),v.s.r>o&&(v.s.r=o),v.e.r<o&&(v.e.r=o),v.s.c>l&&(v.s.c=l),v.e.c<l&&(v.e.c=l),!_.length){l+=d;continue}var R={t:"s",v:_};r.raw||!_.trim().length||M=="s"||(_==="TRUE"?R={t:"b",v:!0}:_==="FALSE"?R={t:"b",v:!1}:isNaN(jr(_))?isNaN(fa(_).getDate())||(R={t:"d",v:Ve(_)},r.cellDates||(R={t:"n",v:ir(R.v)}),R.z=r.dateNF||ve[14]):R={t:"n",v:jr(_)}),r.dense?(a[o]||(a[o]=[]),a[o][l]=R):a[me({r:o,c:l})]=R,l+=d}}}}return a["!ref"]=ke(v),p.length&&(a["!merges"]=p),a}function fo(e,t,r,a){for(var n=e["!merges"]||[],i=[],s=t.s.c;s<=t.e.c;++s){for(var f=0,c=0,o=0;o<n.length;++o)if(!(n[o].s.r>r||n[o].s.c>s)&&!(n[o].e.r<r||n[o].e.c<s)){if(n[o].s.r<r||n[o].s.c<s){f=-1;break}f=n[o].e.r-n[o].s.r+1,c=n[o].e.c-n[o].s.c+1;break}if(!(f<0)){var l=me({r,c:s}),h=a.dense?(e[r]||[])[s]:e[l],d=h&&h.v!=null&&(h.h||wi(h.w||(nt(h),h.w)||""))||"",v={};f>1&&(v.rowspan=f),c>1&&(v.colspan=c),a.editable?d='<span contenteditable="true">'+d+"</span>":h&&(v["data-t"]=h&&h.t||"z",h.v!=null&&(v["data-v"]=h.v),h.z!=null&&(v["data-z"]=h.z),h.l&&(h.l.Target||"#").charAt(0)!="#"&&(d='<a href="'+h.l.Target+'">'+d+"</a>")),v.id=(a.id||"sjs")+"-"+l,i.push(ae("td",d,v))}}var p="<tr>";return p+i.join("")+"</tr>"}var co='<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',oo="</body></html>";function Gg(e,t){var r=e.match(/<table[\s\S]*?>[\s\S]*?<\/table>/gi);if(!r||r.length==0)throw new Error("Invalid HTML: could not find <table>");if(r.length==1)return Ot(nf(r[0],t),t);var a=es();return r.forEach(function(n,i){rs(a,nf(n,t),"Sheet"+(i+1))}),a}function lo(e,t,r){var a=[];return a.join("")+"<table"+(r&&r.id?' id="'+r.id+'"':"")+">"}function ho(e,t){var r=t||{},a=r.header!=null?r.header:co,n=r.footer!=null?r.footer:oo,i=[a],s=Ar(e["!ref"]);r.dense=Array.isArray(e),i.push(lo(e,s,r));for(var f=s.s.r;f<=s.e.r;++f)i.push(fo(e,s,f,r));return i.push("</table>"+n),i.join("")}function uo(e,t,r){var a=r||{},n=0,i=0;if(a.origin!=null)if(typeof a.origin=="number")n=a.origin;else{var s=typeof a.origin=="string"?ze(a.origin):a.origin;n=s.r,i=s.c}var f=t.getElementsByTagName("tr"),c=Math.min(a.sheetRows||1e7,f.length),o={s:{r:0,c:0},e:{r:n,c:i}};if(e["!ref"]){var l=Ar(e["!ref"]);o.s.r=Math.min(o.s.r,l.s.r),o.s.c=Math.min(o.s.c,l.s.c),o.e.r=Math.max(o.e.r,l.e.r),o.e.c=Math.max(o.e.c,l.e.c),n==-1&&(o.e.r=n=l.e.r+1)}var h=[],d=0,v=e["!rows"]||(e["!rows"]=[]),p=0,u=0,m=0,k=0,A=0,_=0;for(e["!cols"]||(e["!cols"]=[]);p<f.length&&u<c;++p){var I=f[p];if(sf(I)){if(a.display)continue;v[u]={hidden:!0}}var D=I.children;for(m=k=0;m<D.length;++m){var P=D[m];if(!(a.display&&sf(P))){var x=P.hasAttribute("data-v")?P.getAttribute("data-v"):P.hasAttribute("v")?P.getAttribute("v"):Pf(P.innerHTML),M=P.getAttribute("data-z")||P.getAttribute("z");for(d=0;d<h.length;++d){var R=h[d];R.s.c==k+i&&R.s.r<u+n&&u+n<=R.e.r&&(k=R.e.c+1-i,d=-1)}_=+P.getAttribute("colspan")||1,((A=+P.getAttribute("rowspan")||1)>1||_>1)&&h.push({s:{r:u+n,c:k+i},e:{r:u+n+(A||1)-1,c:k+i+(_||1)-1}});var z={t:"s",v:x},X=P.getAttribute("data-t")||P.getAttribute("t")||"";x!=null&&(x.length==0?z.t=X||"z":a.raw||x.trim().length==0||X=="s"||(x==="TRUE"?z={t:"b",v:!0}:x==="FALSE"?z={t:"b",v:!1}:isNaN(jr(x))?isNaN(fa(x).getDate())||(z={t:"d",v:Ve(x)},a.cellDates||(z={t:"n",v:ir(z.v)}),z.z=a.dateNF||ve[14]):z={t:"n",v:jr(x)})),z.z===void 0&&M!=null&&(z.z=M);var b="",J=P.getElementsByTagName("A");if(J&&J.length)for(var le=0;le<J.length&&!(J[le].hasAttribute("href")&&(b=J[le].getAttribute("href"),b.charAt(0)!="#"));++le);b&&b.charAt(0)!="#"&&(z.l={Target:b}),a.dense?(e[u+n]||(e[u+n]=[]),e[u+n][k+i]=z):e[me({c:k+i,r:u+n})]=z,o.e.c<k+i&&(o.e.c=k+i),k+=_}}++u}return h.length&&(e["!merges"]=(e["!merges"]||[]).concat(h)),o.e.r=Math.max(o.e.r,u-1+n),e["!ref"]=ke(o),u>=c&&(e["!fullref"]=ke((o.e.r=f.length-p+u-1+n,o))),e}function vo(e,t){var r=t||{},a=r.dense?[]:{};return uo(a,e,t)}function zg(e,t){return Ot(vo(e,t),t)}function sf(e){var t="",r=$g(e);return r&&(t=r(e).getPropertyValue("display")),t||(t=e.style&&e.style.display),t==="none"}function $g(e){return e.ownerDocument.defaultView&&typeof e.ownerDocument.defaultView.getComputedStyle=="function"?e.ownerDocument.defaultView.getComputedStyle:typeof getComputedStyle=="function"?getComputedStyle:null}function Kg(e){var t=e.replace(/[\t\r\n]/g," ").trim().replace(/ +/g," ").replace(/<text:s\/>/g," ").replace(/<text:s text:c="(\d+)"\/>/g,function(a,n){return Array(parseInt(n,10)+1).join(" ")}).replace(/<text:tab[^>]*\/>/g,"	").replace(/<text:line-break\/>/g,`
`),r=Fe(t.replace(/<[^>]*>/g,""));return[r]}var ff={day:["d","dd"],month:["m","mm"],year:["y","yy"],hours:["h","hh"],minutes:["m","mm"],seconds:["s","ss"],"am-pm":["A/P","AM/PM"],"day-of-week":["ddd","dddd"],era:["e","ee"],quarter:["\\Qm",'m\\"th quarter"']};function po(e,t){var r=t||{},a=ki(e),n=[],i,s,f={name:""},c="",o=0,l,h,d={},v=[],p=r.dense?[]:{},u,m,k={value:""},A="",_=0,I=[],D=-1,P=-1,x={s:{r:1e6,c:1e7},e:{r:0,c:0}},M=0,R={},z=[],X={},b=0,J=0,le=[],ie=1,ue=1,ce=[],Le={Names:[]},V={},pe=["",""],_e=[],O={},L="",F=0,N=!1,Y=!1,re=0;for(ba.lastIndex=0,a=a.replace(/<!--([\s\S]*?)-->/mg,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");u=ba.exec(a);)switch(u[3]=u[3].replace(/_.*$/,"")){case"table":case"工作表":u[1]==="/"?(x.e.c>=x.s.c&&x.e.r>=x.s.r?p["!ref"]=ke(x):p["!ref"]="A1:A1",r.sheetRows>0&&r.sheetRows<=x.e.r&&(p["!fullref"]=p["!ref"],x.e.r=r.sheetRows-1,p["!ref"]=ke(x)),z.length&&(p["!merges"]=z),le.length&&(p["!rows"]=le),l.name=l.名称||l.name,typeof JSON<"u"&&JSON.stringify(l),v.push(l.name),d[l.name]=p,Y=!1):u[0].charAt(u[0].length-2)!=="/"&&(l=ge(u[0],!1),D=P=-1,x.s.r=x.s.c=1e7,x.e.r=x.e.c=0,p=r.dense?[]:{},z=[],le=[],Y=!0);break;case"table-row-group":u[1]==="/"?--M:++M;break;case"table-row":case"行":if(u[1]==="/"){D+=ie,ie=1;break}if(h=ge(u[0],!1),h.行号?D=h.行号-1:D==-1&&(D=0),ie=+h["number-rows-repeated"]||1,ie<10)for(re=0;re<ie;++re)M>0&&(le[D+re]={level:M});P=-1;break;case"covered-table-cell":u[1]!=="/"&&++P,r.sheetStubs&&(r.dense?(p[D]||(p[D]=[]),p[D][P]={t:"z"}):p[me({r:D,c:P})]={t:"z"}),A="",I=[];break;case"table-cell":case"数据":if(u[0].charAt(u[0].length-2)==="/")++P,k=ge(u[0],!1),ue=parseInt(k["number-columns-repeated"]||"1",10),m={t:"z",v:null},k.formula&&r.cellFormula!=!1&&(m.f=rf(Fe(k.formula))),(k.数据类型||k["value-type"])=="string"&&(m.t="s",m.v=Fe(k["string-value"]||""),r.dense?(p[D]||(p[D]=[]),p[D][P]=m):p[me({r:D,c:P})]=m),P+=ue-1;else if(u[1]!=="/"){++P,A="",_=0,I=[],ue=1;var te=ie?D+ie-1:D;if(P>x.e.c&&(x.e.c=P),P<x.s.c&&(x.s.c=P),D<x.s.r&&(x.s.r=D),te>x.e.r&&(x.e.r=te),k=ge(u[0],!1),_e=[],O={},m={t:k.数据类型||k["value-type"],v:null},r.cellFormula)if(k.formula&&(k.formula=Fe(k.formula)),k["number-matrix-columns-spanned"]&&k["number-matrix-rows-spanned"]&&(b=parseInt(k["number-matrix-rows-spanned"],10)||0,J=parseInt(k["number-matrix-columns-spanned"],10)||0,X={s:{r:D,c:P},e:{r:D+b-1,c:P+J-1}},m.F=ke(X),ce.push([X,m.F])),k.formula)m.f=rf(k.formula);else for(re=0;re<ce.length;++re)D>=ce[re][0].s.r&&D<=ce[re][0].e.r&&P>=ce[re][0].s.c&&P<=ce[re][0].e.c&&(m.F=ce[re][1]);switch((k["number-columns-spanned"]||k["number-rows-spanned"])&&(b=parseInt(k["number-rows-spanned"],10)||0,J=parseInt(k["number-columns-spanned"],10)||0,X={s:{r:D,c:P},e:{r:D+b-1,c:P+J-1}},z.push(X)),k["number-columns-repeated"]&&(ue=parseInt(k["number-columns-repeated"],10)),m.t){case"boolean":m.t="b",m.v=We(k["boolean-value"]);break;case"float":m.t="n",m.v=parseFloat(k.value);break;case"percentage":m.t="n",m.v=parseFloat(k.value);break;case"currency":m.t="n",m.v=parseFloat(k.value);break;case"date":m.t="d",m.v=Ve(k["date-value"]),r.cellDates||(m.t="n",m.v=ir(m.v)),m.z="m/d/yy";break;case"time":m.t="n",m.v=xl(k["time-value"])/86400,r.cellDates&&(m.t="d",m.v=In(m.v)),m.z="HH:MM:SS";break;case"number":m.t="n",m.v=parseFloat(k.数据数值);break;default:if(m.t==="string"||m.t==="text"||!m.t)m.t="s",k["string-value"]!=null&&(A=Fe(k["string-value"]),I=[]);else throw new Error("Unsupported value type "+m.t)}}else{if(N=!1,m.t==="s"&&(m.v=A||"",I.length&&(m.R=I),N=_==0),V.Target&&(m.l=V),_e.length>0&&(m.c=_e,_e=[]),A&&r.cellText!==!1&&(m.w=A),N&&(m.t="z",delete m.v),(!N||r.sheetStubs)&&!(r.sheetRows&&r.sheetRows<=D))for(var Q=0;Q<ie;++Q){if(ue=parseInt(k["number-columns-repeated"]||"1",10),r.dense)for(p[D+Q]||(p[D+Q]=[]),p[D+Q][P]=Q==0?m:Be(m);--ue>0;)p[D+Q][P+ue]=Be(m);else for(p[me({r:D+Q,c:P})]=m;--ue>0;)p[me({r:D+Q,c:P+ue})]=Be(m);x.e.c<=P&&(x.e.c=P)}ue=parseInt(k["number-columns-repeated"]||"1",10),P+=ue-1,ue=0,m={},A="",I=[]}V={};break;case"document":case"document-content":case"电子表格文档":case"spreadsheet":case"主体":case"scripts":case"styles":case"font-face-decls":case"master-styles":if(u[1]==="/"){if((i=n.pop())[0]!==u[3])throw"Bad state: "+i}else u[0].charAt(u[0].length-2)!=="/"&&n.push([u[3],!0]);break;case"annotation":if(u[1]==="/"){if((i=n.pop())[0]!==u[3])throw"Bad state: "+i;O.t=A,I.length&&(O.R=I),O.a=L,_e.push(O)}else u[0].charAt(u[0].length-2)!=="/"&&n.push([u[3],!1]);L="",F=0,A="",_=0,I=[];break;case"creator":u[1]==="/"?L=a.slice(F,u.index):F=u.index+u[0].length;break;case"meta":case"元数据":case"settings":case"config-item-set":case"config-item-map-indexed":case"config-item-map-entry":case"config-item-map-named":case"shapes":case"frame":case"text-box":case"image":case"data-pilot-tables":case"list-style":case"form":case"dde-links":case"event-listeners":case"chart":if(u[1]==="/"){if((i=n.pop())[0]!==u[3])throw"Bad state: "+i}else u[0].charAt(u[0].length-2)!=="/"&&n.push([u[3],!1]);A="",_=0,I=[];break;case"scientific-number":break;case"currency-symbol":break;case"currency-style":break;case"number-style":case"percentage-style":case"date-style":case"time-style":if(u[1]==="/"){if(R[f.name]=c,(i=n.pop())[0]!==u[3])throw"Bad state: "+i}else u[0].charAt(u[0].length-2)!=="/"&&(c="",f=ge(u[0],!1),n.push([u[3],!0]));break;case"script":break;case"libraries":break;case"automatic-styles":break;case"default-style":case"page-layout":break;case"style":break;case"map":break;case"font-face":break;case"paragraph-properties":break;case"table-properties":break;case"table-column-properties":break;case"table-row-properties":break;case"table-cell-properties":break;case"number":switch(n[n.length-1][0]){case"time-style":case"date-style":s=ge(u[0],!1),c+=ff[u[3]][s.style==="long"?1:0];break}break;case"fraction":break;case"day":case"month":case"year":case"era":case"day-of-week":case"week-of-year":case"quarter":case"hours":case"minutes":case"seconds":case"am-pm":switch(n[n.length-1][0]){case"time-style":case"date-style":s=ge(u[0],!1),c+=ff[u[3]][s.style==="long"?1:0];break}break;case"boolean-style":break;case"boolean":break;case"text-style":break;case"text":if(u[0].slice(-2)==="/>")break;if(u[1]==="/")switch(n[n.length-1][0]){case"number-style":case"date-style":case"time-style":c+=a.slice(o,u.index);break}else o=u.index+u[0].length;break;case"named-range":s=ge(u[0],!1),pe=$n(s["cell-range-address"]);var Z={Name:s.name,Ref:pe[0]+"!"+pe[1]};Y&&(Z.Sheet=v.length),Le.Names.push(Z);break;case"text-content":break;case"text-properties":break;case"embedded-text":break;case"body":case"电子表格":break;case"forms":break;case"table-column":break;case"table-header-rows":break;case"table-rows":break;case"table-column-group":break;case"table-header-columns":break;case"table-columns":break;case"null-date":break;case"graphic-properties":break;case"calculation-settings":break;case"named-expressions":break;case"label-range":break;case"label-ranges":break;case"named-expression":break;case"sort":break;case"sort-by":break;case"sort-groups":break;case"tab":break;case"line-break":break;case"span":break;case"p":case"文本串":if(["master-styles"].indexOf(n[n.length-1][0])>-1)break;if(u[1]==="/"&&(!k||!k["string-value"])){var Ee=Kg(a.slice(_,u.index));A=(A.length>0?A+`
`:"")+Ee[0]}else ge(u[0],!1),_=u.index+u[0].length;break;case"s":break;case"database-range":if(u[1]==="/")break;try{pe=$n(ge(u[0])["target-range-address"]),d[pe[0]]["!autofilter"]={ref:pe[1]}}catch{}break;case"date":break;case"object":break;case"title":case"标题":break;case"desc":break;case"binary-data":break;case"table-source":break;case"scenario":break;case"iteration":break;case"content-validations":break;case"content-validation":break;case"help-message":break;case"error-message":break;case"database-ranges":break;case"filter":break;case"filter-and":break;case"filter-or":break;case"filter-condition":break;case"list-level-style-bullet":break;case"list-level-style-number":break;case"list-level-properties":break;case"sender-firstname":case"sender-lastname":case"sender-initials":case"sender-title":case"sender-position":case"sender-email":case"sender-phone-private":case"sender-fax":case"sender-company":case"sender-phone-work":case"sender-street":case"sender-city":case"sender-postal-code":case"sender-country":case"sender-state-or-province":case"author-name":case"author-initials":case"chapter":case"file-name":case"template-name":case"sheet-name":break;case"event-listener":break;case"initial-creator":case"creation-date":case"print-date":case"generator":case"document-statistic":case"user-defined":case"editing-duration":case"editing-cycles":break;case"config-item":break;case"page-number":break;case"page-count":break;case"time":break;case"cell-range-source":break;case"detective":break;case"operation":break;case"highlighted-range":break;case"data-pilot-table":case"source-cell-range":case"source-service":case"data-pilot-field":case"data-pilot-level":case"data-pilot-subtotals":case"data-pilot-subtotal":case"data-pilot-members":case"data-pilot-member":case"data-pilot-display-info":case"data-pilot-sort-info":case"data-pilot-layout-info":case"data-pilot-field-reference":case"data-pilot-groups":case"data-pilot-group":case"data-pilot-group-member":break;case"rect":break;case"dde-connection-decls":case"dde-connection-decl":case"dde-link":case"dde-source":break;case"properties":break;case"property":break;case"a":if(u[1]!=="/"){if(V=ge(u[0],!1),!V.href)break;V.Target=Fe(V.href),delete V.href,V.Target.charAt(0)=="#"&&V.Target.indexOf(".")>-1?(pe=$n(V.Target.slice(1)),V.Target="#"+pe[0]+"!"+pe[1]):V.Target.match(/^\.\.[\\\/]/)&&(V.Target=V.Target.slice(3))}break;case"table-protection":break;case"data-pilot-grand-total":break;case"office-document-common-attrs":break;default:switch(u[2]){case"dc:":case"calcext:":case"loext:":case"ooo:":case"chartooo:":case"draw:":case"style:":case"chart:":case"form:":case"uof:":case"表:":case"字:":break;default:if(r.WTF)throw new Error(u)}}var C={Sheets:d,SheetNames:v,Workbook:Le};return r.bookSheets&&delete C.Sheets,C}function cf(e,t){t=t||{},Vr(e,"META-INF/manifest.xml")&&x1(rr(e,"META-INF/manifest.xml"),t);var r=Nr(e,"content.xml");if(!r)throw new Error("Missing content.xml in ODS / UOF file");var a=po(be(r),t);return Vr(e,"meta.xml")&&(a.Props=Qf(rr(e,"meta.xml"))),a}function of(e,t){return po(e,t)}var Yg=function(){var e=["<office:master-styles>",'<style:master-page style:name="mp1" style:page-layout-name="mp1">',"<style:header/>",'<style:header-left style:display="false"/>',"<style:footer/>",'<style:footer-left style:display="false"/>',"</style:master-page>","</office:master-styles>"].join(""),t="<office:document-styles "+La({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","office:version":"1.2"})+">"+e+"</office:document-styles>";return function(){return Qe+t}}(),lf=function(){var e=function(i){return De(i).replace(/  +/g,function(s){return'<text:s text:c="'+s.length+'"/>'}).replace(/\t/g,"<text:tab/>").replace(/\n/g,"</text:p><text:p>").replace(/^ /,"<text:s/>").replace(/ $/,"<text:s/>")},t=`          <table:table-cell />
`,r=`          <table:covered-table-cell/>
`,a=function(i,s,f){var c=[];c.push('      <table:table table:name="'+De(s.SheetNames[f])+`" table:style-name="ta1">
`);var o=0,l=0,h=Ar(i["!ref"]||"A1"),d=i["!merges"]||[],v=0,p=Array.isArray(i);if(i["!cols"])for(l=0;l<=h.e.c;++l)c.push("        <table:table-column"+(i["!cols"][l]?' table:style-name="co'+i["!cols"][l].ods+'"':"")+`></table:table-column>
`);var u="",m=i["!rows"]||[];for(o=0;o<h.s.r;++o)u=m[o]?' table:style-name="ro'+m[o].ods+'"':"",c.push("        <table:table-row"+u+`></table:table-row>
`);for(;o<=h.e.r;++o){for(u=m[o]?' table:style-name="ro'+m[o].ods+'"':"",c.push("        <table:table-row"+u+`>
`),l=0;l<h.s.c;++l)c.push(t);for(;l<=h.e.c;++l){var k=!1,A={},_="";for(v=0;v!=d.length;++v)if(!(d[v].s.c>l)&&!(d[v].s.r>o)&&!(d[v].e.c<l)&&!(d[v].e.r<o)){(d[v].s.c!=l||d[v].s.r!=o)&&(k=!0),A["table:number-columns-spanned"]=d[v].e.c-d[v].s.c+1,A["table:number-rows-spanned"]=d[v].e.r-d[v].s.r+1;break}if(k){c.push(r);continue}var I=me({r:o,c:l}),D=p?(i[o]||[])[l]:i[I];if(D&&D.f&&(A["table:formula"]=De(wv(D.f)),D.F&&D.F.slice(0,I.length)==I)){var P=Ar(D.F);A["table:number-matrix-columns-spanned"]=P.e.c-P.s.c+1,A["table:number-matrix-rows-spanned"]=P.e.r-P.s.r+1}if(!D){c.push(t);continue}switch(D.t){case"b":_=D.v?"TRUE":"FALSE",A["office:value-type"]="boolean",A["office:boolean-value"]=D.v?"true":"false";break;case"n":_=D.w||String(D.v||0),A["office:value-type"]="float",A["office:value"]=D.v||0;break;case"s":case"str":_=D.v==null?"":D.v,A["office:value-type"]="string";break;case"d":_=D.w||Ve(D.v).toISOString(),A["office:value-type"]="date",A["office:date-value"]=Ve(D.v).toISOString(),A["table:style-name"]="ce1";break;default:c.push(t);continue}var x=e(_);if(D.l&&D.l.Target){var M=D.l.Target;M=M.charAt(0)=="#"?"#"+kv(M.slice(1)):M,M.charAt(0)!="#"&&!M.match(/^\w+:/)&&(M="../"+M),x=ae("text:a",x,{"xlink:href":M.replace(/&/g,"&amp;")})}c.push("          "+ae("table:table-cell",ae("text:p",x,{}),A)+`
`)}c.push(`        </table:table-row>
`)}return c.push(`      </table:table>
`),c.join("")},n=function(i,s){i.push(` <office:automatic-styles>
`),i.push(`  <number:date-style style:name="N37" number:automatic-order="true">
`),i.push(`   <number:month number:style="long"/>
`),i.push(`   <number:text>/</number:text>
`),i.push(`   <number:day number:style="long"/>
`),i.push(`   <number:text>/</number:text>
`),i.push(`   <number:year/>
`),i.push(`  </number:date-style>
`);var f=0;s.SheetNames.map(function(o){return s.Sheets[o]}).forEach(function(o){if(o&&o["!cols"]){for(var l=0;l<o["!cols"].length;++l)if(o["!cols"][l]){var h=o["!cols"][l];if(h.width==null&&h.wpx==null&&h.wch==null)continue;yt(h),h.ods=f;var d=o["!cols"][l].wpx+"px";i.push('  <style:style style:name="co'+f+`" style:family="table-column">
`),i.push('   <style:table-column-properties fo:break-before="auto" style:column-width="'+d+`"/>
`),i.push(`  </style:style>
`),++f}}});var c=0;s.SheetNames.map(function(o){return s.Sheets[o]}).forEach(function(o){if(o&&o["!rows"]){for(var l=0;l<o["!rows"].length;++l)if(o["!rows"][l]){o["!rows"][l].ods=c;var h=o["!rows"][l].hpx+"px";i.push('  <style:style style:name="ro'+c+`" style:family="table-row">
`),i.push('   <style:table-row-properties fo:break-before="auto" style:row-height="'+h+`"/>
`),i.push(`  </style:style>
`),++c}}}),i.push(`  <style:style style:name="ta1" style:family="table" style:master-page-name="mp1">
`),i.push(`   <style:table-properties table:display="true" style:writing-mode="lr-tb"/>
`),i.push(`  </style:style>
`),i.push(`  <style:style style:name="ce1" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N37"/>
`),i.push(` </office:automatic-styles>
`)};return function(s,f){var c=[Qe],o=La({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:meta":"urn:oasis:names:tc:opendocument:xmlns:meta:1.0","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:presentation":"urn:oasis:names:tc:opendocument:xmlns:presentation:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:chart":"urn:oasis:names:tc:opendocument:xmlns:chart:1.0","xmlns:dr3d":"urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0","xmlns:math":"http://www.w3.org/1998/Math/MathML","xmlns:form":"urn:oasis:names:tc:opendocument:xmlns:form:1.0","xmlns:script":"urn:oasis:names:tc:opendocument:xmlns:script:1.0","xmlns:ooo":"http://openoffice.org/2004/office","xmlns:ooow":"http://openoffice.org/2004/writer","xmlns:oooc":"http://openoffice.org/2004/calc","xmlns:dom":"http://www.w3.org/2001/xml-events","xmlns:xforms":"http://www.w3.org/2002/xforms","xmlns:xsd":"http://www.w3.org/2001/XMLSchema","xmlns:xsi":"http://www.w3.org/2001/XMLSchema-instance","xmlns:sheet":"urn:oasis:names:tc:opendocument:sh33tjs:1.0","xmlns:rpt":"http://openoffice.org/2005/report","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","xmlns:xhtml":"http://www.w3.org/1999/xhtml","xmlns:grddl":"http://www.w3.org/2003/g/data-view#","xmlns:tableooo":"http://openoffice.org/2009/table","xmlns:drawooo":"http://openoffice.org/2010/draw","xmlns:calcext":"urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0","xmlns:loext":"urn:org:documentfoundation:names:experimental:office:xmlns:loext:1.0","xmlns:field":"urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0","xmlns:formx":"urn:openoffice:names:experimental:ooxml-odf-interop:xmlns:form:1.0","xmlns:css3t":"http://www.w3.org/TR/css3-text/","office:version":"1.2"}),l=La({"xmlns:config":"urn:oasis:names:tc:opendocument:xmlns:config:1.0","office:mimetype":"application/vnd.oasis.opendocument.spreadsheet"});f.bookType=="fods"?(c.push("<office:document"+o+l+`>
`),c.push(qf().replace(/office:document-meta/g,"office:meta"))):c.push("<office:document-content"+o+`>
`),n(c,s),c.push(`  <office:body>
`),c.push(`    <office:spreadsheet>
`);for(var h=0;h!=s.SheetNames.length;++h)c.push(a(s.Sheets[s.SheetNames[h]],s,h));return c.push(`    </office:spreadsheet>
`),c.push(`  </office:body>
`),f.bookType=="fods"?c.push("</office:document>"):c.push("</office:document-content>"),c.join("")}}();function mo(e,t){if(t.bookType=="fods")return lf(e,t);var r=mi(),a="",n=[],i=[];return a="mimetype",Te(r,a,"application/vnd.oasis.opendocument.spreadsheet"),a="content.xml",Te(r,a,lf(e,t)),n.push([a,"text/xml"]),i.push([a,"ContentFile"]),a="styles.xml",Te(r,a,Yg(e,t)),n.push([a,"text/xml"]),i.push([a,"StylesFile"]),a="meta.xml",Te(r,a,Qe+qf()),n.push([a,"text/xml"]),i.push([a,"MetadataFile"]),a="manifest.rdf",Te(r,a,O1(i)),n.push([a,"application/rdf+xml"]),a="META-INF/manifest.xml",Te(r,a,A1(n)),r}/*! sheetjs (C) 2013-present SheetJS -- http://sheetjs.com */function Xt(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function oi(e){return typeof TextDecoder<"u"?new TextDecoder().decode(e):be(Ct(e))}function jg(e){return typeof TextEncoder<"u"?new TextEncoder().encode(e):Rr(tt(e))}function Jg(e,t){e:for(var r=0;r<=e.length-t.length;++r){for(var a=0;a<t.length;++a)if(e[r+a]!=t[a])continue e;return!0}return!1}function At(e){var t=e.reduce(function(n,i){return n+i.length},0),r=new Uint8Array(t),a=0;return e.forEach(function(n){r.set(n,a),a+=n.length}),r}function hf(e){return e-=e>>1&1431655765,e=(e&858993459)+(e>>2&858993459),(e+(e>>4)&252645135)*16843009>>>24}function Zg(e,t){for(var r=(e[t+15]&127)<<7|e[t+14]>>1,a=e[t+14]&1,n=t+13;n>=t;--n)a=a*256+e[n];return(e[t+15]&128?-a:a)*Math.pow(10,r-6176)}function qg(e,t,r){var a=Math.floor(r==0?0:Math.LOG10E*Math.log(Math.abs(r)))+6176-20,n=r/Math.pow(10,a-6176);e[t+15]|=a>>7,e[t+14]|=(a&127)<<1;for(var i=0;n>=1;++i,n/=256)e[t+i]=n&255;e[t+15]|=r>=0?0:128}function Xa(e,t){var r=t?t[0]:0,a=e[r]&127;e:if(e[r++]>=128&&(a|=(e[r]&127)<<7,e[r++]<128||(a|=(e[r]&127)<<14,e[r++]<128)||(a|=(e[r]&127)<<21,e[r++]<128)||(a+=(e[r]&127)*Math.pow(2,28),++r,e[r++]<128)||(a+=(e[r]&127)*Math.pow(2,35),++r,e[r++]<128)||(a+=(e[r]&127)*Math.pow(2,42),++r,e[r++]<128)))break e;return t&&(t[0]=r),a}function Ne(e){var t=new Uint8Array(7);t[0]=e&127;var r=1;e:if(e>127){if(t[r-1]|=128,t[r]=e>>7&127,++r,e<=16383||(t[r-1]|=128,t[r]=e>>14&127,++r,e<=2097151)||(t[r-1]|=128,t[r]=e>>21&127,++r,e<=268435455)||(t[r-1]|=128,t[r]=e/256>>>21&127,++r,e<=34359738367)||(t[r-1]|=128,t[r]=e/65536>>>21&127,++r,e<=4398046511103))break e;t[r-1]|=128,t[r]=e/16777216>>>21&127,++r}return t.slice(0,r)}function Ye(e){var t=0,r=e[t]&127;e:if(e[t++]>=128){if(r|=(e[t]&127)<<7,e[t++]<128||(r|=(e[t]&127)<<14,e[t++]<128)||(r|=(e[t]&127)<<21,e[t++]<128))break e;r|=(e[t]&127)<<28}return r}function Re(e){for(var t=[],r=[0];r[0]<e.length;){var a=r[0],n=Xa(e,r),i=n&7;n=Math.floor(n/8);var s=0,f;if(n==0)break;switch(i){case 0:{for(var c=r[0];e[r[0]++]>=128;);f=e.slice(c,r[0])}break;case 5:s=4,f=e.slice(r[0],r[0]+s),r[0]+=s;break;case 1:s=8,f=e.slice(r[0],r[0]+s),r[0]+=s;break;case 2:s=Xa(e,r),f=e.slice(r[0],r[0]+s),r[0]+=s;break;case 3:case 4:default:throw new Error("PB Type ".concat(i," for Field ").concat(n," at offset ").concat(a))}var o={data:f,type:i};t[n]==null?t[n]=[o]:t[n].push(o)}return t}function lr(e){var t=[];return e.forEach(function(r,a){r.forEach(function(n){n.data&&(t.push(Ne(a*8+n.type)),n.type==2&&t.push(Ne(n.data.length)),t.push(n.data))})}),At(t)}function Ki(e,t){return(e==null?void 0:e.map(function(r){return t(r.data)}))||[]}function Wr(e){for(var t,r=[],a=[0];a[0]<e.length;){var n=Xa(e,a),i=Re(e.slice(a[0],a[0]+n));a[0]+=n;var s={id:Ye(i[1][0].data),messages:[]};i[2].forEach(function(f){var c=Re(f.data),o=Ye(c[3][0].data);s.messages.push({meta:c,data:e.slice(a[0],a[0]+o)}),a[0]+=o}),(t=i[3])!=null&&t[0]&&(s.merge=Ye(i[3][0].data)>>>0>0),r.push(s)}return r}function qt(e){var t=[];return e.forEach(function(r){var a=[];a[1]=[{data:Ne(r.id),type:0}],a[2]=[],r.merge!=null&&(a[3]=[{data:Ne(+!!r.merge),type:0}]);var n=[];r.messages.forEach(function(s){n.push(s.data),s.meta[3]=[{type:0,data:Ne(s.data.length)}],a[2].push({data:lr(s.meta),type:2})});var i=lr(a);t.push(Ne(i.length)),t.push(i),n.forEach(function(s){return t.push(s)})}),At(t)}function Qg(e,t){if(e!=0)throw new Error("Unexpected Snappy chunk type ".concat(e));for(var r=[0],a=Xa(t,r),n=[];r[0]<t.length;){var i=t[r[0]]&3;if(i==0){var s=t[r[0]++]>>2;if(s<60)++s;else{var f=s-59;s=t[r[0]],f>1&&(s|=t[r[0]+1]<<8),f>2&&(s|=t[r[0]+2]<<16),f>3&&(s|=t[r[0]+3]<<24),s>>>=0,s++,r[0]+=f}n.push(t.slice(r[0],r[0]+s)),r[0]+=s;continue}else{var c=0,o=0;if(i==1?(o=(t[r[0]]>>2&7)+4,c=(t[r[0]++]&224)<<3,c|=t[r[0]++]):(o=(t[r[0]++]>>2)+1,i==2?(c=t[r[0]]|t[r[0]+1]<<8,r[0]+=2):(c=(t[r[0]]|t[r[0]+1]<<8|t[r[0]+2]<<16|t[r[0]+3]<<24)>>>0,r[0]+=4)),n=[At(n)],c==0)throw new Error("Invalid offset 0");if(c>n[0].length)throw new Error("Invalid offset beyond length");if(o>=c)for(n.push(n[0].slice(-c)),o-=c;o>=n[n.length-1].length;)n.push(n[n.length-1]),o-=n[n.length-1].length;n.push(n[0].slice(-c,-c+o))}}var l=At(n);if(l.length!=a)throw new Error("Unexpected length: ".concat(l.length," != ").concat(a));return l}function Hr(e){for(var t=[],r=0;r<e.length;){var a=e[r++],n=e[r]|e[r+1]<<8|e[r+2]<<16;r+=3,t.push(Qg(a,e.slice(r,r+n))),r+=n}if(r!==e.length)throw new Error("data is not a valid framed stream!");return At(t)}function Qt(e){for(var t=[],r=0;r<e.length;){var a=Math.min(e.length-r,268435455),n=new Uint8Array(4);t.push(n);var i=Ne(a),s=i.length;t.push(i),a<=60?(s++,t.push(new Uint8Array([a-1<<2]))):a<=256?(s+=2,t.push(new Uint8Array([240,a-1&255]))):a<=65536?(s+=3,t.push(new Uint8Array([244,a-1&255,a-1>>8&255]))):a<=16777216?(s+=4,t.push(new Uint8Array([248,a-1&255,a-1>>8&255,a-1>>16&255]))):a<=4294967296&&(s+=5,t.push(new Uint8Array([252,a-1&255,a-1>>8&255,a-1>>16&255,a-1>>>24&255]))),t.push(e.slice(r,r+a)),s+=a,n[0]=0,n[1]=s&255,n[2]=s>>8&255,n[3]=s>>16&255,r+=a}return At(t)}function e_(e,t,r,a){var n=Xt(e),i=n.getUint32(4,!0),s=(a>1?12:8)+hf(i&(a>1?3470:398))*4,f=-1,c=-1,o=NaN,l=new Date(2001,0,1);i&512&&(f=n.getUint32(s,!0),s+=4),s+=hf(i&(a>1?12288:4096))*4,i&16&&(c=n.getUint32(s,!0),s+=4),i&32&&(o=n.getFloat64(s,!0),s+=8),i&64&&(l.setTime(l.getTime()+n.getFloat64(s,!0)*1e3),s+=8);var h;switch(e[2]){case 0:break;case 2:h={t:"n",v:o};break;case 3:h={t:"s",v:t[c]};break;case 5:h={t:"d",v:l};break;case 6:h={t:"b",v:o>0};break;case 7:h={t:"n",v:o/86400};break;case 8:h={t:"e",v:0};break;case 9:if(f>-1)h={t:"s",v:r[f]};else if(c>-1)h={t:"s",v:t[c]};else if(!isNaN(o))h={t:"n",v:o};else throw new Error("Unsupported cell type ".concat(e.slice(0,4)));break;default:throw new Error("Unsupported cell type ".concat(e.slice(0,4)))}return h}function r_(e,t,r){var a=Xt(e),n=a.getUint32(8,!0),i=12,s=-1,f=-1,c=NaN,o=NaN,l=new Date(2001,0,1);n&1&&(c=Zg(e,i),i+=16),n&2&&(o=a.getFloat64(i,!0),i+=8),n&4&&(l.setTime(l.getTime()+a.getFloat64(i,!0)*1e3),i+=8),n&8&&(f=a.getUint32(i,!0),i+=4),n&16&&(s=a.getUint32(i,!0),i+=4);var h;switch(e[1]){case 0:break;case 2:h={t:"n",v:c};break;case 3:h={t:"s",v:t[f]};break;case 5:h={t:"d",v:l};break;case 6:h={t:"b",v:o>0};break;case 7:h={t:"n",v:o/86400};break;case 8:h={t:"e",v:0};break;case 9:if(s>-1)h={t:"s",v:r[s]};else throw new Error("Unsupported cell type ".concat(e[1]," : ").concat(n&31," : ").concat(e.slice(0,4)));break;case 10:h={t:"n",v:c};break;default:throw new Error("Unsupported cell type ".concat(e[1]," : ").concat(n&31," : ").concat(e.slice(0,4)))}return h}function Yn(e,t){var r=new Uint8Array(32),a=Xt(r),n=12,i=0;switch(r[0]=5,e.t){case"n":r[1]=2,qg(r,n,e.v),i|=1,n+=16;break;case"b":r[1]=6,a.setFloat64(n,e.v?1:0,!0),i|=2,n+=8;break;case"s":if(t.indexOf(e.v)==-1)throw new Error("Value ".concat(e.v," missing from SST!"));r[1]=3,a.setUint32(n,t.indexOf(e.v),!0),i|=8,n+=4;break;default:throw"unsupported cell type "+e.t}return a.setUint32(8,i,!0),r.slice(0,n)}function jn(e,t){var r=new Uint8Array(32),a=Xt(r),n=12,i=0;switch(r[0]=3,e.t){case"n":r[2]=2,a.setFloat64(n,e.v,!0),i|=32,n+=8;break;case"b":r[2]=6,a.setFloat64(n,e.v?1:0,!0),i|=32,n+=8;break;case"s":if(t.indexOf(e.v)==-1)throw new Error("Value ".concat(e.v," missing from SST!"));r[2]=3,a.setUint32(n,t.indexOf(e.v),!0),i|=16,n+=4;break;default:throw"unsupported cell type "+e.t}return a.setUint32(4,i,!0),r.slice(0,n)}function t_(e,t,r){switch(e[0]){case 0:case 1:case 2:case 3:return e_(e,t,r,e[0]);case 5:return r_(e,t,r);default:throw new Error("Unsupported payload version ".concat(e[0]))}}function pr(e){var t=Re(e);return Xa(t[1][0].data)}function uf(e,t){var r=Re(t.data),a=Ye(r[1][0].data),n=r[3],i=[];return(n||[]).forEach(function(s){var f=Re(s.data),c=Ye(f[1][0].data)>>>0;switch(a){case 1:i[c]=oi(f[3][0].data);break;case 8:{var o=e[pr(f[9][0].data)][0],l=Re(o.data),h=e[pr(l[1][0].data)][0],d=Ye(h.meta[1][0].data);if(d!=2001)throw new Error("2000 unexpected reference to ".concat(d));var v=Re(h.data);i[c]=v[3].map(function(p){return oi(p.data)}).join("")}break}}),i}function a_(e,t){var r,a,n,i,s,f,c,o,l,h,d,v,p,u,m=Re(e),k=Ye(m[1][0].data)>>>0,A=Ye(m[2][0].data)>>>0,_=((a=(r=m[8])==null?void 0:r[0])==null?void 0:a.data)&&Ye(m[8][0].data)>0||!1,I,D;if((i=(n=m[7])==null?void 0:n[0])!=null&&i.data&&t!=0)I=(f=(s=m[7])==null?void 0:s[0])==null?void 0:f.data,D=(o=(c=m[6])==null?void 0:c[0])==null?void 0:o.data;else if((h=(l=m[4])==null?void 0:l[0])!=null&&h.data&&t!=1)I=(v=(d=m[4])==null?void 0:d[0])==null?void 0:v.data,D=(u=(p=m[3])==null?void 0:p[0])==null?void 0:u.data;else throw"NUMBERS Tile missing ".concat(t," cell storage");for(var P=_?4:1,x=Xt(I),M=[],R=0;R<I.length/2;++R){var z=x.getUint16(R*2,!0);z<65535&&M.push([R,z])}if(M.length!=A)throw"Expected ".concat(A," cells, found ").concat(M.length);var X=[];for(R=0;R<M.length-1;++R)X[M[R][0]]=D.subarray(M[R][1]*P,M[R+1][1]*P);return M.length>=1&&(X[M[M.length-1][0]]=D.subarray(M[M.length-1][1]*P)),{R:k,cells:X}}function n_(e,t){var r,a=Re(t.data),n=(r=a==null?void 0:a[7])!=null&&r[0]?Ye(a[7][0].data)>>>0>0?1:0:-1,i=Ki(a[5],function(s){return a_(s,n)});return{nrows:Ye(a[4][0].data)>>>0,data:i.reduce(function(s,f){return s[f.R]||(s[f.R]=[]),f.cells.forEach(function(c,o){if(s[f.R][o])throw new Error("Duplicate cell r=".concat(f.R," c=").concat(o));s[f.R][o]=c}),s},[])}}function i_(e,t,r){var a,n=Re(t.data),i={s:{r:0,c:0},e:{r:0,c:0}};if(i.e.r=(Ye(n[6][0].data)>>>0)-1,i.e.r<0)throw new Error("Invalid row varint ".concat(n[6][0].data));if(i.e.c=(Ye(n[7][0].data)>>>0)-1,i.e.c<0)throw new Error("Invalid col varint ".concat(n[7][0].data));r["!ref"]=ke(i);var s=Re(n[4][0].data),f=uf(e,e[pr(s[4][0].data)][0]),c=(a=s[17])!=null&&a[0]?uf(e,e[pr(s[17][0].data)][0]):[],o=Re(s[3][0].data),l=0;o[1].forEach(function(h){var d=Re(h.data),v=e[pr(d[2][0].data)][0],p=Ye(v.meta[1][0].data);if(p!=6002)throw new Error("6001 unexpected reference to ".concat(p));var u=n_(e,v);u.data.forEach(function(m,k){m.forEach(function(A,_){var I=me({r:l+k,c:_}),D=t_(A,f,c);D&&(r[I]=D)})}),l+=u.nrows})}function s_(e,t){var r=Re(t.data),a={"!ref":"A1"},n=e[pr(r[2][0].data)],i=Ye(n[0].meta[1][0].data);if(i!=6001)throw new Error("6000 unexpected reference to ".concat(i));return i_(e,n[0],a),a}function f_(e,t){var r,a=Re(t.data),n={name:(r=a[1])!=null&&r[0]?oi(a[1][0].data):"",sheets:[]},i=Ki(a[2],pr);return i.forEach(function(s){e[s].forEach(function(f){var c=Ye(f.meta[1][0].data);c==6e3&&n.sheets.push(s_(e,f))})}),n}function c_(e,t){var r=es(),a=Re(t.data),n=Ki(a[1],pr);if(n.forEach(function(i){e[i].forEach(function(s){var f=Ye(s.meta[1][0].data);if(f==2){var c=f_(e,s);c.sheets.forEach(function(o,l){rs(r,o,l==0?c.name:c.name+"_"+l,!0)})}})}),r.SheetNames.length==0)throw new Error("Empty NUMBERS file");return r}function Jn(e){var t,r,a,n,i={},s=[];if(e.FullPaths.forEach(function(c){if(c.match(/\.iwpv2/))throw new Error("Unsupported password protection")}),e.FileIndex.forEach(function(c){if(c.name.match(/\.iwa$/)){var o;try{o=Hr(c.content)}catch(h){return console.log("?? "+c.content.length+" "+(h.message||h))}var l;try{l=Wr(o)}catch(h){return console.log("## "+(h.message||h))}l.forEach(function(h){i[h.id]=h.messages,s.push(h.id)})}}),!s.length)throw new Error("File has no messages");var f=((n=(a=(r=(t=i==null?void 0:i[1])==null?void 0:t[0])==null?void 0:r.meta)==null?void 0:a[1])==null?void 0:n[0].data)&&Ye(i[1][0].meta[1][0].data)==1&&i[1][0];if(f||s.forEach(function(c){i[c].forEach(function(o){var l=Ye(o.meta[1][0].data)>>>0;if(l==1)if(!f)f=o;else throw new Error("Document has multiple roots")})}),!f)throw new Error("Cannot find Document root");return c_(i,f)}function o_(e,t,r){var a,n,i,s;if(!((a=e[6])!=null&&a[0])||!((n=e[7])!=null&&n[0]))throw"Mutation only works on post-BNC storages!";var f=((s=(i=e[8])==null?void 0:i[0])==null?void 0:s.data)&&Ye(e[8][0].data)>0||!1;if(f)throw"Math only works with normal offsets";for(var c=0,o=Xt(e[7][0].data),l=0,h=[],d=Xt(e[4][0].data),v=0,p=[],u=0;u<t.length;++u){if(t[u]==null){o.setUint16(u*2,65535,!0),d.setUint16(u*2,65535);continue}o.setUint16(u*2,l,!0),d.setUint16(u*2,v,!0);var m,k;switch(typeof t[u]){case"string":m=Yn({t:"s",v:t[u]},r),k=jn({t:"s",v:t[u]},r);break;case"number":m=Yn({t:"n",v:t[u]},r),k=jn({t:"n",v:t[u]},r);break;case"boolean":m=Yn({t:"b",v:t[u]},r),k=jn({t:"b",v:t[u]},r);break;default:throw new Error("Unsupported value "+t[u])}h.push(m),l+=m.length,p.push(k),v+=k.length,++c}for(e[2][0].data=Ne(c);u<e[7][0].data.length/2;++u)o.setUint16(u*2,65535,!0),d.setUint16(u*2,65535,!0);return e[6][0].data=At(h),e[3][0].data=At(p),c}function l_(e,t){if(!t||!t.numbers)throw new Error("Must pass a `numbers` option -- check the README");var r=e.Sheets[e.SheetNames[0]];e.SheetNames.length>1&&console.error("The Numbers writer currently writes only the first table");var a=Ar(r["!ref"]);a.s.r=a.s.c=0;var n=!1;a.e.c>9&&(n=!0,a.e.c=9),a.e.r>49&&(n=!0,a.e.r=49),n&&console.error("The Numbers writer is currently limited to ".concat(ke(a)));var i=yn(r,{range:a,header:1}),s=["~Sh33tJ5~"];i.forEach(function(L){return L.forEach(function(F){typeof F=="string"&&s.push(F)})});var f={},c=[],o=de.read(t.numbers,{type:"base64"});o.FileIndex.map(function(L,F){return[L,o.FullPaths[F]]}).forEach(function(L){var F=L[0],N=L[1];if(F.type==2&&F.name.match(/\.iwa/)){var Y=F.content,re=Hr(Y),te=Wr(re);te.forEach(function(Q){c.push(Q.id),f[Q.id]={deps:[],location:N,type:Ye(Q.messages[0].meta[1][0].data)}})}}),c.sort(function(L,F){return L-F});var l=c.filter(function(L){return L>1}).map(function(L){return[L,Ne(L)]});o.FileIndex.map(function(L,F){return[L,o.FullPaths[F]]}).forEach(function(L){var F=L[0];if(L[1],!!F.name.match(/\.iwa/)){var N=Wr(Hr(F.content));N.forEach(function(Y){Y.messages.forEach(function(re){l.forEach(function(te){Y.messages.some(function(Q){return Ye(Q.meta[1][0].data)!=11006&&Jg(Q.data,te[1])})&&f[te[0]].deps.push(Y.id)})})})}});for(var h=de.find(o,f[1].location),d=Wr(Hr(h.content)),v,p=0;p<d.length;++p){var u=d[p];u.id==1&&(v=u)}var m=pr(Re(v.messages[0].data)[1][0].data);for(h=de.find(o,f[m].location),d=Wr(Hr(h.content)),p=0;p<d.length;++p)u=d[p],u.id==m&&(v=u);for(m=pr(Re(v.messages[0].data)[2][0].data),h=de.find(o,f[m].location),d=Wr(Hr(h.content)),p=0;p<d.length;++p)u=d[p],u.id==m&&(v=u);for(m=pr(Re(v.messages[0].data)[2][0].data),h=de.find(o,f[m].location),d=Wr(Hr(h.content)),p=0;p<d.length;++p)u=d[p],u.id==m&&(v=u);var k=Re(v.messages[0].data);{k[6][0].data=Ne(a.e.r+1),k[7][0].data=Ne(a.e.c+1);var A=pr(k[46][0].data),_=de.find(o,f[A].location),I=Wr(Hr(_.content));{for(var D=0;D<I.length&&I[D].id!=A;++D);if(I[D].id!=A)throw"Bad ColumnRowUIDMapArchive";var P=Re(I[D].messages[0].data);P[1]=[],P[2]=[],P[3]=[];for(var x=0;x<=a.e.c;++x){var M=[];M[1]=M[2]=[{type:0,data:Ne(x+420690)}],P[1].push({type:2,data:lr(M)}),P[2].push({type:0,data:Ne(x)}),P[3].push({type:0,data:Ne(x)})}P[4]=[],P[5]=[],P[6]=[];for(var R=0;R<=a.e.r;++R)M=[],M[1]=M[2]=[{type:0,data:Ne(R+726270)}],P[4].push({type:2,data:lr(M)}),P[5].push({type:0,data:Ne(R)}),P[6].push({type:0,data:Ne(R)});I[D].messages[0].data=lr(P)}_.content=Qt(qt(I)),_.size=_.content.length,delete k[46];var z=Re(k[4][0].data);{z[7][0].data=Ne(a.e.r+1);var X=Re(z[1][0].data),b=pr(X[2][0].data);_=de.find(o,f[b].location),I=Wr(Hr(_.content));{if(I[0].id!=b)throw"Bad HeaderStorageBucket";var J=Re(I[0].messages[0].data);for(R=0;R<i.length;++R){var le=Re(J[2][0].data);le[1][0].data=Ne(R),le[4][0].data=Ne(i[R].length),J[2][R]={type:J[2][0].type,data:lr(le)}}I[0].messages[0].data=lr(J)}_.content=Qt(qt(I)),_.size=_.content.length;var ie=pr(z[2][0].data);_=de.find(o,f[ie].location),I=Wr(Hr(_.content));{if(I[0].id!=ie)throw"Bad HeaderStorageBucket";for(J=Re(I[0].messages[0].data),x=0;x<=a.e.c;++x)le=Re(J[2][0].data),le[1][0].data=Ne(x),le[4][0].data=Ne(a.e.r+1),J[2][x]={type:J[2][0].type,data:lr(le)};I[0].messages[0].data=lr(J)}_.content=Qt(qt(I)),_.size=_.content.length;var ue=pr(z[4][0].data);(function(){for(var L=de.find(o,f[ue].location),F=Wr(Hr(L.content)),N,Y=0;Y<F.length;++Y){var re=F[Y];re.id==ue&&(N=re)}var te=Re(N.messages[0].data);{te[3]=[];var Q=[];s.forEach(function(C,Ue){Q[1]=[{type:0,data:Ne(Ue)}],Q[2]=[{type:0,data:Ne(1)}],Q[3]=[{type:2,data:jg(C)}],te[3].push({type:2,data:lr(Q)})})}N.messages[0].data=lr(te);var Z=qt(F),Ee=Qt(Z);L.content=Ee,L.size=L.content.length})();var ce=Re(z[3][0].data);{var Le=ce[1][0];delete ce[2];var V=Re(Le.data);{var pe=pr(V[2][0].data);(function(){for(var L=de.find(o,f[pe].location),F=Wr(Hr(L.content)),N,Y=0;Y<F.length;++Y){var re=F[Y];re.id==pe&&(N=re)}var te=Re(N.messages[0].data);{delete te[6],delete ce[7];var Q=new Uint8Array(te[5][0].data);te[5]=[];for(var Z=0,Ee=0;Ee<=a.e.r;++Ee){var C=Re(Q);Z+=o_(C,i[Ee],s),C[1][0].data=Ne(Ee),te[5].push({data:lr(C),type:2})}te[1]=[{type:0,data:Ne(a.e.c+1)}],te[2]=[{type:0,data:Ne(a.e.r+1)}],te[3]=[{type:0,data:Ne(Z)}],te[4]=[{type:0,data:Ne(a.e.r+1)}]}N.messages[0].data=lr(te);var Ue=qt(F),Oe=Qt(Ue);L.content=Oe,L.size=L.content.length})()}Le.data=lr(V)}z[3][0].data=lr(ce)}k[4][0].data=lr(z)}v.messages[0].data=lr(k);var _e=qt(d),O=Qt(_e);return h.content=O,h.size=h.content.length,o}function go(e){return function(r){for(var a=0;a!=e.length;++a){var n=e[a];r[n[0]]===void 0&&(r[n[0]]=n[1]),n[2]==="n"&&(r[n[0]]=Number(r[n[0]]))}}}function Yi(e){go([["cellNF",!1],["cellHTML",!0],["cellFormula",!0],["cellStyles",!1],["cellText",!0],["cellDates",!1],["sheetStubs",!1],["sheetRows",0,"n"],["bookDeps",!1],["bookSheets",!1],["bookProps",!1],["bookFiles",!1],["bookVBA",!1],["password",""],["WTF",!1]])(e)}function ji(e){go([["cellDates",!1],["bookSST",!1],["bookType","xlsx"],["compression",!1],["WTF",!1]])(e)}function h_(e){return xe.WS.indexOf(e)>-1?"sheet":e==xe.CS?"chart":e==xe.DS?"dialog":e==xe.MS?"macro":e&&e.length?e:"sheet"}function u_(e,t){if(!e)return 0;try{e=t.map(function(a){return a.id||(a.id=a.strRelID),[a.name,e["!id"][a.id].Target,h_(e["!id"][a.id].Type)]})}catch{return null}return!e||e.length===0?null:e}function d_(e,t,r,a,n,i,s,f,c,o,l,h){try{i[a]=Aa(Nr(e,r,!0),t);var d=rr(e,t),v;switch(f){case"sheet":v=Um(d,t,n,c,i[a],o,l,h);break;case"chart":if(v=Wm(d,t,n,c,i[a],o,l,h),!v||!v["!drawel"])break;var p=Ea(v["!drawel"].Target,t),u=Ma(p),m=C2(Nr(e,p,!0),Aa(Nr(e,u,!0),p)),k=Ea(m,p),A=Ma(k);v=vm(Nr(e,k,!0),k,c,Aa(Nr(e,A,!0),k),o,v);break;case"macro":v=Hm(d,t,n,c,i[a],o,l,h);break;case"dialog":v=Vm(d,t,n,c,i[a],o,l,h);break;default:throw new Error("Unrecognized sheet type "+f)}s[a]=v;var _=[];i&&i[a]&&je(i[a]).forEach(function(I){var D="";if(i[a][I].Type==xe.CMNT){D=Ea(i[a][I].Target,t);var P=$m(rr(e,D,!0),D,c);if(!P||!P.length)return;Js(v,P,!1)}i[a][I].Type==xe.TCMNT&&(D=Ea(i[a][I].Target,t),_=_.concat(I2(rr(e,D,!0),c)))}),_&&_.length&&Js(v,_,!0,c.people||[])}catch(I){if(c.WTF)throw I}}function Br(e){return e.charAt(0)=="/"?e.slice(1):e}function v_(e,t){if(la(),t=t||{},Yi(t),Vr(e,"META-INF/manifest.xml")||Vr(e,"objectdata.xml"))return cf(e,t);if(Vr(e,"Index/Document.iwa")){if(typeof Uint8Array>"u")throw new Error("NUMBERS file parsing requires Uint8Array support");if(typeof Jn<"u"){if(e.FileIndex)return Jn(e);var r=de.utils.cfb_new();return gs(e).forEach(function(le){Te(r,le,Fl(e,le))}),Jn(r)}throw new Error("Unsupported NUMBERS file")}if(!Vr(e,"[Content_Types].xml"))throw Vr(e,"index.xml.gz")?new Error("Unsupported NUMBERS 08 file"):Vr(e,"index.xml")?new Error("Unsupported NUMBERS 09 file"):new Error("Unsupported ZIP file");var a=gs(e),n=S1(Nr(e,"[Content_Types].xml")),i=!1,s,f;if(n.workbooks.length===0&&(f="xl/workbook.xml",rr(e,f,!0)&&n.workbooks.push(f)),n.workbooks.length===0){if(f="xl/workbook.bin",!rr(e,f,!0))throw new Error("Could not find workbook");n.workbooks.push(f),i=!0}n.workbooks[0].slice(-3)=="bin"&&(i=!0);var c={},o={};if(!t.bookSheets&&!t.bookProps){if(Ca=[],n.sst)try{Ca=zm(rr(e,Br(n.sst)),n.sst,t)}catch(le){if(t.WTF)throw le}t.cellStyles&&n.themes.length&&(c=Gm(Nr(e,n.themes[0].replace(/^\//,""),!0)||"",n.themes[0],t)),n.style&&(o=Xm(rr(e,Br(n.style)),n.style,c,t))}n.links.map(function(le){try{var ie=Aa(Nr(e,Ma(Br(le))),le);return Ym(rr(e,Br(le)),ie,le,t)}catch{}});var l=Bm(rr(e,Br(n.workbooks[0])),n.workbooks[0],t),h={},d="";n.coreprops.length&&(d=rr(e,Br(n.coreprops[0]),!0),d&&(h=Qf(d)),n.extprops.length!==0&&(d=rr(e,Br(n.extprops[0]),!0),d&&F1(d,h,t)));var v={};(!t.bookSheets||t.bookProps)&&n.custprops.length!==0&&(d=Nr(e,Br(n.custprops[0]),!0),d&&(v=N1(d,t)));var p={};if((t.bookSheets||t.bookProps)&&(l.Sheets?s=l.Sheets.map(function(ie){return ie.name}):h.Worksheets&&h.SheetNames.length>0&&(s=h.SheetNames),t.bookProps&&(p.Props=h,p.Custprops=v),t.bookSheets&&typeof s<"u"&&(p.SheetNames=s),t.bookSheets?p.SheetNames:t.bookProps))return p;s={};var u={};t.bookDeps&&n.calcchain&&(u=Km(rr(e,Br(n.calcchain)),n.calcchain));var m=0,k={},A,_;{var I=l.Sheets;h.Worksheets=I.length,h.SheetNames=[];for(var D=0;D!=I.length;++D)h.SheetNames[D]=I[D].name}var P=i?"bin":"xml",x=n.workbooks[0].lastIndexOf("/"),M=(n.workbooks[0].slice(0,x+1)+"_rels/"+n.workbooks[0].slice(x+1)+".rels").replace(/^\//,"");Vr(e,M)||(M="xl/_rels/workbook."+P+".rels");var R=Aa(Nr(e,M,!0),M.replace(/_rels.*/,"s5s"));(n.metadata||[]).length>=1&&(t.xlmeta=jm(rr(e,Br(n.metadata[0])),n.metadata[0],t)),(n.people||[]).length>=1&&(t.people=R2(rr(e,Br(n.people[0])),t)),R&&(R=u_(R,l.Sheets));var z=rr(e,"xl/worksheets/sheet.xml",!0)?1:0;e:for(m=0;m!=h.Worksheets;++m){var X="sheet";if(R&&R[m]?(A="xl/"+R[m][1].replace(/[\/]?xl\//,""),Vr(e,A)||(A=R[m][1]),Vr(e,A)||(A=M.replace(/_rels\/.*$/,"")+R[m][1]),X=R[m][2]):(A="xl/worksheets/sheet"+(m+1-z)+"."+P,A=A.replace(/sheet0\./,"sheet.")),_=A.replace(/^(.*)(\/)([^\/]*)$/,"$1/_rels/$3.rels"),t&&t.sheets!=null)switch(typeof t.sheets){case"number":if(m!=t.sheets)continue e;break;case"string":if(h.SheetNames[m].toLowerCase()!=t.sheets.toLowerCase())continue e;break;default:if(Array.isArray&&Array.isArray(t.sheets)){for(var b=!1,J=0;J!=t.sheets.length;++J)typeof t.sheets[J]=="number"&&t.sheets[J]==m&&(b=1),typeof t.sheets[J]=="string"&&t.sheets[J].toLowerCase()==h.SheetNames[m].toLowerCase()&&(b=1);if(!b)continue e}}d_(e,A,_,h.SheetNames[m],m,k,s,X,t,l,c,o)}return p={Directory:n,Workbook:l,Props:h,Custprops:v,Deps:u,Sheets:s,SheetNames:h.SheetNames,Strings:Ca,Styles:o,Themes:c,SSF:Be(ve)},t&&t.bookFiles&&(e.files?(p.keys=a,p.files=e.files):(p.keys=[],p.files={},e.FullPaths.forEach(function(le,ie){le=le.replace(/^Root Entry[\/]/,""),p.keys.push(le),p.files[le]=e.FileIndex[ie]}))),t&&t.bookVBA&&(n.vba.length>0?p.vbaraw=rr(e,Br(n.vba[0]),!0):n.defaults&&n.defaults.bin===U2&&(p.vbaraw=rr(e,"xl/vbaProject.bin",!0))),p}function p_(e,t){var r=t||{},a="Workbook",n=de.find(e,a);try{if(a="/!DataSpaces/Version",n=de.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);if(Ju(n.content),a="/!DataSpaces/DataSpaceMap",n=de.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);var i=qu(n.content);if(i.length!==1||i[0].comps.length!==1||i[0].comps[0].t!==0||i[0].name!=="StrongEncryptionDataSpace"||i[0].comps[0].v!=="EncryptedPackage")throw new Error("ECMA-376 Encrypted file bad "+a);if(a="/!DataSpaces/DataSpaceInfo/StrongEncryptionDataSpace",n=de.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);var s=Qu(n.content);if(s.length!=1||s[0]!="StrongEncryptionTransform")throw new Error("ECMA-376 Encrypted file bad "+a);if(a="/!DataSpaces/TransformInfo/StrongEncryptionTransform/!Primary",n=de.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);r0(n.content)}catch{}if(a="/EncryptionInfo",n=de.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);var f=t0(n.content);if(a="/EncryptedPackage",n=de.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);if(f[0]==4&&typeof decrypt_agile<"u")return decrypt_agile(f[1],n.content,r.password||"",r);if(f[0]==2&&typeof decrypt_std76<"u")return decrypt_std76(f[1],n.content,r.password||"",r);throw new Error("File is password-protected")}function m_(e,t){return t.bookType=="ods"?mo(e,t):t.bookType=="numbers"?l_(e,t):t.bookType=="xlsb"?g_(e,t):_o(e,t)}function g_(e,t){ta=1024,e&&!e.SSF&&(e.SSF=Be(ve)),e&&e.SSF&&(la(),$a(e.SSF),t.revssf=On(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,Oa?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r=t.bookType=="xlsb"?"bin":"xml",a=Bc.indexOf(t.bookType)>-1,n=Di();ji(t=t||{});var i=mi(),s="",f=0;if(t.cellXfs=[],It(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),s="docProps/core.xml",Te(i,s,ec(e.Props,t)),n.coreprops.push(s),Pe(t.rels,2,s,xe.CORE_PROPS),s="docProps/app.xml",!(e.Props&&e.Props.SheetNames))if(!e.Workbook||!e.Workbook.Sheets)e.Props.SheetNames=e.SheetNames;else{for(var c=[],o=0;o<e.SheetNames.length;++o)(e.Workbook.Sheets[o]||{}).Hidden!=2&&c.push(e.SheetNames[o]);e.Props.SheetNames=c}for(e.Props.Worksheets=e.Props.SheetNames.length,Te(i,s,ac(e.Props)),n.extprops.push(s),Pe(t.rels,3,s,xe.EXT_PROPS),e.Custprops!==e.Props&&je(e.Custprops||{}).length>0&&(s="docProps/custom.xml",Te(i,s,nc(e.Custprops)),n.custprops.push(s),Pe(t.rels,4,s,xe.CUST_PROPS)),f=1;f<=e.SheetNames.length;++f){var l={"!id":{}},h=e.Sheets[e.SheetNames[f-1]],d=(h||{})["!type"]||"sheet";switch(d){case"chart":default:s="xl/worksheets/sheet"+f+"."+r,Te(i,s,Zm(f-1,s,t,e,l)),n.sheets.push(s),Pe(t.wbrels,-1,"worksheets/sheet"+f+"."+r,xe.WS[0])}if(h){var v=h["!comments"],p=!1,u="";v&&v.length>0&&(u="xl/comments"+f+"."+r,Te(i,u,eg(v,u)),n.comments.push(u),Pe(l,-1,"../comments"+f+"."+r,xe.CMNT),p=!0),h["!legacy"]&&p&&Te(i,"xl/drawings/vmlDrawing"+f+".vml",bc(f,h["!comments"])),delete h["!comments"],delete h["!legacy"]}l["!id"].rId1&&Te(i,Ma(s),na(l))}return t.Strings!=null&&t.Strings.length>0&&(s="xl/sharedStrings."+r,Te(i,s,Qm(t.Strings,s,t)),n.strs.push(s),Pe(t.wbrels,-1,"sharedStrings."+r,xe.SST)),s="xl/workbook."+r,Te(i,s,Jm(e,s)),n.workbooks.push(s),Pe(t.rels,1,s,xe.WB),s="xl/theme/theme1.xml",Te(i,s,Wi(e.Themes,t)),n.themes.push(s),Pe(t.wbrels,-1,"theme/theme1.xml",xe.THEME),s="xl/styles."+r,Te(i,s,qm(e,s,t)),n.styles.push(s),Pe(t.wbrels,-1,"styles."+r,xe.STY),e.vbaraw&&a&&(s="xl/vbaProject.bin",Te(i,s,e.vbaraw),n.vba.push(s),Pe(t.wbrels,-1,"vbaProject.bin",xe.VBA)),s="xl/metadata."+r,Te(i,s,rg(s)),n.metadata.push(s),Pe(t.wbrels,-1,"metadata."+r,xe.XLMETA),Te(i,"[Content_Types].xml",Zf(n,t)),Te(i,"_rels/.rels",na(t.rels)),Te(i,"xl/_rels/workbook."+r+".rels",na(t.wbrels)),delete t.revssf,delete t.ssf,i}function _o(e,t){ta=1024,e&&!e.SSF&&(e.SSF=Be(ve)),e&&e.SSF&&(la(),$a(e.SSF),t.revssf=On(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,Oa?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r="xml",a=Bc.indexOf(t.bookType)>-1,n=Di();ji(t=t||{});var i=mi(),s="",f=0;if(t.cellXfs=[],It(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),s="docProps/core.xml",Te(i,s,ec(e.Props,t)),n.coreprops.push(s),Pe(t.rels,2,s,xe.CORE_PROPS),s="docProps/app.xml",!(e.Props&&e.Props.SheetNames))if(!e.Workbook||!e.Workbook.Sheets)e.Props.SheetNames=e.SheetNames;else{for(var c=[],o=0;o<e.SheetNames.length;++o)(e.Workbook.Sheets[o]||{}).Hidden!=2&&c.push(e.SheetNames[o]);e.Props.SheetNames=c}e.Props.Worksheets=e.Props.SheetNames.length,Te(i,s,ac(e.Props)),n.extprops.push(s),Pe(t.rels,3,s,xe.EXT_PROPS),e.Custprops!==e.Props&&je(e.Custprops||{}).length>0&&(s="docProps/custom.xml",Te(i,s,nc(e.Custprops)),n.custprops.push(s),Pe(t.rels,4,s,xe.CUST_PROPS));var l=["SheetJ5"];for(t.tcid=0,f=1;f<=e.SheetNames.length;++f){var h={"!id":{}},d=e.Sheets[e.SheetNames[f-1]],v=(d||{})["!type"]||"sheet";switch(v){case"chart":default:s="xl/worksheets/sheet"+f+"."+r,Te(i,s,jc(f-1,t,e,h)),n.sheets.push(s),Pe(t.wbrels,-1,"worksheets/sheet"+f+"."+r,xe.WS[0])}if(d){var p=d["!comments"],u=!1,m="";if(p&&p.length>0){var k=!1;p.forEach(function(A){A[1].forEach(function(_){_.T==!0&&(k=!0)})}),k&&(m="xl/threadedComments/threadedComment"+f+"."+r,Te(i,m,F2(p,l,t)),n.threadedcomments.push(m),Pe(h,-1,"../threadedComments/threadedComment"+f+"."+r,xe.TCMNT)),m="xl/comments"+f+"."+r,Te(i,m,Mc(p)),n.comments.push(m),Pe(h,-1,"../comments"+f+"."+r,xe.CMNT),u=!0}d["!legacy"]&&u&&Te(i,"xl/drawings/vmlDrawing"+f+".vml",bc(f,d["!comments"])),delete d["!comments"],delete d["!legacy"]}h["!id"].rId1&&Te(i,Ma(s),na(h))}return t.Strings!=null&&t.Strings.length>0&&(s="xl/sharedStrings."+r,Te(i,s,yc(t.Strings,t)),n.strs.push(s),Pe(t.wbrels,-1,"sharedStrings."+r,xe.SST)),s="xl/workbook."+r,Te(i,s,ro(e)),n.workbooks.push(s),Pe(t.rels,1,s,xe.WB),s="xl/theme/theme1.xml",Te(i,s,Wi(e.Themes,t)),n.themes.push(s),Pe(t.wbrels,-1,"theme/theme1.xml",xe.THEME),s="xl/styles."+r,Te(i,s,Nc(e,t)),n.styles.push(s),Pe(t.wbrels,-1,"styles."+r,xe.STY),e.vbaraw&&a&&(s="xl/vbaProject.bin",Te(i,s,e.vbaraw),n.vba.push(s),Pe(t.wbrels,-1,"vbaProject.bin",xe.VBA)),s="xl/metadata."+r,Te(i,s,Lc()),n.metadata.push(s),Pe(t.wbrels,-1,"metadata."+r,xe.XLMETA),l.length>1&&(s="xl/persons/person.xml",Te(i,s,N2(l)),n.people.push(s),Pe(t.wbrels,-1,"persons/person.xml",xe.PEOPLE)),Te(i,"[Content_Types].xml",Zf(n,t)),Te(i,"_rels/.rels",na(t.rels)),Te(i,"xl/_rels/workbook."+r+".rels",na(t.wbrels)),delete t.revssf,delete t.ssf,i}function Ji(e,t){var r="";switch((t||{}).type||"base64"){case"buffer":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":r=Dr(e.slice(0,12));break;case"binary":r=e;break;case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];default:throw new Error("Unrecognized type "+(t&&t.type||"undefined"))}return[r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2),r.charCodeAt(3),r.charCodeAt(4),r.charCodeAt(5),r.charCodeAt(6),r.charCodeAt(7)]}function __(e,t){return de.find(e,"EncryptedPackage")?p_(e,t):io(e,t)}function w_(e,t){var r,a=e,n=t||{};return n.type||(n.type=Se&&Buffer.isBuffer(e)?"buffer":"base64"),r=Ff(a,n),v_(r,n)}function wo(e,t){var r=0;e:for(;r<e.length;)switch(e.charCodeAt(r)){case 10:case 13:case 32:++r;break;case 60:return fi(e.slice(r),t);default:break e}return ca.to_workbook(e,t)}function k_(e,t){var r="",a=Ji(e,t);switch(t.type){case"base64":r=Dr(e);break;case"binary":r=e;break;case"buffer":r=e.toString("binary");break;case"array":r=Ut(e);break;default:throw new Error("Unrecognized type "+t.type)}return a[0]==239&&a[1]==187&&a[2]==191&&(r=be(r)),t.type="binary",wo(r,t)}function T_(e,t){var r=e;return t.type=="base64"&&(r=Dr(r)),r=Ie.utils.decode(1200,r.slice(2),"str"),t.type="binary",wo(r,t)}function E_(e){return e.match(/[^\x00-\x7F]/)?tt(e):e}function Zn(e,t,r,a){return a?(r.type="string",ca.to_workbook(e,r)):ca.to_workbook(t,r)}function li(e,t){An();var r=t||{};if(typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer)return li(new Uint8Array(e),(r=Be(r),r.type="array",r));typeof Uint8Array<"u"&&e instanceof Uint8Array&&!r.type&&(r.type=typeof Deno<"u"?"buffer":"array");var a=e,n=[0,0,0,0],i=!1;if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),sa={},r.dateNF&&(sa.dateNF=r.dateNF),r.type||(r.type=Se&&Buffer.isBuffer(e)?"buffer":"base64"),r.type=="file"&&(r.type=Se?"buffer":"binary",a=El(e),typeof Uint8Array<"u"&&!Se&&(r.type="array")),r.type=="string"&&(i=!0,r.type="binary",r.codepage=65001,a=E_(e)),r.type=="array"&&typeof Uint8Array<"u"&&e instanceof Uint8Array&&typeof ArrayBuffer<"u"){var s=new ArrayBuffer(3),f=new Uint8Array(s);if(f.foo="bar",!f.foo)return r=Be(r),r.type="array",li(ui(a),r)}switch((n=Ji(a,r))[0]){case 208:if(n[1]===207&&n[2]===17&&n[3]===224&&n[4]===161&&n[5]===177&&n[6]===26&&n[7]===225)return __(de.read(a,r),r);break;case 9:if(n[1]<=8)return io(a,r);break;case 60:return fi(a,r);case 73:if(n[1]===73&&n[2]===42&&n[3]===0)throw new Error("TIFF Image File is not a spreadsheet");if(n[1]===68)return Pu(a,r);break;case 84:if(n[1]===65&&n[2]===66&&n[3]===76)return Ec.to_workbook(a,r);break;case 80:return n[1]===75&&n[2]<9&&n[3]<9?w_(a,r):Zn(e,a,r,i);case 239:return n[3]===60?fi(a,r):Zn(e,a,r,i);case 255:if(n[1]===254)return T_(a,r);if(n[1]===0&&n[2]===2&&n[3]===0)return Lt.to_workbook(a,r);break;case 0:if(n[1]===0&&(n[2]>=2&&n[3]===0||n[2]===0&&(n[3]===8||n[3]===9)))return Lt.to_workbook(a,r);break;case 3:case 131:case 139:case 140:return si.to_workbook(a,r);case 123:if(n[1]===92&&n[2]===114&&n[3]===116)return Ic.to_workbook(a,r);break;case 10:case 13:case 32:return k_(a,r);case 137:if(n[1]===80&&n[2]===78&&n[3]===71)throw new Error("PNG Image File is not a spreadsheet");break}return Nu.indexOf(n[0])>-1&&n[2]<=12&&n[3]<=31?si.to_workbook(a,r):Zn(e,a,r,i)}function H_(e,t){var r=t||{};return r.type="file",li(e,r)}function ko(e,t){switch(t.type){case"base64":case"binary":break;case"buffer":case"array":t.type="";break;case"file":return Ka(t.file,de.write(e,{type:Se?"buffer":""}));case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");default:throw new Error("Unrecognized type "+t.type)}return de.write(e,t)}function S_(e,t){var r=Be(t||{}),a=m_(e,r);return To(a,r)}function y_(e,t){var r=Be(t||{}),a=_o(e,r);return To(a,r)}function To(e,t){var r={},a=Se?"nodebuffer":typeof Uint8Array<"u"?"array":"string";if(t.compression&&(r.compression="DEFLATE"),t.password)r.type=a;else switch(t.type){case"base64":r.type="base64";break;case"binary":r.type="string";break;case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");case"buffer":case"file":r.type=a;break;default:throw new Error("Unrecognized type "+t.type)}var n=e.FullPaths?de.write(e,{fileType:"zip",type:{nodebuffer:"buffer",string:"binary"}[r.type]||r.type,compression:!!t.compression}):e.generate(r);if(typeof Deno<"u"&&typeof n=="string"){if(t.type=="binary"||t.type=="base64")return n;n=new Uint8Array(za(n))}return t.password&&typeof encrypt_agile<"u"?ko(encrypt_agile(n,t.password),t):t.type==="file"?Ka(t.file,n):t.type=="string"?be(n):n}function x_(e,t){var r=t||{},a=Cg(e,r);return ko(a,r)}function rt(e,t,r){r||(r="");var a=r+e;switch(t.type){case"base64":return Na(tt(a));case"binary":return tt(a);case"string":return e;case"file":return Ka(t.file,a,"utf8");case"buffer":return Se?dt(a,"utf8"):typeof TextEncoder<"u"?new TextEncoder().encode(a):rt(a,{type:"binary"}).split("").map(function(n){return n.charCodeAt(0)})}throw new Error("Unrecognized type "+t.type)}function A_(e,t){switch(t.type){case"base64":return Na(e);case"binary":return e;case"string":return e;case"file":return Ka(t.file,e,"binary");case"buffer":return Se?dt(e,"binary"):e.split("").map(function(r){return r.charCodeAt(0)})}throw new Error("Unrecognized type "+t.type)}function hn(e,t){switch(t.type){case"string":case"base64":case"binary":for(var r="",a=0;a<e.length;++a)r+=String.fromCharCode(e[a]);return t.type=="base64"?Na(r):t.type=="string"?be(r):r;case"file":return Ka(t.file,e);case"buffer":return e;default:throw new Error("Unrecognized type "+t.type)}}function Eo(e,t){An(),eo(e);var r=Be(t||{});if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),r.type=="array"){r.type="binary";var a=Eo(e,r);return r.type="array",za(a)}return y_(e,r)}function Zi(e,t){An(),eo(e);var r=Be(t||{});if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),r.type=="array"){r.type="binary";var a=Zi(e,r);return r.type="array",za(a)}var n=0;if(r.sheet&&(typeof r.sheet=="number"?n=r.sheet:n=e.SheetNames.indexOf(r.sheet),!e.SheetNames[n]))throw new Error("Sheet not found: "+r.sheet+" : "+typeof r.sheet);switch(r.bookType||"xlsb"){case"xml":case"xlml":return rt(kg(e,r),r);case"slk":case"sylk":return rt(Tc.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"htm":case"html":return rt(ho(e.Sheets[e.SheetNames[n]],r),r);case"txt":return A_(xo(e.Sheets[e.SheetNames[n]],r),r);case"csv":return rt(Qi(e.Sheets[e.SheetNames[n]],r),r,"\uFEFF");case"dif":return rt(Ec.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"dbf":return hn(si.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"prn":return rt(ca.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"rtf":return rt(Ic.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"eth":return rt(Sc.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"fods":return rt(mo(e,r),r);case"wk1":return hn(Lt.sheet_to_wk1(e.Sheets[e.SheetNames[n]],r),r);case"wk3":return hn(Lt.book_to_wk3(e,r),r);case"biff2":r.biff||(r.biff=2);case"biff3":r.biff||(r.biff=3);case"biff4":return r.biff||(r.biff=4),hn(so(e,r),r);case"biff5":r.biff||(r.biff=5);case"biff8":case"xla":case"xls":return r.biff||(r.biff=8),x_(e,r);case"xlsx":case"xlsm":case"xlam":case"xlsb":case"numbers":case"ods":return S_(e,r);default:throw new Error("Unrecognized bookType |"+r.bookType+"|")}}function qi(e){if(!e.bookType){var t={xls:"biff8",htm:"html",slk:"sylk",socialcalc:"eth",Sh33tJS:"WTF"},r=e.file.slice(e.file.lastIndexOf(".")).toLowerCase();r.match(/^\.[a-z]+$/)&&(e.bookType=r.slice(1)),e.bookType=t[e.bookType]||e.bookType}}function V_(e,t,r){var a=r||{};return a.type="file",a.file=t,qi(a),Zi(e,a)}function X_(e,t,r){var a=r||{};return a.type="file",a.file=t,qi(a),Eo(e,a)}function G_(e,t,r,a){var n=r||{};n.type="file",n.file=e,qi(n),n.type="buffer";var i=a;return i instanceof Function||(i=r),Et.writeFile(e,Zi(t,n),i)}function So(e,t,r,a,n,i,s,f){var c=Ke(r),o=f.defval,l=f.raw||!Object.prototype.hasOwnProperty.call(f,"raw"),h=!0,d=n===1?[]:{};if(n!==1)if(Object.defineProperty)try{Object.defineProperty(d,"__rowNum__",{value:r,enumerable:!1})}catch{d.__rowNum__=r}else d.__rowNum__=r;if(!s||e[r])for(var v=t.s.c;v<=t.e.c;++v){var p=s?e[r][v]:e[a[v]+c];if(p===void 0||p.t===void 0){if(o===void 0)continue;i[v]!=null&&(d[i[v]]=o);continue}var u=p.v;switch(p.t){case"z":if(u==null)break;continue;case"e":u=u==0?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw new Error("unrecognized type "+p.t)}if(i[v]!=null){if(u==null)if(p.t=="e"&&u===null)d[i[v]]=null;else if(o!==void 0)d[i[v]]=o;else if(l&&u===null)d[i[v]]=null;else continue;else d[i[v]]=l&&(p.t!=="n"||p.t==="n"&&f.rawNumbers!==!1)?u:nt(p,u,f);u!=null&&(h=!1)}}return{row:d,isempty:h}}function yn(e,t){if(e==null||e["!ref"]==null)return[];var r={t:"n",v:0},a=0,n=1,i=[],s=0,f="",c={s:{r:0,c:0},e:{r:0,c:0}},o=t||{},l=o.range!=null?o.range:e["!ref"];switch(o.header===1?a=1:o.header==="A"?a=2:Array.isArray(o.header)?a=3:o.header==null&&(a=0),typeof l){case"string":c=Ce(l);break;case"number":c=Ce(e["!ref"]),c.s.r=l;break;default:c=l}a>0&&(n=0);var h=Ke(c.s.r),d=[],v=[],p=0,u=0,m=Array.isArray(e),k=c.s.r,A=0,_={};m&&!e[k]&&(e[k]=[]);var I=o.skipHidden&&e["!cols"]||[],D=o.skipHidden&&e["!rows"]||[];for(A=c.s.c;A<=c.e.c;++A)if(!(I[A]||{}).hidden)switch(d[A]=He(A),r=m?e[k][A]:e[d[A]+h],a){case 1:i[A]=A-c.s.c;break;case 2:i[A]=d[A];break;case 3:i[A]=o.header[A-c.s.c];break;default:if(r==null&&(r={w:"__EMPTY",t:"s"}),f=s=nt(r,null,o),u=_[s]||0,!u)_[s]=1;else{do f=s+"_"+u++;while(_[f]);_[s]=u,_[f]=1}i[A]=f}for(k=c.s.r+n;k<=c.e.r;++k)if(!(D[k]||{}).hidden){var P=So(e,c,k,d,a,i,m,o);(P.isempty===!1||(a===1?o.blankrows!==!1:o.blankrows))&&(v[p++]=P.row)}return v.length=p,v}var df=/"/g;function yo(e,t,r,a,n,i,s,f){for(var c=!0,o=[],l="",h=Ke(r),d=t.s.c;d<=t.e.c;++d)if(a[d]){var v=f.dense?(e[r]||[])[d]:e[a[d]+h];if(v==null)l="";else if(v.v!=null){c=!1,l=""+(f.rawNumbers&&v.t=="n"?v.v:nt(v,null,f));for(var p=0,u=0;p!==l.length;++p)if((u=l.charCodeAt(p))===n||u===i||u===34||f.forceQuotes){l='"'+l.replace(df,'""')+'"';break}l=="ID"&&(l='"ID"')}else v.f!=null&&!v.F?(c=!1,l="="+v.f,l.indexOf(",")>=0&&(l='"'+l.replace(df,'""')+'"')):l="";o.push(l)}return f.blankrows===!1&&c?null:o.join(s)}function Qi(e,t){var r=[],a=t??{};if(e==null||e["!ref"]==null)return"";var n=Ce(e["!ref"]),i=a.FS!==void 0?a.FS:",",s=i.charCodeAt(0),f=a.RS!==void 0?a.RS:`
`,c=f.charCodeAt(0),o=new RegExp((i=="|"?"\\|":i)+"+$"),l="",h=[];a.dense=Array.isArray(e);for(var d=a.skipHidden&&e["!cols"]||[],v=a.skipHidden&&e["!rows"]||[],p=n.s.c;p<=n.e.c;++p)(d[p]||{}).hidden||(h[p]=He(p));for(var u=0,m=n.s.r;m<=n.e.r;++m)(v[m]||{}).hidden||(l=yo(e,n,m,h,s,c,i,a),l!=null&&(a.strip&&(l=l.replace(o,"")),(l||a.blankrows!==!1)&&r.push((u++?f:"")+l)));return delete a.dense,r.join("")}function xo(e,t){t||(t={}),t.FS="	",t.RS=`
`;var r=Qi(e,t);if(typeof Ie>"u"||t.type=="string")return r;var a=Ie.utils.encode(1200,r,"str");return"ÿþ"+a}function C_(e){var t="",r,a="";if(e==null||e["!ref"]==null)return[];var n=Ce(e["!ref"]),i="",s=[],f,c=[],o=Array.isArray(e);for(f=n.s.c;f<=n.e.c;++f)s[f]=He(f);for(var l=n.s.r;l<=n.e.r;++l)for(i=Ke(l),f=n.s.c;f<=n.e.c;++f)if(t=s[f]+i,r=o?(e[l]||[])[f]:e[t],a="",r!==void 0){if(r.F!=null){if(t=r.F,!r.f)continue;a=r.f,t.indexOf(":")==-1&&(t=t+":"+t)}if(r.f!=null)a=r.f;else{if(r.t=="z")continue;if(r.t=="n"&&r.v!=null)a=""+r.v;else if(r.t=="b")a=r.v?"TRUE":"FALSE";else if(r.w!==void 0)a="'"+r.w;else{if(r.v===void 0)continue;r.t=="s"?a="'"+r.v:a=""+r.v}}c[c.length]=t+"="+a}return c}function Ao(e,t,r){var a=r||{},n=+!a.skipHeader,i=e||{},s=0,f=0;if(i&&a.origin!=null)if(typeof a.origin=="number")s=a.origin;else{var c=typeof a.origin=="string"?ze(a.origin):a.origin;s=c.r,f=c.c}var o,l={s:{c:0,r:0},e:{c:f,r:s+t.length-1+n}};if(i["!ref"]){var h=Ce(i["!ref"]);l.e.c=Math.max(l.e.c,h.e.c),l.e.r=Math.max(l.e.r,h.e.r),s==-1&&(s=h.e.r+1,l.e.r=s+t.length-1+n)}else s==-1&&(s=0,l.e.r=t.length-1+n);var d=a.header||[],v=0;t.forEach(function(u,m){je(u).forEach(function(k){(v=d.indexOf(k))==-1&&(d[v=d.length]=k);var A=u[k],_="z",I="",D=me({c:f+v,r:s+m+n});o=Ga(i,D),A&&typeof A=="object"&&!(A instanceof Date)?i[D]=A:(typeof A=="number"?_="n":typeof A=="boolean"?_="b":typeof A=="string"?_="s":A instanceof Date?(_="d",a.cellDates||(_="n",A=ir(A)),I=a.dateNF||ve[14]):A===null&&a.nullError&&(_="e",A=0),o?(o.t=_,o.v=A,delete o.w,delete o.R,I&&(o.z=I)):i[D]=o={t:_,v:A},I&&(o.z=I))})}),l.e.c=Math.max(l.e.c,f+d.length-1);var p=Ke(s);if(n)for(v=0;v<d.length;++v)i[He(v+f)+p]={t:"s",v:d[v]};return i["!ref"]=ke(l),i}function O_(e,t){return Ao(null,e,t)}function Ga(e,t,r){if(typeof t=="string"){if(Array.isArray(e)){var a=ze(t);return e[a.r]||(e[a.r]=[]),e[a.r][a.c]||(e[a.r][a.c]={t:"z"})}return e[t]||(e[t]={t:"z"})}return typeof t!="number"?Ga(e,me(t)):Ga(e,me({r:t,c:r||0}))}function I_(e,t){if(typeof t=="number"){if(t>=0&&e.SheetNames.length>t)return t;throw new Error("Cannot find sheet # "+t)}else if(typeof t=="string"){var r=e.SheetNames.indexOf(t);if(r>-1)return r;throw new Error("Cannot find sheet name |"+t+"|")}else throw new Error("Cannot find sheet |"+t+"|")}function es(){return{SheetNames:[],Sheets:{}}}function rs(e,t,r,a){var n=1;if(!r)for(;n<=65535&&e.SheetNames.indexOf(r="Sheet"+n)!=-1;++n,r=void 0);if(!r||e.SheetNames.length>=65535)throw new Error("Too many worksheets");if(a&&e.SheetNames.indexOf(r)>=0){var i=r.match(/(^.*?)(\d+)$/);n=i&&+i[2]||0;var s=i&&i[1]||r;for(++n;n<=65535&&e.SheetNames.indexOf(r=s+n)!=-1;++n);}if(Qc(r),e.SheetNames.indexOf(r)>=0)throw new Error("Worksheet with name |"+r+"| already exists!");return e.SheetNames.push(r),e.Sheets[r]=t,r}function F_(e,t,r){e.Workbook||(e.Workbook={}),e.Workbook.Sheets||(e.Workbook.Sheets=[]);var a=I_(e,t);switch(e.Workbook.Sheets[a]||(e.Workbook.Sheets[a]={}),r){case 0:case 1:case 2:break;default:throw new Error("Bad sheet visibility setting "+r)}e.Workbook.Sheets[a].Hidden=r}function R_(e,t){return e.z=t,e}function Co(e,t,r){return t?(e.l={Target:t},r&&(e.l.Tooltip=r)):delete e.l,e}function N_(e,t,r){return Co(e,"#"+t,r)}function P_(e,t,r){e.c||(e.c=[]),e.c.push({t,a:r||"SheetJS"})}function D_(e,t,r,a){for(var n=typeof t!="string"?t:Ce(t),i=typeof t=="string"?t:ke(t),s=n.s.r;s<=n.e.r;++s)for(var f=n.s.c;f<=n.e.c;++f){var c=Ga(e,s,f);c.t="n",c.F=i,delete c.v,s==n.s.r&&f==n.s.c&&(c.f=r,a&&(c.D=!0))}return e}var z_={encode_col:He,encode_row:Ke,encode_cell:me,encode_range:ke,decode_col:Oi,decode_row:Ci,split_cell:e1,decode_cell:ze,decode_range:Ar,format_cell:nt,sheet_add_aoa:Gf,sheet_add_json:Ao,sheet_add_dom:uo,aoa_to_sheet:ua,json_to_sheet:O_,table_to_sheet:vo,table_to_book:zg,sheet_to_csv:Qi,sheet_to_txt:xo,sheet_to_json:yn,sheet_to_html:ho,sheet_to_formulae:C_,sheet_to_row_object_array:yn,sheet_get_cell:Ga,book_new:es,book_append_sheet:rs,book_set_sheet_visibility:F_,cell_set_number_format:R_,cell_set_hyperlink:Co,cell_set_internal_link:N_,cell_add_comment:P_,sheet_set_array_formula:D_,consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}},Ln;function L_(e){Ln=e}function b_(e,t){var r=Ln(),a=t??{};if(e==null||e["!ref"]==null)return r.push(null),r;var n=Ce(e["!ref"]),i=a.FS!==void 0?a.FS:",",s=i.charCodeAt(0),f=a.RS!==void 0?a.RS:`
`,c=f.charCodeAt(0),o=new RegExp((i=="|"?"\\|":i)+"+$"),l="",h=[];a.dense=Array.isArray(e);for(var d=a.skipHidden&&e["!cols"]||[],v=a.skipHidden&&e["!rows"]||[],p=n.s.c;p<=n.e.c;++p)(d[p]||{}).hidden||(h[p]=He(p));var u=n.s.r,m=!1,k=0;return r._read=function(){if(!m)return m=!0,r.push("\uFEFF");for(;u<=n.e.r;)if(++u,!(v[u-1]||{}).hidden&&(l=yo(e,n,u-1,h,s,c,i,a),l!=null&&(a.strip&&(l=l.replace(o,"")),l||a.blankrows!==!1)))return r.push((k++?f:"")+l);return r.push(null)},r}function M_(e,t){var r=Ln(),a=t||{},n=a.header!=null?a.header:co,i=a.footer!=null?a.footer:oo;r.push(n);var s=Ar(e["!ref"]);a.dense=Array.isArray(e),r.push(lo(e,s,a));var f=s.s.r,c=!1;return r._read=function(){if(f>s.e.r)return c||(c=!0,r.push("</table>"+i)),r.push(null);for(;f<=s.e.r;){r.push(fo(e,s,f,a)),++f;break}},r}function B_(e,t){var r=Ln({objectMode:!0});if(e==null||e["!ref"]==null)return r.push(null),r;var a={t:"n",v:0},n=0,i=1,s=[],f=0,c="",o={s:{r:0,c:0},e:{r:0,c:0}},l=t||{},h=l.range!=null?l.range:e["!ref"];switch(l.header===1?n=1:l.header==="A"?n=2:Array.isArray(l.header)&&(n=3),typeof h){case"string":o=Ce(h);break;case"number":o=Ce(e["!ref"]),o.s.r=h;break;default:o=h}n>0&&(i=0);var d=Ke(o.s.r),v=[],p=0,u=Array.isArray(e),m=o.s.r,k=0,A={};u&&!e[m]&&(e[m]=[]);var _=l.skipHidden&&e["!cols"]||[],I=l.skipHidden&&e["!rows"]||[];for(k=o.s.c;k<=o.e.c;++k)if(!(_[k]||{}).hidden)switch(v[k]=He(k),a=u?e[m][k]:e[v[k]+d],n){case 1:s[k]=k-o.s.c;break;case 2:s[k]=v[k];break;case 3:s[k]=l.header[k-o.s.c];break;default:if(a==null&&(a={w:"__EMPTY",t:"s"}),c=f=nt(a,null,l),p=A[f]||0,!p)A[f]=1;else{do c=f+"_"+p++;while(A[c]);A[f]=p,A[c]=1}s[k]=c}return m=o.s.r+i,r._read=function(){for(;m<=o.e.r;)if(!(I[m-1]||{}).hidden){var D=So(e,o,m,v,n,s,u,l);if(++m,D.isempty===!1||(n===1?l.blankrows!==!1:l.blankrows)){r.push(D.row);return}}return r.push(null)},r}var $_={to_json:B_,to_html:M_,to_csv:b_,set_readable:L_};const K_=Ra.version;export{de as CFB,ml as SSF,io as parse_xlscfb,v_ as parse_zip,li as read,H_ as readFile,H_ as readFileSync,U_ as set_cptable,W_ as set_fs,$_ as stream,z_ as utils,K_ as version,Zi as write,V_ as writeFile,G_ as writeFileAsync,V_ as writeFileSync,X_ as writeFileXLSX,Eo as writeXLSX};
