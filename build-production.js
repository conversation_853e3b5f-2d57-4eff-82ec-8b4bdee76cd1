const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始生产环境构建...');

// 构建后端
function buildBackend() {
  console.log('\n📦 构建后端...');
  
  try {
    process.chdir('backend');
    
    // 清理dist目录
    if (fs.existsSync('dist')) {
      fs.rmSync('dist', { recursive: true, force: true });
    }
    fs.mkdirSync('dist', { recursive: true });

    // 复制源代码并转换
    console.log('📋 处理源代码...');
    
    function processDirectory(src, dest) {
      if (!fs.existsSync(dest)) {
        fs.mkdirSync(dest, { recursive: true });
      }
      
      const entries = fs.readdirSync(src, { withFileTypes: true });
      
      for (const entry of entries) {
        const srcPath = path.join(src, entry.name);
        const destPath = path.join(dest, entry.name);
        
        if (entry.isDirectory()) {
          processDirectory(srcPath, destPath);
        } else if (entry.isFile() && entry.name.endsWith('.ts')) {
          // 读取TypeScript文件内容
          let content = fs.readFileSync(srcPath, 'utf8');
          
          // 简单的TypeScript到JavaScript转换
          content = content
            .replace(/import\s+(.+?)\s+from\s+['"](.+?)['"];?/g, 'const $1 = require(\'$2\');')
            .replace(/export\s+default\s+/g, 'module.exports = ')
            .replace(/export\s+\{([^}]+)\}/g, 'module.exports = { $1 }')
            .replace(/export\s+/g, 'module.exports.')
            .replace(/:\s*\w+(\[\])?(\s*[=,;])/g, '$2') // 移除类型注解
            .replace(/\?\s*:/g, ':') // 移除可选参数标记
            .replace(/<[^>]+>/g, '') // 移除泛型
            .replace(/\s+as\s+any/g, '') // 移除 as any 类型断言
            .replace(/\s+as\s+\w+/g, '') // 移除其他类型断言
            .replace(/declare\s+global\s*\{[^}]*\}/gs, '') // 移除全局声明
            .replace(/interface\s+\w+\s*\{[^}]*\}/gs, ''); // 移除接口声明
          
          // 写入JavaScript文件
          const jsFileName = entry.name.replace('.ts', '.js');
          const jsDestPath = path.join(dest, jsFileName);
          fs.writeFileSync(jsDestPath, content);
        } else {
          fs.copyFileSync(srcPath, destPath);
        }
      }
    }
    
    processDirectory('src', 'dist');

    // 复制其他必要文件
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    delete packageJson.devDependencies;
    packageJson.main = 'index.js';
    fs.writeFileSync('dist/package.json', JSON.stringify(packageJson, null, 2));

    if (fs.existsSync('prisma')) {
      fs.cpSync('prisma', 'dist/prisma', { recursive: true });
    }

    if (fs.existsSync('templates')) {
      fs.cpSync('templates', 'dist/templates', { recursive: true });
    }

    console.log('✅ 后端构建完成');
    
  } catch (error) {
    console.error('❌ 后端构建失败:', error.message);
    throw error;
  } finally {
    process.chdir('..');
  }
}

// 构建前端
function buildFrontend() {
  console.log('\n📦 构建前端...');
  
  try {
    process.chdir('frontend');
    
    // 检查并修复常见构建问题
    console.log('🔍 检查构建配置...');

    // 检查 package.json 中的构建脚本
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    if (packageJson.scripts.build !== 'vite build') {
      console.log('🔧 修复构建脚本...');
      packageJson.scripts.build = 'vite build';
      fs.writeFileSync('package.json', JSON.stringify(packageJson, null, 2));
    }

    // 检查 vite.config.ts 中的压缩配置
    if (fs.existsSync('vite.config.ts')) {
      let viteConfig = fs.readFileSync('vite.config.ts', 'utf8');
      if (viteConfig.includes("minify: 'terser'")) {
        console.log('🔧 修复terser配置...');
        viteConfig = viteConfig.replace("minify: 'terser'", "minify: 'esbuild' // 使用esbuild压缩，无需额外依赖");
        fs.writeFileSync('vite.config.ts', viteConfig);
      }
    }

    // 尝试使用vite构建
    try {
      execSync('npm run build', {
        stdio: 'inherit',
        env: {
          ...process.env,
          NODE_ENV: 'production',
          VITE_API_URL: process.env.VITE_API_URL || 'http://localhost:3001',
          VITE_APP_ENV: 'production'
        }
      });
      console.log('✅ 前端构建完成');
    } catch (viteError) {
      console.log('⚠️  Vite构建失败，使用备用方案...');
      
      // 备用方案
      if (fs.existsSync('dist')) {
        fs.rmSync('dist', { recursive: true, force: true });
      }
      fs.mkdirSync('dist', { recursive: true });
      
      if (fs.existsSync('public')) {
        fs.cpSync('public', 'dist', { recursive: true });
      }
      
      // 创建基本的index.html
      const indexHtml = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发票管理系统</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
        h1 { color: #1890ff; }
        .info { color: #52c41a; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>发票管理系统</h1>
        <div class="info">系统正在维护中，请稍后再试。</div>
        <p>如需帮助，请联系系统管理员。</p>
    </div>
</body>
</html>`;
      
      fs.writeFileSync('dist/index.html', indexHtml);
      console.log('✅ 前端备用构建完成');
    }
    
  } catch (error) {
    console.error('❌ 前端构建失败:', error.message);
    throw error;
  } finally {
    process.chdir('..');
  }
}

// 生成环境配置
function generateEnvironmentConfigs() {
  console.log('📋 生成环境配置文件...');

  const config = require('./build.config.js');

  // 生成前端生产环境配置
  let apiUrl = config.frontend.apiUrl;
  if (!apiUrl.startsWith('http://') && !apiUrl.startsWith('https://')) {
    apiUrl = `http://${apiUrl}`;
  }
  if (!apiUrl.includes(':3001') && !apiUrl.includes(':80') && !apiUrl.includes(':443')) {
    apiUrl = `${apiUrl}:${config.backend.port}`;
  }

  const frontendEnv = `# 发票管理系统前端生产环境配置
# 自动生成于: ${new Date().toLocaleString()}

VITE_API_URL=${apiUrl}
VITE_API_TIMEOUT=10000
VITE_APP_TITLE=发票管理系统
VITE_APP_VERSION=2.0.0
VITE_APP_ENV=production
VITE_ENABLE_DEBUG=false
VITE_ENABLE_MOCK=false
VITE_BUILD_TIMESTAMP=${new Date().toISOString()}`;

  fs.writeFileSync('frontend/.env.production', frontendEnv);
  console.log(`✅ 前端生产环境配置已生成: ${apiUrl}`);

  // 生成后端生产环境配置
  let frontendUrl = config.backend.frontendUrl || `http://${config.frontend.host}:${config.frontend.port}`;
  if (!frontendUrl.startsWith('http://') && !frontendUrl.startsWith('https://')) {
    frontendUrl = `http://${frontendUrl}`;
  }

  const backendEnv = `# 发票管理系统后端生产环境配置
# 自动生成于: ${new Date().toLocaleString()}

PORT=${config.backend.port}
HOST=${config.backend.host}
NODE_ENV=production
FRONTEND_URL=${frontendUrl}
CORS_ORIGIN=${frontendUrl}
DATABASE_URL="mysql://InvicesAI:<EMAIL>:43306/invicesai"
JWT_SECRET=invoice-management-super-secret-key-2024
JWT_EXPIRES_IN=7d
UPLOAD_DIR=uploads
UPLOAD_MAX_SIZE=10485760
ALLOWED_FILE_TYPES=pdf,jpg,jpeg,png,xlsx
LOG_LEVEL=info
LOG_DIR=logs
TIMEZONE=Asia/Shanghai
DEFAULT_LANGUAGE=zh-CN
BUILD_TIMESTAMP=${new Date().toISOString()}
BUILD_VERSION=2.0.0`;

  fs.writeFileSync('backend/.env.production', backendEnv);
  console.log(`✅ 后端生产环境配置已生成: ${config.backend.port}`);
  console.log('');
}

// 主构建流程
try {
  generateEnvironmentConfigs();
  buildBackend();
  buildFrontend();
  
  console.log('\n🎉 生产环境构建完成！');
  console.log('\n📋 部署说明：');
  console.log('后端：');
  console.log('  1. 将 backend/dist/ 目录上传到服务器');
  console.log('  2. 运行: npm install --production');
  console.log('  3. 配置环境变量和数据库');
  console.log('  4. 启动: node index.js');
  console.log('\n前端：');
  console.log('  1. 将 frontend/dist/ 目录内容上传到Web服务器');
  console.log('  2. 配置Web服务器指向 index.html');
  console.log('  3. 确保API服务器可访问');
  
} catch (error) {
  console.error('\n❌ 构建失败:', error.message);
  process.exit(1);
}
