const mysql = require('mysql2/promise');
require('dotenv').config();

async function checkTables() {
  let connection;
  
  try {
    // 从环境变量解析数据库连接信息
    const databaseUrl = process.env.DATABASE_URL;
    if (!databaseUrl) {
      throw new Error('DATABASE_URL environment variable is not set');
    }

    // 解析数据库URL
    const url = new URL(databaseUrl);
    const connectionConfig = {
      host: url.hostname,
      port: parseInt(url.port) || 3306,
      user: url.username,
      password: url.password,
      database: url.pathname.slice(1), // 移除开头的 '/'
    };

    console.log('正在连接数据库...');
    console.log(`主机: ${connectionConfig.host}:${connectionConfig.port}`);
    console.log(`数据库: ${connectionConfig.database}`);
    
    // 创建数据库连接
    connection = await mysql.createConnection(connectionConfig);
    console.log('数据库连接成功！');

    // 查询所有表
    const [tables] = await connection.execute('SHOW TABLES');
    
    console.log('\n当前数据库中的表：');
    console.log('='.repeat(50));
    
    tables.forEach((table, index) => {
      const tableName = Object.values(table)[0];
      console.log(`${index + 1}. ${tableName}`);
    });
    
    console.log('='.repeat(50));
    console.log(`总共 ${tables.length} 个表`);
    
    // 检查特定的取得发票相关表
    const expectedTables = [
      'received_invoices',
      'received_invoice_items', 
      'received_invoice_attachments',
      'received_invoice_audit_logs'
    ];
    
    console.log('\n取得发票相关表检查：');
    console.log('-'.repeat(30));
    
    for (const expectedTable of expectedTables) {
      const exists = tables.some(table => Object.values(table)[0] === expectedTable);
      console.log(`${expectedTable}: ${exists ? '✓ 存在' : '✗ 不存在'}`);
    }
    
  } catch (error) {
    console.error('❌ 执行失败:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n数据库连接已关闭');
    }
  }
}

// 执行脚本
checkTables();
