const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始构建后端项目...');

try {
  // 1. 清理dist目录
  console.log('📁 清理构建目录...');
  if (fs.existsSync('dist')) {
    fs.rmSync('dist', { recursive: true, force: true });
  }

  // 2. 编译TypeScript
  console.log('🔨 编译TypeScript...');
  execSync('npx tsc', { stdio: 'inherit' });

  // 3. 复制必要的文件
  console.log('📋 复制必要文件...');
  
  // 复制package.json
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  // 移除开发依赖
  delete packageJson.devDependencies;
  fs.writeFileSync('dist/package.json', JSON.stringify(packageJson, null, 2));

  // 复制prisma目录
  if (fs.existsSync('prisma')) {
    fs.cpSync('prisma', 'dist/prisma', { recursive: true });
  }

  // 复制templates目录
  if (fs.existsSync('templates')) {
    fs.cpSync('templates', 'dist/templates', { recursive: true });
  }

  // 复制环境配置文件
  if (fs.existsSync('.env.production')) {
    fs.copyFileSync('.env.production', 'dist/.env.production');
    console.log('📋 复制生产环境配置文件...');
  }

  if (fs.existsSync('.env')) {
    fs.copyFileSync('.env', 'dist/.env');
    console.log('📋 复制开发环境配置文件...');
  }

  // 4. 生成Prisma客户端
  console.log('🔧 生成Prisma客户端...');
  execSync('cd dist && npx prisma generate', { stdio: 'inherit' });

  console.log('✅ 后端构建完成！');
  console.log('📦 构建文件位于 dist/ 目录');
  console.log('');
  console.log('📁 构建内容包括:');
  console.log('   ✅ 编译后的JavaScript文件');
  console.log('   ✅ package.json (已移除开发依赖)');
  console.log('   ✅ Prisma配置和客户端');
  console.log('   ✅ 模板文件 (templates/)');
  console.log('   ✅ 环境配置文件 (.env, .env.production)');
  console.log('');
  console.log('🚀 部署说明：');
  console.log('   1. 将 dist/ 目录上传到服务器');
  console.log('   2. 在服务器上运行: npm install --production');
  console.log('   3. 配置环境变量（数据库连接等）');
  console.log('   4. 运行数据库迁移: npx prisma migrate deploy');
  console.log('   5. 启动服务选择:');
  console.log('      - 直接启动: npm start');
} catch (error) {
  console.error('❌ 构建失败:', error.message);
  process.exit(1);
}
