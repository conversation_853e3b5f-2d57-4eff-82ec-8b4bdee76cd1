const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addNewMenus() {
  try {
    // 查找报表中心的父菜单ID
    const reportsParent = await prisma.$queryRaw`
      SELECT id FROM menus WHERE \`key\` = 'reports' LIMIT 1
    `;
    
    if (!reportsParent || reportsParent.length === 0) {
      console.error('找不到报表中心父菜单');
      return;
    }
    
    const parentId = reportsParent[0].id;
    console.log('报表中心父菜单ID:', parentId);

    // 添加发票张数汇总菜单
    await prisma.$executeRaw`
      INSERT INTO menus (\`key\`, name, path, icon, parentId, sort, isActive, description, createdAt, updatedAt)
      VALUES (
        'reports-invoice-count-summary',
        '发票张数汇总',
        '/reports/invoice-count-summary',
        NULL,
        ${parentId},
        30,
        1,
        '按公司和季度统计发票张数',
        NOW(),
        NOW()
      )
    `;
    console.log('✅ 发票张数汇总菜单添加成功');

    // 添加所属用户开票汇总菜单
    await prisma.$executeRaw`
      INSERT INTO menus (\`key\`, name, path, icon, parentId, sort, isActive, description, createdAt, updatedAt)
      VALUES (
        'reports-user-summary',
        '所属用户开票汇总',
        '/reports/user-summary',
        NULL,
        ${parentId},
        40,
        1,
        '按所属用户统计开票金额汇总',
        NOW(),
        NOW()
      )
    `;
    console.log('✅ 所属用户开票汇总菜单添加成功');

    // 查看添加的菜单
    const newMenus = await prisma.$queryRaw`
      SELECT id, \`key\`, name, path, parentId, sort, isActive, description 
      FROM menus 
      WHERE \`key\` IN ('reports-invoice-count-summary', 'reports-user-summary')
      ORDER BY sort
    `;
    
    console.log('新添加的菜单:');
    console.table(newMenus);

  } catch (error) {
    console.error('添加菜单失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

addNewMenus();
