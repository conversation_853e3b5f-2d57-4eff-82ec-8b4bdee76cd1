"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.auditLogger = void 0;
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
const auditLogger = async (req, res, next) => {
    // 只记录API路由的操作
    if (!req.path.startsWith('/api/')) {
        return next();
    }
    // 跳过健康检查和认证路由
    if (req.path === '/api/health' || req.path.startsWith('/api/auth/login')) {
        return next();
    }
    const originalSend = res.send;
    let responseBody;
    // 拦截响应
    res.send = function (body) {
        responseBody = body;
        return originalSend.call(this, body);
    };
    // 在响应结束后记录审计日志
    res.on('finish', async () => {
        try {
            // 确定操作类型
            let action = 'UNKNOWN';
            switch (req.method) {
                case 'GET':
                    action = 'VIEW';
                    break;
                case 'POST':
                    action = 'CREATE';
                    break;
                case 'PUT':
                case 'PATCH':
                    action = 'UPDATE';
                    break;
                case 'DELETE':
                    action = 'DELETE';
                    break;
            }
            // 提取表名和记录ID
            const pathParts = req.path.split('/');
            let tableName = 'unknown';
            let recordId = null;
            if (pathParts.length >= 3) {
                tableName = pathParts[2]; // /api/companies -> companies
                if (pathParts.length >= 4 && pathParts[3]) {
                    recordId = pathParts[3]; // /api/companies/123 -> 123
                }
            }
            // 只记录成功的操作（状态码 < 400） - 临时禁用以解决连接池问题
            /*
            if (res.statusCode < 400) {
              await prisma.auditLog.create({
                data: {
                  action,
                  tableName,
                  recordId: recordId || 'unknown',
                  newValues: req.method !== 'GET' ? req.body : null,
                  ipAddress: req.ip || req.connection?.remoteAddress || 'unknown',
                  userAgent: req.get('User-Agent') || 'unknown',
                  userId: req.user?.id || 'system',
                },
              });
            }
            */
        }
        catch (error) {
            console.error('Failed to create audit log:', error);
            // 不影响主要业务流程
        }
    });
    next();
};
exports.auditLogger = auditLogger;
//# sourceMappingURL=auditLogger.js.map