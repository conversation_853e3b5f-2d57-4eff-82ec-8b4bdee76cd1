"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const client_1 = require("@prisma/client");
const errorHandler_1 = require("../middleware/errorHandler");
const auth_1 = require("../middleware/auth");
const joi_1 = __importDefault(require("joi"));
const router = express_1.default.Router();
const prisma = new client_1.PrismaClient();
// 应用认证中间件到所有路由
router.use(auth_1.authenticate);
// 验证查询参数的schema
const dashboardQuerySchema = joi_1.default.object({
    companyId: joi_1.default.string().optional(),
    year: joi_1.default.number().integer().min(2000).max(2100).optional(),
});
// 获取仪表板统计数据
router.get('/stats', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    // 验证查询参数
    const { error, value } = dashboardQuerySchema.validate(req.query);
    if (error) {
        throw new errorHandler_1.AppError(error.details[0].message, 400);
    }
    const { companyId, year } = value;
    // 构建基础查询条件
    const baseWhere = {};
    // 如果是普通用户，只能查看有权限的公司数据
    if (req.user?.role !== 'ADMIN') {
        const userCompanyIds = await (0, auth_1.getUserCompanyIds)(req.user.id);
        if (userCompanyIds.length === 0) {
            // 用户没有任何公司权限，返回空结果
            return res.json({
                success: true,
                data: {
                    overview: {
                        totalCompanies: 0,
                        totalInvoices: 0,
                        totalAmount: 0,
                        normalInvoices: 0,
                        cancelledInvoices: 0,
                        unverifiedInvoices: 0,
                    },
                    recentInvoices: [],
                    monthlyStats: [],
                    distributions: {
                        status: [],
                        type: [],
                        verification: [],
                    },
                    topCompanies: [],
                },
            });
        }
        baseWhere.companyId = { in: userCompanyIds };
    }
    else if (companyId) {
        baseWhere.companyId = companyId;
    }
    // 如果指定了年度，添加年度过滤条件
    if (year) {
        const yearStart = new Date(year, 0, 1); // 年初
        const yearEnd = new Date(year, 11, 31, 23, 59, 59); // 年末
        baseWhere.invoiceDate = {
            gte: yearStart,
            lte: yearEnd,
        };
    }
    // 基础统计
    let totalCompanies = 0;
    if (req.user?.role === 'ADMIN') {
        totalCompanies = await prisma.company.count({
            where: { isActive: true },
        });
    }
    else {
        // 普通用户只统计有权限的公司数量
        const userCompanyIds = await (0, auth_1.getUserCompanyIds)(req.user.id);
        totalCompanies = userCompanyIds.length;
    }
    const totalInvoices = await prisma.invoice.count({
        where: baseWhere,
    });
    const totalAmount = await prisma.invoice.aggregate({
        where: baseWhere,
        _sum: { totalAmount: true, taxAmount: true },
    });
    const normalInvoices = await prisma.invoice.count({
        where: { ...baseWhere, status: 'NORMAL' },
    });
    const cancelledInvoices = await prisma.invoice.count({
        where: { ...baseWhere, status: 'CANCELLED' },
    });
    const unverifiedInvoices = await prisma.invoice.count({
        where: { ...baseWhere, verificationStatus: 'UNVERIFIED' },
    });
    // 本月统计
    const currentMonth = new Date();
    const monthStart = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
    const monthEnd = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0, 23, 59, 59);
    const monthlyInvoices = await prisma.invoice.count({
        where: {
            ...baseWhere,
            invoiceDate: {
                gte: monthStart,
                lte: monthEnd,
            },
        },
    });
    const monthlyAmount = await prisma.invoice.aggregate({
        where: {
            ...baseWhere,
            invoiceDate: {
                gte: monthStart,
                lte: monthEnd,
            },
        },
        _sum: { totalAmount: true },
    });
    // 上月统计（用于计算增长率）
    const lastMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1);
    const lastMonthEnd = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 0, 23, 59, 59);
    const lastMonthInvoices = await prisma.invoice.count({
        where: {
            ...baseWhere,
            invoiceDate: {
                gte: lastMonth,
                lte: lastMonthEnd,
            },
        },
    });
    const lastMonthAmount = await prisma.invoice.aggregate({
        where: {
            ...baseWhere,
            invoiceDate: {
                gte: lastMonth,
                lte: lastMonthEnd,
            },
        },
        _sum: { totalAmount: true },
    });
    // 计算增长率
    const invoiceGrowthRate = lastMonthInvoices > 0
        ? ((monthlyInvoices - lastMonthInvoices) / lastMonthInvoices * 100).toFixed(1)
        : '0';
    const lastMonthAmountNum = Number(lastMonthAmount._sum.totalAmount || 0);
    const monthlyAmountNum = Number(monthlyAmount._sum.totalAmount || 0);
    const amountGrowthRate = lastMonthAmountNum > 0
        ? (((monthlyAmountNum - lastMonthAmountNum) / lastMonthAmountNum * 100)).toFixed(1)
        : '0';
    // 本周统计
    const weekStart = new Date();
    weekStart.setDate(weekStart.getDate() - weekStart.getDay());
    weekStart.setHours(0, 0, 0, 0);
    const weeklyInvoices = await prisma.invoice.count({
        where: {
            ...baseWhere,
            invoiceDate: {
                gte: weekStart,
            },
        },
    });
    // 最近发票
    const recentInvoices = await prisma.invoice.findMany({
        where: baseWhere,
        take: 10,
        orderBy: { createdAt: 'desc' },
        include: {
            company: {
                select: {
                    id: true,
                    name: true,
                    taxId: true,
                },
            },
        },
    });
    // 月度统计（最近12个月）
    const monthlyStats = await getMonthlyStats(12, companyId, year);
    // 状态分布
    const statusDistribution = await prisma.invoice.groupBy({
        by: ['status'],
        where: baseWhere,
        _count: { _all: true },
        _sum: { totalAmount: true },
    });
    // 发票类型分布
    const typeDistribution = await prisma.invoice.groupBy({
        by: ['invoiceType'],
        where: baseWhere,
        _count: { _all: true },
        _sum: { totalAmount: true },
    });
    // 查验状态分布
    const verificationDistribution = await prisma.invoice.groupBy({
        by: ['verificationStatus'],
        where: baseWhere,
        _count: { _all: true },
    });
    // 公司排行（按发票金额）
    const topCompanies = await prisma.company.findMany({
        where: { isActive: true },
        include: {
            _count: {
                select: { invoices: true },
            },
            invoices: {
                select: {
                    totalAmount: true,
                },
            },
        },
        take: 10,
    });
    const companiesWithStats = topCompanies.map(company => ({
        id: company.id,
        name: company.name,
        taxId: company.taxId,
        invoiceCount: company._count.invoices,
        totalAmount: company.invoices.reduce((sum, invoice) => sum + Number(invoice.totalAmount), 0),
    })).sort((a, b) => b.totalAmount - a.totalAmount);
    res.json({
        success: true,
        data: {
            overview: {
                totalCompanies,
                totalInvoices,
                totalAmount: totalAmount._sum.totalAmount || 0,
                totalTaxAmount: totalAmount._sum.taxAmount || 0,
                normalInvoices,
                cancelledInvoices,
                unverifiedInvoices,
                monthlyInvoices,
                monthlyAmount: monthlyAmount._sum.totalAmount || 0,
                weeklyInvoices,
                invoiceGrowthRate: parseFloat(invoiceGrowthRate),
                amountGrowthRate: parseFloat(amountGrowthRate),
            },
            recentInvoices,
            monthlyStats,
            distributions: {
                status: statusDistribution,
                type: typeDistribution,
                verification: verificationDistribution,
            },
            topCompanies: companiesWithStats,
        },
    });
}));
// 获取月度趋势数据
router.get('/trends', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const monthlyData = await getMonthlyStats(24); // 最近24个月
    // 计算同比增长率
    const trendsWithGrowth = monthlyData.map((current, index) => {
        const previousYear = monthlyData.find(item => {
            const currentDate = new Date(current.month);
            const previousYearDate = new Date(item.month);
            return previousYearDate.getFullYear() === currentDate.getFullYear() - 1 &&
                previousYearDate.getMonth() === currentDate.getMonth();
        });
        let invoiceGrowth = 0;
        let amountGrowth = 0;
        if (previousYear) {
            invoiceGrowth = previousYear.invoiceCount > 0
                ? ((current.invoiceCount - previousYear.invoiceCount) / previousYear.invoiceCount) * 100
                : 0;
            amountGrowth = previousYear.totalAmount > 0
                ? ((current.totalAmount - previousYear.totalAmount) / previousYear.totalAmount) * 100
                : 0;
        }
        return {
            ...current,
            invoiceGrowth: Math.round(invoiceGrowth * 100) / 100,
            amountGrowth: Math.round(amountGrowth * 100) / 100,
        };
    });
    res.json({
        success: true,
        data: trendsWithGrowth,
    });
}));
// 获取税务分析数据
router.get('/tax-analysis', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    // 按发票类型统计税额
    const taxByType = await prisma.invoice.groupBy({
        by: ['invoiceType'],
        _sum: {
            amount: true,
            taxAmount: true,
            totalAmount: true,
        },
        _count: { _all: true },
    });
    // 计算平均税率
    const taxAnalysis = taxByType.map(item => {
        const amount = Number(item._sum.amount || 0);
        const taxAmount = Number(item._sum.taxAmount || 0);
        const avgTaxRate = amount > 0 ? (taxAmount / amount) * 100 : 0;
        return {
            invoiceType: item.invoiceType,
            count: item._count._all,
            totalAmount: Number(item._sum.totalAmount || 0),
            taxAmount,
            avgTaxRate: Math.round(avgTaxRate * 100) / 100,
        };
    });
    // 月度税额趋势
    const monthlyTaxTrends = await getMonthlyTaxStats();
    res.json({
        success: true,
        data: {
            taxByType: taxAnalysis,
            monthlyTrends: monthlyTaxTrends,
        },
    });
}));
// 获取预警信息
router.get('/alerts', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const alerts = [];
    // 重复发票预警
    const duplicateInvoices = await prisma.invoice.count({
        where: { isDuplicate: true },
    });
    if (duplicateInvoices > 0) {
        alerts.push({
            type: 'warning',
            title: '重复发票预警',
            message: `发现 ${duplicateInvoices} 张重复发票，请及时处理`,
            count: duplicateInvoices,
        });
    }
    // 未查验发票预警
    const unverifiedCount = await prisma.invoice.count({
        where: { verificationStatus: 'UNVERIFIED' },
    });
    if (unverifiedCount > 0) {
        alerts.push({
            type: 'info',
            title: '未查验发票',
            message: `有 ${unverifiedCount} 张发票尚未查验`,
            count: unverifiedCount,
        });
    }
    // 大额发票预警（超过10万元）
    const largeAmountInvoices = await prisma.invoice.count({
        where: {
            totalAmount: { gte: 100000 },
            status: 'NORMAL',
        },
    });
    if (largeAmountInvoices > 0) {
        alerts.push({
            type: 'error',
            title: '大额发票待处理',
            message: `有 ${largeAmountInvoices} 张大额发票（≥10万元）待处理`,
            count: largeAmountInvoices,
        });
    }
    res.json({
        success: true,
        data: alerts,
    });
}));
// 辅助函数：获取月度统计数据
async function getMonthlyStats(months = 12, companyId, year) {
    let endDate = new Date();
    let startDate = new Date();
    if (year) {
        // 如果指定了年度，只显示该年度的月份
        startDate = new Date(year, 0, 1);
        endDate = new Date(year, 11, 31, 23, 59, 59);
    }
    else {
        startDate.setMonth(startDate.getMonth() - months);
    }
    const monthlyData = [];
    const current = new Date(startDate);
    while (current <= endDate) {
        const monthStart = new Date(current.getFullYear(), current.getMonth(), 1);
        const monthEnd = new Date(current.getFullYear(), current.getMonth() + 1, 0, 23, 59, 59);
        const where = {
            invoiceDate: {
                gte: monthStart,
                lte: monthEnd,
            },
        };
        if (companyId) {
            where.companyId = companyId;
        }
        const stats = await prisma.invoice.aggregate({
            where,
            _count: { _all: true },
            _sum: { totalAmount: true },
        });
        monthlyData.push({
            month: `${current.getFullYear()}-${String(current.getMonth() + 1).padStart(2, '0')}`,
            invoiceCount: stats._count._all,
            totalAmount: Number(stats._sum.totalAmount || 0),
        });
        current.setMonth(current.getMonth() + 1);
    }
    return monthlyData;
}
// 辅助函数：获取月度税额统计
async function getMonthlyTaxStats(months = 12) {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - months);
    const monthlyTaxData = [];
    const current = new Date(startDate);
    while (current <= endDate) {
        const monthStart = new Date(current.getFullYear(), current.getMonth(), 1);
        const monthEnd = new Date(current.getFullYear(), current.getMonth() + 1, 0, 23, 59, 59);
        const stats = await prisma.invoice.aggregate({
            where: {
                invoiceDate: {
                    gte: monthStart,
                    lte: monthEnd,
                },
            },
            _sum: {
                amount: true,
                taxAmount: true,
            },
        });
        const amount = Number(stats._sum.amount || 0);
        const taxAmount = Number(stats._sum.taxAmount || 0);
        const taxRate = amount > 0 ? (taxAmount / amount) * 100 : 0;
        monthlyTaxData.push({
            month: `${current.getFullYear()}-${String(current.getMonth() + 1).padStart(2, '0')}`,
            amount,
            taxAmount,
            taxRate: Math.round(taxRate * 100) / 100,
        });
        current.setMonth(current.getMonth() + 1);
    }
    return monthlyTaxData;
}
exports.default = router;
//# sourceMappingURL=dashboard.js.map