"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkResourceOwner = exports.authorize = exports.authenticate = exports.getUserCompanyIds = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const client_1 = require("@prisma/client");
const errorHandler_1 = require("./errorHandler");
const prisma = new client_1.PrismaClient();
// 获取用户有权限的公司ID列表
const getUserCompanyIds = async (userId) => {
    try {
        // 先获取用户信息
        const user = await prisma.user.findUnique({
            where: { id: userId },
            select: { role: true }
        });
        // 如果是管理员，返回所有公司ID
        if (user?.role === 'ADMIN') {
            const allCompanies = await prisma.company.findMany({
                where: { isActive: true },
                select: { id: true }
            });
            return allCompanies.map(c => c.id);
        }
        // 使用Prisma ORM查询用户公司权限
        try {
            const userCompanies = await prisma.userCompany.findMany({
                where: { userId: userId },
                select: { companyId: true }
            });
            if (userCompanies.length > 0) {
                return userCompanies.map(uc => uc.companyId);
            }
        }
        catch (tableError) {
            // 静默处理权限查询错误
        }
        // 如果用户公司权限表不存在或用户没有权限，返回空数组
        return [];
    }
    catch (error) {
        console.error('获取用户公司权限失败:', error);
        return [];
    }
};
exports.getUserCompanyIds = getUserCompanyIds;
// JWT认证中间件
const authenticate = async (req, res, next) => {
    try {
        const token = req.header('Authorization')?.replace('Bearer ', '');
        if (!token) {
            throw new errorHandler_1.AppError('访问令牌缺失', 401);
        }
        const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET);
        const user = await prisma.user.findUnique({
            where: { id: decoded.userId },
            select: {
                id: true,
                username: true,
                email: true,
                name: true,
                role: true,
                status: true,
            },
        });
        if (!user || user.status !== 'ACTIVE') {
            throw new errorHandler_1.AppError('用户不存在或已被禁用', 401);
        }
        req.user = user;
        next();
    }
    catch (error) {
        next(error);
    }
};
exports.authenticate = authenticate;
// 权限检查中间件
const authorize = (...roles) => {
    return (req, res, next) => {
        if (!req.user) {
            return next(new errorHandler_1.AppError('用户未认证', 401));
        }
        if (!roles.includes(req.user.role)) {
            return next(new errorHandler_1.AppError('权限不足', 403));
        }
        next();
    };
};
exports.authorize = authorize;
// 资源所有者检查中间件（用户只能访问自己的资源）
const checkResourceOwner = (resourceField = 'userId') => {
    return async (req, res, next) => {
        try {
            if (!req.user) {
                return next(new errorHandler_1.AppError('用户未认证', 401));
            }
            // 管理员可以访问所有资源
            if (req.user.role === 'ADMIN') {
                return next();
            }
            const resourceId = req.params.id;
            if (!resourceId) {
                return next(new errorHandler_1.AppError('资源ID缺失', 400));
            }
            // 这里需要根据具体的资源类型来检查所有权
            // 示例：检查发票是否属于当前用户的公司
            // 实际实现需要根据业务逻辑调整
            next();
        }
        catch (error) {
            next(error);
        }
    };
};
exports.checkResourceOwner = checkResourceOwner;
//# sourceMappingURL=auth.js.map