"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const client_1 = require("@prisma/client");
const joi_1 = __importDefault(require("joi"));
const errorHandler_1 = require("../middleware/errorHandler");
const auth_1 = require("../middleware/auth");
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const multer_1 = __importDefault(require("multer"));
const XLSX = __importStar(require("xlsx"));
const router = express_1.default.Router();
const prisma = new client_1.PrismaClient();
// 应用认证中间件到所有路由
router.use(auth_1.authenticate);
// 验证schemas
const createReceivedInvoiceSchema = joi_1.default.object({
    invoiceNumber: joi_1.default.string().required().messages({
        'any.required': '发票号码是必填项',
    }),
    invoiceCode: joi_1.default.string().required().messages({
        'any.required': '发票代码是必填项',
    }),
    invoiceDate: joi_1.default.date().required().messages({
        'any.required': '开票日期是必填项',
    }),
    amount: joi_1.default.number().positive().required().messages({
        'number.positive': '金额必须大于0',
        'any.required': '金额是必填项',
    }),
    taxAmount: joi_1.default.number().min(0).required().messages({
        'number.min': '税额不能为负数',
        'any.required': '税额是必填项',
    }),
    totalAmount: joi_1.default.number().positive().required().messages({
        'number.positive': '价税合计必须大于0',
        'any.required': '价税合计是必填项',
    }),
    buyerName: joi_1.default.string().required().messages({
        'any.required': '购买方名称是必填项',
    }),
    buyerTaxId: joi_1.default.string().required().messages({
        'any.required': '购买方税号是必填项',
    }),
    buyerAddress: joi_1.default.string().optional(),
    buyerPhone: joi_1.default.string().optional(),
    buyerBank: joi_1.default.string().optional(),
    sellerName: joi_1.default.string().required().messages({
        'any.required': '销售方名称是必填项',
    }),
    sellerTaxId: joi_1.default.string().required().messages({
        'any.required': '销售方税号是必填项',
    }),
    sellerAddress: joi_1.default.string().optional(),
    sellerPhone: joi_1.default.string().optional(),
    sellerBank: joi_1.default.string().optional(),
    invoiceType: joi_1.default.string().valid(...Object.values(client_1.InvoiceType)).required(),
    status: joi_1.default.string().valid(...Object.values(client_1.InvoiceStatus)).default('NORMAL'),
    remark: joi_1.default.string().optional(),
    projectName: joi_1.default.string().optional(),
    department: joi_1.default.string().optional(),
    costCenter: joi_1.default.string().optional(),
    companyId: joi_1.default.string().required().messages({
        'any.required': '公司ID是必填项',
    }),
    receivedInvoiceItems: joi_1.default.array().items(joi_1.default.object({
        itemName: joi_1.default.string().required(),
        specification: joi_1.default.string().optional(),
        unit: joi_1.default.string().optional(),
        quantity: joi_1.default.number().positive().required(),
        unitPrice: joi_1.default.number().min(0).required(),
        amount: joi_1.default.number().min(0).required(),
        taxRate: joi_1.default.number().min(0).max(1).required(),
        taxAmount: joi_1.default.number().min(0).required(),
    })).optional(),
});
const updateReceivedInvoiceSchema = createReceivedInvoiceSchema.fork(['invoiceNumber', 'invoiceCode', 'invoiceDate', 'amount', 'taxAmount', 'totalAmount',
    'buyerName', 'buyerTaxId', 'sellerName', 'sellerTaxId', 'invoiceType', 'companyId'], (schema) => schema.optional());
const querySchema = joi_1.default.object({
    page: joi_1.default.number().integer().min(1).default(1),
    pageSize: joi_1.default.number().integer().min(1).max(100).default(10),
    search: joi_1.default.string().optional(),
    companyId: joi_1.default.string().optional(),
    year: joi_1.default.number().integer().min(2000).max(2100).optional(),
    invoiceType: joi_1.default.string().valid(...Object.values(client_1.InvoiceType)).optional(),
    status: joi_1.default.string().valid(...Object.values(client_1.InvoiceStatus)).optional(),
    verificationStatus: joi_1.default.string().valid(...Object.values(client_1.VerificationStatus)).optional(),
    startDate: joi_1.default.date().optional(),
    endDate: joi_1.default.date().optional(),
    sellerCompanies: joi_1.default.alternatives().try(joi_1.default.array().items(joi_1.default.string()), joi_1.default.string()).optional(),
    onlyShowPartnerCompanies: joi_1.default.string().valid('true', 'false').optional(),
    sortBy: joi_1.default.string().valid('invoiceDate', 'totalAmount', 'createdAt').default('createdAt'),
    sortOrder: joi_1.default.string().valid('asc', 'desc').default('desc'),
});
// 配置multer用于文件上传
const upload = (0, multer_1.default)({
    storage: multer_1.default.memoryStorage(),
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
    },
    fileFilter: (req, file, cb) => {
        if (file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
            file.mimetype === 'application/vnd.ms-excel') {
            cb(null, true);
        }
        else {
            cb(new Error('只支持Excel文件格式'));
        }
    },
});
// 下载Excel模板 - 必须在动态路由之前
router.get('/template', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const templatePath = path_1.default.join(__dirname, '../../templates/invoice-template.xlsx');
    if (!fs_1.default.existsSync(templatePath)) {
        throw new errorHandler_1.AppError('模板文件不存在', 404);
    }
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename="invoice-template.xlsx"');
    const fileStream = fs_1.default.createReadStream(templatePath);
    fileStream.pipe(res);
}));
// Excel导入 - 必须在动态路由之前
router.post('/import', upload.single('file'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.file) {
        throw new errorHandler_1.AppError('请选择要上传的Excel文件', 400);
    }
    console.log('原始文件名:', req.file.filename);
    const originalFileName = Buffer.from(req.file.originalname, 'latin1').toString('utf8');
    console.log('处理后文件名:', originalFileName);
    console.log('处理后文件名字节:', Buffer.from(originalFileName).toString('hex'));
    if (!originalFileName || !originalFileName.includes("取得")) {
        throw new errorHandler_1.AppError('Excel文件名称必须要包含"取得",请选择正确的取得发票文件信息', 400);
    }
    try {
        // 读取Excel文件
        const workbook = XLSX.read(req.file.buffer, { type: 'buffer' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        // 将工作表转换为JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        if (jsonData.length < 2) {
            throw new errorHandler_1.AppError('Excel文件格式不正确或没有数据', 400);
        }
        // 获取表头
        const headers = jsonData[0];
        const dataRows = jsonData.slice(1);
        // 映射Excel列到数据库字段 - 根据实际模板更新
        const fieldMapping = {
            // 基本发票信息
            '发票号码': 'invoiceNumber',
            '数电发票号码': 'invoiceCode', // 数电发票号码映射到发票代码字段
            '发票代码': 'invoiceCode',
            '开票日期': 'invoiceDate',
            // 购买方信息
            '购买方名称': 'buyerName',
            '购方识别号': 'buyerTaxId', // 实际模板中的字段名
            '购买方纳税人识别号': 'buyerTaxId',
            '购买方地址、电话': 'buyerAddress',
            // 销售方信息
            '销方名称': 'sellerName', // 实际模板中的字段名
            '销售方名称': 'sellerName',
            '销方识别号': 'sellerTaxId', // 实际模板中的字段名
            '销售方纳税人识别号': 'sellerTaxId',
            '销售方地址、电话': 'sellerAddress',
            // 金额信息
            '金额': 'amount',
            '税额': 'taxAmount',
            '价税合计': 'totalAmount',
            '发票状态': 'status',
            // 商品信息
            '货物或应税劳务名称': 'itemName', // 实际模板中的字段名
            '商品名称': 'itemName',
            '规格型号': 'specification',
            '单位': 'unit',
            '数量': 'quantity',
            '单价': 'unitPrice',
            '税率': 'taxRate',
            // 其他信息
            '开票人': 'drawer',
            '复核': 'reviewer',
            '收款人': 'payee',
            '备注': 'remarks'
        };
        // 添加调试信息
        console.log('Excel表头:', headers);
        console.log('字段映射:', fieldMapping);
        let successCount = 0;
        let failureCount = 0;
        let duplicateCount = 0;
        const errors = [];
        const newCompanyIds = []; // 记录新创建的公司ID
        // 处理每一行数据
        for (let i = 0; i < dataRows.length; i++) {
            const row = dataRows[i];
            const rowNumber = i + 2; // Excel行号（从2开始，因为第1行是表头）
            try {
                if (!row || row.length === 0 || !row.some(cell => cell !== null && cell !== undefined && cell !== '')) {
                    continue; // 跳过空行
                }
                // // 获取默认公司（如果没有指定公司）
                // const defaultCompany = await prisma.company.findFirst({
                //     where: { isActive: true },
                // });
                // if (!defaultCompany) {
                //     throw new errorHandler_1.AppError('系统中没有可用的公司，请先创建公司', 400);
                // }
                // 构建取得发票数据
                const receivedInvoiceData = {
                    // companyId: defaultCompany.id,
                    invoiceType: 'ORDINARY_VAT', // 默认普通发票
                    status: 'NORMAL',
                    verificationStatus: 'UNVERIFIED',
                };
                // 映射Excel数据到取得发票字段
                headers.forEach((header, index) => {
                    const field = fieldMapping[header];
                    if (field && row[index] !== null && row[index] !== undefined && row[index] !== '') {
                        let value = row[index];
                        // 特殊处理不同类型的字段
                        if (field === 'invoiceDate') {
                            // 处理日期格式
                            if (typeof value === 'number') {
                                // Excel日期序列号
                                const date = XLSX.SSF.parse_date_code(value);
                                value = new Date(date.y, date.m - 1, date.d);
                            }
                            else if (typeof value === 'string') {
                                value = new Date(value);
                            }
                        }
                        else if (['amount', 'taxAmount', 'totalAmount', 'quantity', 'unitPrice', 'taxRate'].includes(field)) {
                            // 处理数字字段
                            value = parseFloat(value.toString().replace(/[,，]/g, '')) || 0;
                        }
                        else if (field === 'status') {
                            // 处理状态字段
                            value = value === '正常' ? 'NORMAL' : value === '作废' ? 'CANCELLED' : 'NORMAL';
                        }
                        // 对于发票代码字段，如果已经有值就不覆盖（支持数电发票号码和发票代码二选一）
                        if (field === 'invoiceCode' && receivedInvoiceData[field]) {
                            return; // 已经有发票代码了，跳过
                        }
                        receivedInvoiceData[field] = value;
                    }
                });
                // 验证必填字段 - 发票号码和数电发票号码(发票代码)只需要有一个即可
                const hasInvoiceNumber = receivedInvoiceData.invoiceNumber && receivedInvoiceData.invoiceNumber.toString().trim();
                const hasInvoiceCode = receivedInvoiceData.invoiceCode && receivedInvoiceData.invoiceCode.toString().trim();
                if (!hasInvoiceNumber && !hasInvoiceCode) {
                    errors.push({ row: rowNumber, message: '发票号码和数电发票号码至少需要填写一个' });
                    failureCount++;
                    continue;
                }
                if (!receivedInvoiceData.buyerName) {
                    errors.push({ row: rowNumber, message: '购买方名称不能为空' });
                    failureCount++;
                    continue;
                }
                if (!receivedInvoiceData.sellerName) {
                    errors.push({ row: rowNumber, message: '销售方名称不能为空' });
                    failureCount++;
                    continue;
                }
                // 检查取得发票是否已存在 - 只在取得发票表(receivedInvoice)中检查重复
                // 注意：这里只检查取得发票表，不检查开具发票表
                let existingReceivedInvoice = null;
                if (hasInvoiceNumber && hasInvoiceCode) {
                    // 两个字段都有值，检查两个字段的组合
                    existingReceivedInvoice = await prisma.receivedInvoice.findFirst({
                        where: {
                            invoiceNumber: receivedInvoiceData.invoiceNumber,
                            invoiceCode: receivedInvoiceData.invoiceCode,
                        },
                    });
                }
                else if (hasInvoiceNumber) {
                    // 只有发票号码，检查发票号码
                    existingReceivedInvoice = await prisma.receivedInvoice.findFirst({
                        where: {
                            invoiceNumber: receivedInvoiceData.invoiceNumber,
                        },
                    });
                }
                else if (hasInvoiceCode) {
                    // 只有发票代码，检查发票代码
                    existingReceivedInvoice = await prisma.receivedInvoice.findFirst({
                        where: {
                            invoiceCode: receivedInvoiceData.invoiceCode,
                        },
                    });
                }
                if (existingReceivedInvoice) {
                    duplicateCount++;
                    continue;
                }
                // 确保必填字段有值 - 如果没有发票号码但有发票代码，使用发票代码作为发票号码
                if (!receivedInvoiceData.invoiceNumber && receivedInvoiceData.invoiceCode) {
                    receivedInvoiceData.invoiceNumber = receivedInvoiceData.invoiceCode;
                }
                // 确保有公司ID - 总是根据购买方信息查找公司（取得发票中我们是购买方）
                // 尝试根据购买方信息找到对应的公司
                let company = null;
                // 首先尝试精确匹配
                if (receivedInvoiceData.buyerTaxId) {
                    company = await prisma.company.findFirst({
                        where: { taxId: receivedInvoiceData.buyerTaxId }
                    });
                }
                // 如果税号没找到，尝试公司名称匹配
                if (!company && receivedInvoiceData.buyerName) {
                    company = await prisma.company.findFirst({
                        where: { name: receivedInvoiceData.buyerName }
                    });
                }
                // // 如果还是没找到，尝试模糊匹配公司名称
                // if (!company && receivedInvoiceData.buyerName) {
                //   company = await prisma.company.findFirst({
                //     where: {
                //       name: {
                //         contains: receivedInvoiceData.buyerName.replace(/有限公司|股份有限公司|集团|公司$/g, '').trim()
                //       }
                //     }
                //   });
                // }
                if (company) {
                    receivedInvoiceData.companyId = company.id;
                    console.log(`取得发票 ${receivedInvoiceData.invoiceNumber} 匹配到公司: ${company.name} (${company.id})`);
                }
                else {
                    // 如果找不到公司，返回错误
                    errors.push({ row: rowNumber, message: `购买方公司未在公司管理维护，请维护${receivedInvoiceData.buyerName} 的公司信息` });
                    failureCount++;
                    continue;
                }
                // 处理税率：确保税率在正确的范围内 (0-1之间)
                let taxRate = receivedInvoiceData.taxRate || 0;
                if (taxRate > 1) {
                    // 如果税率大于1，假设是百分比形式，转换为小数
                    taxRate = taxRate / 100;
                }
                // 确保税率不超过数据库字段限制 (最大9.9999)
                if (taxRate > 9.9999) {
                    taxRate = 0.13; // 默认13%税率
                }
                // 处理必需的数值字段，确保它们不为null或undefined
                const amount = Number(receivedInvoiceData.amount) || 0;
                const taxAmount = Number(receivedInvoiceData.taxAmount) || 0;
                let quantity = Number(receivedInvoiceData.quantity) || 1;
                let unitPrice = Number(receivedInvoiceData.unitPrice) || amount; // 如果没有单价，使用金额作为单价
                const totalAmount = Number(receivedInvoiceData.totalAmount) || (amount + taxAmount);
                // 确保quantity在数据库字段范围内 (Decimal(10,4) 最大值约为999999.9999)
                if (quantity > 999999.9999) {
                    quantity = 1; // 超出范围时使用默认值1
                }
                if (quantity < 0) {
                    quantity = 1; // 负数时使用默认值1
                }
                // 确保unitPrice在数据库字段范围内 (Decimal(15,4) 最大值约为99999999999.9999)
                if (unitPrice > 99999999999.9999) {
                    unitPrice = amount; // 超出范围时使用金额
                }
                if (unitPrice < 0) {
                    unitPrice = Math.abs(unitPrice); // 负数时取绝对值
                }
                // 分离取得发票项目数据，为必需字段提供默认值
                const receivedInvoiceItemData = {
                    itemName: receivedInvoiceData.itemName || '未知商品',
                    specification: receivedInvoiceData.specification || null,
                    unit: receivedInvoiceData.unit || null,
                    quantity: quantity,
                    unitPrice: unitPrice,
                    amount: amount,
                    taxRate: taxRate,
                    taxAmount: taxAmount,
                    totalAmount: totalAmount
                };
                // 从取得发票数据中移除项目相关字段，并确保主记录的数值字段正确
                const cleanReceivedInvoiceData = { ...receivedInvoiceData };
                delete cleanReceivedInvoiceData.itemName;
                delete cleanReceivedInvoiceData.specification;
                delete cleanReceivedInvoiceData.unit;
                delete cleanReceivedInvoiceData.quantity;
                delete cleanReceivedInvoiceData.unitPrice;
                delete cleanReceivedInvoiceData.taxRate; // taxRate只保存在ReceivedInvoiceItem中
                // 确保主记录的数值字段正确
                cleanReceivedInvoiceData.amount = amount;
                cleanReceivedInvoiceData.taxAmount = taxAmount;
                cleanReceivedInvoiceData.totalAmount = totalAmount;
                // 确保使用正确的公司ID（这是最重要的）
                cleanReceivedInvoiceData.companyId = receivedInvoiceData.companyId;
                // 创建取得发票和取得发票项目
                await prisma.receivedInvoice.create({
                    data: {
                        ...cleanReceivedInvoiceData,
                        receivedInvoiceItems: receivedInvoiceItemData.itemName ? {
                            create: [receivedInvoiceItemData]
                        } : undefined
                    }
                });
                successCount++;
            }
            catch (error) {
                console.error(`处理第${rowNumber}行时出错:`, error);
                errors.push({
                    row: rowNumber,
                    message: error instanceof Error ? error.message : '未知错误'
                });
                failureCount++;
            }
        }
        // 为当前登录用户分配新创建公司的权限
        if (newCompanyIds.length > 0 && req.user) {
            try {
                console.log(`开始为用户 ${req.user.id} (${req.user.name}) 分配 ${newCompanyIds.length} 个新创建公司的权限`);
                // 为当前用户分配新创建公司的权限
                let assignedCount = 0;
                for (const companyId of newCompanyIds) {
                    try {
                        // 检查权限是否已存在
                        const existingPermission = await prisma.userCompany.findUnique({
                            where: {
                                userId_companyId: {
                                    userId: req.user.id,
                                    companyId: companyId
                                }
                            }
                        });
                        if (!existingPermission) {
                            // 创建新的权限记录
                            await prisma.userCompany.create({
                                data: {
                                    userId: req.user.id,
                                    companyId: companyId
                                }
                            });
                            assignedCount++;
                            console.log(`✅ 成功为用户 ${req.user.id} 分配公司 ${companyId} 的权限`);
                        }
                        else {
                            console.log(`⚠️ 用户 ${req.user.id} 已有公司 ${companyId} 的权限，跳过`);
                        }
                    }
                    catch (permError) {
                        console.error(`❌ 为用户 ${req.user.id} 分配公司 ${companyId} 权限失败:`, permError);
                    }
                }
                console.log(`✅ 为用户 ${req.user.id} 成功分配了 ${assignedCount} 个新创建公司的权限`);
            }
            catch (error) {
                console.error('❌ 分配公司权限失败:', error);
                // 不影响导入结果，只记录错误
            }
        }
        res.json({
            success: true,
            data: {
                totalCount: dataRows.length,
                successCount,
                failureCount,
                duplicateCount,
                errors: errors.slice(0, 50), // 最多返回50个错误
                newCompaniesAssigned: newCompanyIds.length, // 分配给当前用户的新公司数量
            },
            message: `导入完成：成功${successCount}条，失败${failureCount}条，重复${duplicateCount}条${newCompanyIds.length > 0 ? `，已为您分配${newCompanyIds.length}个新公司的权限` : ''}`,
        });
    }
    catch (error) {
        console.error('Excel导入失败:', error);
        throw new errorHandler_1.AppError('Excel文件解析失败，请检查文件格式', 400);
    }
}));
// 获取取得发票列表
router.get('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    // 预处理查询参数，特别是sellerCompanies
    const queryParams = { ...req.query };
    // 如果sellerCompanies是字符串，将其转换为数组
    if (queryParams.sellerCompanies && typeof queryParams.sellerCompanies === 'string') {
        queryParams.sellerCompanies = [queryParams.sellerCompanies];
    }
    const { error, value } = querySchema.validate(queryParams);
    if (error) {
        console.error('查询参数验证失败:', error.details[0].message, '原始参数:', req.query);
        throw new errorHandler_1.AppError(error.details[0].message, 400);
    }
    const { page, pageSize, search, companyId, year, invoiceType, status, verificationStatus, startDate, endDate, sellerCompanies, sortBy, sortOrder, onlyShowPartnerCompanies } = value;
    const skip = (page - 1) * pageSize;
    // 构建查询条件
    const where = {
        // 只查询激活公司的取得发票
        company: {
            isActive: true,
        },
    };
    // 如果是普通用户，只能查看有权限的公司数据
    if (req.user?.role !== 'ADMIN') {
        const userCompanyIds = await (0, auth_1.getUserCompanyIds)(req.user.id);
        if (userCompanyIds.length === 0) {
            // 用户没有任何公司权限，返回空结果
            return res.json({
                success: true,
                data: {
                    data: [],
                    pagination: {
                        page,
                        pageSize,
                        total: 0,
                        totalPages: 0,
                    },
                },
            });
        }
        where.companyId = { in: userCompanyIds };
    }
    if (search) {
        // 支持多公司搜索，用逗号分隔
        const searchTerms = search.split(',').map((term) => term.trim()).filter((term) => term);
        if (searchTerms.length > 1) {
            // 多个搜索词，每个词都要匹配 - 搜索销售方、销售方税号、发票号码、金额
            where.OR = searchTerms.flatMap((term) => {
                const conditions = [
                    { invoiceNumber: { contains: term } },
                    { sellerName: { contains: term } },
                    { sellerTaxId: { contains: term } }
                ];
                // 如果搜索词是数字，也搜索金额
                if (!isNaN(Number(term))) {
                    conditions.push({ totalAmount: { equals: Number(term) } });
                }
                return conditions;
            });
        }
        else {
            // 单个搜索词 - 搜索销售方、销售方税号、发票号码、金额
            const conditions = [
                { invoiceNumber: { contains: search } },
                { sellerName: { contains: search } },
                { sellerTaxId: { contains: search } }
            ];
            // 如果搜索词是数字，也搜索金额
            if (!isNaN(Number(search))) {
                conditions.push({ totalAmount: { equals: Number(search) } });
            }
            where.OR = conditions;
        }
    }
    if (companyId)
        where.companyId = companyId;
    if (invoiceType)
        where.invoiceType = invoiceType;
    if (status)
        where.status = status;
    if (verificationStatus)
        where.verificationStatus = verificationStatus;
    // 处理年度过滤
    if (year) {
        const yearStart = new Date(year, 0, 1); // 年初
        const yearEnd = new Date(year, 11, 31, 23, 59, 59); // 年末
        where.invoiceDate = {
            gte: yearStart,
            lte: yearEnd,
        };
    }
    else if (startDate || endDate) {
        where.invoiceDate = {};
        if (startDate)
            where.invoiceDate.gte = new Date(startDate);
        if (endDate)
            where.invoiceDate.lte = new Date(endDate);
    }
    // "只显示往来公司"过滤：销售方按照公司管理的公司列表进行过滤
    if (onlyShowPartnerCompanies === 'true') {
        // 获取公司管理中的所有公司名称
        const allCompanies = await prisma.company.findMany({
            where: {
                isActive: true
            },
            select: {
                name: true
            }
        });
        const companyNames = allCompanies.map(company => company.name);
        where.sellerName = { in: companyNames };
    }
    // 购买方公司过滤 - 支持模糊查询
    if (sellerCompanies) {
        // 确保sellerCompanies是数组
        const companiesArray = Array.isArray(sellerCompanies) ? sellerCompanies : [sellerCompanies];
        console.log('取得发票购买方过滤参数:', companiesArray);
        if (companiesArray.length > 0) {
            // 检查是否是公司ID（数字字符串或UUID）还是公司名称
            const isCompanyIds = companiesArray.every(item => /^\d+$/.test(item) || // 纯数字
                typeof item === 'number' || // 数字类型
                /^[a-f0-9-]{36}$/i.test(item) || // UUID格式
                /^[a-z0-9]{25}$/i.test(item) // cuid格式
            );
            console.log('是否为公司ID:', isCompanyIds);
            if (isCompanyIds) {
                // 如果是公司ID，按原逻辑处理
                if (where.companyId) {
                    // 如果已经有companyId条件（来自用户权限），取交集
                    const existingIds = Array.isArray(where.companyId.in) ? where.companyId.in : [where.companyId];
                    const filteredIds = companiesArray.filter(id => existingIds.includes(String(id)));
                    where.companyId = { in: filteredIds };
                    console.log('使用公司ID过滤（有权限限制）:', filteredIds);
                }
                else {
                    // 没有现有的companyId条件，直接设置
                    where.companyId = { in: companiesArray.map(id => String(id)) };
                    console.log('使用公司ID过滤（无权限限制）:', companiesArray);
                }
            }
            else {
                // 如果是公司名称，支持模糊查询 - 改为查询购买方
                if (companiesArray.length === 1) {
                    // 单个条件，使用模糊查询
                    where.buyerName = { contains: companiesArray[0] };
                    console.log('使用购买方公司名称模糊查询:', companiesArray[0]);
                }
                else {
                    // 多个条件，使用OR组合模糊查询
                    where.OR = companiesArray.map(name => ({
                        buyerName: { contains: name }
                    }));
                    console.log('使用多个购买方公司名称模糊查询:', companiesArray);
                }
            }
        }
    }
    // 获取总数
    const total = await prisma.receivedInvoice.count({ where });
    // 获取数据
    const receivedInvoices = await prisma.receivedInvoice.findMany({
        where,
        skip,
        take: pageSize,
        orderBy: { [sortBy]: sortOrder },
        include: {
            company: {
                select: {
                    id: true,
                    name: true,
                    taxId: true,
                },
            },
            _count: {
                select: {
                    receivedInvoiceItems: true,
                    attachments: true,
                },
            },
        },
    });
    res.json({
        success: true,
        data: {
            data: receivedInvoices,
            pagination: {
                page,
                pageSize,
                total,
                totalPages: Math.ceil(total / pageSize),
            },
        },
    });
}));
// 导出取得发票数据
router.get('/export', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const { search, startDate, endDate, status, sellerCompanies, onlyShowPartnerCompanies } = req.query;
        // 获取用户有权限的公司ID
        const userCompanyIds = await (0, auth_1.getUserCompanyIds)(req.user.id);
        if (userCompanyIds.length === 0) {
            throw new errorHandler_1.AppError('没有权限访问公司数据', 403);
        }
        // 构建查询条件（与列表查询相同的逻辑）
        const where = {
            companyId: { in: userCompanyIds },
            company: {
                isActive: true,
            },
        };
        // 搜索条件 - 搜索销售方、销售方税号、发票号码、金额
        if (search) {
            const conditions = [
                { invoiceNumber: { contains: search, mode: 'insensitive' } },
                { sellerName: { contains: search, mode: 'insensitive' } },
                { sellerTaxId: { contains: search, mode: 'insensitive' } }
            ];
            // 如果搜索词是数字，也搜索金额
            if (!isNaN(Number(search))) {
                conditions.push({ totalAmount: { equals: Number(search) } });
            }
            where.OR = conditions;
        }
        // 日期范围
        if (startDate || endDate) {
            where.invoiceDate = {};
            if (startDate) {
                where.invoiceDate.gte = new Date(startDate);
            }
            if (endDate) {
                const endDateTime = new Date(endDate);
                endDateTime.setHours(23, 59, 59, 999);
                where.invoiceDate.lte = endDateTime;
            }
        }
        // 状态过滤
        if (status) {
            where.status = status;
        }
        // "只显示往来公司"过滤
        if (onlyShowPartnerCompanies === 'true') {
            const allCompanies = await prisma.company.findMany({
                where: {
                    isActive: true
                },
                select: {
                    name: true
                }
            });
            const companyNames = allCompanies.map(company => company.name);
            where.sellerName = { in: companyNames };
        }
        // 购买方公司过滤
        if (sellerCompanies) {
            const sellerCompanyArray = Array.isArray(sellerCompanies) ? sellerCompanies : [sellerCompanies];
            where.buyerName = { in: sellerCompanyArray };
        }
        // 查询所有符合条件的取得发票数据
        const receivedInvoices = await prisma.receivedInvoice.findMany({
            where,
            include: {
                company: {
                    select: {
                        name: true,
                        organization: true
                    }
                },
                receivedInvoiceItems: true
            },
            orderBy: { invoiceDate: 'desc' }
        });
        // 读取Excel模板
        const templatePath = path_1.default.join(__dirname, '../../templates/invoice-template.xlsx');
        if (!fs_1.default.existsSync(templatePath)) {
            throw new errorHandler_1.AppError('Excel模板文件不存在', 404);
        }
        const workbook = XLSX.readFile(templatePath);
        const worksheet = workbook.Sheets[workbook.SheetNames[0]];
        // 准备导出数据
        const exportData = receivedInvoices.map(invoice => {
            const firstItem = invoice.receivedInvoiceItems?.[0];
            return {
                '发票号码': invoice.invoiceNumber,
                '数电发票号码': invoice.invoiceCode,
                '开票日期': invoice.invoiceDate ? new Date(invoice.invoiceDate).toLocaleDateString('zh-CN') : '',
                '购买方名称': invoice.buyerName,
                '购方识别号': invoice.buyerTaxId,
                '购买方地址、电话': invoice.buyerAddress,
                '销方名称': invoice.sellerName,
                '销方识别号': invoice.sellerTaxId,
                '销售方地址、电话': invoice.sellerAddress,
                '金额': invoice.amount,
                '税额': invoice.taxAmount,
                '价税合计': invoice.totalAmount,
                '发票状态': invoice.status === 'NORMAL' ? '正常' : '作废',
                '货物或应税劳务名称': firstItem?.itemName || '',
                '规格型号': firstItem?.specification || '',
                '单位': firstItem?.unit || '',
                '数量': firstItem?.quantity || '',
                '单价': firstItem?.unitPrice || '',
                '税率': firstItem?.taxRate ? (Number(firstItem.taxRate) * 100).toFixed(2) + '%' : '',
                '开票人': invoice.drawer || '',
                '复核': '', // ReceivedInvoice模型中没有reviewer字段
                '收款人': '', // ReceivedInvoice模型中没有payee字段
                '备注': invoice.remarks || ''
            };
        });
        // 创建新的工作表
        const newWorksheet = XLSX.utils.json_to_sheet(exportData);
        // 创建新的工作簿
        const newWorkbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(newWorkbook, newWorksheet, '取得发票');
        // 生成Excel文件
        const buffer = XLSX.write(newWorkbook, { type: 'buffer', bookType: 'xlsx' });
        // 设置响应头
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', 'attachment; filename="received-invoices-export.xlsx"');
        // 发送文件
        res.send(buffer);
    }
    catch (error) {
        console.error('导出取得发票失败:', error);
        res.status(500).json({
            success: false,
            message: '导出失败'
        });
    }
}));
// 获取单个取得发票详情
router.get('/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const receivedInvoice = await prisma.receivedInvoice.findUnique({
        where: { id },
        include: {
            company: {
                select: {
                    id: true,
                    name: true,
                    taxId: true,
                },
            },
            receivedInvoiceItems: true,
            attachments: true, // 恢复附件查询
        },
    });
    if (!receivedInvoice) {
        throw new errorHandler_1.AppError('取得发票不存在', 404);
    }
    res.json({
        success: true,
        data: receivedInvoice,
    });
}));
// 更新取得发票
router.put('/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const { error, value } = updateReceivedInvoiceSchema.validate(req.body);
    if (error) {
        throw new errorHandler_1.AppError(error.details[0].message, 400);
    }
    const { receivedInvoiceItems, ...receivedInvoiceData } = value;
    // 检查取得发票是否存在
    const existingReceivedInvoice = await prisma.receivedInvoice.findUnique({
        where: { id },
    });
    if (!existingReceivedInvoice) {
        throw new errorHandler_1.AppError('取得发票不存在', 404);
    }
    // 更新取得发票
    const updatedReceivedInvoice = await prisma.receivedInvoice.update({
        where: { id },
        data: {
            ...receivedInvoiceData,
            receivedInvoiceItems: receivedInvoiceItems ? {
                deleteMany: {},
                create: receivedInvoiceItems,
            } : undefined,
        },
        include: {
            company: {
                select: {
                    id: true,
                    name: true,
                    taxId: true,
                },
            },
            receivedInvoiceItems: true,
            attachments: true, // 恢复附件查询
        },
    });
    res.json({
        success: true,
        data: updatedReceivedInvoice,
        message: '取得发票更新成功',
    });
}));
// 删除取得发票
router.delete('/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const receivedInvoice = await prisma.receivedInvoice.findUnique({
        where: { id },
    });
    if (!receivedInvoice) {
        throw new errorHandler_1.AppError('取得发票不存在', 404);
    }
    await prisma.receivedInvoice.delete({
        where: { id },
    });
    res.json({
        success: true,
        message: '取得发票删除成功',
    });
}));
// 创建取得发票
router.post('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { error, value } = createReceivedInvoiceSchema.validate(req.body);
    if (error) {
        throw new errorHandler_1.AppError(error.details[0].message, 400);
    }
    const { receivedInvoiceItems, ...receivedInvoiceData } = value;
    // 检查取得发票号码是否已存在 - 只在取得发票表(receivedInvoice)中检查
    // 注意：这里只检查取得发票表，不检查开具发票表
    const existingReceivedInvoice = await prisma.receivedInvoice.findFirst({
        where: {
            OR: [
                { invoiceNumber: receivedInvoiceData.invoiceNumber },
                { invoiceCode: receivedInvoiceData.invoiceCode },
            ],
        },
    });
    if (existingReceivedInvoice) {
        throw new errorHandler_1.AppError('发票号码或发票代码已存在', 400);
    }
    // 验证公司是否存在
    const company = await prisma.company.findUnique({
        where: { id: receivedInvoiceData.companyId },
    });
    if (!company) {
        throw new errorHandler_1.AppError('指定的公司不存在', 400);
    }
    // 创建取得发票
    const receivedInvoice = await prisma.receivedInvoice.create({
        data: {
            ...receivedInvoiceData,
            receivedInvoiceItems: receivedInvoiceItems ? {
                create: receivedInvoiceItems,
            } : undefined,
        },
        include: {
            company: {
                select: {
                    id: true,
                    name: true,
                    taxId: true,
                },
            },
            receivedInvoiceItems: true,
        },
    });
    res.status(201).json({
        success: true,
        data: receivedInvoice,
        message: '取得发票创建成功',
    });
}));
exports.default = router;
//# sourceMappingURL=receivedInvoices.js.map