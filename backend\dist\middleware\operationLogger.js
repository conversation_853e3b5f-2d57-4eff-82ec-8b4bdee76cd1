"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.operationLogger = void 0;
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
// 操作名称映射
const getOperationName = (method, path) => {
    const pathSegments = path.split('/').filter(Boolean);
    // 移除 'api' 前缀
    if (pathSegments[0] === 'api') {
        pathSegments.shift();
    }
    const resource = pathSegments[0] || 'unknown';
    const action = pathSegments[1];
    switch (method.toUpperCase()) {
        case 'GET':
            if (path.includes('/dashboard'))
                return '查看仪表盘';
            if (path.includes('/companies') && action === 'import')
                return '导入公司数据';
            if (path.includes('/companies'))
                return '查询公司列表';
            if (path.includes('/invoices'))
                return '查询发票列表';
            if (path.includes('/received-invoices'))
                return '查询取得发票列表';
            if (path.includes('/users'))
                return '查询用户列表';
            if (path.includes('/menus'))
                return '查询菜单权限';
            if (path.includes('/operation-logs'))
                return '查询操作日志';
            return `查询${resource}`;
        case 'POST':
            if (path.includes('/auth/login'))
                return '用户登录';
            if (path.includes('/auth/logout'))
                return '用户登出';
            if (path.includes('/companies/import'))
                return '导入公司数据';
            if (path.includes('/invoices/import'))
                return '导入发票数据';
            if (path.includes('/companies'))
                return '创建公司';
            if (path.includes('/invoices'))
                return '创建发票';
            if (path.includes('/received-invoices'))
                return '创建取得发票';
            if (path.includes('/users'))
                return '创建用户';
            return `创建${resource}`;
        case 'PUT':
            if (path.includes('/companies'))
                return '更新公司信息';
            if (path.includes('/invoices'))
                return '更新发票信息';
            if (path.includes('/received-invoices'))
                return '更新取得发票信息';
            if (path.includes('/users') && path.includes('/password'))
                return '修改用户密码';
            if (path.includes('/users') && path.includes('/menu-permissions'))
                return '设置用户菜单权限';
            if (path.includes('/users') && path.includes('/companies'))
                return '设置用户公司权限';
            if (path.includes('/users'))
                return '更新用户信息';
            return `更新${resource}`;
        case 'DELETE':
            if (path.includes('/companies'))
                return '删除公司';
            if (path.includes('/invoices'))
                return '删除发票';
            if (path.includes('/received-invoices'))
                return '删除取得发票';
            if (path.includes('/users'))
                return '删除用户';
            return `删除${resource}`;
        default:
            return `${method} ${resource}`;
    }
};
// 脱敏敏感数据
const sanitizeData = (data) => {
    if (!data || typeof data !== 'object') {
        return data;
    }
    const sensitiveFields = ['password', 'token', 'secret', 'key', 'authorization'];
    const sanitized = { ...data };
    for (const field of sensitiveFields) {
        if (sanitized[field]) {
            sanitized[field] = '***';
        }
    }
    // 递归处理嵌套对象
    for (const key in sanitized) {
        if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
            sanitized[key] = sanitizeData(sanitized[key]);
        }
    }
    return sanitized;
};
// 判断是否需要记录日志
const shouldLog = (path) => {
    // 不记录的路径
    const excludePaths = [
        '/api/operation-logs', // 避免查询日志时产生新日志
        '/health',
        '/favicon.ico',
        '/static',
        '/public'
    ];
    return !excludePaths.some(excludePath => path.startsWith(excludePath));
};
const operationLogger = (req, res, next) => {
    if (!shouldLog(req.path)) {
        return next();
    }
    const startTime = Date.now();
    const originalSend = res.send;
    let responseData;
    // 拦截响应数据
    res.send = function (data) {
        responseData = data;
        return originalSend.call(this, data);
    };
    // 在响应结束后记录日志
    res.on('finish', async () => {
        try {
            const duration = Date.now() - startTime;
            const isSuccess = res.statusCode < 400;
            // 解析响应数据
            let parsedResponseData = null;
            try {
                if (typeof responseData === 'string') {
                    parsedResponseData = JSON.parse(responseData);
                }
                else {
                    parsedResponseData = responseData;
                }
            }
            catch (e) {
                // 如果解析失败，保存原始数据
                parsedResponseData = responseData;
            }
            // 获取客户端IP
            const ipAddress = req.ip ||
                req.connection?.remoteAddress ||
                req.socket?.remoteAddress ||
                req.connection?.socket?.remoteAddress ||
                'unknown';
            // 创建操作日志
            await prisma.operationLog.create({
                data: {
                    userId: req.user?.id || null,
                    username: req.user?.username || req.user?.name || null,
                    operationName: getOperationName(req.method, req.path),
                    method: req.method,
                    path: req.path,
                    userAgent: req.get('User-Agent') || null,
                    ipAddress: ipAddress,
                    requestParams: req.method !== 'GET' ? sanitizeData(req.body) : sanitizeData(req.query),
                    responseData: sanitizeData(parsedResponseData),
                    statusCode: res.statusCode,
                    isSuccess: isSuccess,
                    errorMessage: !isSuccess && parsedResponseData?.message ? parsedResponseData.message : null,
                    duration: duration,
                },
            });
        }
        catch (error) {
            // 记录日志失败不应该影响主要业务流程
            console.error('Failed to create operation log:', error);
        }
    });
    next();
};
exports.operationLogger = operationLogger;
//# sourceMappingURL=operationLogger.js.map