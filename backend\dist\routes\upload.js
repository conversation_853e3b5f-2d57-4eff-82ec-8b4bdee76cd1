"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const client_1 = require("@prisma/client");
const errorHandler_1 = require("../middleware/errorHandler");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
const prisma = new client_1.PrismaClient();
// 应用认证中间件到大部分路由（除了附件下载）
// router.use(authenticate);
// 确保上传目录存在
const uploadDir = process.env.UPLOAD_DIR || 'uploads';
const invoiceUploadsDir = path_1.default.join(uploadDir, 'invoices');
const tempUploadsDir = path_1.default.join(uploadDir, 'temp');
[uploadDir, invoiceUploadsDir, tempUploadsDir].forEach(dir => {
    if (!fs_1.default.existsSync(dir)) {
        fs_1.default.mkdirSync(dir, { recursive: true });
    }
});
// 配置multer存储
const storage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        const uploadType = req.body.uploadType || 'temp';
        const destDir = uploadType === 'invoice' ? invoiceUploadsDir : tempUploadsDir;
        cb(null, destDir);
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const ext = path_1.default.extname(file.originalname);
        cb(null, file.fieldname + '-' + uniqueSuffix + ext);
    }
});
// 文件过滤器
const fileFilter = (req, file, cb) => {
    const allowedTypes = {
        'image/jpeg': ['.jpg', '.jpeg'],
        'image/png': ['.png'],
        'image/gif': ['.gif'],
        'application/pdf': ['.pdf'],
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
        'application/vnd.ms-excel': ['.xls'],
    };
    if (allowedTypes[file.mimetype]) {
        cb(null, true);
    }
    else {
        cb(new errorHandler_1.AppError('不支持的文件类型', 400));
    }
};
const upload = (0, multer_1.default)({
    storage,
    fileFilter,
    limits: {
        fileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760'), // 10MB
    },
});
// 单文件上传
router.post('/single', auth_1.authenticate, upload.single('file'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.file) {
        throw new errorHandler_1.AppError('请选择要上传的文件', 400);
    }
    const fileInfo = {
        originalName: req.file.originalname,
        filename: req.file.filename,
        path: req.file.path,
        size: req.file.size,
        mimetype: req.file.mimetype,
        uploadedAt: new Date(),
    };
    res.json({
        success: true,
        data: fileInfo,
        message: '文件上传成功',
    });
}));
// 多文件上传
router.post('/multiple', auth_1.authenticate, upload.array('files', 10), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const files = req.files;
    if (!files || files.length === 0) {
        throw new errorHandler_1.AppError('请选择要上传的文件', 400);
    }
    const fileInfos = files.map(file => ({
        originalName: file.originalname,
        filename: file.filename,
        path: file.path,
        size: file.size,
        mimetype: file.mimetype,
        uploadedAt: new Date(),
    }));
    res.json({
        success: true,
        data: fileInfos,
        message: `成功上传 ${files.length} 个文件`,
    });
}));
// Excel导入发票
router.post('/import-excel', auth_1.authenticate, upload.single('file'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.file) {
        throw new errorHandler_1.AppError('请选择Excel文件', 400);
    }
    const { companyId } = req.body;
    if (!companyId) {
        throw new errorHandler_1.AppError('请选择公司', 400);
    }
    // 检查公司是否存在
    const company = await prisma.company.findFirst({
        where: {
            id: companyId,
            isActive: true,
        },
    });
    if (!company) {
        throw new errorHandler_1.AppError('公司不存在', 404);
    }
    // 模拟Excel解析过程
    // 在实际应用中，这里会使用xlsx库解析Excel文件
    const mockImportResult = {
        success: true,
        total: 100,
        imported: 85,
        failed: 15,
        errors: [
            {
                row: 5,
                field: 'invoiceNumber',
                value: '',
                message: '发票号码不能为空',
            },
            {
                row: 12,
                field: 'amount',
                value: 'abc',
                message: '金额必须是数字',
            },
            {
                row: 23,
                field: 'invoiceDate',
                value: '2024-13-01',
                message: '日期格式不正确',
            },
        ],
    };
    // 清理临时文件
    setTimeout(() => {
        fs_1.default.unlink(req.file.path, (err) => {
            if (err)
                console.error('Failed to delete temp file:', err);
        });
    }, 5000);
    res.json({
        success: true,
        data: mockImportResult,
        message: 'Excel导入完成',
    });
}));
// OCR识别发票
router.post('/ocr-invoice', auth_1.authenticate, upload.single('file'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.file) {
        throw new errorHandler_1.AppError('请选择图片或PDF文件', 400);
    }
    // 模拟OCR识别过程
    // 在实际应用中，这里会调用OCR服务（如百度OCR、腾讯OCR等）
    const mockOcrResult = {
        invoiceNumber: '12345678',
        invoiceCode: '001',
        invoiceDate: '2024-01-15',
        amount: 10000,
        taxAmount: 1300,
        totalAmount: 11300,
        buyerName: '北京科技有限公司',
        buyerTaxId: '91110000123456789X',
        sellerName: '上海贸易有限公司',
        sellerTaxId: '91310000987654321Y',
        invoiceType: 'SPECIAL_VAT',
        confidence: 0.95, // 识别置信度
        recognizedFields: [
            'invoiceNumber',
            'invoiceCode',
            'invoiceDate',
            'amount',
            'taxAmount',
            'totalAmount',
            'buyerName',
            'buyerTaxId',
            'sellerName',
            'sellerTaxId',
        ],
    };
    // 保存识别结果到数据库（可选）
    // 这里可以创建一个临时记录，等待用户确认后再正式创建发票
    res.json({
        success: true,
        data: mockOcrResult,
        message: 'OCR识别完成',
    });
}));
// 为发票添加附件
router.post('/invoice-attachment', auth_1.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    // 配置专门的发票附件上传
    const invoiceUpload = (0, multer_1.default)({
        storage: multer_1.default.diskStorage({
            destination: (req, file, cb) => {
                cb(null, invoiceUploadsDir); // 直接存储到发票目录
            },
            filename: (req, file, cb) => {
                const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
                const ext = path_1.default.extname(file.originalname);
                cb(null, file.fieldname + '-' + uniqueSuffix + ext);
            }
        }),
        fileFilter,
        limits: {
            fileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760'), // 10MB
        },
    }).single('file');
    // 使用专门的上传配置
    invoiceUpload(req, res, async (err) => {
        if (err) {
            throw new errorHandler_1.AppError(err.message, 400);
        }
        if (!req.file) {
            throw new errorHandler_1.AppError('请选择文件', 400);
        }
        const { invoiceId, fileType, isOriginal, invoiceType } = req.body;
        if (!invoiceId) {
            throw new errorHandler_1.AppError('发票ID是必填项', 400);
        }
        // 检查发票是否存在（支持开具发票和取得发票）
        let invoice = null;
        if (invoiceType === 'received') {
            invoice = await prisma.receivedInvoice.findUnique({
                where: { id: invoiceId },
            });
        }
        else {
            invoice = await prisma.invoice.findUnique({
                where: { id: invoiceId },
            });
        }
        if (!invoice) {
            throw new errorHandler_1.AppError('发票不存在', 404);
        }
        // 创建附件记录（根据发票类型选择不同的表）
        let attachment;
        // 处理中文文件名，确保正确编码
        const originalName = Buffer.from(req.file.originalname, 'latin1').toString('utf8');
        if (invoiceType === 'received') {
            attachment = await prisma.receivedInvoiceAttachment.create({
                data: {
                    fileName: originalName, // 直接存储原始文件名（处理中文）
                    filePath: req.file.path,
                    fileSize: req.file.size,
                    mimeType: req.file.mimetype,
                    fileType: req.file.mimetype.startsWith('image/') ? 'IMAGE' :
                        req.file.mimetype === 'application/pdf' ? 'PDF' :
                            fileType || 'OTHER',
                    isOriginal: isOriginal === 'true',
                    receivedInvoiceId: invoiceId,
                },
            });
        }
        else {
            attachment = await prisma.invoiceAttachment.create({
                data: {
                    fileName: originalName, // 直接存储原始文件名（处理中文）
                    filePath: req.file.path,
                    fileSize: req.file.size,
                    mimeType: req.file.mimetype,
                    fileType: req.file.mimetype.startsWith('image/') ? 'IMAGE' :
                        req.file.mimetype === 'application/pdf' ? 'PDF' :
                            fileType || 'OTHER',
                    isOriginal: isOriginal === 'true',
                    invoiceId,
                },
            });
        }
        res.json({
            success: true,
            data: attachment,
            message: '附件上传成功',
        });
    });
}));
// 删除发票附件
router.delete('/invoice-attachment/:attachmentId', auth_1.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { attachmentId } = req.params;
    const { invoiceType } = req.query;
    try {
        let attachment = null;
        // 根据发票类型查找附件
        if (invoiceType === 'received') {
            attachment = await prisma.receivedInvoiceAttachment.findUnique({
                where: { id: attachmentId },
            });
        }
        else {
            attachment = await prisma.invoiceAttachment.findUnique({
                where: { id: attachmentId },
            });
        }
        if (!attachment) {
            throw new errorHandler_1.AppError('附件不存在', 404);
        }
        // 删除物理文件
        if (attachment.filePath && fs_1.default.existsSync(attachment.filePath)) {
            fs_1.default.unlinkSync(attachment.filePath);
        }
        // 删除数据库记录
        if (invoiceType === 'received') {
            await prisma.receivedInvoiceAttachment.delete({
                where: { id: attachmentId },
            });
        }
        else {
            await prisma.invoiceAttachment.delete({
                where: { id: attachmentId },
            });
        }
        res.json({
            success: true,
            message: '附件删除成功',
        });
    }
    catch (error) {
        console.error('删除附件失败:', error);
        throw new errorHandler_1.AppError('删除附件失败', 500);
    }
}));
// 删除文件
router.delete('/file/:filename', (0, auth_1.authorize)('ADMIN', 'FINANCE'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { filename } = req.params;
    // 安全检查：确保文件名不包含路径遍历字符
    if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
        throw new errorHandler_1.AppError('无效的文件名', 400);
    }
    const filePath = path_1.default.join(tempUploadsDir, filename);
    if (fs_1.default.existsSync(filePath)) {
        fs_1.default.unlinkSync(filePath);
        res.json({
            success: true,
            message: '文件删除成功',
        });
    }
    else {
        throw new errorHandler_1.AppError('文件不存在', 404);
    }
}));
// 获取文件信息
router.get('/file/:filename', auth_1.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { filename } = req.params;
    // 安全检查
    if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
        throw new errorHandler_1.AppError('无效的文件名', 400);
    }
    const filePath = path_1.default.join(uploadDir, filename);
    if (fs_1.default.existsSync(filePath)) {
        const stats = fs_1.default.statSync(filePath);
        res.json({
            success: true,
            data: {
                filename,
                size: stats.size,
                createdAt: stats.birthtime,
                modifiedAt: stats.mtime,
            },
        });
    }
    else {
        throw new errorHandler_1.AppError('文件不存在', 404);
    }
}));
// 通过附件ID下载/预览文件
router.get('/attachment/:attachmentId', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { attachmentId } = req.params;
    const { preview, type, token } = req.query;
    // 如果提供了token参数，验证token
    if (token) {
        try {
            console.log('验证URL token:', token);
            console.log('JWT_SECRET:', process.env.JWT_SECRET);
            const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET || 'invoice-management-super-secret-key-2024');
            console.log('Token验证成功:', decoded);
            // 将用户信息添加到请求对象中
            req.user = decoded;
        }
        catch (error) {
            console.error('Token验证失败:', error);
            return res.status(401).json({
                success: false,
                message: '无效的访问令牌',
                error: error.message
            });
        }
    }
    else {
        // 如果没有token参数，检查Authorization头
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({
                success: false,
                message: '缺少访问令牌'
            });
        }
        try {
            const token = authHeader.substring(7);
            const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET || 'your-secret-key');
            req.user = decoded;
        }
        catch (error) {
            return res.status(401).json({
                success: false,
                message: '无效的访问令牌'
            });
        }
    }
    try {
        let attachment = null;
        // 根据类型查找附件
        if (type === 'received') {
            attachment = await prisma.receivedInvoiceAttachment.findUnique({
                where: { id: attachmentId },
            });
        }
        else {
            attachment = await prisma.invoiceAttachment.findUnique({
                where: { id: attachmentId },
            });
        }
        if (!attachment) {
            return res.status(404).json({
                success: false,
                message: '附件不存在',
                error: `Attachment not found: ${attachmentId}, type: ${type}`
            });
        }
        const filePath = attachment.filePath;
        if (!fs_1.default.existsSync(filePath)) {
            return res.status(404).json({
                success: false,
                message: '文件不存在',
                error: `File not found: ${filePath}`
            });
        }
        // 获取文件信息
        const stats = fs_1.default.statSync(filePath);
        const ext = path_1.default.extname(filePath).toLowerCase();
        // 使用原始文件名作为下载文件名
        const originalFileName = attachment.fileName;
        // 设置响应头
        if (preview === 'true') {
            // 预览模式
            if (ext === '.pdf') {
                res.setHeader('Content-Type', 'application/pdf');
                res.setHeader('Content-Disposition', 'inline; filename*=UTF-8\'\'' + encodeURIComponent(originalFileName));
                // 设置CORS头以支持iframe预览
                res.setHeader('X-Frame-Options', 'SAMEORIGIN');
                res.setHeader('Content-Security-Policy', 'frame-ancestors \'self\'');
            }
            else if (['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'].includes(ext)) {
                const mimeTypes = {
                    '.jpg': 'image/jpeg',
                    '.jpeg': 'image/jpeg',
                    '.png': 'image/png',
                    '.gif': 'image/gif',
                    '.webp': 'image/webp',
                    '.bmp': 'image/bmp',
                };
                res.setHeader('Content-Type', mimeTypes[ext] || 'application/octet-stream');
                res.setHeader('Content-Disposition', 'inline; filename*=UTF-8\'\'' + encodeURIComponent(originalFileName));
            }
            else {
                res.setHeader('Content-Type', 'application/octet-stream');
                res.setHeader('Content-Disposition', 'attachment; filename*=UTF-8\'\'' + encodeURIComponent(originalFileName));
            }
        }
        else {
            // 下载模式
            res.setHeader('Content-Type', 'application/octet-stream');
            res.setHeader('Content-Disposition', 'attachment; filename*=UTF-8\'\'' + encodeURIComponent(originalFileName));
        }
        res.setHeader('Content-Length', stats.size);
        res.setHeader('Cache-Control', 'public, max-age=31536000'); // 缓存一年
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
        // 发送文件
        const fileStream = fs_1.default.createReadStream(filePath);
        fileStream.pipe(res);
    }
    catch (error) {
        console.error('下载附件失败:', error);
        throw new errorHandler_1.AppError('下载附件失败', 500);
    }
}));
// 下载/预览文件（兼容旧接口）
router.get('/download/:filename', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { filename } = req.params;
    const { preview } = req.query;
    // 安全检查
    if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
        throw new errorHandler_1.AppError('无效的文件名', 400);
    }
    // 首先从数据库查找文件的实际路径
    let filePath = '';
    let actualFileName = filename;
    try {
        // 先在开具发票附件中查找
        const invoiceAttachment = await prisma.invoiceAttachment.findFirst({
            where: { fileName: filename }
        });
        if (invoiceAttachment) {
            filePath = invoiceAttachment.filePath;
            actualFileName = invoiceAttachment.fileName;
        }
        else {
            // 再在取得发票附件中查找
            const receivedAttachment = await prisma.receivedInvoiceAttachment.findFirst({
                where: { fileName: filename }
            });
            if (receivedAttachment) {
                filePath = receivedAttachment.filePath;
                actualFileName = receivedAttachment.fileName;
            }
        }
    }
    catch (error) {
        console.error('查询附件信息失败:', error);
    }
    // 如果数据库中没找到，尝试直接在文件系统中查找
    if (!filePath || !fs_1.default.existsSync(filePath)) {
        const possiblePaths = [
            path_1.default.join(invoiceUploadsDir, filename),
            path_1.default.join(tempUploadsDir, filename),
            path_1.default.join(uploadDir, filename),
        ];
        for (const possiblePath of possiblePaths) {
            if (fs_1.default.existsSync(possiblePath)) {
                filePath = possiblePath;
                break;
            }
        }
    }
    if (!filePath || !fs_1.default.existsSync(filePath)) {
        throw new errorHandler_1.AppError('文件不存在', 404);
    }
    // 获取文件信息
    const stats = fs_1.default.statSync(filePath);
    const ext = path_1.default.extname(filename).toLowerCase();
    // 使用实际文件名作为下载文件名
    let originalFileName = actualFileName;
    // 设置响应头
    if (preview === 'true') {
        // 预览模式
        if (ext === '.pdf') {
            res.setHeader('Content-Type', 'application/pdf');
            res.setHeader('Content-Disposition', 'inline; filename*=UTF-8\'\'' + encodeURIComponent(originalFileName));
            // 设置CORS头以支持iframe预览
            res.setHeader('X-Frame-Options', 'SAMEORIGIN');
            res.setHeader('Content-Security-Policy', 'frame-ancestors \'self\'');
        }
        else if (['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'].includes(ext)) {
            const mimeTypes = {
                '.jpg': 'image/jpeg',
                '.jpeg': 'image/jpeg',
                '.png': 'image/png',
                '.gif': 'image/gif',
                '.webp': 'image/webp',
                '.bmp': 'image/bmp',
            };
            res.setHeader('Content-Type', mimeTypes[ext] || 'application/octet-stream');
            res.setHeader('Content-Disposition', 'inline; filename*=UTF-8\'\'' + encodeURIComponent(originalFileName));
        }
        else {
            res.setHeader('Content-Type', 'application/octet-stream');
            res.setHeader('Content-Disposition', 'attachment; filename*=UTF-8\'\'' + encodeURIComponent(originalFileName));
        }
    }
    else {
        // 下载模式
        res.setHeader('Content-Type', 'application/octet-stream');
        res.setHeader('Content-Disposition', 'attachment; filename*=UTF-8\'\'' + encodeURIComponent(originalFileName));
    }
    res.setHeader('Content-Length', stats.size);
    res.setHeader('Cache-Control', 'public, max-age=31536000'); // 缓存一年
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    // 发送文件
    const fileStream = fs_1.default.createReadStream(filePath);
    fileStream.pipe(res);
}));
exports.default = router;
//# sourceMappingURL=upload.js.map