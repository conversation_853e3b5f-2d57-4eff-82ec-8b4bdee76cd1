const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始构建前端项目...');

try {
  // 1. 清理dist目录
  console.log('📁 清理构建目录...');
  if (fs.existsSync('dist')) {
    fs.rmSync('dist', { recursive: true, force: true });
  }

  // 2. 设置生产环境变量
  console.log('🔧 设置生产环境...');
  process.env.NODE_ENV = 'production';

  // 3. 构建项目
  console.log('🔨 构建React项目...');
  execSync('npm run build', { stdio: 'inherit' });

  // 4. 创建nginx配置文件
  console.log('📋 生成nginx配置...');
  const nginxConfig = `server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    # 处理React Router的路由
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API代理到后端
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # 静态资源缓存
    location ~* \\.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript;
}`;

  fs.writeFileSync('dist/nginx.conf', nginxConfig);

  // 5. 创建Docker配置
  console.log('🐳 生成Docker配置...');
  const dockerfile = `FROM nginx:alpine

# 复制构建文件
COPY dist/ /usr/share/nginx/html/

# 复制nginx配置
COPY dist/nginx.conf /etc/nginx/conf.d/default.conf

# 暴露端口
EXPOSE 80

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]`;

  fs.writeFileSync('Dockerfile', dockerfile);

  // 6. 创建部署说明
  const deploymentGuide = `# 发票管理系统前端部署指南

## 方式一：直接部署到Web服务器

1. 将 dist/ 目录中的所有文件上传到Web服务器根目录
2. 配置Web服务器（Apache/Nginx）支持SPA路由
3. 确保API请求能正确代理到后端服务器

## 方式二：使用Docker部署

1. 构建Docker镜像：
   \`\`\`bash
   docker build -t invoice-management-frontend .
   \`\`\`

2. 运行容器：
   \`\`\`bash
   docker run -d -p 80:80 invoice-management-frontend
   \`\`\`

## 方式三：使用Nginx

1. 安装Nginx
2. 将 dist/nginx.conf 复制到 /etc/nginx/conf.d/
3. 将 dist/ 目录内容复制到 /usr/share/nginx/html/
4. 重启Nginx服务

## 注意事项

- 确保后端API服务运行在 localhost:3001
- 如果后端地址不同，请修改nginx配置中的proxy_pass
- 生产环境建议启用HTTPS
`;

  fs.writeFileSync('DEPLOYMENT.md', deploymentGuide);

  console.log('✅ 前端构建完成！');
  console.log('📦 构建文件位于 dist/ 目录');
  console.log('📋 已生成以下文件：');
  console.log('   - dist/nginx.conf (Nginx配置)');
  console.log('   - Dockerfile (Docker配置)');
  console.log('   - DEPLOYMENT.md (部署说明)');

} catch (error) {
  console.error('❌ 构建失败:', error.message);
  process.exit(1);
}
