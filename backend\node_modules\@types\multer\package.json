{"name": "@types/multer", "version": "1.4.13", "description": "TypeScript definitions for multer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/multer", "license": "MIT", "contributors": [{"name": "jt000", "githubUsername": "jt000", "url": "https://github.com/jt000"}, {"name": "vili<PERSON><PERSON><PERSON>", "githubUsername": "vilic", "url": "https://github.com/vilic"}, {"name": "<PERSON>-<PERSON>", "githubUsername": "DavidBR-SW", "url": "https://github.com/DavidBR-SW"}, {"name": "<PERSON>", "githubUsername": "mxl", "url": "https://github.com/mxl"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "hyun<PERSON><PERSON>", "url": "https://github.com/hyunseob"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/b<PERSON><PERSON><PERSON>bas"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/multer"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "ea3009a3d338b242f4c077422a23fd37c202bfbb89cba2db6a6cfefc3c6a578d", "typeScriptVersion": "5.1"}