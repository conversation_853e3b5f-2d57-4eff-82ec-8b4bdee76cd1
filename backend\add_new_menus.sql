-- 添加新的报表菜单项
-- 首先查找报表中心的父菜单ID
SET @reports_parent_id = (SELECT id FROM menus WHERE `key` = 'reports' LIMIT 1);

-- 添加发票张数汇总菜单
INSERT INTO menus (`key`, name, path, icon, parentId, sort, isActive, description, createdAt, updatedAt)
VALUES (
  'reports-invoice-count-summary',
  '发票张数汇总',
  '/reports/invoice-count-summary',
  NULL,
  @reports_parent_id,
  30,
  1,
  '按公司和季度统计发票张数',
  NOW(),
  NOW()
);

-- 添加所属用户开票汇总菜单
INSERT INTO menus (`key`, name, path, icon, parentId, sort, isActive, description, createdAt, updatedAt)
VALUES (
  'reports-user-summary',
  '所属用户开票汇总',
  '/reports/user-summary',
  NULL,
  @reports_parent_id,
  40,
  1,
  '按所属用户统计开票金额汇总',
  NOW(),
  NOW()
);

-- 查看添加的菜单
SELECT id, `key`, name, path, parentId, sort, isActive, description 
FROM menus 
WHERE `key` IN ('reports-invoice-count-summary', 'reports-user-summary')
ORDER BY sort;
