-- 创建取得发票相关表的SQL脚本

-- 创建取得发票主表
CREATE TABLE IF NOT EXISTS `received_invoices` (
  `id` VARCHAR(191) NOT NULL,
  `invoiceNumber` VARCHAR(191) NOT NULL,
  `invoiceCode` VARCHAR(191) NOT NULL,
  `invoiceDate` DATETIME(3) NOT NULL,
  `amount` DECIMAL(15,2) NOT NULL,
  `taxAmount` DECIMAL(15,2) NOT NULL,
  `totalAmount` DECIMAL(15,2) NOT NULL,
  `buyerName` VARCHAR(191) NOT NULL,
  `buyerTaxId` VARCHAR(191) NOT NULL,
  `buyerAddress` VARCHAR(191) NULL,
  `buyerPhone` VARCHAR(191) NULL,
  `buyerBank` VARCHAR(191) NULL,
  `sellerName` VARCHAR(191) NOT NULL,
  `sellerTaxId` VARCHAR(191) NOT NULL,
  `sellerAddress` VARCHAR(191) NULL,
  `sellerPhone` VARCHAR(191) NULL,
  `sellerBank` VARCHAR(191) NULL,
  `invoiceType` ENUM('SPECIAL_VAT','ORDINARY_VAT','ELECTRONIC','RECEIPT','OTHER') NOT NULL,
  `status` ENUM('NORMAL','CANCELLED') NOT NULL DEFAULT 'NORMAL',
  `verificationStatus` ENUM('UNVERIFIED','VERIFIED','FAILED','DUPLICATE') NOT NULL DEFAULT 'UNVERIFIED',
  `drawer` VARCHAR(191) NULL,
  `remarks` TEXT NULL,
  `remark` TEXT NULL,
  `projectName` VARCHAR(191) NULL,
  `department` VARCHAR(191) NULL,
  `costCenter` VARCHAR(191) NULL,
  `isDuplicate` BOOLEAN NOT NULL DEFAULT FALSE,
  `isArchived` BOOLEAN NOT NULL DEFAULT FALSE,
  `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `companyId` VARCHAR(191) NOT NULL,
  
  PRIMARY KEY (`id`),
  UNIQUE INDEX `received_invoices_invoiceNumber_key` (`invoiceNumber`),
  INDEX `received_invoices_invoiceNumber_invoiceCode_idx` (`invoiceNumber`, `invoiceCode`),
  INDEX `received_invoices_companyId_idx` (`companyId`),
  INDEX `received_invoices_invoiceDate_idx` (`invoiceDate`),
  INDEX `received_invoices_status_idx` (`status`),
  
  CONSTRAINT `received_invoices_companyId_fkey` FOREIGN KEY (`companyId`) REFERENCES `companies`(`id`) ON DELETE CASCADE ON UPDATE CASCADE
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建取得发票明细表
CREATE TABLE IF NOT EXISTS `received_invoice_items` (
  `id` VARCHAR(191) NOT NULL,
  `itemName` VARCHAR(191) NOT NULL,
  `specification` VARCHAR(191) NULL,
  `unit` VARCHAR(191) NULL,
  `quantity` DECIMAL(10,4) NOT NULL,
  `unitPrice` DECIMAL(15,4) NOT NULL,
  `amount` DECIMAL(15,2) NOT NULL,
  `taxRate` DECIMAL(5,4) NOT NULL,
  `taxAmount` DECIMAL(15,2) NOT NULL,
  `totalAmount` DECIMAL(15,2) NOT NULL,
  `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `receivedInvoiceId` VARCHAR(191) NOT NULL,
  
  PRIMARY KEY (`id`),
  INDEX `received_invoice_items_receivedInvoiceId_idx` (`receivedInvoiceId`),
  
  CONSTRAINT `received_invoice_items_receivedInvoiceId_fkey` FOREIGN KEY (`receivedInvoiceId`) REFERENCES `received_invoices`(`id`) ON DELETE CASCADE ON UPDATE CASCADE
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建取得发票附件表
CREATE TABLE IF NOT EXISTS `received_invoice_attachments` (
  `id` VARCHAR(191) NOT NULL,
  `fileName` VARCHAR(191) NOT NULL,
  `filePath` VARCHAR(191) NOT NULL,
  `fileSize` INT NOT NULL,
  `mimeType` VARCHAR(191) NOT NULL,
  `fileType` ENUM('PDF','IMAGE','EXCEL','OTHER') NOT NULL,
  `isOriginal` BOOLEAN NOT NULL DEFAULT FALSE,
  `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `receivedInvoiceId` VARCHAR(191) NOT NULL,
  
  PRIMARY KEY (`id`),
  INDEX `received_invoice_attachments_receivedInvoiceId_idx` (`receivedInvoiceId`),
  
  CONSTRAINT `received_invoice_attachments_receivedInvoiceId_fkey` FOREIGN KEY (`receivedInvoiceId`) REFERENCES `received_invoices`(`id`) ON DELETE CASCADE ON UPDATE CASCADE
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建取得发票审计日志表
CREATE TABLE IF NOT EXISTS `received_invoice_audit_logs` (
  `id` VARCHAR(191) NOT NULL,
  `action` VARCHAR(191) NOT NULL,
  `tableName` VARCHAR(191) NOT NULL,
  `recordId` VARCHAR(191) NOT NULL,
  `oldValues` JSON NULL,
  `newValues` JSON NULL,
  `ipAddress` VARCHAR(191) NULL,
  `userAgent` VARCHAR(191) NULL,
  `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `userId` VARCHAR(191) NULL,
  `receivedInvoiceId` VARCHAR(191) NULL,
  
  PRIMARY KEY (`id`),
  INDEX `received_invoice_audit_logs_userId_idx` (`userId`),
  INDEX `received_invoice_audit_logs_tableName_recordId_idx` (`tableName`, `recordId`),
  INDEX `received_invoice_audit_logs_createdAt_idx` (`createdAt`),
  INDEX `received_invoice_audit_logs_receivedInvoiceId_idx` (`receivedInvoiceId`),
  
  CONSTRAINT `received_invoice_audit_logs_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `received_invoice_audit_logs_receivedInvoiceId_fkey` FOREIGN KEY (`receivedInvoiceId`) REFERENCES `received_invoices`(`id`) ON DELETE SET NULL ON UPDATE CASCADE
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

SELECT '取得发票相关表创建完成！' AS message;
