const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

async function addTableComments() {
  let connection;

  try {
    // 从环境变量解析数据库连接信息
    const databaseUrl = process.env.DATABASE_URL;
    if (!databaseUrl) {
      throw new Error('DATABASE_URL environment variable is not set');
    }

    // 解析数据库URL
    const url = new URL(databaseUrl);
    const connectionConfig = {
      host: url.hostname,
      port: parseInt(url.port) || 3306,
      user: url.username,
      password: url.password,
      database: url.pathname.slice(1), // 移除开头的 '/'
      multipleStatements: true // 允许执行多条SQL语句
    };

    console.log('正在连接数据库...');
    console.log(`主机: ${connectionConfig.host}:${connectionConfig.port}`);
    console.log(`数据库: ${connectionConfig.database}`);

    // 创建数据库连接
    connection = await mysql.createConnection(connectionConfig);
    console.log('数据库连接成功！');

    // 读取SQL文件
    const sqlFilePath = path.join(__dirname, 'add_table_comments.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

    console.log('正在执行SQL注释脚本...');

    // 分割SQL语句并逐条执行
    const sqlStatements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`共有 ${sqlStatements.length} 条SQL语句需要执行`);

    for (let i = 0; i < sqlStatements.length; i++) {
      const statement = sqlStatements[i];
      if (statement.trim()) {
        try {
          await connection.execute(statement);
          console.log(`✓ 执行第 ${i + 1} 条语句成功`);
        } catch (error) {
          console.error(`✗ 执行第 ${i + 1} 条语句失败:`, error.message);
          console.error(`语句内容: ${statement.substring(0, 100)}...`);
        }
      }
    }

    console.log('✅ 数据库表和字段注释添加完成！');
    console.log('已为以下表添加了详细的中文注释：');
    console.log('  - users (用户表)');
    console.log('  - companies (公司表)');
    console.log('  - invoices (开具发票主表)');
    console.log('  - received_invoices (取得发票主表)');
    console.log('  - invoice_items (开具发票明细表)');
    console.log('  - received_invoice_items (取得发票明细表)');
    console.log('  - invoice_attachments (开具发票附件表)');
    console.log('  - received_invoice_attachments (取得发票附件表)');
    console.log('  - audit_logs (开具发票审计日志表)');
    console.log('  - received_invoice_audit_logs (取得发票审计日志表)');

  } catch (error) {
    console.error('❌ 执行失败:', error.message);

    if (error.code === 'ECONNREFUSED') {
      console.error('无法连接到数据库，请检查：');
      console.error('1. 数据库服务是否正在运行');
      console.error('2. 连接参数是否正确');
      console.error('3. 网络连接是否正常');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('数据库访问被拒绝，请检查用户名和密码');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.error('数据库不存在，请检查数据库名称');
    }

    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

// 执行脚本
addTableComments();
