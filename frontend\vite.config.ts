import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react({
      // 添加React JSX运行时配置
      jsxRuntime: 'automatic',
      // 确保React在全局可用
      jsxImportSource: 'react'
    })
  ],
  server: {
    host: 'localhost',
    port: 5173,
    strictPort: true,
    open: true
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    minify: 'esbuild',
    rollupOptions: {
      output: {
        manualChunks: {
          // 将React相关库单独打包
          'react-vendor': ['react', 'react-dom'],
          // 将Ant Design单独打包
          'antd-vendor': ['antd', '@ant-design/icons'],
          // 将其他第三方库单独打包
          'vendor': ['axios', 'dayjs', 'react-router-dom']
        }
      }
    }
  },
  define: {
    // 确保React在全局可用
    global: 'globalThis',
  },
  optimizeDeps: {
    include: ['react', 'react-dom', 'antd', '@ant-design/icons']
  },
  base: './'
})
