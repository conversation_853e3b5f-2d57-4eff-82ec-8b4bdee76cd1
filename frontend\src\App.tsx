import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom';
import {
  ConfigProvider,
  App as AntApp,
  Layout,
  Menu,
  Button,
  Form,
  Input,
  Card,
  Typography,
  Space,
  message,
  Table,
  Tag,
  Modal,
  Popconfirm,
  Row,
  Col,
  Statistic,
  Progress,
  List,
  Avatar,
  Select,
  Divider,
  Dropdown,
  Collapse,
  DatePicker,
  Checkbox,
  InputNumber,
  Empty,
  Switch,
  Pagination,
  Spin,
  Tabs
} from 'antd';
import {
  UserOutlined,
  LockOutlined,
  DashboardOutlined,
  BankOutlined,
  FileTextOutlined,
  ImportOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UploadOutlined,
  MoneyCollectOutlined,
  TeamOutlined,
  FileProtectOutlined,
  RiseOutlined,

  CheckCircleOutlined,
  ExclamationCircleOutlined,
  FileImageOutlined,
  EyeOutlined,
  PieChartOutlined,
  ThunderboltOutlined,
  Bar<PERSON><PERSON>Outlined,
  SearchOutlined,
  DownloadOutlined,
  <PERSON>Outlined,
  <PERSON><PERSON>ircleOutlined,
  Line<PERSON><PERSON>Outlined,
  <PERSON>Outlined,
  Clock<PERSON>ircleOutlined,
  DollarOutlined,
  ReloadOutlined,
  CalendarOutlined,
  TableOutlined
} from '@ant-design/icons';
import zhCN from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import axios from 'axios';
import './App.css';

// 配置dayjs
dayjs.locale('zh-cn');

const { Header, Sider, Content } = Layout;
const { Title, Text } = Typography;

// API配置
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3001',
});

// 添加请求拦截器，自动添加认证头
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// 添加响应拦截器，处理认证错误
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      // 不要使用window.location.href，让React Router处理路由
      // window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// 登录组件
const Login: React.FC<{ onLogin: () => void }> = ({ onLogin }) => {
  const [loading, setLoading] = useState(false);

  // 设置登录页面标题
  useEffect(() => {
    document.title = '发票管理-登录';
  }, []);

  const handleLogin = async (values: { email: string; password: string }) => {
    setLoading(true);
    try {
      // 将email字段改为emailOrUsername发送到后端
      const loginData = {
        emailOrUsername: values.email,
        password: values.password
      };
      // console.log('正在尝试登录:', loginData.emailOrUsername);
      const response = await api.post('/api/auth/login', loginData);
      // console.log('登录响应:', response.data);
      if (response.data.success) {
        localStorage.setItem('token', response.data.data.token);
        // console.log('登录成功，token已保存');
        message.success('登录成功！');
        onLogin();
      }
    } catch (error: any) {
      console.error('登录失败:', error);
      // 显示具体的错误信息
      console.error('登录API调用失败:', error.response?.status, error.response?.data);
      message.error(error.response?.data?.message || '登录失败，请检查网络连接或联系管理员');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-container">
      {/* 动画人物 */}
      <div className="animated-person">
        <div className="person-head"></div>
        <div className="person-body">
          <div className="person-arms"></div>
        </div>
        <div className="person-legs">
          <div className="person-leg"></div>
          <div className="person-leg"></div>
        </div>
      </div>

      {/* 浮动图标 */}
      <div className="floating-icons">
        <div className="floating-icon">📊</div>
        <div className="floating-icon">💼</div>
        <div className="floating-icon">📋</div>
        <div className="floating-icon">💰</div>
      </div>

      {/* 云朵装饰 */}
      <div className="cloud cloud1"></div>
      <div className="cloud cloud2"></div>

      {/* 粒子效果 */}
      <div className="particles">
        <div className="particle"></div>
        <div className="particle"></div>
        <div className="particle"></div>
        <div className="particle"></div>
        <div className="particle"></div>
        <div className="particle"></div>
        <div className="particle"></div>
        <div className="particle"></div>
        <div className="particle"></div>
      </div>

      {/* 几何图形装饰 */}
      <div className="geometric-shapes">
        <div className="shape triangle"></div>
        <div className="shape circle"></div>
        <div className="shape square"></div>
      </div>

      {/* 光效装饰 */}
      <div className="light-rays">
        <div className="light-ray"></div>
        <div className="light-ray"></div>
        <div className="light-ray"></div>
      </div>

      <Card className="login-card" style={{ width: 400, zIndex: 10, position: 'relative' }}>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div style={{ textAlign: 'center' }}>
            <Title level={2}>发票管理系统</Title>
            <Text type="secondary">请登录您的账户</Text>
          </div>

          <Form onFinish={handleLogin} size="large">
            <Form.Item
              name="email"
              rules={[
                { required: true, message: '请输入用户名或邮箱地址!' }
              ]}
            >
              <Input prefix={<UserOutlined />} placeholder="用户名或邮箱地址" />
            </Form.Item>

            <Form.Item
              name="password"
              rules={[{ required: true, message: '请输入密码!' }]}
            >
              <Input.Password prefix={<LockOutlined />} placeholder="密码" />
            </Form.Item>

            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading} style={{ width: '100%' }}>
                登录
              </Button>
            </Form.Item>
          </Form>


        </Space>
      </Card>
    </div>
  );
};

// 仪表板组件
const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [monthlyData, setMonthlyData] = useState<any[]>([]);
  const [companyStats, setCompanyStats] = useState<any[]>([]);
  const [recentInvoices, setRecentInvoices] = useState<any[]>([]);
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());
  const [selectedTimeRange, setSelectedTimeRange] = useState<string>('year');
  const [operationLogs, setOperationLogs] = useState<any[]>([]);
  const [quickStats, setQuickStats] = useState<any>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [quarterlyCompanyStats, setQuarterlyCompanyStats] = useState<any[]>([]);
  const [currentTime, setCurrentTime] = useState<string>(new Date().toLocaleString('zh-CN'));

  useEffect(() => {
    fetchDashboardData();
    // 设置自动刷新
    const interval = setInterval(() => {
      fetchDashboardData(true);
    }, 30000); // 30秒刷新一次
    return () => clearInterval(interval);
  }, [selectedYear, selectedTimeRange]);

  // 实时时间更新
  useEffect(() => {
    const timeInterval = setInterval(() => {
      setCurrentTime(new Date().toLocaleString('zh-CN'));
    }, 1000); // 每秒更新一次
    return () => clearInterval(timeInterval);
  }, []);

  const fetchDashboardData = async (isAutoRefresh = false) => {
    try {
      if (isAutoRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      // 并行获取多个数据源，添加错误处理
      const requests = [
        api.get(`/api/invoices/stats/summary?year=${selectedYear}`).catch(() => ({ data: { data: null } })),
        api.get(`/api/invoices/stats/quarterly?year=${selectedYear}`).catch(() => ({ data: { data: { companies: [] } } })),
        api.get('/api/invoices?page=1&pageSize=20&sortBy=createdAt&sortOrder=desc').catch(() => ({ data: { data: { data: [] } } })),
        api.get(`/api/invoices/stats/company-summary?years=${selectedYear}`).catch(() => ({ data: { data: { companies: [] } } }))
      ];

      const [statsRes, quarterlyRes, invoicesRes, companySummaryRes] = await Promise.all(requests);

      console.log('API响应状态:', {
        stats: (statsRes as any).status || 'error',
        quarterly: (quarterlyRes as any).status || 'error',
        invoices: (invoicesRes as any).status || 'error',
        companySummary: (companySummaryRes as any).status || 'error'
      });

      // 安全设置统计数据
      const statsData = statsRes.data?.data || {
        totalInvoices: 0,
        totalAmount: 0,
        statusStats: []
      };
      setStats(statsData);

      // 处理季度数据为月度数据
      const companies = quarterlyRes.data?.data?.companies || [];
      if (companies && companies.length > 0) {
        // 计算每月总计
        const monthlyTotals = [];
        const months = ['jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'];
        const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];

        months.forEach((month, index) => {
          const total = companies.reduce((sum: number, company: any) => {
            const monthlyData = company?.monthly || {};
            return sum + (monthlyData[month] || 0);
          }, 0);
          monthlyTotals.push({
            month: monthNames[index],
            amount: total,
            count: companies.reduce((sum: number, company: any) => {
              const monthlyData = company?.monthly || {};
              return sum + (monthlyData[month] > 0 ? 1 : 0);
            }, 0)
          });
        });

        setMonthlyData(monthlyTotals);

        // 取前8个公司作为公司统计
        const topCompanies = companies
          .map((company: any) => {
            const monthlyData = company?.monthly || {};
            const total = Object.values(monthlyData).reduce((sum: number, val: any) => sum + (Number(val) || 0), 0);
            return {
              name: company?.name || '未知公司',
              taxId: company?.taxId || '',
              total: total,
              monthlyData: monthlyData
            };
          })
          .filter((company: any) => company.total > 0)
          .sort((a: any, b: any) => b.total - a.total)
          .slice(0, 8);

        setCompanyStats(topCompanies);
      } else {
        setMonthlyData([]);
        setCompanyStats([]);
      }

      // 处理公司汇总数据
      const companySummary = companySummaryRes.data?.data?.companies || [];
      if (companySummary && companySummary.length > 0) {
        // 计算快速统计
        const totalCompanies = companySummary.length;
        const activeCompanies = companySummary.filter((c: any) => {
          const yearlyData = c?.yearlyData || {};
          return Object.values(yearlyData).some((val: any) => Number(val) > 0);
        }).length;

        const totalAmount = companySummary.reduce((sum: number, c: any) => {
          const yearlyData = c?.yearlyData || {};
          return sum + (Object.values(yearlyData).reduce((s: number, v: any) => s + (Number(v) || 0), 0) as number);
        }, 0);

        const avgAmount = totalCompanies > 0 ? totalAmount / totalCompanies : 0;

        setQuickStats({
          totalCompanies,
          activeCompanies,
          avgAmount,
          inactiveCompanies: totalCompanies - activeCompanies
        });
      } else {
        setQuickStats({
          totalCompanies: 0,
          activeCompanies: 0,
          avgAmount: 0,
          inactiveCompanies: 0
        });
      }

      // 设置最近发票数据
      const invoicesData = invoicesRes.data?.data?.data || [];
      setRecentInvoices(invoicesData);

      // 处理本季度公司统计数据 - 使用现有的quarterly数据
      if (companies && companies.length > 0) {
        // 计算当前季度
        const currentDate = new Date();
        const currentQuarter = Math.floor(currentDate.getMonth() / 3) + 1;

        // 计算每个公司本季度和年度的开票总金额
        const quarterlyStats = companies.map((company: any) => {
          const monthlyData = company?.monthly || {};
          let quarterlyAmount = 0;
          let yearlyAmount = 0;

          // 计算年度总金额
          yearlyAmount = Object.values(monthlyData).reduce((sum: number, val: any) => sum + (Number(val) || 0), 0) as number;

          // 根据当前季度计算对应月份的总金额
          switch (currentQuarter) {
            case 1: // Q1: 1-3月
              quarterlyAmount = (monthlyData.jan || 0) + (monthlyData.feb || 0) + (monthlyData.mar || 0);
              break;
            case 2: // Q2: 4-6月
              quarterlyAmount = (monthlyData.apr || 0) + (monthlyData.may || 0) + (monthlyData.jun || 0);
              break;
            case 3: // Q3: 7-9月
              quarterlyAmount = (monthlyData.jul || 0) + (monthlyData.aug || 0) + (monthlyData.sep || 0);
              break;
            case 4: // Q4: 10-12月
              quarterlyAmount = (monthlyData.oct || 0) + (monthlyData.nov || 0) + (monthlyData.dec || 0);
              break;
          }

          return {
            name: company?.name || '未知公司',
            taxId: company?.taxId || '',
            quarterlyAmount: quarterlyAmount,
            yearlyAmount: yearlyAmount,
            isOverLimit: quarterlyAmount > 300000, // 超过30万标记
            monthly: monthlyData // 保留月度数据用于表格显示
          };
        }).filter((company: any) => company.quarterlyAmount > 0 || company.yearlyAmount > 0) // 只显示有开票的公司
          .sort((a: any, b: any) => b.quarterlyAmount - a.quarterlyAmount); // 按季度金额降序排列

        setQuarterlyCompanyStats(quarterlyStats);
      } else {
        setQuarterlyCompanyStats([]);
      }

    } catch (error) {
      console.error('获取仪表板数据失败:', error);
      if (!isAutoRefresh) {
        message.error('获取仪表板数据失败');
      }

      // 设置默认值以防止错误
      setStats({
        totalInvoices: 0,
        totalAmount: 0,
        statusStats: []
      });
      setMonthlyData([]);
      setCompanyStats([]);
      setRecentInvoices([]);
      setQuickStats({
        totalCompanies: 0,
        activeCompanies: 0,
        avgAmount: 0,
        inactiveCompanies: 0
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // 格式化金额
  const formatAmount = (amount: number) => {
    if (!amount || isNaN(amount)) return '¥0万';
    if (amount >= 10000) {
      const wanAmount = amount / 10000;
      // 如果小数部分为0，只显示整数；否则显示完整小数
      if (wanAmount % 1 === 0) {
        return `¥${wanAmount}万`;
      } else {
        // 移除末尾的0，保留有意义的小数位
        return `¥${wanAmount.toFixed(2).replace(/\.?0+$/, '')}万`;
      }
    }
    return amount.toLocaleString();
  };

  // 安全获取状态统计数据
  const getStatusCount = (status: string) => {
    if (!stats?.statusStats || !Array.isArray(stats.statusStats)) {
      return 0;
    }
    const statusItem = stats.statusStats.find((s: any) => s.status === status);
    return statusItem?._count?._all || 0;
  };

  // 计算本月新增（当前月份的发票数量）
  const getCurrentMonthStats = () => {
    if (!monthlyData || monthlyData.length === 0 || !recentInvoices) {
      return { count: 0, amount: 0 };
    }

    const currentMonth = new Date().getMonth(); // 0-11
    const currentMonthData = monthlyData[currentMonth];

    // 安全地过滤当月发票
    const currentMonthInvoices = recentInvoices.filter(invoice => {
      try {
        if (!invoice?.invoiceDate) return false;
        const invoiceMonth = new Date(invoice.invoiceDate).getMonth();
        return invoiceMonth === currentMonth;
      } catch (error) {
        return false;
      }
    });

    return {
      count: currentMonthInvoices.length,
      amount: currentMonthData?.amount || 0
    };
  };

  const currentMonthStats = getCurrentMonthStats();

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '60vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        borderRadius: '20px',
        margin: '24px',
        color: 'white'
      }}>
        <Spin size="large" style={{ color: 'white' }} />
        <div style={{ marginTop: 24, fontSize: '18px', fontWeight: 'bold' }}>
          正在加载仪表板数据...
        </div>
        <div style={{ marginTop: 8, opacity: 0.8 }}>
          请稍候，正在获取最新的发票统计信息
        </div>
      </div>
    );
  }

  return (
    <div style={{
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
      height: 'calc(100vh - 150px)',
      overflow: 'hidden',
      display: 'flex',
      flexDirection: 'column'
    }}>
      <div style={{
        flex: 1,
        overflow: 'auto',
        padding: '16px'
      }}>
      {/* 头部区域 */}
      <div style={{
        marginBottom: 32,
        background: 'rgba(255, 255, 255, 0.9)',
        borderRadius: '20px',
        padding: '24px',
        backdropFilter: 'blur(10px)',
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
      }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Title level={1} style={{
              margin: 0,
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              fontSize: '32px',
              fontWeight: 'bold'
            }}>
              <ClockCircleOutlined style={{ marginRight: 12, color: '#667eea' }} />
              {currentTime}
            </Title>
          </Col>
          <Col>
            <Space size="large">
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={() => fetchDashboardData()}
                style={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  border: 'none',
                  borderRadius: '12px',
                  height: '40px',
                  paddingLeft: '20px',
                  paddingRight: '20px'
                }}
              >
                刷新数据
              </Button>
              <Select
                value={selectedYear}
                onChange={setSelectedYear}
                style={{ width: 120 }}
                size="large"
                suffixIcon={<CalendarOutlined />}
              >
                {Array.from({ length: 10 }, (_, i) => {
                  const year = new Date().getFullYear() - i;
                  return (
                    <Select.Option key={year} value={year}>{year}年</Select.Option>
                  );
                })}
              </Select>
            </Space>
          </Col>
        </Row>
      </div>

      {/* 核心指标卡片 */}
      <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card
            hoverable
            style={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              border: 'none',
              borderRadius: '20px',
              boxShadow: '0 10px 30px rgba(102, 126, 234, 0.3)',
              transition: 'all 0.3s ease',
              overflow: 'hidden',
              position: 'relative'
            }}
            styles={{ body: { padding: '24px' } }}
          >
            <div style={{ position: 'absolute', top: '-20px', right: '-20px', width: '80px', height: '80px', background: 'rgba(255,255,255,0.1)', borderRadius: '50%' }} />
            <div style={{ position: 'relative', zIndex: 1 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 16 }}>
                <div>
                  <div style={{ color: 'rgba(255,255,255,0.8)', fontSize: '14px', marginBottom: 8 }}>发票总数</div>
                  <div style={{ color: 'white', fontSize: '32px', fontWeight: 'bold', lineHeight: 1 }}>
                    {(stats?.totalInvoices || 0).toLocaleString()}
                  </div>
                  <div style={{ color: 'rgba(255,255,255,0.6)', fontSize: '12px', marginTop: 4 }}>
                    全部发票数量统计
                  </div>
                </div>
                <div style={{
                  background: 'rgba(255,255,255,0.2)',
                  borderRadius: '12px',
                  padding: '12px',
                  backdropFilter: 'blur(10px)'
                }}>
                  <FileTextOutlined style={{ color: 'white', fontSize: '24px' }} />
                </div>
              </div>
              <div style={{
                background: 'rgba(255,255,255,0.1)',
                height: '4px',
                borderRadius: '2px',
                overflow: 'hidden'
              }}>
                <div style={{
                  background: 'rgba(255,255,255,0.8)',
                  height: '100%',
                  width: '85%',
                  borderRadius: '2px',
                  transition: 'width 1s ease'
                }} />
              </div>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card
            hoverable
            style={{
              background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
              border: 'none',
              borderRadius: '20px',
              boxShadow: '0 10px 30px rgba(240, 147, 251, 0.3)',
              transition: 'all 0.3s ease',
              overflow: 'hidden',
              position: 'relative'
            }}
            styles={{ body: { padding: '24px' } }}
          >
            <div style={{ position: 'absolute', top: '-20px', right: '-20px', width: '80px', height: '80px', background: 'rgba(255,255,255,0.1)', borderRadius: '50%' }} />
            <div style={{ position: 'relative', zIndex: 1 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 16 }}>
                <div>
                  <div style={{ color: 'rgba(255,255,255,0.8)', fontSize: '14px', marginBottom: 8 }}>总金额</div>
                  <div style={{ color: 'white', fontSize: '32px', fontWeight: 'bold', lineHeight: 1 }}>
                    {formatAmount(stats?.totalAmount || 0)}
                  </div>
                  <div style={{ color: 'rgba(255,255,255,0.6)', fontSize: '12px', marginTop: 4 }}>
                    发票总金额统计
                  </div>
                </div>
                <div style={{
                  background: 'rgba(255,255,255,0.2)',
                  borderRadius: '12px',
                  padding: '12px',
                  backdropFilter: 'blur(10px)'
                }}>
                  <DollarOutlined style={{ color: 'white', fontSize: '24px' }} />
                </div>
              </div>
              <div style={{
                background: 'rgba(255,255,255,0.1)',
                height: '4px',
                borderRadius: '2px',
                overflow: 'hidden'
              }}>
                <div style={{
                  background: 'rgba(255,255,255,0.8)',
                  height: '100%',
                  width: '92%',
                  borderRadius: '2px',
                  transition: 'width 1s ease'
                }} />
              </div>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card
            hoverable
            style={{
              background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
              border: 'none',
              borderRadius: '20px',
              boxShadow: '0 10px 30px rgba(79, 172, 254, 0.3)',
              transition: 'all 0.3s ease',
              overflow: 'hidden',
              position: 'relative'
            }}
            styles={{ body: { padding: '24px' } }}
          >
            <div style={{ position: 'absolute', top: '-20px', right: '-20px', width: '80px', height: '80px', background: 'rgba(255,255,255,0.1)', borderRadius: '50%' }} />
            <div style={{ position: 'relative', zIndex: 1 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 16 }}>
                <div>
                  <div style={{ color: 'rgba(255,255,255,0.8)', fontSize: '14px', marginBottom: 8 }}>本月新增</div>
                  <div style={{ color: 'white', fontSize: '32px', fontWeight: 'bold', lineHeight: 1 }}>
                    {currentMonthStats.count}
                  </div>
                  <div style={{ color: 'rgba(255,255,255,0.6)', fontSize: '12px', marginTop: 4 }}>
                    金额: {formatAmount(currentMonthStats.amount)}元
                  </div>
                </div>
                <div style={{
                  background: 'rgba(255,255,255,0.2)',
                  borderRadius: '12px',
                  padding: '12px',
                  backdropFilter: 'blur(10px)'
                }}>
                  <PlusOutlined style={{ color: 'white', fontSize: '24px' }} />
                </div>
              </div>
              <div style={{
                background: 'rgba(255,255,255,0.1)',
                height: '4px',
                borderRadius: '2px',
                overflow: 'hidden'
              }}>
                <div style={{
                  background: 'rgba(255,255,255,0.8)',
                  height: '100%',
                  width: '68%',
                  borderRadius: '2px',
                  transition: 'width 1s ease'
                }} />
              </div>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card
            hoverable
            style={{
              background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
              border: 'none',
              borderRadius: '20px',
              boxShadow: '0 10px 30px rgba(67, 233, 123, 0.3)',
              transition: 'all 0.3s ease',
              overflow: 'hidden',
              position: 'relative'
            }}
            styles={{ body: { padding: '24px' } }}
          >
            <div style={{ position: 'absolute', top: '-20px', right: '-20px', width: '80px', height: '80px', background: 'rgba(255,255,255,0.1)', borderRadius: '50%' }} />
            <div style={{ position: 'relative', zIndex: 1 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 16 }}>
                <div>
                  <div style={{ color: 'rgba(255,255,255,0.8)', fontSize: '14px', marginBottom: 8 }}>正常发票</div>
                  <div style={{ color: 'white', fontSize: '32px', fontWeight: 'bold', lineHeight: 1 }}>
                    {getStatusCount('NORMAL').toLocaleString()}
                  </div>
                  <div style={{ color: 'rgba(255,255,255,0.6)', fontSize: '12px', marginTop: 4 }}>
                    状态正常的发票
                  </div>
                </div>
                <div style={{
                  background: 'rgba(255,255,255,0.2)',
                  borderRadius: '12px',
                  padding: '12px',
                  backdropFilter: 'blur(10px)'
                }}>
                  <CheckCircleOutlined style={{ color: 'white', fontSize: '24px' }} />
                </div>
              </div>
              <div style={{
                background: 'rgba(255,255,255,0.1)',
                height: '4px',
                borderRadius: '2px',
                overflow: 'hidden'
              }}>
                <div style={{
                  background: 'rgba(255,255,255,0.8)',
                  height: '100%',
                  width: `${Math.round((getStatusCount('NORMAL') / (stats?.totalInvoices || 1)) * 100)}%`,
                  borderRadius: '2px',
                  transition: 'width 1s ease'
                }} />
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 本季度公司开票统计 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col xs={24}>
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <TrophyOutlined style={{ color: '#1890ff' }} />
                <span>季度/年度公司开票统计</span>
                <span style={{ fontSize: '12px', color: '#999', marginLeft: '8px' }}>
                  (超过30万元的公司将以红色背景显示)
                </span>
              </div>
            }
            extra={<Button type="link" onClick={() => navigate('/reports/quarterly-summary')}>查看详情</Button>}
            style={{ borderRadius: '12px' }}
          >
            <div style={{ overflow: 'hidden' }}>
              {quarterlyCompanyStats.length > 0 ? (
                <div style={{
                  display: 'flex',
                  gap: '16px',
                  overflowX: 'auto',
                  paddingBottom: '8px',
                  scrollbarWidth: 'thin'
                }}>
                  {quarterlyCompanyStats.map((item: any, index: number) => (
                    <div
                      key={index}
                      style={{
                        minWidth: 'auto',
                        maxWidth: 'none',
                        flexShrink: 0,
                        width: 'fit-content'
                      }}
                    >
                      <Card
                        size="small"
                        hoverable
                        style={{
                          backgroundColor: item.isOverLimit ? 'rgba(255, 77, 79, 0.1)' : 'rgba(24, 144, 255, 0.05)',
                          border: item.isOverLimit ? '2px solid #ff4d4f' : '1px solid #d9d9d9',
                          borderRadius: '12px',
                          transition: 'all 0.3s ease',
                          height: '120px',
                          position: 'relative'
                        }}
                        styles={{ body: { padding: '12px', position: 'relative' } }}
                      >
                        <div style={{ textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
                          {/* 排名徽章 - 调整到卡片内部 */}
                          <div style={{
                            position: 'absolute',
                            top: '8px',
                            right: '8px',
                            width: '24px',
                            height: '24px',
                            borderRadius: '50%',
                            backgroundColor: item.isOverLimit ? '#ff4d4f' : (index < 3 ? ['#ffd700', '#c0c0c0', '#cd7f32'][index] : '#1890ff'),
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: 'white',
                            fontSize: '12px',
                            fontWeight: 'bold',
                            zIndex: 1
                          }}>
                            {index + 1}
                          </div>

                          {/* 公司名称 */}
                          <div style={{
                            fontWeight: 'bold',
                            fontSize: '14px',
                            color: item.isOverLimit ? '#ff4d4f' : '#333',
                            marginBottom: '8px',
                            paddingRight: '32px', // 为右上角徽章留出空间
                            wordWrap: 'break-word',
                            lineHeight: '1.2'
                          }} title={item.name}>
                            {item.name}
                          </div>

                          {/* 季度金额 - 格式化为万元 */}
                          <div style={{
                            color: item.isOverLimit ? '#ff4d4f' : '#1890ff',
                            fontWeight: 'bold',
                            fontSize: '16px',
                            marginBottom: '4px'
                          }}>
                            季度: ¥{(item.quarterlyAmount / 10000).toFixed(2)}万
                          </div>

                          {/* 年度金额 - 格式化为万元 */}
                          <div style={{
                            color: '#666',
                            fontSize: '14px',
                            marginBottom: '8px'
                          }}>
                            年度: ¥{(item.yearlyAmount / 10000).toFixed(2)}万
                          </div>

                          {/* 超限标签 */}
                          {item.isOverLimit && (
                            <div style={{
                              fontSize: '12px',
                              color: '#ff4d4f',
                              backgroundColor: 'rgba(255, 77, 79, 0.2)',
                              padding: '2px 8px',
                              borderRadius: '12px',
                              alignSelf: 'center'
                            }}>
                              超限
                            </div>
                          )}
                        </div>
                      </Card>
                    </div>
                  ))}
                </div>
              ) : (
                <div style={{ textAlign: 'center', color: '#999', padding: '40px 0' }}>
                  <TrophyOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                  <div>暂无本季度开票数据</div>
                </div>
              )}
            </div>
          </Card>
        </Col>
      </Row>

      {/* 季度开票汇总表格 */}
      <Row gutter={[16, 16]}>
        <Col xs={24}>
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <TableOutlined style={{ color: '#1890ff' }} />
                <span>{selectedYear}年季度开票汇总</span>
              </div>
            }
            extra={<Button type="link" onClick={() => navigate('/reports/quarterly-summary')}>查看详情</Button>}
            style={{ borderRadius: '12px' }}
          >
            {quarterlyCompanyStats.length > 0 ? (
              <Table
                columns={[
                  {
                    title: '序号',
                    key: 'index',
                    width: 80,
                    align: 'center' as const,
                    render: (_: any, __: any, index: number) => index + 1,
                  },
                  {
                    title: '公司名称',
                    dataIndex: 'name',
                    key: 'name',
                    width: 300,
                    fixed: 'left' as const,
                  },
                  {
                    title: '一季度',
                    key: 'q1',
                    width: 150,
                    align: 'right' as const,
                    className: 'quarterly-column',
                    onHeaderCell: () => ({
                      style: { backgroundColor: '#bae7ff', fontWeight: 'bold' }
                    }),
                    onCell: (record: any) => {
                      const total = (record.monthly?.jan || 0) + (record.monthly?.feb || 0) + (record.monthly?.mar || 0);
                      let backgroundColor = '#e6f7ff';
                      if (total >= 250000 && total < 300000) {
                        backgroundColor = 'rgba(255, 165, 0, 0.8)'; // 80%透明橘色
                      } else if (total >= 300000) {
                        backgroundColor = 'rgba(255, 0, 0, 0.7)'; // 70%透明红色
                      }
                      return { style: { backgroundColor } };
                    },
                    render: (_, record: any) => {
                      // 一季度：1-3月累加
                      const total = (record.monthly?.jan || 0) + (record.monthly?.feb || 0) + (record.monthly?.mar || 0);
                      return <span style={{ fontWeight: 'bold' }}>{formatAmount(total)}</span>;
                    },
                  },
                  {
                    title: '二季度',
                    key: 'q2',
                    width: 150,
                    align: 'right' as const,
                    className: 'quarterly-column',
                    onHeaderCell: () => ({
                      style: { backgroundColor: '#bae7ff', fontWeight: 'bold' }
                    }),
                    onCell: (record: any) => {
                      const total = (record.monthly?.apr || 0) + (record.monthly?.may || 0) + (record.monthly?.jun || 0);
                      let backgroundColor = '#e6f7ff';
                      if (total >= 250000 && total < 300000) {
                        backgroundColor = 'rgba(255, 165, 0, 0.8)'; // 80%透明橘色
                      } else if (total >= 300000) {
                        backgroundColor = 'rgba(255, 0, 0, 0.7)'; // 70%透明红色
                      }
                      return { style: { backgroundColor } };
                    },
                    render: (_, record: any) => {
                      // 二季度：4-6月累加
                      const total = (record.monthly?.apr || 0) + (record.monthly?.may || 0) + (record.monthly?.jun || 0);
                      return <span style={{ fontWeight: 'bold' }}>{formatAmount(total)}</span>;
                    },
                  },
                  {
                    title: '三季度',
                    key: 'q3',
                    width: 150,
                    align: 'right' as const,
                    className: 'quarterly-column',
                    onHeaderCell: () => ({
                      style: { backgroundColor: '#bae7ff', fontWeight: 'bold' }
                    }),
                    onCell: (record: any) => {
                      const total = (record.monthly?.jul || 0) + (record.monthly?.aug || 0) + (record.monthly?.sep || 0);
                      let backgroundColor = '#e6f7ff';
                      if (total >= 250000 && total < 300000) {
                        backgroundColor = 'rgba(255, 165, 0, 0.8)'; // 80%透明橘色
                      } else if (total >= 300000) {
                        backgroundColor = 'rgba(255, 0, 0, 0.7)'; // 70%透明红色
                      }
                      return { style: { backgroundColor } };
                    },
                    render: (_, record: any) => {
                      // 三季度：7-9月累加
                      const total = (record.monthly?.jul || 0) + (record.monthly?.aug || 0) + (record.monthly?.sep || 0);
                      return <span style={{ fontWeight: 'bold' }}>{formatAmount(total)}</span>;
                    },
                  },
                  {
                    title: '四季度',
                    key: 'q4',
                    width: 150,
                    align: 'right' as const,
                    className: 'quarterly-column',
                    onHeaderCell: () => ({
                      style: { backgroundColor: '#bae7ff', fontWeight: 'bold' }
                    }),
                    onCell: (record: any) => {
                      const total = (record.monthly?.oct || 0) + (record.monthly?.nov || 0) + (record.monthly?.dec || 0);
                      let backgroundColor = '#e6f7ff';
                      if (total >= 250000 && total < 300000) {
                        backgroundColor = 'rgba(255, 165, 0, 0.8)'; // 80%透明橘色
                      } else if (total >= 300000) {
                        backgroundColor = 'rgba(255, 0, 0, 0.7)'; // 70%透明红色
                      }
                      return { style: { backgroundColor } };
                    },
                    render: (_, record: any) => {
                      // 四季度：10-12月累加
                      const total = (record.monthly?.oct || 0) + (record.monthly?.nov || 0) + (record.monthly?.dec || 0);
                      return <span style={{ fontWeight: 'bold' }}>{formatAmount(total)}</span>;
                    },
                  },
                  {
                    title: '年度总计',
                    key: 'total',
                    width: 150,
                    align: 'right' as const,
                    fixed: 'right' as const,
                    onHeaderCell: () => ({
                      style: { backgroundColor: '#52c41a', color: 'white', fontWeight: 'bold' }
                    }),
                    onCell: () => ({
                      style: { backgroundColor: '#f6ffed' }
                    }),
                    render: (_, record: any) => {
                      // 年度总计使用yearlyAmount字段
                      return <span style={{ fontWeight: 'bold', color: '#52c41a', fontSize: '16px' }}>{formatAmount(record.yearlyAmount || 0)}</span>;
                    },
                  },
                ]}
                dataSource={quarterlyCompanyStats.slice(0, 10)} // 只显示前10条
                rowKey={(record) => record.id || record.name}
                pagination={false}
                scroll={{ x: 1000 }}
                size="small"
                bordered
                locale={{ emptyText: '暂无数据，请先在开票管理中添加发票数据' }}
              />
            ) : (
              <div style={{ textAlign: 'center', color: '#999', padding: '40px 0' }}>
                <TableOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                <div>暂无{selectedYear}年季度开票数据</div>
              </div>
            )}
          </Card>
        </Col>
      </Row>
      </div>
    </div>
  );
};

// 公司管理组件
const Companies: React.FC<{ userInfo?: any }> = ({ userInfo }) => {
  const [companies, setCompanies] = useState([]);
  const [loading, setLoading] = useState(true);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [editingCompany, setEditingCompany] = useState<any>(null);
  const [searchText, setSearchText] = useState('');
  const [organizationFilter, setOrganizationFilter] = useState('');
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [importResult, setImportResult] = useState<any>(null);
  const [uploading, setUploading] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState<any>(null);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [form] = Form.useForm();

  useEffect(() => {
    fetchCompanies();
  }, []);

  const fetchCompanies = async (search = '', organization = '', page = 1, pageSize?: number) => {
    try {
      setLoading(true);
      const currentPageSize = pageSize || pagination.pageSize;
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: currentPageSize.toString()
      });
      if (search) params.append('search', search);
      if (organization) params.append('organization', organization);
      const response = await api.get(`/api/companies?${params}`);
      const data = response.data.data;
      setCompanies(data?.data || data || []);
      setPagination({
        current: page,
        pageSize: currentPageSize,
        total: data?.pagination?.total || data?.total || 0,
      });
    } catch (error) {
      console.error('获取公司列表失败:', error);
      message.error('获取公司列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
    fetchCompanies(value, organizationFilter, 1);
  };

  const handleOrganizationFilter = (value: string) => {
    setOrganizationFilter(value);
    fetchCompanies(searchText, value, 1);
  };

  const handleTableChange = (paginationConfig: any) => {
    fetchCompanies(searchText, organizationFilter, paginationConfig.current);
  };

  const handleCompanyImport = async (file: File) => {
    setUploading(true);
    setImportResult(null);

    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await api.post('/api/companies/import', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      const importResult = response.data.data;
      setImportResult(importResult);

      // 显示导入结果
      if (importResult.successCount > 0) {
        message.success(`公司导入成功！成功导入 ${importResult.successCount} 条记录`);
      } else {
        message.success(`公司导入完成！成功导入 ${importResult.successCount} 条记录`);
      }

      fetchCompanies(searchText, organizationFilter);
    } catch (error: any) {
      console.error('公司导入失败:', error);
      message.error(error.response?.data?.message || '公司导入失败');
    } finally {
      setUploading(false);
    }
  };

  const handleDownloadCompanyTemplate = async () => {
    try {
      const response = await api.get('/api/companies/template', { responseType: 'blob' });
      const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = '公司导入模板.xlsx';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success('模板下载成功！');
    } catch (error) {
      console.error('下载模板失败:', error);
      message.error('下载模板失败');
    }
  };

  const handleEdit = (record: any) => {
    setEditingCompany(record);
    // 转换日期字段为dayjs对象
    const formData = {
      ...record,
      registrationDate: record.registrationDate ? dayjs(record.registrationDate) : null,
      lastInvoiceDate: record.lastInvoiceDate ? dayjs(record.lastInvoiceDate) : null,
    };
    form.setFieldsValue(formData);
    setEditModalVisible(true);
  };

  const handleAdd = () => {
    setEditingCompany(null);
    form.resetFields();
    // 默认填入当前登录用户姓名到所属用户字段
    form.setFieldsValue({
      organization: userInfo?.name || ''
    });
    setAddModalVisible(true);
  };

  const handleViewDetail = (record: any) => {
    setSelectedCompany(record);
    setDetailModalVisible(true);
  };

  const handleDelete = async (record: any) => {
    try {
      await api.delete(`/api/companies/${record.id}`);
      message.success('公司删除成功！');
      fetchCompanies(searchText, organizationFilter);
    } catch (error) {
      console.error('删除公司失败:', error);
      message.error('删除公司失败');
    }
  };

  const handleSave = async () => {
    try {
      const values = await form.validateFields();

      // 转换日期字段为ISO字符串，处理空字符串
      const submitData = {
        ...values,
        registrationDate: values.registrationDate ? values.registrationDate.toISOString() : null,
        lastInvoiceDate: values.lastInvoiceDate ? values.lastInvoiceDate.toISOString() : null,
        // 处理空字符串字段
        email: values.email?.trim() || null,
        phone: values.phone?.trim() || null,
        address: values.address?.trim() || null,
        contact: values.contact?.trim() || null,
        organization: values.organization?.trim() || null,
        remarks: values.remarks?.trim() || null,
      };

      // 删除undefined的字段
      Object.keys(submitData).forEach(key => {
        if (submitData[key] === undefined) {
          delete submitData[key];
        }
      });

      // console.log('提交的数据:', submitData);

      if (editingCompany) {
        // 编辑
        await api.put(`/api/companies/${editingCompany.id}`, submitData);
        message.success('公司信息更新成功！');
      } else {
        // 添加
        await api.post('/api/companies', submitData);
        message.success('公司添加成功！');
      }
      setEditModalVisible(false);
      setAddModalVisible(false);
      setEditingCompany(null);
      form.resetFields();
      fetchCompanies(searchText, organizationFilter);
    } catch (error: any) {
      console.error('保存公司失败:', error);
      console.error('错误详情:', error.response?.data);
      const errorMessage = error.response?.data?.message || '保存失败';
      message.error(errorMessage);
    }
  };

  const columns = [
    {
      title: '序号',
      key: 'index',
      width: 60,
      render: (_: any, __: any, index: number) => index + 1,
    },
    { title: '公司名称', dataIndex: 'name', key: 'name' },
    { title: '税号', dataIndex: 'taxId', key: 'taxId' },
    { title: '所属用户', dataIndex: 'organization', key: 'organization' },
    { title: '联系人', dataIndex: 'contact', key: 'contact' },
    { title: '电话', dataIndex: 'phone', key: 'phone' },
    {
      title: '最新发票日期',
      dataIndex: 'latestInvoiceDate',
      key: 'latestInvoiceDate',
      render: (date: string) => date ? new Date(date).toLocaleDateString('zh-CN') : '-'
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'success' : 'error'}>
          {isActive ? '激活' : '禁用'}
        </Tag>
      )
    },
    {
      title: '注册日期',
      dataIndex: 'registrationDate',
      key: 'registrationDate',
      render: (date: string) => date ? new Date(date).toLocaleDateString() : '-'
    },
    { title: '地址', dataIndex: 'address', key: 'address' },
    {
      title: '操作',
      key: 'action',
      render: (_, record: any) => (
        <Space>
          <Button type="link" icon={<EyeOutlined />} onClick={() => handleViewDetail(record)}>详情</Button>
          {record.permissions?.canEdit && (
            <Button type="link" icon={<EditOutlined />} onClick={() => handleEdit(record)}>编辑</Button>
          )}
          {record.permissions?.canDelete && (
            <Popconfirm title="确定要删除这个公司吗？" onConfirm={() => handleDelete(record)}>
              <Button type="link" danger icon={<DeleteOutlined />}>删除</Button>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
          <Title level={2}>公司管理</Title>
          <Space>
            <Button icon={<DownloadOutlined />} onClick={handleDownloadCompanyTemplate}>导出模板</Button>
            <Button icon={<UploadOutlined />} onClick={() => setImportModalVisible(true)}>批量导入</Button>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>添加公司</Button>
          </Space>
        </div>
        <div style={{ display: 'flex', gap: 16, alignItems: 'center' }}>
          <Input.Search
            placeholder="搜索公司名称或税号"
            allowClear
            style={{ width: 300 }}
            value={searchText}
            onSearch={handleSearch}
            onChange={(e) => {
              if (!e.target.value) {
                handleSearch('');
              }
            }}
          />
          <Input
            placeholder="过滤所属用户"
            allowClear
            style={{ width: 200 }}
            value={organizationFilter}
            onChange={(e) => {
              handleOrganizationFilter(e.target.value);
            }}
            prefix={<SearchOutlined />}
          />
        </div>
      </div>
      <Card>
        <Table
          columns={columns}
          dataSource={companies}
          rowKey="id"
          loading={loading}
          size="small"
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `共 ${total} 条记录，第 ${range[0]}-${range[1]} 条`,
            pageSizeOptions: ['10', '20', '50', '100'],
            onChange: (page, pageSize) => {
              if (pageSize !== pagination.pageSize) {
                setPagination(prev => ({ ...prev, pageSize, current: 1 }));
                fetchCompanies(searchText, organizationFilter, 1, pageSize);
              } else {
                fetchCompanies(searchText, organizationFilter, page);
              }
            },
            onShowSizeChange: (current, size) => {
              setPagination(prev => ({ ...prev, pageSize: size, current: 1 }));
              fetchCompanies(searchText, organizationFilter, 1, size);
            },
          }}
        />
      </Card>

      {/* 编辑模态框 */}
      <Modal
        title="编辑公司"
        open={editModalVisible}
        onOk={handleSave}
        onCancel={() => { setEditModalVisible(false); form.resetFields(); }}
        width={800}
      >
        <Form form={form} layout="horizontal" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="公司名称" name="name" rules={[{ required: true, message: '请输入公司名称' }]}>
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="税号" name="taxId" rules={[{ required: true, message: '请输入税号' }]}>
                <Input />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="所属用户" name="organization">
                <Input placeholder="所属用户姓名" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="联系人" name="contact">
                <Input />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="电话" name="phone">
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="邮箱"
                name="email"
                rules={[
                  {
                    type: 'email',
                    message: '请输入有效的邮箱地址',
                  },
                ]}
              >
                <Input placeholder="请输入邮箱地址（可选）" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item label="地址" name="address" labelCol={{ span: 3 }} wrapperCol={{ span: 21 }}>
                <Input />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="注册日期" name="registrationDate">
                <DatePicker
                  style={{ width: '100%' }}
                  placeholder="选择注册日期"
                  format="YYYY-MM-DD"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="状态" name="isActive" valuePropName="checked">
                <Switch checkedChildren="激活" unCheckedChildren="禁用" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item label="备注" name="remarks" labelCol={{ span: 3 }} wrapperCol={{ span: 21 }}>
                <Input.TextArea rows={12} placeholder="请输入备注信息..." />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* 添加模态框 */}
      <Modal
        title="添加公司"
        open={addModalVisible}
        onOk={handleSave}
        onCancel={() => { setAddModalVisible(false); form.resetFields(); }}
        width={800}
      >
        <Form form={form} layout="horizontal" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="公司名称" name="name" rules={[{ required: true, message: '请输入公司名称' }]}>
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="税号" name="taxId" rules={[{ required: true, message: '请输入税号' }]}>
                <Input />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="所属用户" name="organization">
                <Input placeholder="所属用户姓名" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="联系人" name="contact">
                <Input />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="电话" name="phone">
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="邮箱"
                name="email"
                rules={[
                  {
                    type: 'email',
                    message: '请输入有效的邮箱地址',
                  },
                ]}
              >
                <Input placeholder="请输入邮箱地址（可选）" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item label="地址" name="address" labelCol={{ span: 3 }} wrapperCol={{ span: 21 }}>
                <Input />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="注册日期" name="registrationDate">
                <DatePicker
                  style={{ width: '100%' }}
                  placeholder="选择注册日期"
                  format="YYYY-MM-DD"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="状态" name="isActive" valuePropName="checked" initialValue={true}>
                <Switch checkedChildren="激活" unCheckedChildren="禁用" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item label="备注" name="remarks" labelCol={{ span: 3 }} wrapperCol={{ span: 21 }}>
                <Input.TextArea rows={12} placeholder="请输入备注信息..." />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* 公司批量导入模态框 */}
      <Modal
        title="批量导入公司"
        open={importModalVisible}
        onCancel={() => { setImportModalVisible(false); setImportResult(null); }}
        footer={[
          <Button key="close" onClick={() => { setImportModalVisible(false); setImportResult(null); }}>关闭</Button>
        ]}
        width={800}
      >
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <Title level={4}>导入步骤</Title>
            <ol>
              <li>下载公司导入模板</li>
              <li>按照模板格式填写公司数据</li>
              <li>上传填写好的Excel文件</li>
            </ol>
          </div>

          <div>
            <Space>
              <Button type="primary" icon={<UploadOutlined />} onClick={handleDownloadCompanyTemplate}>
                下载模板
              </Button>
              <input
                type="file"
                accept=".xlsx,.xls"
                onChange={(e) => { const file = e.target.files?.[0]; if (file) handleCompanyImport(file); }}
                style={{ display: 'none' }}
                id="company-excel-upload"
              />
              <Button
                type="primary"
                icon={<UploadOutlined />}
                loading={uploading}
                onClick={() => document.getElementById('company-excel-upload')?.click()}
              >
                {uploading ? '上传中...' : '上传Excel文件'}
              </Button>
            </Space>
          </div>

          {importResult && (
            <Card title="导入结果" style={{ marginTop: 16 }}>
              <Row gutter={16}>
                <Col span={6}>
                  <Statistic title="总记录数" value={importResult.totalCount} />
                </Col>
                <Col span={6}>
                  <Statistic title="成功导入" value={importResult.successCount} valueStyle={{ color: '#3f8600' }} />
                </Col>
                <Col span={6}>
                  <Statistic title="失败记录" value={importResult.failureCount} valueStyle={{ color: '#cf1322' }} />
                </Col>
                <Col span={6}>
                  <Statistic title="重复记录" value={importResult.duplicateCount} valueStyle={{ color: '#fa8c16' }} />
                </Col>
              </Row>

              {importResult.errors && importResult.errors.length > 0 && (
                <div style={{ marginTop: 16 }}>
                  <Title level={5}>错误详情</Title>
                  <List
                    size="small"
                    dataSource={importResult.errors}
                    renderItem={(error: any, index: number) => (
                      <List.Item>
                        <Text type="danger">第 {error.row} 行: {error.message}</Text>
                      </List.Item>
                    )}
                  />
                </div>
              )}
            </Card>
          )}
        </Space>
      </Modal>

      {/* 公司详情模态框 */}
      <Modal
        title="公司详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="edit" type="primary" icon={<EditOutlined />} onClick={() => {
            setDetailModalVisible(false);
            handleEdit(selectedCompany);
          }}>
            编辑
          </Button>,
          <Button key="close" onClick={() => setDetailModalVisible(false)}>关闭</Button>
        ]}
        width={800}
      >
        {selectedCompany && (
          <div>
            {/* 基本信息 */}
            <Card title="基本信息" size="small" style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <div style={{ display: 'flex', marginBottom: 12 }}>
                    <Text type="secondary" style={{ width: '80px', flexShrink: 0 }}>公司名称:</Text>
                    <Text strong style={{ fontSize: '16px' }}>{selectedCompany.name}</Text>
                  </div>
                </Col>
                <Col span={12}>
                  <div style={{ display: 'flex', marginBottom: 12 }}>
                    <Text type="secondary" style={{ width: '80px', flexShrink: 0 }}>税号:</Text>
                    <Text strong>{selectedCompany.taxId}</Text>
                  </div>
                </Col>
                <Col span={12}>
                  <div style={{ display: 'flex', marginBottom: 12 }}>
                    <Text type="secondary" style={{ width: '80px', flexShrink: 0 }}>所属用户:</Text>
                    <Text>{selectedCompany.organization || '-'}</Text>
                  </div>
                </Col>
                <Col span={12}>
                  <div style={{ display: 'flex', marginBottom: 12 }}>
                    <Text type="secondary" style={{ width: '80px', flexShrink: 0 }}>联系人:</Text>
                    <Text>{selectedCompany.contact || '-'}</Text>
                  </div>
                </Col>
              </Row>
            </Card>

            {/* 联系信息 */}
            <Card title="联系信息" size="small" style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <div style={{ display: 'flex', marginBottom: 12 }}>
                    <Text type="secondary" style={{ width: '80px', flexShrink: 0 }}>电话:</Text>
                    <Text>{selectedCompany.phone || '-'}</Text>
                  </div>
                </Col>
                <Col span={12}>
                  <div style={{ display: 'flex', marginBottom: 12 }}>
                    <Text type="secondary" style={{ width: '80px', flexShrink: 0 }}>邮箱:</Text>
                    <Text>{selectedCompany.email || '-'}</Text>
                  </div>
                </Col>
                <Col span={24}>
                  <div style={{ display: 'flex', marginBottom: 12 }}>
                    <Text type="secondary" style={{ width: '80px', flexShrink: 0 }}>地址:</Text>
                    <Text>{selectedCompany.address || '-'}</Text>
                  </div>
                </Col>
              </Row>
            </Card>

            {/* 备注信息 */}
            {selectedCompany.remarks && (
              <Card title="备注信息" size="small" style={{ marginBottom: 16 }}>
                <div style={{
                  padding: '12px',
                  backgroundColor: '#fafafa',
                  borderRadius: '6px',
                  border: '1px solid #d9d9d9'
                }}>
                  <Text>{selectedCompany.remarks}</Text>
                </div>
              </Card>
            )}

            {/* 重要日期 */}
            {selectedCompany.registrationDate && (
              <Card title="重要日期" size="small" style={{ marginBottom: 16 }}>
                <Row gutter={[16, 16]}>
                  <Col span={12}>
                    <div style={{ display: 'flex', marginBottom: 12 }}>
                      <Text type="secondary" style={{ width: '100px', flexShrink: 0 }}>注册日期:</Text>
                      <Text>{new Date(selectedCompany.registrationDate).toLocaleDateString()}</Text>
                    </div>
                  </Col>
                </Row>
              </Card>
            )}

            {/* 统计信息 */}
            <Card title="统计信息" size="small">
              <Row gutter={16}>
                <Col span={8}>
                  <Statistic
                    title="关联发票数量"
                    value={selectedCompany._count?.invoices || 0}
                    suffix="张"
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="创建时间"
                    value={selectedCompany.createdAt ? new Date(selectedCompany.createdAt).toLocaleDateString() : '-'}
                    valueStyle={{ color: '#666' }}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="更新时间"
                    value={selectedCompany.updatedAt ? new Date(selectedCompany.updatedAt).toLocaleDateString() : '-'}
                    valueStyle={{ color: '#666' }}
                  />
                </Col>
              </Row>
            </Card>
          </div>
        )}
      </Modal>
    </div>
  );
};

// 取得发票管理组件
const ReceivedInvoices: React.FC = () => {
  const [receivedInvoices, setReceivedInvoices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [editingReceivedInvoice, setEditingReceivedInvoice] = useState<any>(null);
  const [selectedReceivedInvoice, setSelectedReceivedInvoice] = useState<any>(null);
  const [editingReceivedInvoiceItems, setEditingReceivedInvoiceItems] = useState<any[]>([]);
  const [receivedInvoiceFileModalData, setReceivedInvoiceFileModalData] = useState<any>(null);
  const [filePreviewModalVisible, setFilePreviewModalVisible] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  // 获取当前季度的第一天和今天
  const getCurrentQuarterStart = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth(); // 0-11 (JavaScript月份从0开始)
    const quarter = Math.floor(month / 3); // 0-3
    const quarterStartMonth = quarter * 3; // 季度开始月份 (0, 3, 6, 9)

    // 使用本地时间格式化，避免时区问题
    return `${year}-${String(quarterStartMonth + 1).padStart(2, '0')}-01`;
  };

  const getTodayDate = () => {
    return new Date().toISOString().split('T')[0];
  };

  const [searchParams, setSearchParams] = useState({
    search: '',
    startDate: getCurrentQuarterStart(),
    endDate: getTodayDate(),
    status: '',
    sellerCompanies: [] as string[]
  });
  const [companiesForFilter, setCompaniesForFilter] = useState<any[]>([]);
  const [onlyShowPartnerCompanies, setOnlyShowPartnerCompanies] = useState(false);
  const [allCompanies, setAllCompanies] = useState<any[]>([]);
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [previewUrl, setPreviewUrl] = useState('');
  const [previewFileName, setPreviewFileName] = useState('');
  const [form] = Form.useForm();

  useEffect(() => {
    fetchReceivedInvoices();
    fetchCompaniesForFilter();
    fetchAllCompanies();
  }, []);

  const fetchCompaniesForFilter = async () => {
    try {
      const response = await api.get('/api/companies/active');
      const companies = response.data.data || [];
      setCompaniesForFilter(Array.isArray(companies) ? companies : []);
    } catch (error) {
      console.error('获取公司列表失败:', error);
    }
  };

  // 获取公司管理中的所有公司列表
  const fetchAllCompanies = async () => {
    try {
      const response = await api.get('/api/companies');
      const companies = response.data.data || [];
      setAllCompanies(Array.isArray(companies) ? companies : []);
    } catch (error) {
      console.error('获取公司管理列表失败:', error);
    }
  };

  const fetchReceivedInvoices = async (customParams = {}, page = 1, pageSize?: number) => {
    try {
      setLoading(true);
      const currentPageSize = pageSize || pagination.pageSize;
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: currentPageSize.toString()
      });

      // 处理搜索参数
      const searchData = { ...searchParams, ...customParams };

      if (searchData.search) {
        params.append('search', searchData.search);
      }
      if (searchData.startDate) {
        params.append('startDate', searchData.startDate);
      }
      if (searchData.endDate) {
        params.append('endDate', searchData.endDate);
      }
      if (searchData.status) {
        params.append('status', searchData.status);
      }
      if (searchData.sellerCompanies && Array.isArray(searchData.sellerCompanies) && searchData.sellerCompanies.length > 0) {
        searchData.sellerCompanies.forEach((company: string) => {
          params.append('sellerCompanies', company);
        });
      }

      // 添加"只显示往来公司"过滤参数
      if (onlyShowPartnerCompanies) {
        params.append('onlyShowPartnerCompanies', 'true');
      }

      const response = await api.get(`/api/received-invoices?${params}`);
      const data = response.data.data;
      setReceivedInvoices(data.data || []);
      setPagination({
        current: page,
        pageSize: currentPageSize,
        total: data.pagination?.total || 0,
      });
    } catch (error) {
      console.error('获取取得发票列表失败:', error);
      message.error('获取取得发票列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    fetchReceivedInvoices(searchParams, 1);
  };

  const handleReset = () => {
    setSearchParams({
      search: '',
      startDate: getCurrentQuarterStart(),
      endDate: getTodayDate(),
      status: '',
      sellerCompanies: []
    });
    setOnlyShowPartnerCompanies(false);
    fetchReceivedInvoices({}, 1);
  };

  const handleTableChange = (paginationConfig: any) => {
    fetchReceivedInvoices(searchParams, paginationConfig.current);
  };

  // 处理"只显示往来公司"复选框变化
  const handlePartnerCompaniesChange = async (checked: boolean) => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: '1',
        pageSize: pagination.pageSize.toString()
      });

      // 处理搜索参数
      if (searchParams.search) {
        params.append('search', searchParams.search);
      }
      if (searchParams.startDate) {
        params.append('startDate', searchParams.startDate);
      }
      if (searchParams.endDate) {
        params.append('endDate', searchParams.endDate);
      }
      if (searchParams.status) {
        params.append('status', searchParams.status);
      }
      if (searchParams.sellerCompanies && Array.isArray(searchParams.sellerCompanies) && searchParams.sellerCompanies.length > 0) {
        searchParams.sellerCompanies.forEach((company: string) => {
          params.append('sellerCompanies', company);
        });
      }

      // 添加"只显示往来公司"过滤参数（使用新的值）
      if (checked) {
        params.append('onlyShowPartnerCompanies', 'true');
      }

      const response = await api.get(`/api/received-invoices?${params}`);
      const data = response.data.data;
      setReceivedInvoices(data.data || []);
      setPagination({
        current: 1,
        pageSize: pagination.pageSize,
        total: data.pagination?.total || 0,
      });
    } catch (error) {
      console.error('获取取得发票列表失败:', error);
      message.error('获取取得发票列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 导出取得发票数据
  const handleExport = async () => {
    try {
      const params = new URLSearchParams();

      // 处理搜索参数
      if (searchParams.search) {
        params.append('search', searchParams.search);
      }
      if (searchParams.startDate) {
        params.append('startDate', searchParams.startDate);
      }
      if (searchParams.endDate) {
        params.append('endDate', searchParams.endDate);
      }
      if (searchParams.status) {
        params.append('status', searchParams.status);
      }
      if (searchParams.sellerCompanies && Array.isArray(searchParams.sellerCompanies) && searchParams.sellerCompanies.length > 0) {
        searchParams.sellerCompanies.forEach((company: string) => {
          params.append('sellerCompanies', company);
        });
      }

      // 添加"只显示往来公司"过滤参数
      if (onlyShowPartnerCompanies) {
        params.append('onlyShowPartnerCompanies', 'true');
      }

      const response = await api.get(`/api/received-invoices/export?${params}`, {
        responseType: 'blob'
      });

      // 创建下载链接
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // 生成文件名
      const now = new Date();
      const timestamp = now.toISOString().slice(0, 19).replace(/[:-]/g, '').replace('T', '_');
      link.download = `取得发票_${timestamp}.xlsx`;

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      message.success('导出成功！');
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败');
    }
  };

  const getStatusColor = (status: string) => {
    const colors = { NORMAL: 'green', CANCELLED: 'red' };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getStatusText = (status: string) => {
    const texts = { NORMAL: '正常', CANCELLED: '作废' };
    return texts[status as keyof typeof texts] || status;
  };

  const handleAdd = () => {
    setEditingReceivedInvoice(null);
    form.resetFields();
    setAddModalVisible(true);
  };

  const handleEdit = async (record: any) => {
    try {
      // 获取完整的取得发票信息，包括明细
      const response = await api.get(`/api/received-invoices/${record.id}`);
      const fullReceivedInvoice = response.data.data;

      setEditingReceivedInvoice(fullReceivedInvoice);
      setEditingReceivedInvoiceItems(fullReceivedInvoice.receivedInvoiceItems || []);

      // 设置表单值，处理日期格式
      const formValues = {
        ...fullReceivedInvoice,
        invoiceDate: fullReceivedInvoice.invoiceDate ? fullReceivedInvoice.invoiceDate.split('T')[0] : ''
      };
      form.setFieldsValue(formValues);
      setEditModalVisible(true);
    } catch (error: any) {
      message.error('获取取得发票详情失败');
    }
  };

  const handleDelete = async (record: any) => {
    try {
      await api.delete(`/api/received-invoices/${record.id}`);
      message.success('取得发票删除成功！');
      fetchReceivedInvoices();
    } catch (error: any) {
      message.error('删除取得发票失败');
    }
  };

  const handleSave = async () => {
    try {
      const values = await form.validateFields();

      // 构建保存数据，包含取得发票明细
      const saveData = {
        ...values,
        receivedInvoiceItems: editingReceivedInvoiceItems
      };

      if (editingReceivedInvoice) {
        // 编辑
        await api.put(`/api/received-invoices/${editingReceivedInvoice.id}`, saveData);
        message.success('取得发票更新成功！');
      } else {
        // 添加
        await api.post('/api/received-invoices', saveData);
        message.success('取得发票添加成功！');
      }
      setEditModalVisible(false);
      setAddModalVisible(false);
      setEditingReceivedInvoice(null);
      setEditingReceivedInvoiceItems([]);
      form.resetFields();
      fetchReceivedInvoices();
    } catch (error) {
      console.error('保存取得发票失败:', error);
      message.error('保存失败');
    }
  };

  const handleViewDetail = async (record: any) => {
    try {
      const response = await api.get(`/api/received-invoices/${record.id}`);
      setSelectedReceivedInvoice(response.data.data);
      setDetailModalVisible(true);
    } catch (error: any) {
      message.error(error.response?.data?.message || '获取取得发票详情失败');
    }
  };

  // 处理取得发票文件上传
  const handleReceivedInvoiceFileUpload = async (invoiceId: string, file: File) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('invoiceId', invoiceId);
      formData.append('fileType', 'IMAGE');
      formData.append('isOriginal', 'true');
      formData.append('invoiceType', 'received');

      const response = await api.post('/api/upload/invoice-attachment', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      message.success('发票文件上传成功！');
      // 移除刷新发票列表的操作，只在文件预览界面更新附件信息
      return response.data.data;
    } catch (error: any) {
      console.error('发票文件上传失败:', error);
      message.error(error.response?.data?.message || '发票文件上传失败');
      throw error;
    }
  };

  const handleViewReceivedInvoiceFile = async (record: any) => {
    try {
      // console.log('正在获取取得发票详情:', record.id);
      // 获取发票详情，包括附件信息
      const response = await api.get(`/api/received-invoices/${record.id}`);
      // console.log('取得发票详情响应:', response.data);
      const invoiceDetail = response.data.data;
      const attachments = invoiceDetail.attachments || [];
      // console.log('取得发票附件数据:', attachments);

      // 设置当前查看的发票数据，用于文件上传和删除
      setReceivedInvoiceFileModalData({
        ...invoiceDetail,
        attachments: attachments
      });

      // 打开文件预览Modal
      setFilePreviewModalVisible(true);
    } catch (error: any) {
      console.error('查看发票文件失败:', error);
      console.error('错误详情:', error.response?.data);
      console.error('错误状态码:', error.response?.status);
      message.error(error.response?.data?.message || '查看发票文件失败');
    }
  };

  const columns = [
    {
      title: '序号',
      key: 'index',
      width: 50,
      fixed: 'left' as const,
      render: (_: any, __: any, index: number) => (pagination.current - 1) * pagination.pageSize + index + 1
    },
    { title: '发票号码', dataIndex: 'invoiceNumber', key: 'invoiceNumber', width: 120, fixed: 'left' as const },
    { title: '发票代码', dataIndex: 'invoiceCode', key: 'invoiceCode', width: 110 },
    { title: '开票日期', dataIndex: 'invoiceDate', key: 'invoiceDate', width: 90, render: (date: string) => date?.split('T')[0] },
    { title: '购买方', dataIndex: 'buyerName', key: 'buyerName', width: 180 },
    { title: '销售方', dataIndex: 'sellerName', key: 'sellerName', width: 180 },
    { title: '金额', dataIndex: 'amount', key: 'amount', width: 100, align: 'right' as const, render: (amount: number) => `¥${amount?.toLocaleString() || 0}` },
    { title: '税额', dataIndex: 'taxAmount', key: 'taxAmount', width: 100, align: 'right' as const, render: (tax: number) => `¥${tax?.toLocaleString() || 0}` },
    { title: '价税合计', dataIndex: 'totalAmount', key: 'totalAmount', width: 110, align: 'right' as const, render: (total: number) => `¥${total?.toLocaleString() || 0}` },
    { title: '状态', dataIndex: 'status', key: 'status', width: 70, render: (status: string) => <Tag color={getStatusColor(status)}>{getStatusText(status)}</Tag> },
    {
      title: '操作', key: 'action', width: 180, fixed: 'right' as const,
      render: (_, record: any) => (
        <Space>
          <Button type="link" size="small" onClick={() => handleViewDetail(record)}>详情</Button>
          <Button type="link" size="small" icon={<FileImageOutlined />} onClick={() => handleViewReceivedInvoiceFile(record)}>查看发票</Button>
          <Dropdown
            menu={{
              items: [
                {
                  key: 'edit',
                  label: '编辑',
                  icon: <EditOutlined />,
                  onClick: () => handleEdit(record)
                },
                {
                  key: 'delete',
                  label: '删除',
                  icon: <DeleteOutlined />,
                  danger: true,
                  onClick: () => {
                    Modal.confirm({
                      title: '确认删除',
                      content: '确定要删除这条取得发票记录吗？',
                      okText: '确定',
                      cancelText: '取消',
                      onOk: () => handleDelete(record)
                    });
                  }
                }
              ]
            }}
            trigger={['click']}
          >
            <Button type="link" size="small">
              操作 <span style={{ fontSize: '10px' }}>▼</span>
            </Button>
          </Dropdown>
        </Space>
      ),
    },
  ];

  return (
    <div>
      {/* 搜索表单 */}
      <Card style={{ marginBottom: 16 }}>
        <Form layout="inline">
          <Form.Item label="搜索">
            <Input
              placeholder="发票号码、购买方、税号"
              style={{ width: 250 }}
              value={searchParams.search}
              onChange={(e) => setSearchParams({ ...searchParams, search: e.target.value })}
              allowClear
            />
          </Form.Item>
          <Form.Item label="销售方">
            <Select
              mode="tags"
              placeholder="选择或输入销售方"
              style={{ width: 250 }}
              value={searchParams.sellerCompanies}
              onChange={(value) => {
                // 确保value始终是数组
                const arrayValue = Array.isArray(value) ? value : (value ? [value] : []);
                setSearchParams({ ...searchParams, sellerCompanies: arrayValue });
              }}
              allowClear
              showSearch
              filterOption={(input, option) =>
                String(option?.children || '')?.toLowerCase().includes(input.toLowerCase())
              }
              tokenSeparators={[',']}
            >
              {Array.isArray(companiesForFilter) && companiesForFilter.map(company => (
                <Select.Option key={company.id} value={company.name}>{company.name}</Select.Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item label="开票日期">
            <DatePicker
              value={searchParams.startDate ? dayjs(searchParams.startDate) : null}
              onChange={(date) => setSearchParams({ ...searchParams, startDate: date ? date.format('YYYY-MM-DD') : '' })}
              size="small"
              style={{ width: 150 }}
            />
            <span style={{ margin: '0 8px' }}>至</span>
            <DatePicker
              value={searchParams.endDate ? dayjs(searchParams.endDate) : null}
              onChange={(date) => setSearchParams({ ...searchParams, endDate: date ? date.format('YYYY-MM-DD') : '' })}
              size="small"
              style={{ width: 150 }}
            />
          </Form.Item>
          <Form.Item label="状态">
            <select
              value={searchParams.status}
              onChange={(e) => setSearchParams({ ...searchParams, status: e.target.value })}
              style={{ width: 120, height: 32, border: '1px solid #d9d9d9', borderRadius: 6, padding: '0 8px' }}
            >
              <option value="">全部</option>
              <option value="NORMAL">正常</option>
              <option value="CANCELLED">作废</option>
            </select>
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" onClick={handleSearch}>搜索</Button>
              <Button onClick={handleReset}>重置</Button>
              <Checkbox
                checked={onlyShowPartnerCompanies}
                onChange={(e) => {
                  const newValue = e.target.checked;
                  setOnlyShowPartnerCompanies(newValue);
                  // 立即触发数据更新，使用新的值
                  handlePartnerCompaniesChange(newValue);
                }}
              >
                只显示往来公司
              </Checkbox>
              <Button icon={<DownloadOutlined />} onClick={handleExport}>导出</Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      <Card style={{ height: 'calc(100vh - 280px)', display: 'flex', flexDirection: 'column' }}>
        <div style={{ flex: 1, overflow: 'hidden' }}>
          <Table
            columns={columns}
            dataSource={receivedInvoices}
            rowKey="id"
            scroll={{ x: 1800, y: 'calc(100vh - 430px)' }}
            size="small"
            loading={loading}
            locale={{ emptyText: '暂无数据，请先添加取得发票数据' }}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `共 ${total} 条记录，第 ${range[0]}-${range[1]} 条`,
              pageSizeOptions: ['10', '20', '50', '100'],
              onChange: (page, pageSize) => {
                if (pageSize !== pagination.pageSize) {
                  setPagination(prev => ({ ...prev, pageSize, current: 1 }));
                  fetchReceivedInvoices(searchParams, 1, pageSize);
                } else {
                  fetchReceivedInvoices(searchParams, page);
                }
              },
              onShowSizeChange: (current, size) => {
                setPagination(prev => ({ ...prev, pageSize: size, current: 1 }));
                fetchReceivedInvoices(searchParams, 1, size);
              },
            }}
            onChange={handleTableChange}
            rowClassName={(record, index) => {
              const classes = [];

              // 获取当前行的年份
              const currentRowYear = new Date(record.invoiceDate).getFullYear();

              // 检查与上一行的年份差异
              if (index > 0) {
                const prevRecord = receivedInvoices[index - 1];
                const prevRowYear = new Date(prevRecord.invoiceDate).getFullYear();
                if (currentRowYear !== prevRowYear) {
                  classes.push('cross-year-top');
                }
              }

              // 检查与下一行的年份差异
              if (index < receivedInvoices.length - 1) {
                const nextRecord = receivedInvoices[index + 1];
                const nextRowYear = new Date(nextRecord.invoiceDate).getFullYear();
                if (currentRowYear !== nextRowYear) {
                  classes.push('cross-year-bottom');
                }
              }

              return classes.join(' ');
            }}
            summary={(pageData) => {
              const totalAmount = receivedInvoices.reduce((sum: number, invoice: any) => sum + (parseFloat(invoice.totalAmount) || 0), 0);
              const totalTaxAmount = receivedInvoices.reduce((sum: number, invoice: any) => sum + (parseFloat(invoice.taxAmount) || 0), 0);
              const invoiceCount = receivedInvoices.length;

              return (
                <Table.Summary fixed="bottom">
                  <Table.Summary.Row style={{ backgroundColor: '#fafafa', fontWeight: 'bold' }}>
                    <Table.Summary.Cell index={0} colSpan={3}>
                      <span style={{ fontSize: '16px', color: '#1890ff', fontWeight: 'bold' }}>当前页合计 - 共 {invoiceCount} 张取得发票</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={3}></Table.Summary.Cell>
                    <Table.Summary.Cell index={4}></Table.Summary.Cell>
                    <Table.Summary.Cell index={5}></Table.Summary.Cell>
                    <Table.Summary.Cell index={6} align="right">
                      <span style={{ fontSize: '16px', color: '#666' }}>¥{(totalAmount - totalTaxAmount).toLocaleString()}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={7} align="right">
                      <span style={{ fontSize: '16px', color: '#faad14' }}>¥{totalTaxAmount.toLocaleString()}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={8} align="right">
                      <span style={{ fontSize: '16px', color: '#52c41a', fontWeight: 'bold' }}>¥{totalAmount.toLocaleString()}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={9}></Table.Summary.Cell>
                    <Table.Summary.Cell index={10}></Table.Summary.Cell>
                  </Table.Summary.Row>
                </Table.Summary>
              );
            }}
          />
        </div>
      </Card>

      {/* 取得发票详情模态框 */}
      <Modal
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', paddingRight: '24px' }}>
            <span>取得发票详情</span>
            {selectedReceivedInvoice && (
              <Tag color={getStatusColor(selectedReceivedInvoice.status)} style={{ fontSize: '14px', padding: '4px 12px' }}>
                {getStatusText(selectedReceivedInvoice.status)}
              </Tag>
            )}
          </div>
        }
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="view-file" type="primary" icon={<FileImageOutlined />} onClick={() => handleViewReceivedInvoiceFile(selectedReceivedInvoice)}>
            查看发票
          </Button>,
          <Button key="close" onClick={() => setDetailModalVisible(false)}>关闭</Button>
        ]}
        width={1200}
      >
        {selectedReceivedInvoice && (
          <div>

            {/* 基本信息 */}
            <Card title="基本信息" size="small" style={{ marginBottom: 16 }}>
              <Row gutter={[16, 8]}>
                <Col span={6}>
                  <div style={{ marginBottom: 8 }}>
                    <Text type="secondary">发票号码</Text>
                    <div style={{ fontWeight: 'bold', fontSize: '16px' }}>{selectedReceivedInvoice.invoiceNumber}</div>
                  </div>
                </Col>
                <Col span={6}>
                  <div style={{ marginBottom: 8 }}>
                    <Text type="secondary">发票代码</Text>
                    <div style={{ fontWeight: 'bold', fontSize: '16px' }}>{selectedReceivedInvoice.invoiceCode}</div>
                  </div>
                </Col>
                <Col span={6}>
                  <div style={{ marginBottom: 8 }}>
                    <Text type="secondary">开票日期</Text>
                    <div style={{ fontWeight: 'bold', fontSize: '16px' }}>{selectedReceivedInvoice.invoiceDate?.split('T')[0]}</div>
                  </div>
                </Col>
                <Col span={6}>
                  <div style={{ marginBottom: 8 }}>
                    <Text type="secondary">开票人</Text>
                    <div style={{ fontWeight: 'bold', fontSize: '16px' }}>{selectedReceivedInvoice.drawer || '-'}</div>
                  </div>
                </Col>
                <Col span={24}>
                  <div style={{ marginBottom: 8 }}>
                    <Text type="secondary">备注</Text>
                    <div style={{ marginTop: 4, padding: '8px', backgroundColor: '#f5f5f5', borderRadius: '4px', minHeight: '32px' }}>
                      {selectedReceivedInvoice.remarks || ''}
                    </div>
                  </div>
                </Col>
              </Row>
            </Card>

            {/* 购买方和销售方信息 */}
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={12}>
                <Card title="购买方信息" size="small">
                  <div style={{ marginBottom: 12 }}>
                    <Text type="secondary">公司名称</Text>
                    <div style={{ fontWeight: 'bold', fontSize: '16px', marginTop: 4 }}>{selectedReceivedInvoice.buyerName}</div>
                  </div>
                  <div style={{ marginBottom: 12 }}>
                    <Text type="secondary">纳税人识别号</Text>
                    <div style={{ fontWeight: 'bold', marginTop: 4 }}>{selectedReceivedInvoice.buyerTaxId}</div>
                  </div>
                  <div style={{ marginBottom: 8 }}>
                    <Text type="secondary">地址电话</Text>
                    <div style={{ marginTop: 4 }}>{selectedReceivedInvoice.buyerAddress} {selectedReceivedInvoice.buyerPhone}</div>
                  </div>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="销售方信息" size="small">
                  <div style={{ marginBottom: 12 }}>
                    <Text type="secondary">公司名称</Text>
                    <div style={{ fontWeight: 'bold', fontSize: '16px', marginTop: 4 }}>{selectedReceivedInvoice.sellerName}</div>
                  </div>
                  <div style={{ marginBottom: 12 }}>
                    <Text type="secondary">纳税人识别号</Text>
                    <div style={{ fontWeight: 'bold', marginTop: 4 }}>{selectedReceivedInvoice.sellerTaxId}</div>
                  </div>
                  <div style={{ marginBottom: 8 }}>
                    <Text type="secondary">地址电话</Text>
                    <div style={{ marginTop: 4 }}>{selectedReceivedInvoice.sellerAddress} {selectedReceivedInvoice.sellerPhone}</div>
                  </div>
                </Card>
              </Col>
            </Row>

            {/* 金额信息 */}
            <Card title="金额信息" size="small" style={{ marginBottom: 16 }}>
              <Row gutter={16}>
                <Col span={8}>
                  <Statistic
                    title="金额"
                    value={selectedReceivedInvoice.amount || 0}
                    precision={2}
                    prefix="¥"
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="税额"
                    value={selectedReceivedInvoice.taxAmount || 0}
                    precision={2}
                    prefix="¥"
                    valueStyle={{ color: '#faad14' }}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="价税合计"
                    value={selectedReceivedInvoice.totalAmount || 0}
                    precision={2}
                    prefix="¥"
                    valueStyle={{ color: '#52c41a', fontSize: '20px', fontWeight: 'bold' }}
                  />
                </Col>
              </Row>
            </Card>

            {/* 发票明细 */}
            {selectedReceivedInvoice.invoiceItems && selectedReceivedInvoice.invoiceItems.length > 0 && (
              <Card title="发票明细" size="small" style={{ marginBottom: 16 }}>
                <Table
                  dataSource={selectedReceivedInvoice.invoiceItems}
                  rowKey="id"
                  size="small"
                  pagination={false}
                  columns={[
                    { title: '商品名称', dataIndex: 'itemName', key: 'itemName' },
                    { title: '规格型号', dataIndex: 'specification', key: 'specification' },
                    { title: '单位', dataIndex: 'unit', key: 'unit', width: 80 },
                    { title: '数量', dataIndex: 'quantity', key: 'quantity', width: 100, align: 'right' as const },
                    { title: '单价', dataIndex: 'unitPrice', key: 'unitPrice', width: 120, align: 'right' as const, render: (price: number) => `¥${price?.toLocaleString()}` },
                    { title: '金额', dataIndex: 'amount', key: 'amount', width: 120, align: 'right' as const, render: (amount: number) => `¥${amount?.toLocaleString()}` },
                    { title: '税率', dataIndex: 'taxRate', key: 'taxRate', width: 100, align: 'right' as const, render: (rate: number) => `${(rate * 100).toFixed(2)}%` },
                    { title: '税额', dataIndex: 'taxAmount', key: 'taxAmount', width: 120, align: 'right' as const, render: (tax: number) => `¥${tax?.toLocaleString()}` },
                    { title: '价税合计', dataIndex: 'totalAmount', key: 'totalAmount', width: 130, align: 'right' as const, render: (total: number) => `¥${total?.toLocaleString() || 0}` },
                  ]}
                />
              </Card>
            )}
          </div>
        )}
      </Modal>

      {/* 取得发票文件预览模态框 */}
      <Modal
        title={`发票文件预览 - ${receivedInvoiceFileModalData?.invoiceNumber || ''}`}
        open={filePreviewModalVisible}
        onCancel={() => setFilePreviewModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setFilePreviewModalVisible(false)}>关闭</Button>
        ]}
        width={1200}
        style={{ top: 20 }}
      >
        {receivedInvoiceFileModalData && (
          <div style={{ padding: '20px' }}>
            {receivedInvoiceFileModalData.attachments && receivedInvoiceFileModalData.attachments.length > 0 ? (
              <div>
                <Title level={5} style={{ marginBottom: 16 }}>发票附件 ({receivedInvoiceFileModalData.attachments.length}个文件)</Title>
                <List
                  dataSource={receivedInvoiceFileModalData.attachments}
                  renderItem={(attachment: any, index: number) => (
                    <List.Item
                      key={attachment.id}
                      style={{
                        border: '1px solid #f0f0f0',
                        borderRadius: '8px',
                        marginBottom: '12px',
                        padding: '16px',
                        backgroundColor: '#fafafa'
                      }}
                    >
                      <List.Item.Meta
                        avatar={
                          <div style={{ fontSize: '24px' }}>
                            {attachment.fileType === 'PDF' ? '📄' : '🖼️'}
                          </div>
                        }
                        title={
                          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <span>{attachment.fileName}</span>
                            <Space>
                              <Tag color={attachment.fileType === 'PDF' ? 'red' : 'blue'}>
                                {attachment.fileType}
                              </Tag>
                              {attachment.isOriginal && <Tag color="green">原件</Tag>}
                            </Space>
                          </div>
                        }
                        description={
                          <div>
                            <Text type="secondary">
                              文件大小: {(attachment.fileSize / 1024).toFixed(1)} KB
                            </Text>
                            <br />
                            <Space style={{ marginTop: 8 }}>
                              <Button
                                size="small"
                                icon={<EyeOutlined />}
                                onClick={() => {
                                  const token = localStorage.getItem('token');
                                  const previewUrlWithToken = `http://localhost:3001/api/upload/attachment/${attachment.id}?preview=true&type=received&token=${token}`;
                                  setPreviewUrl(previewUrlWithToken);
                                  setPreviewFileName(attachment.fileName);
                                  setPreviewModalVisible(true);
                                }}
                              >
                                预览
                              </Button>
                              <Button
                                size="small"
                                icon={<DownloadOutlined />}
                                onClick={() => {
                                  const token = localStorage.getItem('token');
                                  const downloadUrl = `http://localhost:3001/api/upload/attachment/${attachment.id}?type=received&token=${token}`;
                                  const link = document.createElement('a');
                                  link.href = downloadUrl;
                                  link.download = attachment.fileName;
                                  document.body.appendChild(link);
                                  link.click();
                                  document.body.removeChild(link);
                                }}
                              >
                                下载
                              </Button>
                              <Button
                                size="small"
                                danger
                                icon={<DeleteOutlined />}
                                onClick={() => {
                                  Modal.confirm({
                                    title: '确认删除',
                                    content: `确定要删除文件"${attachment.fileName}"吗？此操作不可撤销。`,
                                    okText: '确定',
                                    cancelText: '取消',
                                    onOk: async () => {
                                      try {
                                        await api.delete(`/api/upload/invoice-attachment/${attachment.id}?invoiceType=received`);
                                        message.success('文件删除成功');
                                        // 直接更新当前的发票附件信息，不进行弹窗
                                        const response = await api.get(`/api/received-invoices/${receivedInvoiceFileModalData.id}`);
                                        const updatedInvoiceDetail = response.data.data;
                                        setReceivedInvoiceFileModalData({
                                          ...updatedInvoiceDetail,
                                          attachments: updatedInvoiceDetail.attachments || []
                                        });
                                      } catch (error: any) {
                                        console.error('删除文件失败:', error);
                                        message.error(error.response?.data?.message || '删除文件失败');
                                      }
                                    }
                                  });
                                }}
                              >
                                删除
                              </Button>
                            </Space>
                          </div>
                        }
                      />

                    </List.Item>
                  )}
                />
              </div>
            ) : (
              <div style={{ textAlign: 'center', padding: '40px' }}>
                <div style={{ fontSize: '48px', marginBottom: '16px' }}>📄</div>
                <Title level={4} type="secondary">暂无发票附件</Title>
                <Text type="secondary">请先上传发票文件</Text>
              </div>
            )}

            {/* 上传按钮 */}
            <div style={{ textAlign: 'center', marginTop: '16px' }}>
              <input
                type="file"
                accept="image/*,.pdf"
                onChange={async (e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    try {
                      await handleReceivedInvoiceFileUpload(receivedInvoiceFileModalData.id, file);
                      // 获取更新后的发票数据
                      const response = await api.get(`/api/received-invoices/${receivedInvoiceFileModalData.id}`);
                      const updatedInvoice = response.data.data;
                      setReceivedInvoiceFileModalData({
                        ...updatedInvoice,
                        attachments: updatedInvoice.attachments || []
                      });
                      // 清空文件输入框
                      e.target.value = '';
                    } catch (error) {
                      console.error('上传文件失败:', error);
                    }
                  }
                }}
                style={{ display: 'none' }}
                id={`received-invoice-file-upload-${receivedInvoiceFileModalData.id}`}
              />
              <Button
                type="primary"
                icon={<UploadOutlined />}
                onClick={() => document.getElementById(`received-invoice-file-upload-${receivedInvoiceFileModalData.id}`)?.click()}
              >
                上传发票文件
              </Button>
            </div>
          </div>
        )}
      </Modal>

      {/* 编辑取得发票模态框 */}
      <Modal
        title="编辑取得发票"
        open={editModalVisible}
        onOk={handleSave}
        onCancel={() => {
          setEditModalVisible(false);
          form.resetFields();
          setEditingReceivedInvoiceItems([]);
        }}
        width={1200}
      >
        <Form form={form} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="发票号码" name="invoiceNumber" rules={[{ required: true, message: '请输入发票号码' }]}>
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="发票代码" name="invoiceCode">
                <Input />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="开票日期" name="invoiceDate" rules={[{ required: true, message: '请选择开票日期' }]}>
                <DatePicker
                  size="small"
                  style={{ width: '100%' }}
                  format="YYYY-MM-DD"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="状态" name="status" initialValue="NORMAL">
                <Select>
                  <Select.Option value="NORMAL">正常</Select.Option>
                  <Select.Option value="CANCELLED">作废</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="购买方名称" name="buyerName" rules={[{ required: true, message: '请输入购买方名称' }]}>
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="购买方税号" name="buyerTaxId">
                <Input />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="销售方名称" name="sellerName" rules={[{ required: true, message: '请输入销售方名称' }]}>
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="销售方税号" name="sellerTaxId">
                <Input />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label="金额" name="amount" rules={[{ required: true, message: '请输入金额' }]}>
                <Input type="number" step="0.01" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="税额" name="taxAmount" rules={[{ required: true, message: '请输入税额' }]}>
                <Input type="number" step="0.01" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="价税合计" name="totalAmount" rules={[{ required: true, message: '请输入价税合计' }]}>
                <Input type="number" step="0.01" />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item label="备注" name="remarks">
            <Input.TextArea rows={3} placeholder="请输入备注信息..." />
          </Form.Item>
        </Form>
      </Modal>

      {/* 文件预览模态框 */}
      <Modal
        title={`文件预览 - ${previewFileName}`}
        open={previewModalVisible}
        onCancel={() => setPreviewModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setPreviewModalVisible(false)}>关闭</Button>
        ]}
        width="60vw"
        style={{ top: 0 }}
        styles={{ body: { padding: 0, overflow: 'hidden' } }}
        centered
      >
        {previewUrl && (
          <div style={{
            width: '100%',
            height: '70vh',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            overflow: 'hidden'
          }}>
            <iframe
              src={previewUrl}
              style={{
                width: '100%',
                height: '100%',
                border: 'none',
                maxWidth: '100%',
                maxHeight: '100%'
              }}
              title="文件预览"
              onLoad={() => {
                // iframe加载完成后的处理
                // console.log('PDF预览加载完成');
              }}
            />
          </div>
        )}
      </Modal>
    </div>
  );
};

// 发票管理组件
const Invoices: React.FC = () => {
  const [invoices, setInvoices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [editingInvoice, setEditingInvoice] = useState<any>(null);
  const [selectedInvoice, setSelectedInvoice] = useState<any>(null);
  const [editingInvoiceItems, setEditingInvoiceItems] = useState<any[]>([]);
  const [invoiceFileModalData, setInvoiceFileModalData] = useState<any>(null);
  const [invoiceFilePreviewModalVisible, setInvoiceFilePreviewModalVisible] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  // 获取当前季度的第一天和今天
  const getCurrentQuarterStart = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth(); // 0-11 (JavaScript月份从0开始)
    const quarter = Math.floor(month / 3); // 0-3
    const quarterStartMonth = quarter * 3; // 季度开始月份 (0, 3, 6, 9)

    // 使用本地时间格式化，避免时区问题
    return `${year}-${String(quarterStartMonth + 1).padStart(2, '0')}-01`;
  };

  const getTodayDate = () => {
    return new Date().toISOString().split('T')[0];
  };

  const [searchParams, setSearchParams] = useState(() => ({
    search: '',
    startDate: getCurrentQuarterStart(),
    endDate: getTodayDate(),
    status: '',
    sellerCompanies: [] as string[]
  }));
  const [companiesForFilter, setCompaniesForFilter] = useState<any[]>([]);
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [previewUrl, setPreviewUrl] = useState('');
  const [previewFileName, setPreviewFileName] = useState('');
  const [form] = Form.useForm();

  useEffect(() => {
    fetchInvoices();
    fetchCompaniesForFilter();
  }, []);

  const fetchCompaniesForFilter = async () => {
    try {
      const response = await api.get('/api/companies/active');
      const companies = response.data.data || [];
      setCompaniesForFilter(Array.isArray(companies) ? companies : []);
    } catch (error) {
      console.error('获取公司列表失败:', error);
    }
  };

  const fetchInvoices = async (customParams = {}, page = 1, pageSize?: number) => {
    try {
      setLoading(true);
      const currentPageSize = pageSize || pagination.pageSize;
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: currentPageSize.toString()
      });

      // 处理搜索参数
      const searchData = { ...searchParams, ...customParams };

      if (searchData.search) {
        params.append('search', searchData.search);
      }
      if (searchData.startDate) {
        params.append('startDate', searchData.startDate);
      }
      if (searchData.endDate) {
        params.append('endDate', searchData.endDate);
      }
      if (searchData.status) {
        params.append('status', searchData.status);
      }
      if (searchData.sellerCompanies && Array.isArray(searchData.sellerCompanies) && searchData.sellerCompanies.length > 0) {
        searchData.sellerCompanies.forEach((company: string) => {
          params.append('sellerCompanies', company);
        });
      }

      const response = await api.get(`/api/invoices?${params}`);
      const data = response.data.data;
      setInvoices(data.data || []);
      setPagination({
        current: page,
        pageSize: currentPageSize,
        total: data.pagination?.total || 0,
      });
    } catch (error) {
      console.error('获取发票列表失败:', error);
      message.error('获取发票列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    fetchInvoices(searchParams, 1);
  };

  const handleReset = () => {
    setSearchParams({
      search: '',
      startDate: getCurrentQuarterStart(),
      endDate: getTodayDate(),
      status: '',
      sellerCompanies: []
    });
    fetchInvoices({}, 1);
  };

  const handleFixCompanyIds = async () => {
    try {
      const response = await api.post('/api/invoices/fix-company-ids');
      const result = response.data.data;

      message.success(`修复完成：更新了${result.updatedCount}条发票，${result.errorCount}条失败`);

      // 显示详细结果
      Modal.info({
        title: '发票公司归属修复结果',
        width: 600,
        content: (
          <div>
            <p><strong>总发票数：</strong>{result.totalInvoices}</p>
            <p><strong>更新成功：</strong>{result.updatedCount}</p>
            <p><strong>更新失败：</strong>{result.errorCount}</p>
            {result.errors && result.errors.length > 0 && (
              <div>
                <p><strong>错误详情：</strong></p>
                <ul>
                  {result.errors.map((error: string, index: number) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        ),
      });

      // 刷新发票列表
      fetchInvoices(searchParams, pagination.current);
    } catch (error: any) {
      console.error('修复发票公司归属失败:', error);
      message.error(error.response?.data?.message || '修复失败');
    }
  };

  const handleDebugUserPermissions = async () => {
    try {
      // 石磊用户的ID
      const leiShiUserId = 'cmb70ud6d0000v0z8atel4lt1';
      const response = await api.get(`/api/menus/user/${leiShiUserId}/permissions/debug`);

      // console.log('石磊用户权限调试信息:', response.data);

      // 显示调试信息
      Modal.info({
        title: '石磊用户权限调试信息',
        width: 800,
        content: (
          <div>
            <pre style={{ maxHeight: '400px', overflow: 'auto' }}>
              {JSON.stringify(response.data.data, null, 2)}
            </pre>
          </div>
        ),
      });

    } catch (error: any) {
      console.error('获取用户权限调试信息失败:', error);
      message.error(error.response?.data?.message || '获取调试信息失败');
    }
  };

  const handleTableChange = (paginationConfig: any) => {
    fetchInvoices(searchParams, paginationConfig.current);
  };

  const getStatusColor = (status: string) => {
    const colors = { NORMAL: 'green', CANCELLED: 'red' };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getStatusText = (status: string) => {
    const texts = { NORMAL: '正常', CANCELLED: '作废' };
    return texts[status as keyof typeof texts] || status;
  };

  const handleAdd = () => {
    setEditingInvoice(null);
    form.resetFields();
    setAddModalVisible(true);
  };

  const handleEdit = async (record: any) => {
    try {
      // 获取完整的发票信息，包括明细
      const response = await api.get(`/api/invoices/${record.id}`);
      const fullInvoice = response.data.data;

      setEditingInvoice(fullInvoice);
      setEditingInvoiceItems(fullInvoice.invoiceItems || []);

      // 设置表单值，处理日期格式
      const formValues = {
        ...fullInvoice,
        invoiceDate: fullInvoice.invoiceDate ? fullInvoice.invoiceDate.split('T')[0] : ''
      };
      form.setFieldsValue(formValues);
      setEditModalVisible(true);
    } catch (error) {
      console.error('获取发票详情失败:', error);
      message.error('获取发票详情失败');
    }
  };

  const handleAddInvoiceItem = () => {
    const newItem = {
      itemName: '',
      specification: '',
      unit: '',
      quantity: 0,
      unitPrice: 0,
      amount: 0,
      taxRate: 0.13,
      taxAmount: 0,
      totalAmount: 0
    };
    setEditingInvoiceItems([...editingInvoiceItems, newItem]);
  };

  const handleRemoveInvoiceItem = (index: number) => {
    const newItems = editingInvoiceItems.filter((_, i) => i !== index);
    setEditingInvoiceItems(newItems);
  };

  const handleInvoiceItemChange = (index: number, field: string, value: any) => {
    const newItems = [...editingInvoiceItems];
    newItems[index] = { ...newItems[index], [field]: value };

    // 自动计算相关字段
    if (['quantity', 'unitPrice'].includes(field)) {
      const quantity = field === 'quantity' ? value : newItems[index].quantity;
      const unitPrice = field === 'unitPrice' ? value : newItems[index].unitPrice;
      newItems[index].amount = quantity * unitPrice;
      newItems[index].taxAmount = newItems[index].amount * newItems[index].taxRate;
      newItems[index].totalAmount = newItems[index].amount + newItems[index].taxAmount;
    } else if (field === 'taxRate') {
      newItems[index].taxAmount = newItems[index].amount * value;
      newItems[index].totalAmount = newItems[index].amount + newItems[index].taxAmount;
    } else if (field === 'amount') {
      newItems[index].taxAmount = value * newItems[index].taxRate;
      newItems[index].totalAmount = value + newItems[index].taxAmount;
    }

    setEditingInvoiceItems(newItems);
  };

  const handleDelete = async (record: any) => {
    try {
      await api.delete(`/api/invoices/${record.id}`);
      message.success('发票删除成功！');
      fetchInvoices();
    } catch (error) {
      console.error('删除发票失败:', error);
      message.error('删除发票失败');
    }
  };

  const handleSave = async () => {
    try {
      const values = await form.validateFields();

      // 构建保存数据，包含发票明细
      const saveData = {
        ...values,
        invoiceItems: editingInvoiceItems
      };

      if (editingInvoice) {
        // 编辑
        await api.put(`/api/invoices/${editingInvoice.id}`, saveData);
        message.success('发票更新成功！');
      } else {
        // 添加
        await api.post('/api/invoices', saveData);
        message.success('发票添加成功！');
      }
      setEditModalVisible(false);
      setAddModalVisible(false);
      setEditingInvoice(null);
      setEditingInvoiceItems([]);
      form.resetFields();
      fetchInvoices();
    } catch (error) {
      console.error('保存发票失败:', error);
      message.error('保存失败');
    }
  };

  const handleViewDetail = async (record: any) => {
    try {
      const response = await api.get(`/api/invoices/${record.id}`);
      setSelectedInvoice(response.data.data);
      setDetailModalVisible(true);
    } catch (error) {
      console.error('获取发票详情失败:', error);
      message.error('获取发票详情失败');
    }
  };

  // 处理发票文件上传
  const handleInvoiceFileUpload = async (invoiceId: string, file: File) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('invoiceId', invoiceId);
      formData.append('fileType', 'IMAGE');
      formData.append('isOriginal', 'true');
      formData.append('invoiceType', 'issued');

      const response = await api.post('/api/upload/invoice-attachment', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      message.success('发票文件上传成功！');
      // 移除刷新发票列表的操作，只在文件预览界面更新附件信息
      return response.data.data;
    } catch (error: any) {
      console.error('发票文件上传失败:', error);
      message.error(error.response?.data?.message || '发票文件上传失败');
      throw error;
    }
  };

  const handleViewInvoiceFile = async (record: any) => {
    try {
      // 获取发票详情，包括附件信息
      const response = await api.get(`/api/invoices/${record.id}`);
      const invoiceDetail = response.data.data;
      const attachments = invoiceDetail.attachments || [];

      // 设置当前查看的发票数据，用于文件上传和删除
      setInvoiceFileModalData({
        ...invoiceDetail,
        attachments: attachments
      });

      // 打开文件预览Modal
      setInvoiceFilePreviewModalVisible(true);
    } catch (error: any) {
      console.error('查看发票文件失败:', error);
      message.error(error.response?.data?.message || '查看发票文件失败');
    }
  };

  const columns = [
    {
      title: '序号',
      key: 'index',
      width: 50,
      fixed: 'left' as const,
      render: (_: any, __: any, index: number) => (pagination.current - 1) * pagination.pageSize + index + 1
    },
    { title: '发票号码', dataIndex: 'invoiceNumber', key: 'invoiceNumber', width: 120, fixed: 'left' as const },
    { title: '发票代码', dataIndex: 'invoiceCode', key: 'invoiceCode', width: 110 },
    { title: '开票日期', dataIndex: 'invoiceDate', key: 'invoiceDate', width: 90, render: (date: string) => date?.split('T')[0] },
    { title: '购买方', dataIndex: 'buyerName', key: 'buyerName', width: 180 },
    { title: '销售方', dataIndex: 'sellerName', key: 'sellerName', width: 180 },
    { title: '金额', dataIndex: 'amount', key: 'amount', width: 100, align: 'right' as const, render: (amount: number) => `¥${amount?.toLocaleString() || 0}` },
    { title: '税额', dataIndex: 'taxAmount', key: 'taxAmount', width: 100, align: 'right' as const, render: (tax: number) => `¥${tax?.toLocaleString() || 0}` },
    { title: '价税合计', dataIndex: 'totalAmount', key: 'totalAmount', width: 110, align: 'right' as const, render: (total: number) => `¥${total?.toLocaleString() || 0}` },
    { title: '状态', dataIndex: 'status', key: 'status', width: 70, render: (status: string) => <Tag color={getStatusColor(status)}>{getStatusText(status)}</Tag> },
    {
      title: '操作', key: 'action', width: 180, fixed: 'right' as const,
      render: (_, record: any) => (
        <Space>
          <Button type="link" size="small" onClick={() => handleViewDetail(record)}>详情</Button>
          <Button type="link" size="small" icon={<FileImageOutlined />} onClick={() => handleViewInvoiceFile(record)}>查看发票</Button>
          <Dropdown
            menu={{
              items: [
                {
                  key: 'edit',
                  label: '编辑',
                  icon: <EditOutlined />,
                  onClick: () => handleEdit(record)
                },
                {
                  key: 'delete',
                  label: '删除',
                  icon: <DeleteOutlined />,
                  danger: true,
                  onClick: () => {
                    Modal.confirm({
                      title: '确认删除',
                      content: '确定要删除这条发票记录吗？',
                      okText: '确定',
                      cancelText: '取消',
                      onOk: () => handleDelete(record)
                    });
                  }
                }
              ]
            }}
            trigger={['click']}
          >
            <Button type="link" size="small">
              操作 <span style={{ fontSize: '10px' }}>▼</span>
            </Button>
          </Dropdown>
        </Space>
      ),
    },
  ];

  return (
    <div>
      {/* 搜索表单 */}
      <Card style={{ marginBottom: 16 }}>
        <Form layout="inline">
          <Form.Item label="搜索">
            <Input
              placeholder="发票号码、购买方、税号"
              style={{ width: 250 }}
              value={searchParams.search}
              onChange={(e) => setSearchParams({ ...searchParams, search: e.target.value })}
              allowClear
            />
          </Form.Item>
          <Form.Item label="销售方">
            <Select
              mode="tags"
              placeholder="选择或输入销售方"
              style={{ width: 250 }}
              value={searchParams.sellerCompanies}
              onChange={(value) => {
                // 确保value始终是数组
                const arrayValue = Array.isArray(value) ? value : (value ? [value] : []);
                setSearchParams({ ...searchParams, sellerCompanies: arrayValue });
              }}
              allowClear
              showSearch
              filterOption={(input, option) =>
                String(option?.children || '')?.toLowerCase().includes(input.toLowerCase())
              }
              tokenSeparators={[',']}
            >
              {Array.isArray(companiesForFilter) && companiesForFilter.map(company => (
                <Select.Option key={company.id} value={company.name}>{company.name}</Select.Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item label="开票日期">
            <DatePicker
              value={searchParams.startDate ? dayjs(searchParams.startDate) : null}
              onChange={(date) => setSearchParams({ ...searchParams, startDate: date ? date.format('YYYY-MM-DD') : '' })}
              size="small"
              style={{ width: 150 }}
            />
            <span style={{ margin: '0 8px' }}>至</span>
            <DatePicker
              value={searchParams.endDate ? dayjs(searchParams.endDate) : null}
              onChange={(date) => setSearchParams({ ...searchParams, endDate: date ? date.format('YYYY-MM-DD') : '' })}
              size="small"
              style={{ width: 150 }}
            />
          </Form.Item>
          <Form.Item label="状态">
            <select
              value={searchParams.status}
              onChange={(e) => setSearchParams({ ...searchParams, status: e.target.value })}
              style={{ width: 120, height: 32, border: '1px solid #d9d9d9', borderRadius: 6, padding: '0 8px' }}
            >
              <option value="">全部</option>
              <option value="NORMAL">正常</option>
              <option value="CANCELLED">作废</option>
            </select>
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" onClick={handleSearch}>搜索</Button>
              <Button onClick={handleReset}>重置</Button>
              {/* 临时隐藏调试按钮，后续可能会启用 */}
              {/* <Button type="dashed" onClick={handleFixCompanyIds} style={{ color: '#ff4d4f' }}>修复公司归属</Button> */}
              {/* <Button type="dashed" onClick={handleDebugUserPermissions} style={{ color: '#1890ff' }}>调试用户权限</Button> */}
            </Space>
          </Form.Item>
        </Form>
      </Card>

      <Card style={{ height: 'calc(100vh - 280px)', display: 'flex', flexDirection: 'column' }}>
        <div style={{ flex: 1, overflow: 'hidden' }}>
          <Table
            columns={columns}
            dataSource={invoices}
            rowKey="id"
            scroll={{ x: 1800, y: 'calc(100vh - 430px)' }}
            size="small"
            loading={loading}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `共 ${total} 条记录，第 ${range[0]}-${range[1]} 条`,
              pageSizeOptions: ['10', '20', '50', '100'],
              onChange: (page, pageSize) => {
                if (pageSize !== pagination.pageSize) {
                  setPagination(prev => ({ ...prev, pageSize, current: 1 }));
                  fetchInvoices(searchParams, 1, pageSize);
                } else {
                  fetchInvoices(searchParams, page);
                }
              },
              onShowSizeChange: (current, size) => {
                setPagination(prev => ({ ...prev, pageSize: size, current: 1 }));
                fetchInvoices(searchParams, 1, size);
              },
            }}
            onChange={handleTableChange}
            rowClassName={(record, index) => {
              const classes = [];

              // 获取当前行的年份
              const currentRowYear = new Date(record.invoiceDate).getFullYear();

              // 检查与上一行的年份差异
              if (index > 0) {
                const prevRecord = invoices[index - 1];
                const prevRowYear = new Date(prevRecord.invoiceDate).getFullYear();
                if (currentRowYear !== prevRowYear) {
                  classes.push('cross-year-top');
                }
              }

              // 检查与下一行的年份差异
              if (index < invoices.length - 1) {
                const nextRecord = invoices[index + 1];
                const nextRowYear = new Date(nextRecord.invoiceDate).getFullYear();
                if (currentRowYear !== nextRowYear) {
                  classes.push('cross-year-bottom');
                }
              }

              return classes.join(' ');
            }}
            summary={(pageData) => {
              const totalAmount = invoices.reduce((sum: number, invoice: any) => sum + (parseFloat(invoice.totalAmount) || 0), 0);
              const totalTaxAmount = invoices.reduce((sum: number, invoice: any) => sum + (parseFloat(invoice.taxAmount) || 0), 0);
              const invoiceCount = invoices.length;

              return (
                <Table.Summary fixed="bottom">
                  <Table.Summary.Row style={{ backgroundColor: '#fafafa', fontWeight: 'bold' }}>
                    <Table.Summary.Cell index={0} colSpan={3}>
                      <span style={{ fontSize: '16px', color: '#1890ff', fontWeight: 'bold' }}>当前页合计 - 共 {invoiceCount} 张发票</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={3}></Table.Summary.Cell>
                    <Table.Summary.Cell index={4}></Table.Summary.Cell>
                    <Table.Summary.Cell index={5}></Table.Summary.Cell>
                    <Table.Summary.Cell index={6} align="right">
                      <span style={{ fontSize: '16px', color: '#666' }}>¥{(totalAmount - totalTaxAmount).toLocaleString()}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={7} align="right">
                      <span style={{ fontSize: '16px', color: '#faad14' }}>¥{totalTaxAmount.toLocaleString()}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={8} align="right">
                      <span style={{ fontSize: '16px', color: '#52c41a', fontWeight: 'bold' }}>¥{totalAmount.toLocaleString()}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={9}></Table.Summary.Cell>
                    <Table.Summary.Cell index={10}></Table.Summary.Cell>
                  </Table.Summary.Row>
                </Table.Summary>
              );
            }}
          />
        </div>
      </Card>

      {/* 发票详情模态框 */}
      <Modal
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', paddingRight: '24px' }}>
            <span>发票详情</span>
            {selectedInvoice && (
              <Tag color={getStatusColor(selectedInvoice.status)} style={{ fontSize: '14px', padding: '4px 12px' }}>
                {getStatusText(selectedInvoice.status)}
              </Tag>
            )}
          </div>
        }
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="view-file" type="primary" icon={<FileImageOutlined />} onClick={() => handleViewInvoiceFile(selectedInvoice)}>
            查看发票
          </Button>,
          <Button key="close" onClick={() => setDetailModalVisible(false)}>关闭</Button>
        ]}
        width={1200}
      >
        {selectedInvoice && (
          <div>

            {/* 基本信息 */}
            <Card title="基本信息" size="small" style={{ marginBottom: 16 }}>
              <Row gutter={[16, 8]}>
                <Col span={6}>
                  <div style={{ marginBottom: 8 }}>
                    <Text type="secondary">发票号码</Text>
                    <div style={{ fontWeight: 'bold', fontSize: '16px' }}>{selectedInvoice.invoiceNumber}</div>
                  </div>
                </Col>
                <Col span={6}>
                  <div style={{ marginBottom: 8 }}>
                    <Text type="secondary">发票代码</Text>
                    <div style={{ fontWeight: 'bold', fontSize: '16px' }}>{selectedInvoice.invoiceCode}</div>
                  </div>
                </Col>
                <Col span={6}>
                  <div style={{ marginBottom: 8 }}>
                    <Text type="secondary">开票日期</Text>
                    <div style={{ fontWeight: 'bold', fontSize: '16px' }}>{selectedInvoice.invoiceDate?.split('T')[0]}</div>
                  </div>
                </Col>
                <Col span={6}>
                  <div style={{ marginBottom: 8 }}>
                    <Text type="secondary">开票人</Text>
                    <div style={{ fontWeight: 'bold', fontSize: '16px' }}>{selectedInvoice.drawer || '-'}</div>
                  </div>
                </Col>
                <Col span={24}>
                  <div style={{ marginBottom: 8 }}>
                    <Text type="secondary">备注</Text>
                    <div style={{ marginTop: 4, padding: '8px', backgroundColor: '#f5f5f5', borderRadius: '4px', minHeight: '32px' }}>
                      {selectedInvoice.remarks || ''}
                    </div>
                  </div>
                </Col>
              </Row>
            </Card>

            {/* 购买方和销售方信息 */}
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={12}>
                <Card title="购买方信息" size="small">
                  <div style={{ marginBottom: 12 }}>
                    <Text type="secondary">公司名称</Text>
                    <div style={{ fontWeight: 'bold', fontSize: '16px', marginTop: 4 }}>{selectedInvoice.buyerName}</div>
                  </div>
                  <div style={{ marginBottom: 12 }}>
                    <Text type="secondary">纳税人识别号</Text>
                    <div style={{ fontWeight: 'bold', marginTop: 4 }}>{selectedInvoice.buyerTaxId}</div>
                  </div>
                  <div style={{ marginBottom: 8 }}>
                    <Text type="secondary">地址电话</Text>
                    <div style={{ marginTop: 4 }}>{selectedInvoice.buyerAddress} {selectedInvoice.buyerPhone}</div>
                  </div>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="销售方信息" size="small">
                  <div style={{ marginBottom: 12 }}>
                    <Text type="secondary">公司名称</Text>
                    <div style={{ fontWeight: 'bold', fontSize: '16px', marginTop: 4 }}>{selectedInvoice.sellerName}</div>
                  </div>
                  <div style={{ marginBottom: 12 }}>
                    <Text type="secondary">纳税人识别号</Text>
                    <div style={{ fontWeight: 'bold', marginTop: 4 }}>{selectedInvoice.sellerTaxId}</div>
                  </div>
                  <div style={{ marginBottom: 8 }}>
                    <Text type="secondary">地址电话</Text>
                    <div style={{ marginTop: 4 }}>{selectedInvoice.sellerAddress} {selectedInvoice.sellerPhone}</div>
                  </div>
                </Card>
              </Col>
            </Row>

            {/* 金额信息 */}
            <Card title="金额信息" size="small" style={{ marginBottom: 16 }}>
              <Row gutter={16}>
                <Col span={8}>
                  <Statistic
                    title="金额"
                    value={selectedInvoice.amount || 0}
                    precision={2}
                    prefix="¥"
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="税额"
                    value={selectedInvoice.taxAmount || 0}
                    precision={2}
                    prefix="¥"
                    valueStyle={{ color: '#faad14' }}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="价税合计"
                    value={selectedInvoice.totalAmount || 0}
                    precision={2}
                    prefix="¥"
                    valueStyle={{ color: '#52c41a', fontSize: '20px', fontWeight: 'bold' }}
                  />
                </Col>
              </Row>
            </Card>

            {/* 发票明细 */}
            {selectedInvoice.invoiceItems && selectedInvoice.invoiceItems.length > 0 && (
              <Card title="发票明细" size="small" style={{ marginBottom: 16 }}>
                <Table
                  dataSource={selectedInvoice.invoiceItems}
                  rowKey="id"
                  size="small"
                  pagination={false}
                  columns={[
                    { title: '商品名称', dataIndex: 'itemName', key: 'itemName' },
                    { title: '规格型号', dataIndex: 'specification', key: 'specification' },
                    { title: '单位', dataIndex: 'unit', key: 'unit', width: 80 },
                    { title: '数量', dataIndex: 'quantity', key: 'quantity', width: 100, align: 'right' as const },
                    { title: '单价', dataIndex: 'unitPrice', key: 'unitPrice', width: 120, align: 'right' as const, render: (price: number) => `¥${price?.toLocaleString()}` },
                    { title: '金额', dataIndex: 'amount', key: 'amount', width: 120, align: 'right' as const, render: (amount: number) => `¥${amount?.toLocaleString()}` },
                    { title: '税率', dataIndex: 'taxRate', key: 'taxRate', width: 100, align: 'right' as const, render: (rate: number) => `${(rate * 100).toFixed(2)}%` },
                    { title: '税额', dataIndex: 'taxAmount', key: 'taxAmount', width: 120, align: 'right' as const, render: (tax: number) => `¥${tax?.toLocaleString()}` },
                    { title: '价税合计', dataIndex: 'totalAmount', key: 'totalAmount', width: 130, align: 'right' as const, render: (total: number) => `¥${total?.toLocaleString() || 0}` },
                  ]}
                />
              </Card>
            )}


          </div>
        )}
      </Modal>

      {/* 编辑发票模态框 */}
      <Modal
        title="编辑发票"
        open={editModalVisible}
        onOk={handleSave}
        onCancel={() => { setEditModalVisible(false); form.resetFields(); setEditingInvoiceItems([]); }}
        width={1400}
      >
        <Form form={form} layout="horizontal" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
          {/* 基本信息 */}
          <Card title="基本信息" size="small" style={{ marginBottom: 12 }}>
            <Row gutter={12}>
              <Col span={12}>
                <Form.Item label="发票号码" name="invoiceNumber" rules={[{ required: true, message: '请输入发票号码' }]} style={{ marginBottom: 12 }}>
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="发票代码" name="invoiceCode" rules={[{ required: true, message: '请输入发票代码' }]} style={{ marginBottom: 12 }}>
                  <Input />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={12}>
              <Col span={12}>
                <Form.Item label="开票日期" name="invoiceDate" rules={[{ required: true, message: '请选择开票日期' }]} style={{ marginBottom: 12 }}>
                  <DatePicker
                    size="small"
                    style={{ width: '100%' }}
                    format="YYYY-MM-DD"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="开票人" name="drawer" style={{ marginBottom: 12 }}>
                  <Input />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={12}>
              <Col span={24}>
                <Form.Item label="备注" name="remarks" labelCol={{ span: 3 }} wrapperCol={{ span: 21 }} style={{ marginBottom: 12 }}>
                  <Input.TextArea rows={3} placeholder="请输入备注信息..." />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 购买方和销售方信息 */}
          <Row gutter={12} style={{ marginBottom: 12 }}>
            <Col span={12}>
              <Card title="购买方信息" size="small">
                <Form.Item label="公司名称" name="buyerName" rules={[{ required: true, message: '请输入购买方名称' }]} labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} style={{ marginBottom: 10 }}>
                  <Input />
                </Form.Item>
                <Form.Item label="纳税人识别号" name="buyerTaxId" rules={[{ required: true, message: '请输入购买方税号' }]} labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} style={{ marginBottom: 10 }}>
                  <Input />
                </Form.Item>
                <Form.Item label="地址电话" name="buyerAddress" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} style={{ marginBottom: 10 }}>
                  <Input />
                </Form.Item>
              </Card>
            </Col>
            <Col span={12}>
              <Card title="销售方信息" size="small">
                <Form.Item label="公司名称" name="sellerName" rules={[{ required: true, message: '请输入销售方名称' }]} labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} style={{ marginBottom: 10 }}>
                  <Input />
                </Form.Item>
                <Form.Item label="纳税人识别号" name="sellerTaxId" rules={[{ required: true, message: '请输入销售方税号' }]} labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} style={{ marginBottom: 10 }}>
                  <Input />
                </Form.Item>
                <Form.Item label="地址电话" name="sellerAddress" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} style={{ marginBottom: 10 }}>
                  <Input />
                </Form.Item>
              </Card>
            </Col>
          </Row>

          {/* 金额信息 */}
          <Card title="金额信息" size="small" style={{ marginBottom: 12 }}>
            <Row gutter={12}>
              <Col span={12}>
                <Form.Item label="金额" name="amount" rules={[{ required: true, message: '请输入金额' }]} style={{ marginBottom: 12 }}>
                  <Input type="number" step="0.01" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="税额" name="taxAmount" rules={[{ required: true, message: '请输入税额' }]} style={{ marginBottom: 12 }}>
                  <Input type="number" step="0.01" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={12}>
              <Col span={12}>
                <Form.Item label="价税合计" name="totalAmount" rules={[{ required: true, message: '请输入价税合计' }]} style={{ marginBottom: 12 }}>
                  <Input type="number" step="0.01" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="发票状态" name="status" style={{ marginBottom: 12 }}>
                  <select style={{ width: '100%', height: 32, border: '1px solid #d9d9d9', borderRadius: 6, padding: '0 8px' }}>
                    <option value="NORMAL">正常</option>
                    <option value="CANCELLED">作废</option>
                  </select>
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 发票明细 */}
          <Card title="发票明细" size="small">
            <Button
              type="dashed"
              onClick={handleAddInvoiceItem}
              style={{ width: '100%', marginBottom: 16 }}
              icon={<PlusOutlined />}
            >
              添加明细
            </Button>
            <Table
              dataSource={editingInvoiceItems}
              rowKey={(record, index) => `item-${index}`}
              size="small"
              pagination={false}
              scroll={{ x: 1200 }}
              columns={[
                { title: '商品名称', dataIndex: 'itemName', key: 'itemName', width: 150, render: (text, record, index) => (
                  <Input value={text} onChange={(e) => handleInvoiceItemChange(index, 'itemName', e.target.value)} />
                )},
                { title: '规格型号', dataIndex: 'specification', key: 'specification', width: 120, render: (text, record, index) => (
                  <Input value={text} onChange={(e) => handleInvoiceItemChange(index, 'specification', e.target.value)} />
                )},
                { title: '单位', dataIndex: 'unit', key: 'unit', width: 80, render: (text, record, index) => (
                  <Input value={text} onChange={(e) => handleInvoiceItemChange(index, 'unit', e.target.value)} />
                )},
                { title: '数量', dataIndex: 'quantity', key: 'quantity', width: 100, render: (text, record, index) => (
                  <Input type="number" step="0.01" value={text} onChange={(e) => handleInvoiceItemChange(index, 'quantity', parseFloat(e.target.value) || 0)} />
                )},
                { title: '单价', dataIndex: 'unitPrice', key: 'unitPrice', width: 120, render: (text, record, index) => (
                  <Input type="number" step="0.01" value={text} onChange={(e) => handleInvoiceItemChange(index, 'unitPrice', parseFloat(e.target.value) || 0)} />
                )},
                { title: '金额', dataIndex: 'amount', key: 'amount', width: 120, render: (text, record, index) => (
                  <Input type="number" step="0.01" value={text} onChange={(e) => handleInvoiceItemChange(index, 'amount', parseFloat(e.target.value) || 0)} />
                )},
                { title: '税率', dataIndex: 'taxRate', key: 'taxRate', width: 100, render: (text, record, index) => (
                  <Input type="number" step="0.01" value={text} onChange={(e) => handleInvoiceItemChange(index, 'taxRate', parseFloat(e.target.value) || 0)} />
                )},
                { title: '税额', dataIndex: 'taxAmount', key: 'taxAmount', width: 120, render: (text, record, index) => (
                  <Input type="number" step="0.01" value={text} onChange={(e) => handleInvoiceItemChange(index, 'taxAmount', parseFloat(e.target.value) || 0)} />
                )},
                { title: '价税合计', dataIndex: 'totalAmount', key: 'totalAmount', width: 130, render: (text, record, index) => (
                  <Input type="number" step="0.01" value={text} onChange={(e) => handleInvoiceItemChange(index, 'totalAmount', parseFloat(e.target.value) || 0)} />
                )},
                { title: '操作', key: 'action', width: 80, render: (_, record, index) => (
                  <Button type="link" danger onClick={() => handleRemoveInvoiceItem(index)}>删除</Button>
                )},
              ]}
            />
          </Card>


        </Form>
      </Modal>

      {/* 添加发票模态框 */}
      <Modal
        title="添加发票"
        open={addModalVisible}
        onOk={handleSave}
        onCancel={() => { setAddModalVisible(false); form.resetFields(); }}
        width={1400}
      >
        <Form form={form} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="发票号码" name="invoiceNumber" rules={[{ required: true, message: '请输入发票号码' }]}>
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="发票代码" name="invoiceCode" rules={[{ required: true, message: '请输入发票代码' }]}>
                <Input />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="开票日期" name="invoiceDate" rules={[{ required: true, message: '请选择开票日期' }]}>
                <DatePicker
                  size="small"
                  style={{ width: '100%' }}
                  format="YYYY-MM-DD"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="发票状态" name="status" initialValue="NORMAL">
                <select style={{ width: '100%', height: 32, border: '1px solid #d9d9d9', borderRadius: 6, padding: '0 8px' }}>
                  <option value="NORMAL">正常</option>
                  <option value="CANCELLED">作废</option>
                </select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label="金额" name="amount" rules={[{ required: true, message: '请输入金额' }]}>
                <Input type="number" step="0.01" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="税额" name="taxAmount" rules={[{ required: true, message: '请输入税额' }]}>
                <Input type="number" step="0.01" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="价税合计" name="totalAmount" rules={[{ required: true, message: '请输入价税合计' }]}>
                <Input type="number" step="0.01" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="购买方名称" name="buyerName" rules={[{ required: true, message: '请输入购买方名称' }]}>
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="购买方税号" name="buyerTaxId" rules={[{ required: true, message: '请输入购买方税号' }]}>
                <Input />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="销售方名称" name="sellerName" rules={[{ required: true, message: '请输入销售方名称' }]}>
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="销售方税号" name="sellerTaxId" rules={[{ required: true, message: '请输入销售方税号' }]}>
                <Input />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* 开具发票文件预览模态框 */}
      <Modal
        title={`发票文件预览 - ${invoiceFileModalData?.invoiceNumber || ''}`}
        open={invoiceFilePreviewModalVisible}
        onCancel={() => setInvoiceFilePreviewModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setInvoiceFilePreviewModalVisible(false)}>关闭</Button>
        ]}
        width={1200}
        style={{ top: 20 }}
      >
        {invoiceFileModalData && (
          <div style={{ padding: '20px' }}>
            {invoiceFileModalData.attachments && invoiceFileModalData.attachments.length > 0 ? (
              <div>
                <Title level={5} style={{ marginBottom: 16 }}>发票附件 ({invoiceFileModalData.attachments.length}个文件)</Title>
                <List
                  dataSource={invoiceFileModalData.attachments}
                  renderItem={(attachment: any, index: number) => (
                    <List.Item
                      key={attachment.id}
                      style={{
                        border: '1px solid #f0f0f0',
                        borderRadius: '8px',
                        marginBottom: '12px',
                        padding: '16px',
                        backgroundColor: '#fafafa'
                      }}
                    >
                      <List.Item.Meta
                        avatar={
                          <div style={{ fontSize: '24px' }}>
                            {attachment.fileType === 'PDF' ? '📄' : '🖼️'}
                          </div>
                        }
                        title={
                          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <span>{attachment.fileName}</span>
                            <Space>
                              <Tag color={attachment.fileType === 'PDF' ? 'red' : 'blue'}>
                                {attachment.fileType}
                              </Tag>
                              {attachment.isOriginal && <Tag color="green">原件</Tag>}
                            </Space>
                          </div>
                        }
                        description={
                          <div>
                            <Text type="secondary">
                              文件大小: {(attachment.fileSize / 1024).toFixed(1)} KB
                            </Text>
                            <br />
                            <Space style={{ marginTop: 8 }}>
                              <Button
                                size="small"
                                icon={<EyeOutlined />}
                                onClick={() => {
                                  const token = localStorage.getItem('token');
                                  const previewUrlWithToken = `http://localhost:3001/api/upload/attachment/${attachment.id}?preview=true&type=issued&token=${token}`;
                                  setPreviewUrl(previewUrlWithToken);
                                  setPreviewFileName(attachment.fileName);
                                  setPreviewModalVisible(true);
                                }}
                              >
                                预览
                              </Button>
                              <Button
                                size="small"
                                icon={<DownloadOutlined />}
                                onClick={() => {
                                  const token = localStorage.getItem('token');
                                  const downloadUrl = `http://localhost:3001/api/upload/attachment/${attachment.id}?type=issued&token=${token}`;
                                  const link = document.createElement('a');
                                  link.href = downloadUrl;
                                  link.download = attachment.fileName;
                                  document.body.appendChild(link);
                                  link.click();
                                  document.body.removeChild(link);
                                }}
                              >
                                下载
                              </Button>
                              <Button
                                size="small"
                                danger
                                icon={<DeleteOutlined />}
                                onClick={() => {
                                  Modal.confirm({
                                    title: '确认删除',
                                    content: `确定要删除文件 "${attachment.fileName}" 吗？`,
                                    onOk: async () => {
                                      try {
                                        await api.delete(`/api/upload/invoice-attachment/${attachment.id}?invoiceType=issued`);
                                        message.success('文件删除成功');
                                        // 直接更新当前的发票附件信息，不重新打开弹窗
                                        const response = await api.get(`/api/invoices/${invoiceFileModalData.id}`);
                                        const updatedInvoiceDetail = response.data.data;
                                        setInvoiceFileModalData({
                                          ...updatedInvoiceDetail,
                                          attachments: updatedInvoiceDetail.attachments || []
                                        });
                                      } catch (error: any) {
                                        console.error('删除文件失败:', error);
                                        message.error(error.response?.data?.message || '删除文件失败');
                                      }
                                    },
                                  });
                                }}
                              >
                                删除
                              </Button>
                            </Space>
                          </div>
                        }
                      />

                    </List.Item>
                  )}
                />
              </div>
            ) : (
              <div style={{ textAlign: 'center', padding: '40px' }}>
                <div style={{ fontSize: '48px', marginBottom: '16px' }}>📄</div>
                <Title level={4} type="secondary">暂无发票附件</Title>
                <Text type="secondary">请先上传发票文件</Text>
              </div>
            )}

            {/* 上传按钮 */}
            <div style={{ textAlign: 'center', marginTop: '16px' }}>
              <input
                type="file"
                accept="image/*,.pdf"
                onChange={async (e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    try {
                      await handleInvoiceFileUpload(invoiceFileModalData.id, file);
                      // 获取更新后的发票数据
                      const response = await api.get(`/api/invoices/${invoiceFileModalData.id}`);
                      const updatedInvoice = response.data.data;
                      setInvoiceFileModalData({
                        ...updatedInvoice,
                        attachments: updatedInvoice.attachments || []
                      });
                      // 清空文件输入框
                      e.target.value = '';
                    } catch (error) {
                      console.error('上传文件失败:', error);
                    }
                  }
                }}
                style={{ display: 'none' }}
                id={`invoice-file-upload-${invoiceFileModalData.id}`}
              />
              <Button
                type="primary"
                icon={<UploadOutlined />}
                onClick={() => document.getElementById(`invoice-file-upload-${invoiceFileModalData.id}`)?.click()}
              >
                上传发票文件
              </Button>
            </div>
          </div>
        )}
      </Modal>

      {/* 文件预览模态框 */}
      <Modal
        title={`文件预览 - ${previewFileName}`}
        open={previewModalVisible}
        onCancel={() => setPreviewModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setPreviewModalVisible(false)}>关闭</Button>
        ]}
        width="60vw"
        style={{ top: 0 }}
        styles={{ body: { padding: 0, overflow: 'hidden' } }}
        centered
      >
        {previewUrl && (
          <div style={{
            width: '100%',
            height: '70vh',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            overflow: 'hidden'
          }}>
            <iframe
              src={previewUrl}
              style={{
                width: '100%',
                height: '100%',
                border: 'none',
                maxWidth: '100%',
                maxHeight: '100%'
              }}
              title="文件预览"
              onLoad={() => {
                // iframe加载完成后的处理
                // console.log('PDF预览加载完成');
              }}
            />
          </div>
        )}
      </Modal>
    </div>
  );
};

// 用户管理组件
const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState<any>(null);
  const [form] = Form.useForm();
  const [companies, setCompanies] = useState<any[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [searchParams, setSearchParams] = useState({
    search: '',
    role: '',
    status: '',
  });
  const [menuPermissionModalVisible, setMenuPermissionModalVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [menuPermissions, setMenuPermissions] = useState<any[]>([]);
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [passwordForm] = Form.useForm();

  useEffect(() => {
    fetchUsers();
    fetchCompanies();
  }, []);

  const fetchUsers = async (customParams = {}, page = 1) => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pagination.pageSize.toString()
      });

      const searchData = { ...searchParams, ...customParams };
      if (searchData.search) params.append('search', searchData.search);
      if (searchData.role) params.append('role', searchData.role);
      if (searchData.status) params.append('status', searchData.status);

      const response = await api.get(`/api/users?${params}`);
      const data = response.data.data;
      setUsers(data.data || []);
      setPagination({
        current: page,
        pageSize: pagination.pageSize,
        total: data.total || 0,
      });
    } catch (error) {
      console.error('获取用户列表失败:', error);
      message.error('获取用户列表失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchCompanies = async () => {
    try {
      const response = await api.get('/api/companies/active');
      const companiesData = response.data.data || [];
      setCompanies(Array.isArray(companiesData) ? companiesData : []);
    } catch (error) {
      console.error('获取公司列表失败:', error);
    }
  };

  const handleSearch = () => {
    fetchUsers(searchParams, 1);
  };

  const handleReset = () => {
    setSearchParams({ search: '', role: '', status: '' });
    fetchUsers({}, 1);
  };

  const handleTableChange = (paginationConfig: any) => {
    fetchUsers(searchParams, paginationConfig.current);
  };

  const handleAdd = () => {
    setEditingUser(null);
    form.resetFields();
    setAddModalVisible(true);
  };

  const handleEdit = (record: any) => {
    setEditingUser(record);
    form.setFieldsValue({
      ...record,
      companyIds: record.userCompanies?.map((uc: any) => uc.companyId) || []
    });
    setEditModalVisible(true);
  };

  const handleSave = async () => {
    try {
      const values = await form.validateFields();

      if (editingUser) {
        await api.put(`/api/users/${editingUser.id}`, values);
        message.success('用户更新成功！');
      } else {
        await api.post('/api/users', values);
        message.success('用户添加成功！');
      }

      setEditModalVisible(false);
      setAddModalVisible(false);
      setEditingUser(null);
      form.resetFields();
      fetchUsers();
    } catch (error) {
      console.error('保存用户失败:', error);
      message.error('保存失败');
    }
  };

  const handleDelete = async (record: any) => {
    try {
      await api.delete(`/api/users/${record.id}`);
      message.success('用户删除成功！');
      fetchUsers();
    } catch (error) {
      console.error('删除用户失败:', error);
      message.error('删除用户失败');
    }
  };

  // 菜单权限管理
  const handleMenuPermission = async (record: any) => {
    setSelectedUser(record);
    setMenuPermissionModalVisible(true);

    // 获取用户菜单权限
    try {
      const response = await api.get(`/api/users/${record.id}/menu-permissions`);
      setMenuPermissions(response.data.data || []);
    } catch (error) {
      console.error('获取用户菜单权限失败:', error);
      // 如果API失败，使用默认菜单结构
      const defaultMenus = [
        { id: 'dashboard', key: 'dashboard', name: '仪表板', parentId: null, canView: false, canEdit: false, canDelete: false, canExport: false },
        { id: 'companies', key: 'companies', name: '公司管理', parentId: null, canView: false, canEdit: false, canDelete: false, canExport: false },
        { id: 'invoices', key: 'invoices', name: '发票管理', parentId: null, canView: false, canEdit: false, canDelete: false, canExport: false },
        { id: 'issued-invoices', key: 'issued-invoices', name: '开具发票', parentId: 'invoices', canView: false, canEdit: false, canDelete: false, canExport: false },
        { id: 'received-invoices', key: 'received-invoices', name: '取得发票', parentId: 'invoices', canView: false, canEdit: false, canDelete: false, canExport: false },
        { id: 'users', key: 'users', name: '用户管理', parentId: null, canView: false, canEdit: false, canDelete: false, canExport: false },
        { id: 'reports', key: 'reports', name: '报表分析', parentId: null, canView: false, canEdit: false, canDelete: false, canExport: false },
        { id: 'invoice-relations', key: 'invoice-relations', name: '开票关系', parentId: 'reports', canView: false, canEdit: false, canDelete: false, canExport: false },
      ];
      setMenuPermissions(defaultMenus);
    }
  };

  const handleSaveMenuPermissions = async () => {
    try {
      await api.put(`/api/users/${selectedUser.id}/menu-permissions`, {
        permissions: menuPermissions.map(menu => ({
          menuId: menu.id,
          canView: menu.canView,
          canEdit: menu.canEdit,
          canDelete: menu.canDelete,
          canExport: menu.canExport,
        }))
      });
      message.success('菜单权限更新成功！');
      setMenuPermissionModalVisible(false);
      setSelectedUser(null);
      setMenuPermissions([]);
    } catch (error) {
      console.error('更新菜单权限失败:', error);
      message.error('更新菜单权限失败');
    }
  };

  const handleMenuPermissionChange = (menuId: string, permission: string, value: boolean) => {
    setMenuPermissions(prev => prev.map(menu =>
      menu.id === menuId ? { ...menu, [permission]: value } : menu
    ));
  };

  // 修改密码
  const handleChangePassword = (record: any) => {
    setSelectedUser(record);
    passwordForm.resetFields();
    setPasswordModalVisible(true);
  };

  // 处理用户管理中的生成随机密码
  const handleGenerateUserPassword = () => {
    const randomPassword = generateRandomPassword();
    passwordForm.setFieldsValue({
      newPassword: randomPassword,
      confirmPassword: randomPassword
    });
    message.success(`已生成16位随机密码`);
  };

  const handleSavePassword = async () => {
    try {
      const values = await passwordForm.validateFields();
      await api.put(`/api/users/${selectedUser.id}/password`, {
        newPassword: values.newPassword
      });
      message.success('密码修改成功！');
      setPasswordModalVisible(false);
      setSelectedUser(null);
      passwordForm.resetFields();
    } catch (error) {
      console.error('修改密码失败:', error);
      message.error('修改密码失败');
    }
  };

  const columns = [
    { title: '用户名', dataIndex: 'username', key: 'username' },
    { title: '邮箱', dataIndex: 'email', key: 'email' },
    { title: '姓名', dataIndex: 'name', key: 'name' },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      render: (role: string) => {
        const roleMap = { ADMIN: '管理员', USER: '普通用户' };
        return <Tag color={role === 'ADMIN' ? 'red' : 'blue'}>{roleMap[role as keyof typeof roleMap] || role}</Tag>;
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = { ACTIVE: '激活', INACTIVE: '禁用' };
        return <Tag color={status === 'ACTIVE' ? 'green' : 'red'}>{statusMap[status as keyof typeof statusMap] || status}</Tag>;
      }
    },
    {
      title: '权限公司',
      key: 'companies',
      render: (record: any) => {
        const userCompanies = record.userCompanies || [];
        return userCompanies.length > 0 ? (
          <div>
            {userCompanies.map((uc: any) => (
              <Tag key={uc.companyId}>{uc.company?.name || uc.companyId}</Tag>
            ))}
          </div>
        ) : <Text type="secondary">无权限限制</Text>;
      }
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => new Date(date).toLocaleDateString()
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: any) => (
        <Space>
          <Button type="link" size="small" onClick={() => handleEdit(record)}>编辑</Button>
          <Button type="link" size="small" onClick={() => handleChangePassword(record)}>修改密码</Button>
          <Button type="link" size="small" onClick={() => handleMenuPermission(record)}>菜单权限</Button>
          <Popconfirm
            title="确定要删除这个用户吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" size="small" danger>删除</Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      {/* 搜索表单 */}
      <Card style={{ marginBottom: 16 }}>
        <Form layout="inline">
          <Form.Item label="搜索">
            <Input
              placeholder="用户名、邮箱、姓名"
              style={{ width: 200 }}
              value={searchParams.search}
              onChange={(e) => setSearchParams({ ...searchParams, search: e.target.value })}
              allowClear
            />
          </Form.Item>
          <Form.Item label="角色">
            <Select
              placeholder="选择角色"
              style={{ width: 120 }}
              value={searchParams.role}
              onChange={(value) => setSearchParams({ ...searchParams, role: value })}
              allowClear
            >
              <Select.Option value="ADMIN">管理员</Select.Option>
              <Select.Option value="USER">普通用户</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item label="状态">
            <Select
              placeholder="选择状态"
              style={{ width: 120 }}
              value={searchParams.status}
              onChange={(value) => setSearchParams({ ...searchParams, status: value })}
              allowClear
            >
              <Select.Option value="ACTIVE">激活</Select.Option>
              <Select.Option value="INACTIVE">禁用</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" onClick={handleSearch}>搜索</Button>
              <Button onClick={handleReset}>重置</Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={3}>用户管理</Title>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            添加用户
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showTotal: (total) => `共 ${total} 条记录`,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
          onChange={handleTableChange}
        />
      </Card>

      {/* 添加/编辑用户模态框 */}
      <Modal
        title={editingUser ? '编辑用户' : '添加用户'}
        open={editModalVisible || addModalVisible}
        onOk={handleSave}
        onCancel={() => {
          setEditModalVisible(false);
          setAddModalVisible(false);
          setEditingUser(null);
          form.resetFields();
        }}
        width={600}
      >
        <Form form={form} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="用户名" name="username" rules={[{ required: true, message: '请输入用户名' }]}>
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="邮箱" name="email" rules={[
                { required: true, message: '请输入邮箱' },
                { type: 'email', message: '请输入有效的邮箱地址' }
              ]}>
                <Input />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="姓名" name="name" rules={[{ required: true, message: '请输入姓名' }]}>
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="角色" name="role" rules={[{ required: true, message: '请选择角色' }]}>
                <Select>
                  <Select.Option value="ADMIN">管理员</Select.Option>
                  <Select.Option value="USER">普通用户</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="状态" name="status" rules={[{ required: true, message: '请选择状态' }]}>
                <Select>
                  <Select.Option value="ACTIVE">激活</Select.Option>
                  <Select.Option value="INACTIVE">禁用</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              {!editingUser && (
                <Form.Item label="密码" name="password" rules={[{ required: true, message: '请输入密码' }]}>
                  <Input.Password />
                </Form.Item>
              )}
            </Col>
          </Row>
          <Form.Item label="权限公司" name="companyIds" help="只能查看授权公司的权限数据">
            <Select
              mode="multiple"
              placeholder="选择用户可以访问的公司"
              allowClear
            >
              {companies.map(company => (
                <Select.Option key={company.id} value={company.id}>{company.name}</Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 菜单权限管理模态框 */}
      <Modal
        title={`设置用户菜单权限 - ${selectedUser?.name || ''}`}
        open={menuPermissionModalVisible}
        onOk={handleSaveMenuPermissions}
        onCancel={() => {
          setMenuPermissionModalVisible(false);
          setSelectedUser(null);
          setMenuPermissions([]);
        }}
        width={800}
        okText="保存"
        cancelText="取消"
      >
        <div style={{ maxHeight: '500px', overflowY: 'auto' }}>
          <Table
            dataSource={menuPermissions}
            rowKey="id"
            pagination={false}
            size="small"
            columns={[
              {
                title: '菜单名称',
                dataIndex: 'name',
                key: 'name',
                render: (text: string, record: any) => (
                  <span style={{ paddingLeft: record.parentId ? 20 : 0 }}>
                    {record.parentId && '└ '}{text}
                  </span>
                ),
              },
              {
                title: '查看',
                dataIndex: 'canView',
                key: 'canView',
                width: 80,
                render: (value: boolean, record: any) => (
                  <Checkbox
                    checked={value}
                    onChange={(e) => handleMenuPermissionChange(record.id, 'canView', e.target.checked)}
                  />
                ),
              },
              {
                title: '编辑',
                dataIndex: 'canEdit',
                key: 'canEdit',
                width: 80,
                render: (value: boolean, record: any) => (
                  <Checkbox
                    checked={value}
                    onChange={(e) => handleMenuPermissionChange(record.id, 'canEdit', e.target.checked)}
                  />
                ),
              },
              {
                title: '删除',
                dataIndex: 'canDelete',
                key: 'canDelete',
                width: 80,
                render: (value: boolean, record: any) => (
                  <Checkbox
                    checked={value}
                    onChange={(e) => handleMenuPermissionChange(record.id, 'canDelete', e.target.checked)}
                  />
                ),
              },
              {
                title: '导出',
                dataIndex: 'canExport',
                key: 'canExport',
                width: 80,
                render: (value: boolean, record: any) => (
                  <Checkbox
                    checked={value}
                    onChange={(e) => handleMenuPermissionChange(record.id, 'canExport', e.target.checked)}
                  />
                ),
              },
            ]}
          />
        </div>
        <div style={{ marginTop: 16, padding: 12, backgroundColor: '#f5f5f5', borderRadius: 4 }}>
          <Text type="secondary">
            <strong>权限说明：</strong><br />
            • <strong>查看</strong>：可以访问该菜单页面<br />
            • <strong>编辑</strong>：可以修改数据<br />
            • <strong>删除</strong>：可以删除数据<br />
            • <strong>导出</strong>：可以导出数据
          </Text>
        </div>
      </Modal>

      {/* 修改密码模态框 */}
      <Modal
        title={`修改密码 - ${selectedUser?.name || ''}`}
        open={passwordModalVisible}
        onCancel={() => {
          setPasswordModalVisible(false);
          setSelectedUser(null);
          passwordForm.resetFields();
        }}
        footer={[
          <Button
            key="generate"
            type="default"
            onClick={handleGenerateUserPassword}
            style={{ float: 'left' }}
          >
            随机密码
          </Button>,
          <Button
            key="cancel"
            onClick={() => {
              setPasswordModalVisible(false);
              setSelectedUser(null);
              passwordForm.resetFields();
            }}
          >
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={handleSavePassword}
          >
            确定
          </Button>
        ]}
      >
        <Form form={passwordForm} layout="vertical">
          <Form.Item
            label="新密码"
            name="newPassword"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 6, message: '密码至少6个字符' }
            ]}
          >
            <Input.Password
              placeholder="请输入新密码"
            />
          </Form.Item>
          <Form.Item
            label="确认密码"
            name="confirmPassword"
            dependencies={['newPassword']}
            rules={[
              { required: true, message: '请确认新密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('newPassword') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input.Password
              placeholder="请再次输入新密码"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

// 操作日志组件
const OperationLogs: React.FC = () => {
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [searchParams, setSearchParams] = useState({
    username: '',
    operationName: '',
    method: '',
    path: '',
    isSuccess: undefined as boolean | undefined,
    startDate: '',
    endDate: '',
  });
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedLog, setSelectedLog] = useState<any>(null);
  const [stats, setStats] = useState<any>(null);

  useEffect(() => {
    fetchLogs();
    fetchStats();
  }, []);

  const fetchLogs = async (params = searchParams, page = pagination.current) => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams();
      queryParams.append('page', page.toString());
      queryParams.append('pageSize', pagination.pageSize.toString());

      Object.entries(params).forEach(([key, value]) => {
        if (value !== '' && value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });

      const response = await api.get(`/api/operation-logs?${queryParams}`);
      if (response.data.success) {
        setLogs(response.data.data.data);
        setPagination({
          ...pagination,
          current: page,
          total: response.data.data.total,
        });
      }
    } catch (error) {
      console.error('获取操作日志失败:', error);
      message.error('获取操作日志失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await api.get('/api/operation-logs/stats/summary');
      if (response.data.success) {
        setStats(response.data.data);
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
    }
  };

  const handleSearch = () => {
    fetchLogs(searchParams, 1);
  };

  const handleReset = () => {
    const resetParams = {
      username: '',
      operationName: '',
      method: '',
      path: '',
      isSuccess: undefined,
      startDate: '',
      endDate: '',
    };
    setSearchParams(resetParams);
    fetchLogs(resetParams, 1);
  };

  const handleTableChange = (paginationConfig: any) => {
    fetchLogs(searchParams, paginationConfig.current);
  };

  const showDetail = (record: any) => {
    setSelectedLog(record);
    setDetailModalVisible(true);
  };

  const columns = [
    {
      title: '操作时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作用户',
      dataIndex: 'username',
      key: 'username',
      width: 120,
      render: (username: string, record: any) => username || '未登录用户',
    },
    {
      title: '操作名称',
      dataIndex: 'operationName',
      key: 'operationName',
      width: 150,
    },
    {
      title: '请求方法',
      dataIndex: 'method',
      key: 'method',
      width: 80,
      render: (method: string) => (
        <Tag color={
          method === 'GET' ? 'blue' :
          method === 'POST' ? 'green' :
          method === 'PUT' ? 'orange' :
          method === 'DELETE' ? 'red' : 'default'
        }>
          {method}
        </Tag>
      ),
    },
    {
      title: '请求路径',
      dataIndex: 'path',
      key: 'path',
      width: 200,
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'isSuccess',
      key: 'isSuccess',
      width: 80,
      render: (isSuccess: boolean) => (
        <Tag color={isSuccess ? 'success' : 'error'} icon={isSuccess ? <CheckCircleOutlined /> : <CloseCircleOutlined />}>
          {isSuccess ? '成功' : '失败'}
        </Tag>
      ),
    },
    {
      title: '耗时',
      dataIndex: 'duration',
      key: 'duration',
      width: 80,
      render: (duration: number) => duration ? `${duration}ms` : '-',
    },
    {
      title: 'IP地址',
      dataIndex: 'ipAddress',
      key: 'ipAddress',
      width: 120,
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_, record: any) => (
        <Button type="link" size="small" onClick={() => showDetail(record)}>
          详情
        </Button>
      ),
    },
  ];

  return (
    <div>
      <Title level={2}>操作日志</Title>

      {/* 统计卡片 */}
      {stats && (
        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="今日操作"
                value={stats.today.total}
                prefix={<HistoryOutlined />}
                suffix="次"
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="今日成功"
                value={stats.today.success}
                prefix={<CheckCircleOutlined />}
                suffix="次"
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="今日失败"
                value={stats.today.failed}
                prefix={<CloseCircleOutlined />}
                suffix="次"
                valueStyle={{ color: '#cf1322' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="本周操作"
                value={stats.week.total}
                prefix={<HistoryOutlined />}
                suffix="次"
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* 搜索表单 */}
      <Card style={{ marginBottom: 16 }}>
        <Form layout="inline">
          <Form.Item label="用户名">
            <Input
              placeholder="搜索用户名"
              style={{ width: 150 }}
              value={searchParams.username}
              onChange={(e) => setSearchParams({ ...searchParams, username: e.target.value })}
              allowClear
            />
          </Form.Item>
          <Form.Item label="操作名称">
            <Input
              placeholder="搜索操作名称"
              style={{ width: 150 }}
              value={searchParams.operationName}
              onChange={(e) => setSearchParams({ ...searchParams, operationName: e.target.value })}
              allowClear
            />
          </Form.Item>
          <Form.Item label="请求方法">
            <Select
              placeholder="选择方法"
              style={{ width: 120 }}
              value={searchParams.method}
              onChange={(value) => setSearchParams({ ...searchParams, method: value })}
              allowClear
            >
              <Select.Option value="GET">GET</Select.Option>
              <Select.Option value="POST">POST</Select.Option>
              <Select.Option value="PUT">PUT</Select.Option>
              <Select.Option value="DELETE">DELETE</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item label="状态">
            <Select
              placeholder="选择状态"
              style={{ width: 120 }}
              value={searchParams.isSuccess}
              onChange={(value) => setSearchParams({ ...searchParams, isSuccess: value })}
              allowClear
            >
              <Select.Option value={true}>成功</Select.Option>
              <Select.Option value={false}>失败</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item label="开始时间">
            <DatePicker
              value={searchParams.startDate ? dayjs(searchParams.startDate) : null}
              onChange={(date) => setSearchParams({ ...searchParams, startDate: date ? date.format('YYYY-MM-DD') : '' })}
              style={{ width: 150 }}
            />
          </Form.Item>
          <Form.Item label="结束时间">
            <DatePicker
              value={searchParams.endDate ? dayjs(searchParams.endDate) : null}
              onChange={(date) => setSearchParams({ ...searchParams, endDate: date ? date.format('YYYY-MM-DD') : '' })}
              style={{ width: 150 }}
            />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" onClick={handleSearch}>搜索</Button>
              <Button onClick={handleReset}>重置</Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      {/* 操作日志表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={logs}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={handleTableChange}
          size="small"
          scroll={{ x: 1200, y: 600 }}
        />
      </Card>

      {/* 详情弹窗 */}
      <Modal
        title="操作日志详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={1200}
      >
        {selectedLog && (
          <div>
            <Row gutter={[16, 16]}>
              <Col span={8}>
                <Text strong>操作时间：</Text>
                <Text>{dayjs(selectedLog.createdAt).format('YYYY-MM-DD HH:mm:ss')}</Text>
              </Col>
              <Col span={8}>
                <Text strong>操作用户：</Text>
                <Text>{selectedLog.username || '未登录用户'}</Text>
              </Col>
              <Col span={8}>
                <Text strong>操作名称：</Text>
                <Text>{selectedLog.operationName}</Text>
              </Col>
              <Col span={8}>
                <Text strong>请求方法：</Text>
                <Tag color={
                  selectedLog.method === 'GET' ? 'blue' :
                  selectedLog.method === 'POST' ? 'green' :
                  selectedLog.method === 'PUT' ? 'orange' :
                  selectedLog.method === 'DELETE' ? 'red' : 'default'
                }>
                  {selectedLog.method}
                </Tag>
              </Col>
              <Col span={8}>
                <Text strong>状态码：</Text>
                <Text>{selectedLog.statusCode}</Text>
              </Col>
              <Col span={8}>
                <Text strong>执行状态：</Text>
                <Tag color={selectedLog.isSuccess ? 'success' : 'error'}>
                  {selectedLog.isSuccess ? '成功' : '失败'}
                </Tag>
              </Col>
              <Col span={8}>
                <Text strong>耗时：</Text>
                <Text>{selectedLog.duration ? `${selectedLog.duration}ms` : '-'}</Text>
              </Col>
              <Col span={8}>
                <Text strong>IP地址：</Text>
                <Text>{selectedLog.ipAddress}</Text>
              </Col>
              <Col span={8}>
                <Text strong>用户角色：</Text>
                <Text>{selectedLog.user?.role || '-'}</Text>
              </Col>
              <Col span={24}>
                <Text strong>请求路径：</Text>
                <Text code>{selectedLog.path}</Text>
              </Col>
              <Col span={24}>
                <Text strong>浏览器信息：</Text>
                <Text ellipsis>{selectedLog.userAgent}</Text>
              </Col>
              {selectedLog.requestParams && (
                <Col span={24}>
                  <Text strong>请求参数：</Text>
                  <pre style={{
                    background: '#f5f5f5',
                    padding: '8px',
                    borderRadius: '4px',
                    maxHeight: '200px',
                    overflow: 'auto',
                    fontSize: '12px'
                  }}>
                    {JSON.stringify(selectedLog.requestParams, null, 2)}
                  </pre>
                </Col>
              )}
              {selectedLog.responseData && (
                <Col span={24}>
                  <Text strong>返回数据：</Text>
                  <pre style={{
                    background: '#f5f5f5',
                    padding: '8px',
                    borderRadius: '4px',
                    maxHeight: '200px',
                    overflow: 'auto',
                    fontSize: '12px'
                  }}>
                    {JSON.stringify(selectedLog.responseData, null, 2)}
                  </pre>
                </Col>
              )}
              {selectedLog.errorMessage && (
                <Col span={24}>
                  <Text strong>错误信息：</Text>
                  <Text type="danger">{selectedLog.errorMessage}</Text>
                </Col>
              )}
            </Row>
          </div>
        )}
      </Modal>
    </div>
  );
};

// 报表组件
const Reports: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();

  // 将所有Hooks移到组件顶层
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());
  const [allCompanies, setAllCompanies] = useState<any[]>([]);

  // 季度开票汇总状态
  const [quarterlySelectedCompanies, setQuarterlySelectedCompanies] = useState<string[]>([]);
  const [quarterlyData, setQuarterlyData] = useState<any[]>([]);
  const [quarterlyLoading, setQuarterlyLoading] = useState(false);

  // 公司开票汇总状态
  const [selectedYears, setSelectedYears] = useState<number[]>([]);
  const [companySelectedCompanies, setCompanySelectedCompanies] = useState<string[]>([]);
  const [companyData, setCompanyData] = useState<any[]>([]);
  const [companyLoading, setCompanyLoading] = useState(false);

  // 发票张数汇总状态
  const [invoiceCountSelectedYears, setInvoiceCountSelectedYears] = useState<number[]>([new Date().getFullYear()]); // 默认当年
  const [invoiceCountSelectedCompanies, setInvoiceCountSelectedCompanies] = useState<string[]>([]); // 默认空，查询所有公司
  const [invoiceCountData, setInvoiceCountData] = useState<any[]>([]);
  const [invoiceCountLoading, setInvoiceCountLoading] = useState(false);
  const [invoiceCountAvailableYears, setInvoiceCountAvailableYears] = useState<number[]>([]); // 可用年度列表

  // 所属用户开票汇总状态
  const [userSummarySelectedYears, setUserSummarySelectedYears] = useState<number[]>([]);
  const [userSummaryData, setUserSummaryData] = useState<any[]>([]);
  const [userSummaryLoading, setUserSummaryLoading] = useState(false);
  const [userSummaryDetailVisible, setUserSummaryDetailVisible] = useState(false);
  const [userSummaryDetailData, setUserSummaryDetailData] = useState<any[]>([]);
  const [selectedUserSummary, setSelectedUserSummary] = useState<any>(null);

  // 用户开票汇总状态
  const [userInvoiceSummaryData, setUserInvoiceSummaryData] = useState<any[]>([]);
  const [userInvoiceSummaryLoading, setUserInvoiceSummaryLoading] = useState(false);
  const [userInvoiceSummarySelectedUsers, setUserInvoiceSummarySelectedUsers] = useState<string[]>([]);
  const [userInvoiceSummaryAllUsers, setUserInvoiceSummaryAllUsers] = useState<any[]>([]);
  const [userInvoiceSummaryDetailVisible, setUserInvoiceSummaryDetailVisible] = useState(false);
  const [userInvoiceSummaryDetailData, setUserInvoiceSummaryDetailData] = useState<any>({ issuedInvoices: [], receivedInvoices: [] });
  const [userInvoiceSummarySelectedRecord, setUserInvoiceSummarySelectedRecord] = useState<any>(null);

  // 用户开票汇总分组状态
  const [userInvoiceSummaryGroupVisible, setUserInvoiceSummaryGroupVisible] = useState(false);
  const [userInvoiceSummaryGroupData, setUserInvoiceSummaryGroupData] = useState<any>({ issuedGroups: [], receivedGroups: [] });
  const [userInvoiceSummaryGroupListVisible, setUserInvoiceSummaryGroupListVisible] = useState(false);
  const [userInvoiceSummaryGroupListData, setUserInvoiceSummaryGroupListData] = useState<any[]>([]);
  const [userInvoiceSummaryGroupListTitle, setUserInvoiceSummaryGroupListTitle] = useState('');





  // 格式化金额显示（万元）
  const formatAmount = (value: number | null | undefined) => {
    if (value === null || value === undefined || isNaN(value)) {
      return '¥0万';
    }
    const wanValue = value / 10000;
    // 如果是整数，不显示小数点；否则显示所有小数位
    return `¥${wanValue.toFixed(wanValue % 1 === 0 ? 0 : 2)}万`;
  };

  // 获取季度统计数据
  const fetchQuarterlyData = async () => {
    setQuarterlyLoading(true);
    try {
      const params = new URLSearchParams();
      params.append('year', selectedYear.toString());
      if (quarterlySelectedCompanies.length > 0) {
        quarterlySelectedCompanies.forEach(companyId => {
          params.append('companyIds', companyId);
        });
      }

      const response = await api.get(`/api/invoices/stats/quarterly?${params}`);
      setQuarterlyData(response.data.data.companies || []);
    } catch (error) {
      console.error('获取季度统计数据失败:', error);
      message.error('获取季度统计数据失败');
      setQuarterlyData([]);
    } finally {
      setQuarterlyLoading(false);
    }
  };

  // 处理查询按钮点击
  const handleQuery = () => {
    fetchQuarterlyData();
  };

  // 导出季度开票汇总
  const handleExportQuarterly = async () => {
    if (!quarterlyData || quarterlyData.length === 0) {
      message.warning('暂无数据可导出');
      return;
    }

    try {
      // 准备导出数据 - 显示原始数据，不进行格式化
      const exportData = quarterlyData.map((company, index) => ({
        '序号': index + 1,
        '公司名称': company.name,
        '税号': company.taxId,
        '1月': company.monthly.jan || 0,
        '2月': company.monthly.feb || 0,
        '3月': company.monthly.mar || 0,
        '一季度': (company.monthly.jan || 0) + (company.monthly.feb || 0) + (company.monthly.mar || 0),
        '4月': company.monthly.apr || 0,
        '5月': company.monthly.may || 0,
        '6月': company.monthly.jun || 0,
        '二季度': (company.monthly.apr || 0) + (company.monthly.may || 0) + (company.monthly.jun || 0),
        '7月': company.monthly.jul || 0,
        '8月': company.monthly.aug || 0,
        '9月': company.monthly.sep || 0,
        '三季度': (company.monthly.jul || 0) + (company.monthly.aug || 0) + (company.monthly.sep || 0),
        '10月': company.monthly.oct || 0,
        '11月': company.monthly.nov || 0,
        '12月': company.monthly.dec || 0,
        '四季度': (company.monthly.oct || 0) + (company.monthly.nov || 0) + (company.monthly.dec || 0),
        '年度总计': Object.values(company.monthly).reduce((sum: number, val: any) => sum + (val || 0), 0)
      }));

      // 使用xlsx导出
      const XLSX = await import('xlsx');
      const ws = XLSX.utils.json_to_sheet(exportData);

      // 设置列宽
      const colWidths = [
        { wch: 6 },   // 序号
        { wch: 25 },  // 公司名称
        { wch: 20 },  // 税号
        { wch: 15 },  // 1月
        { wch: 15 },  // 2月
        { wch: 15 },  // 3月
        { wch: 15 },  // 一季度
        { wch: 15 },  // 4月
        { wch: 15 },  // 5月
        { wch: 15 },  // 6月
        { wch: 15 },  // 二季度
        { wch: 15 },  // 7月
        { wch: 15 },  // 8月
        { wch: 15 },  // 9月
        { wch: 15 },  // 三季度
        { wch: 15 },  // 10月
        { wch: 15 },  // 11月
        { wch: 15 },  // 12月
        { wch: 15 },  // 四季度
        { wch: 18 },  // 年度总计
      ];
      ws['!cols'] = colWidths;

      // 设置数字格式（千分位）
      const range = XLSX.utils.decode_range(ws['!ref']!);
      for (let R = range.s.r + 1; R <= range.e.r; ++R) {
        for (let C = 3; C <= range.e.c; ++C) { // 从第4列开始（金额列）
          const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });
          if (ws[cellAddress] && typeof ws[cellAddress].v === 'number') {
            ws[cellAddress].z = '#,##0.00';
          }
        }
      }

      // 设置标题行样式
      for (let C = range.s.c; C <= range.e.c; ++C) {
        const cellAddress = XLSX.utils.encode_cell({ r: 0, c: C });
        if (ws[cellAddress]) {
          ws[cellAddress].s = {
            fill: { fgColor: { rgb: "4F81BD" } },
            font: { color: { rgb: "FFFFFF" }, bold: true },
            alignment: { horizontal: "center", vertical: "center" }
          };
        }
      }

      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, `${selectedYear}年季度开票汇总`);
      XLSX.writeFile(wb, `${selectedYear}年季度开票汇总.xlsx`);
      message.success('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败');
    }
  };

  // 获取公司开票汇总数据
  const fetchCompanyData = async () => {
    setCompanyLoading(true);
    try {
      const params = new URLSearchParams();
      if (selectedYears.length > 0) {
        selectedYears.forEach(year => {
          params.append('years', year.toString());
        });
      }
      if (companySelectedCompanies.length > 0) {
        companySelectedCompanies.forEach(companyId => {
          params.append('companyIds', companyId);
        });
      }

      const response = await api.get(`/api/invoices/stats/company-summary?${params}`);
      const companies = response.data.data.companies || [];

      // 如果只选择了一个公司，按年度分组显示月份和季度汇总
      if (companySelectedCompanies.length === 1) {
        const groupedData = companies.map((company: any) => ({
          ...company,
          // 计算季度汇总
          q1: company.monthly.jan + company.monthly.feb + company.monthly.mar,
          q2: company.monthly.apr + company.monthly.may + company.monthly.jun,
          q3: company.monthly.jul + company.monthly.aug + company.monthly.sep,
          q4: company.monthly.oct + company.monthly.nov + company.monthly.dec,
          yearTotal: Object.values(company.monthly).reduce((sum: number, val: any) => sum + val, 0)
        }));
        setCompanyData(groupedData);
      } else {
        setCompanyData(companies);
      }
    } catch (error) {
      console.error('获取公司开票汇总数据失败:', error);
      message.error('获取公司开票汇总数据失败');
      setCompanyData([]);
    } finally {
      setCompanyLoading(false);
    }
  };

  // 获取发票张数汇总数据
  const fetchInvoiceCountData = async () => {
    setInvoiceCountLoading(true);
    try {
      const params = new URLSearchParams();
      if (invoiceCountSelectedYears.length > 0) {
        params.append('years', invoiceCountSelectedYears.join(','));
      }
      if (invoiceCountSelectedCompanies.length > 0) {
        params.append('companyIds', invoiceCountSelectedCompanies.join(','));
      }

      const response = await api.get(`/api/invoices/stats/invoice-count-summary?${params}`);

      const responseData = response.data.data || {};
      const companies = responseData.companies || [];
      const summary = responseData.summary || {};
      const availableYears = responseData.availableYears || [];

      setInvoiceCountData(companies);
      setInvoiceCountAvailableYears(availableYears);
    } catch (error) {
      console.error('获取发票张数汇总数据失败:', error);
      message.error('获取发票张数汇总数据失败');
      setInvoiceCountData([]);
    } finally {
      setInvoiceCountLoading(false);
    }
  };

  // 处理发票张数汇总查询
  const handleInvoiceCountQuery = () => {
    fetchInvoiceCountData();
  };



  // 用户开票汇总专用的发票详情状态
  const [userSummaryInvoiceDetailVisible, setUserSummaryInvoiceDetailVisible] = useState(false);
  const [userSummarySelectedInvoice, setUserSummarySelectedInvoice] = useState<any>(null);
  const [userSummarySelectedReceivedInvoice, setUserSummarySelectedReceivedInvoice] = useState<any>(null);

  // 查看开具发票详情（用户开票汇总专用）
  const handleViewInvoice = async (record: any) => {
    try {
      const response = await api.get(`/api/invoices/${record.id}`);
      setUserSummarySelectedInvoice(response.data.data);
      setUserSummarySelectedReceivedInvoice(null); // 清空取得发票数据
      setUserSummaryInvoiceDetailVisible(true);
    } catch (error) {
      console.error('获取发票详情失败:', error);
      message.error('获取发票详情失败');
    }
  };

  // 查看取得发票详情（用户开票汇总专用）
  const handleViewReceivedInvoice = async (record: any) => {
    try {
      const response = await api.get(`/api/received-invoices/${record.id}`);
      setUserSummarySelectedReceivedInvoice(response.data.data);
      setUserSummarySelectedInvoice(null); // 清空开具发票数据
      setUserSummaryInvoiceDetailVisible(true);
    } catch (error: any) {
      message.error(error.response?.data?.message || '获取取得发票详情失败');
    }
  };



  // 获取用户开票汇总数据
  const fetchUserInvoiceSummaryData = async () => {
    setUserInvoiceSummaryLoading(true);
    try {
      const params = new URLSearchParams();
      if (userInvoiceSummarySelectedUsers.length > 0) {
        params.append('userIds', userInvoiceSummarySelectedUsers.join(','));
      }

      const response = await api.get(`/api/invoices/stats/user-invoice-summary?${params}`);

      if (response.data.success) {
        const { userSummary, allUsers } = response.data.data;
        setUserInvoiceSummaryData(userSummary);
        setUserInvoiceSummaryAllUsers(allUsers);
        // 移除自动提示，避免页面加载时重复显示
        // message.success(`获取用户开票汇总成功，共 ${userSummary.length} 条记录`);
      } else {
        message.error(response.data.message || '获取用户开票汇总失败');
      }
    } catch (error) {
      console.error('获取用户开票汇总失败:', error);
      message.error('获取用户开票汇总失败');
    } finally {
      setUserInvoiceSummaryLoading(false);
    }
  };

  // 获取用户开票详细信息
  const fetchUserInvoiceSummaryDetail = async (ownerUserId: string, otherUserId: string) => {
    try {
      const params = new URLSearchParams();
      params.append('ownerUserId', ownerUserId);
      params.append('otherUserId', otherUserId);

      const response = await api.get(`/api/invoices/stats/user-invoice-detail?${params}`);

      if (response.data.success) {
        setUserInvoiceSummaryDetailData(response.data.data);
        setUserInvoiceSummaryDetailVisible(true);
      } else {
        message.error(response.data.message || '获取详细信息失败');
      }
    } catch (error) {
      console.error('获取用户开票详细信息失败:', error);
      message.error('获取详细信息失败');
    }
  };

  // 获取用户开票汇总分组信息
  const fetchUserInvoiceSummaryGroupDetail = async (ownerUserId: string, otherUserId: string) => {
    try {
      const params = new URLSearchParams();
      params.append('ownerUserId', ownerUserId);
      params.append('otherUserId', otherUserId);

      const response = await api.get(`/api/invoices/stats/user-invoice-group?${params}`);

      if (response.data.success) {
        setUserInvoiceSummaryGroupData(response.data.data);
        setUserInvoiceSummaryGroupVisible(true);
      } else {
        message.error(response.data.message || '获取汇总信息失败');
      }
    } catch (error) {
      console.error('获取用户开票汇总分组信息失败:', error);
      message.error('获取汇总信息失败');
    }
  };

  // 获取用户开票汇总分组发票列表
  const fetchUserInvoiceSummaryGroupList = async (ownerUserId: string, otherUserId: string, buyerName: string, sellerName: string, invoiceType: string) => {
    try {
      const params = new URLSearchParams();
      params.append('ownerUserId', ownerUserId);
      params.append('otherUserId', otherUserId);
      params.append('buyerName', buyerName);
      params.append('sellerName', sellerName);
      params.append('invoiceType', invoiceType);

      const response = await api.get(`/api/invoices/stats/user-invoice-group-list?${params}`);

      if (response.data.success) {
        setUserInvoiceSummaryGroupListData(response.data.data);
        setUserInvoiceSummaryGroupListTitle(`${buyerName} → ${sellerName} (${invoiceType === 'issued' ? '开具发票' : '取得发票'})`);
        setUserInvoiceSummaryGroupListVisible(true);
      } else {
        message.error(response.data.message || '获取发票列表失败');
      }
    } catch (error) {
      console.error('获取用户开票汇总分组发票列表失败:', error);
      message.error('获取发票列表失败');
    }
  };

  // 获取所属用户开票汇总数据
  const fetchUserSummaryData = async () => {
    setUserSummaryLoading(true);
    try {
      const params = new URLSearchParams();
      if (userSummarySelectedYears.length > 0) {
        params.append('years', userSummarySelectedYears.join(','));
      }

      const response = await api.get(`/api/invoices/stats/user-summary?${params}`);
      const users = response.data.data?.users || [];

      setUserSummaryData(users);
    } catch (error) {
      console.error('获取所属用户开票汇总数据失败:', error);
      message.error('获取所属用户开票汇总数据失败');
      setUserSummaryData([]);
    } finally {
      setUserSummaryLoading(false);
    }
  };

  // 处理所属用户开票汇总查询
  const handleUserSummaryQuery = () => {
    fetchUserSummaryData();
  };

  // 查看用户开票明细
  const handleViewUserInvoiceDetail = async (userSummary: any) => {
    try {
      const params = new URLSearchParams();
      params.append('organization', userSummary.organization);
      if (userSummarySelectedYears.length > 0) {
        params.append('years', userSummarySelectedYears.join(','));
      }

      const response = await api.get(`/api/invoices/stats/user-invoice-detail?${params}`);
      const invoices = response.data.data?.invoices || [];

      setSelectedUserSummary(userSummary);
      setUserSummaryDetailData(invoices);
      setUserSummaryDetailVisible(true);
    } catch (error) {
      console.error('获取用户开票明细失败:', error);
      message.error('获取用户开票明细失败');
    }
  };

  // 处理公司开票汇总查询
  const handleCompanyQuery = () => {
    if (companySelectedCompanies.length === 0) {
      message.warning('请先选择公司');
      return;
    }
    fetchCompanyData();
  };

  // 导出公司开票汇总
  const handleExportCompany = async () => {
    if (!companyData || companyData.length === 0) {
      message.warning('暂无数据可导出');
      return;
    }

    try {
      // 准备导出数据 - 显示原始数据，不进行格式化，不包含税号
      const exportData = companyData.map((company, index) => ({
        '序号': index + 1,
        '公司名称': company.name,
        '年度': company.year,
        '1月': company.monthly.jan || 0,
        '2月': company.monthly.feb || 0,
        '3月': company.monthly.mar || 0,
        '一季度': (company.monthly.jan || 0) + (company.monthly.feb || 0) + (company.monthly.mar || 0),
        '4月': company.monthly.apr || 0,
        '5月': company.monthly.may || 0,
        '6月': company.monthly.jun || 0,
        '二季度': (company.monthly.apr || 0) + (company.monthly.may || 0) + (company.monthly.jun || 0),
        '7月': company.monthly.jul || 0,
        '8月': company.monthly.aug || 0,
        '9月': company.monthly.sep || 0,
        '三季度': (company.monthly.jul || 0) + (company.monthly.aug || 0) + (company.monthly.sep || 0),
        '10月': company.monthly.oct || 0,
        '11月': company.monthly.nov || 0,
        '12月': company.monthly.dec || 0,
        '四季度': (company.monthly.oct || 0) + (company.monthly.nov || 0) + (company.monthly.dec || 0),
        '年度总计': Object.values(company.monthly).reduce((sum: number, val: any) => sum + (val || 0), 0)
      }));

      // 使用xlsx导出
      const XLSX = await import('xlsx');
      const ws = XLSX.utils.json_to_sheet(exportData);

      // 设置列宽
      const colWidths = [
        { wch: 6 },   // 序号
        { wch: 25 },  // 公司名称
        { wch: 8 },   // 年度
        { wch: 15 },  // 1月
        { wch: 15 },  // 2月
        { wch: 15 },  // 3月
        { wch: 15 },  // 一季度
        { wch: 15 },  // 4月
        { wch: 15 },  // 5月
        { wch: 15 },  // 6月
        { wch: 15 },  // 二季度
        { wch: 15 },  // 7月
        { wch: 15 },  // 8月
        { wch: 15 },  // 9月
        { wch: 15 },  // 三季度
        { wch: 15 },  // 10月
        { wch: 15 },  // 11月
        { wch: 15 },  // 12月
        { wch: 15 },  // 四季度
        { wch: 18 },  // 年度总计
      ];
      ws['!cols'] = colWidths;

      // 设置数字格式（千分位）
      const range = XLSX.utils.decode_range(ws['!ref']!);
      for (let R = range.s.r + 1; R <= range.e.r; ++R) {
        for (let C = 3; C <= range.e.c; ++C) { // 从第4列开始（金额列）
          const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });
          if (ws[cellAddress] && typeof ws[cellAddress].v === 'number') {
            ws[cellAddress].z = '#,##0.00';
          }
        }
      }

      // 设置标题行样式
      for (let C = range.s.c; C <= range.e.c; ++C) {
        const cellAddress = XLSX.utils.encode_cell({ r: 0, c: C });
        if (ws[cellAddress]) {
          ws[cellAddress].s = {
            fill: { fgColor: { rgb: "4F81BD" } },
            font: { color: { rgb: "FFFFFF" }, bold: true },
            alignment: { horizontal: "center", vertical: "center" }
          };
        }
      }

      const wb = XLSX.utils.book_new();
      const yearText = selectedYears.length > 0 ? selectedYears.join(',') : '全部';
      XLSX.utils.book_append_sheet(wb, ws, `公司开票汇总`);
      XLSX.writeFile(wb, `${yearText}年公司开票汇总.xlsx`);
      message.success('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败');
    }
  };

  useEffect(() => {
    // 获取公司列表
    const fetchCompanies = async () => {
      try {
        const response = await api.get('/api/companies/active');
        const companies = response.data.data || [];
        setAllCompanies(Array.isArray(companies) ? companies : []);
      } catch (error) {
        console.error('获取公司列表失败:', error);
        setAllCompanies([]); // 确保在错误时设置为空数组
      }
    };
    fetchCompanies();
  }, []);

  useEffect(() => {
    // 只在路径变化时初始化数据，避免频繁调用
    if (location.pathname.includes('quarterly-summary')) {
      // 季度汇总页面只在有年度选择时才获取数据
      if (selectedYear) {
        fetchQuarterlyData();
      }
    } else if (location.pathname.includes('company-summary')) {
      // 公司开票汇总页面不自动获取数据，需要用户手动点击查询
      // fetchCompanyData();
    } else if (location.pathname.includes('invoice-count-summary')) {
      // 发票张数汇总页面自动查询（使用默认的当年和所有公司）
      fetchInvoiceCountData();
    } else if (location.pathname.includes('user-summary')) {
      // 用户开票汇总页面自动查询
      fetchUserInvoiceSummaryData();
    }
  }, [location.pathname]);

  // 单独处理季度汇总的年度和公司变化
  useEffect(() => {
    if (location.pathname.includes('quarterly-summary') && selectedYear) {
      fetchQuarterlyData();
    }
  }, [selectedYear, quarterlySelectedCompanies]);

  // 单独处理公司开票汇总的年度和公司变化 - 不自动查询，需要用户手动点击查询按钮
  // useEffect(() => {
  //   if (location.pathname.includes('company-summary')) {
  //     fetchCompanyData();
  //   }
  // }, [selectedYears, selectedCompanies]);

  const getReportTitle = () => {
    const path = location.pathname;
    if (path.includes('quarterly-summary')) return '季度开票汇总';
    if (path.includes('company-summary')) return '公司开票汇总';
    if (path.includes('invoice-count-summary')) return '发票张数汇总';
    if (path.includes('user-summary')) return '用户开票汇总';
    if (path.includes('relations')) return '开票关系';
    return '报表中心';
  };

  // 开票关系穿透图状态
  const [penetrationData, setPenetrationData] = useState<any>({ chains: [], nodes: [], edges: [], stats: {} });
  const [penetrationLoading, setPenetrationLoading] = useState(false);
  const [penetrationFilters, setPenetrationFilters] = useState({
    companyName: '',
    startDate: '',
    endDate: '',
    maxDepth: 5,
  });
  const [penetrationPagination, setPenetrationPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 获取开票关系穿透图数据
  const fetchPenetrationData = async () => {
    setPenetrationLoading(true);
    try {
      const params = new URLSearchParams();
      if (penetrationFilters.companyName) params.append('companyName', penetrationFilters.companyName);
      if (penetrationFilters.startDate) params.append('startDate', penetrationFilters.startDate);
      if (penetrationFilters.endDate) params.append('endDate', penetrationFilters.endDate);
      params.append('maxDepth', penetrationFilters.maxDepth.toString());

      const response = await api.get(`/api/invoice-relations/penetration?${params}`);
      const data = response.data.data || { chains: [], nodes: [], edges: [], stats: {} };
      setPenetrationData(data);

      // 更新分页信息
      setPenetrationPagination(prev => ({
        ...prev,
        total: data.chains?.length || 0
      }));
    } catch (error) {
      console.error('获取开票关系穿透图失败:', error);
      message.error('获取开票关系穿透图失败');
      setPenetrationData({ chains: [], nodes: [], edges: [], stats: {} });
    } finally {
      setPenetrationLoading(false);
    }
  };

  const getReportContent = () => {
    const path = location.pathname;

    if (path.includes('quarterly-summary')) {
      // 这些状态已经在组件顶层定义了，不需要重复定义

      // 使用真实的季度数据，如果没有数据则显示空表格
      const companyData = quarterlyData;

      const columns = [
        {
          title: '序号',
          dataIndex: 'id',
          key: 'id',
          width: 60,
          align: 'center' as const,
          fixed: 'left' as const,
        },
        {
          title: '公司名称',
          dataIndex: 'name',
          key: 'name',
          width: 250,
          fixed: 'left' as const,
        },
        {
          title: '1月',
          dataIndex: ['monthly', 'jan'],
          key: 'jan',
          width: 100,
          align: 'right' as const,
          render: formatAmount,
        },
        {
          title: '2月',
          dataIndex: ['monthly', 'feb'],
          key: 'feb',
          width: 100,
          align: 'right' as const,
          render: formatAmount,
        },
        {
          title: '3月',
          dataIndex: ['monthly', 'mar'],
          key: 'mar',
          width: 100,
          align: 'right' as const,
          render: formatAmount,
        },
        {
          title: '一季度',
          key: 'q1',
          width: 120,
          align: 'right' as const,
          className: 'quarterly-column',
          onHeaderCell: () => ({
            style: { backgroundColor: '#bae7ff', fontWeight: 'bold' }
          }),
          onCell: (record: any) => {
            const total = record.monthly.jan + record.monthly.feb + record.monthly.mar;
            let backgroundColor = '#e6f7ff';
            if (total >= 250000 && total < 300000) {
              backgroundColor = 'rgba(255, 165, 0, 0.8)'; // 80%透明橘色
            } else if (total >= 300000) {
              backgroundColor = 'rgba(255, 0, 0, 0.7)'; // 70%透明红色
            }
            return { style: { backgroundColor } };
          },
          render: (record: any) => {
            const total = record.monthly.jan + record.monthly.feb + record.monthly.mar;
            return <span style={{ fontWeight: 'bold' }}>{formatAmount(total)}</span>;
          },
        },
        {
          title: '4月',
          dataIndex: ['monthly', 'apr'],
          key: 'apr',
          width: 100,
          align: 'right' as const,
          render: formatAmount,
        },
        {
          title: '5月',
          dataIndex: ['monthly', 'may'],
          key: 'may',
          width: 100,
          align: 'right' as const,
          render: formatAmount,
        },
        {
          title: '6月',
          dataIndex: ['monthly', 'jun'],
          key: 'jun',
          width: 100,
          align: 'right' as const,
          render: formatAmount,
        },
        {
          title: '二季度',
          key: 'q2',
          width: 120,
          align: 'right' as const,
          onHeaderCell: () => ({
            style: { backgroundColor: '#bae7ff', fontWeight: 'bold' }
          }),
          onCell: (record: any) => {
            const total = record.monthly.apr + record.monthly.may + record.monthly.jun;
            let backgroundColor = '#e6f7ff';
            if (total >= 250000 && total < 300000) {
              backgroundColor = 'rgba(255, 165, 0, 0.8)'; // 80%透明橘色
            } else if (total >= 300000) {
              backgroundColor = 'rgba(255, 0, 0, 0.7)'; // 70%透明红色
            }
            return { style: { backgroundColor } };
          },
          render: (record: any) => {
            const total = record.monthly.apr + record.monthly.may + record.monthly.jun;
            return <span style={{ fontWeight: 'bold' }}>{formatAmount(total)}</span>;
          },
        },
        {
          title: '7月',
          dataIndex: ['monthly', 'jul'],
          key: 'jul',
          width: 100,
          align: 'right' as const,
          render: formatAmount,
        },
        {
          title: '8月',
          dataIndex: ['monthly', 'aug'],
          key: 'aug',
          width: 100,
          align: 'right' as const,
          render: formatAmount,
        },
        {
          title: '9月',
          dataIndex: ['monthly', 'sep'],
          key: 'sep',
          width: 100,
          align: 'right' as const,
          render: formatAmount,
        },
        {
          title: '三季度',
          key: 'q3',
          width: 120,
          align: 'right' as const,
          onHeaderCell: () => ({
            style: { backgroundColor: '#bae7ff', fontWeight: 'bold' }
          }),
          onCell: (record: any) => {
            const total = record.monthly.jul + record.monthly.aug + record.monthly.sep;
            let backgroundColor = '#e6f7ff';
            if (total >= 250000 && total < 300000) {
              backgroundColor = 'rgba(255, 165, 0, 0.8)'; // 80%透明橘色
            } else if (total >= 300000) {
              backgroundColor = 'rgba(255, 0, 0, 0.7)'; // 70%透明红色
            }
            return { style: { backgroundColor } };
          },
          render: (record: any) => {
            const total = record.monthly.jul + record.monthly.aug + record.monthly.sep;
            return <span style={{ fontWeight: 'bold' }}>{formatAmount(total)}</span>;
          },
        },
        {
          title: '10月',
          dataIndex: ['monthly', 'oct'],
          key: 'oct',
          width: 100,
          align: 'right' as const,
          render: formatAmount,
        },
        {
          title: '11月',
          dataIndex: ['monthly', 'nov'],
          key: 'nov',
          width: 100,
          align: 'right' as const,
          render: formatAmount,
        },
        {
          title: '12月',
          dataIndex: ['monthly', 'dec'],
          key: 'dec',
          width: 100,
          align: 'right' as const,
          render: formatAmount,
        },
        {
          title: '四季度',
          key: 'q4',
          width: 120,
          align: 'right' as const,
          onHeaderCell: () => ({
            style: { backgroundColor: '#bae7ff', fontWeight: 'bold' }
          }),
          onCell: (record: any) => {
            const total = record.monthly.oct + record.monthly.nov + record.monthly.dec;
            let backgroundColor = '#e6f7ff';
            if (total >= 250000 && total < 300000) {
              backgroundColor = 'rgba(255, 165, 0, 0.8)'; // 80%透明橘色
            } else if (total >= 300000) {
              backgroundColor = 'rgba(255, 0, 0, 0.7)'; // 70%透明红色
            }
            return { style: { backgroundColor } };
          },
          render: (record: any) => {
            const total = record.monthly.oct + record.monthly.nov + record.monthly.dec;
            return <span style={{ fontWeight: 'bold' }}>{formatAmount(total)}</span>;
          },
        },
        {
          title: '年度总计',
          key: 'total',
          width: 120,
          align: 'right' as const,
          fixed: 'right' as const,
          onHeaderCell: () => ({
            style: { backgroundColor: '#52c41a', color: 'white', fontWeight: 'bold' }
          }),
          onCell: () => ({
            style: { backgroundColor: '#f6ffed' }
          }),
          render: (record: any) => {
            const total = Object.values(record.monthly).reduce((sum: number, val: any) => sum + (Number(val) || 0), 0) as number;
            return <span style={{ fontWeight: 'bold', color: '#52c41a', fontSize: '16px' }}>{formatAmount(total)}</span>;
          },
        },
      ];

      // 使用后端返回的数据，不需要前端过滤（后端已经根据companyIds参数过滤了）
      const filteredData = quarterlyData;

      return (
        <div>
          {/* 查询条件 */}
          <Card title="查询条件" size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16} align="bottom">
              <Col span={6}>
                <Form.Item label={<span><Text strong>年度</Text> <Text type="danger">*</Text></span>} style={{ marginBottom: 0 }}>
                  <Select
                    value={selectedYear}
                    onChange={setSelectedYear}
                    style={{ width: '100%' }}
                    placeholder="请选择年度"
                    size="small"
                  >
                    {Array.from({ length: new Date().getFullYear() - 2019 + 1 }, (_, i) => new Date().getFullYear() - i).filter(year => year >= 2019).map(year => (
                      <Select.Option key={year} value={year}>{year}年</Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={10}>
                <Form.Item label={<Text strong>公司</Text>} style={{ marginBottom: 0 }}>
                  <Select
                    mode="multiple"
                    value={quarterlySelectedCompanies}
                    onChange={setQuarterlySelectedCompanies}
                    style={{ width: '100%' }}
                    placeholder="请选择公司（不选则显示全部）"
                    allowClear
                    size="small"
                  >
                    {Array.isArray(allCompanies) && allCompanies.map(company => (
                      <Select.Option key={company.id} value={company.id}>{company.name}</Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Space>
                  <Button type="primary" icon={<SearchOutlined />} size="small" onClick={handleQuery} loading={quarterlyLoading}>
                    查询
                  </Button>
                  <Button icon={<DownloadOutlined />} size="small" onClick={handleExportQuarterly}>
                    导出
                  </Button>
                </Space>
              </Col>
            </Row>
          </Card>

          <Card style={{ marginBottom: 16 }}>
            <Table
              columns={columns}
              dataSource={filteredData}
              rowKey={(record) => record.id || record.name}
              pagination={false}
              scroll={{ x: 1800 }}
              size="small"
              bordered
              loading={quarterlyLoading}
              locale={{ emptyText: '暂无数据，请先在开票管理中添加发票数据' }}
              summary={() => {
                // 如果没有数据，不显示合计行
                if (!filteredData || filteredData.length === 0) {
                  return null;
                }

                // 计算合计行（基于过滤后的数据）
                const totals = {
                  jan: 0, feb: 0, mar: 0, apr: 0, may: 0, jun: 0,
                  jul: 0, aug: 0, sep: 0, oct: 0, nov: 0, dec: 0
                };

                filteredData.forEach(company => {
                  if (company.monthly) {
                    Object.keys(totals).forEach(month => {
                      totals[month as keyof typeof totals] += company.monthly[month as keyof typeof company.monthly] || 0;
                    });
                  }
                });

                const q1Total = totals.jan + totals.feb + totals.mar;
                const q2Total = totals.apr + totals.may + totals.jun;
                const q3Total = totals.jul + totals.aug + totals.sep;
                const q4Total = totals.oct + totals.nov + totals.dec;
                const yearTotal = q1Total + q2Total + q3Total + q4Total;

                return (
                  <Table.Summary.Row style={{ backgroundColor: '#ffe7ba', fontWeight: 'bold' }}>
                    <Table.Summary.Cell index={0} colSpan={2} align="center">
                      <span style={{ fontSize: '16px', color: '#d46b08', fontWeight: 'bold' }}>合计</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={2} align="right">
                      <span style={{ color: '#d46b08' }}>{formatAmount(totals.jan)}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={3} align="right">
                      <span style={{ color: '#d46b08' }}>{formatAmount(totals.feb)}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={4} align="right">
                      <span style={{ color: '#d46b08' }}>{formatAmount(totals.mar)}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={5} align="right">
                      <span style={{ backgroundColor: '#bae7ff', color: '#1890ff', fontWeight: 'bold', padding: '4px' }}>{formatAmount(q1Total)}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={6} align="right">
                      <span style={{ color: '#d46b08' }}>{formatAmount(totals.apr)}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={7} align="right">
                      <span style={{ color: '#d46b08' }}>{formatAmount(totals.may)}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={8} align="right">
                      <span style={{ color: '#d46b08' }}>{formatAmount(totals.jun)}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={9} align="right">
                      <span style={{ backgroundColor: '#bae7ff', color: '#1890ff', fontWeight: 'bold', padding: '4px' }}>{formatAmount(q2Total)}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={10} align="right">
                      <span style={{ color: '#d46b08' }}>{formatAmount(totals.jul)}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={11} align="right">
                      <span style={{ color: '#d46b08' }}>{formatAmount(totals.aug)}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={12} align="right">
                      <span style={{ color: '#d46b08' }}>{formatAmount(totals.sep)}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={13} align="right">
                      <span style={{ backgroundColor: '#bae7ff', color: '#1890ff', fontWeight: 'bold', padding: '4px' }}>{formatAmount(q3Total)}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={14} align="right">
                      <span style={{ color: '#d46b08' }}>{formatAmount(totals.oct)}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={15} align="right">
                      <span style={{ color: '#d46b08' }}>{formatAmount(totals.nov)}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={16} align="right">
                      <span style={{ color: '#d46b08' }}>{formatAmount(totals.dec)}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={17} align="right">
                      <span style={{ backgroundColor: '#bae7ff', color: '#1890ff', fontWeight: 'bold', padding: '4px' }}>{formatAmount(q4Total)}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={18} align="right">
                      <span style={{ fontWeight: 'bold', color: '#52c41a', fontSize: '16px', backgroundColor: '#f6ffed', padding: '4px' }}>{formatAmount(yearTotal)}</span>
                    </Table.Summary.Cell>
                  </Table.Summary.Row>
                );
              }}
            />
          </Card>
        </div>
      );
    }

    if (path.includes('invoice-count-summary')) {
      // 发票张数汇总表格列定义 - 完全按照公司开票汇总样式
      const invoiceCountColumns = [
        {
          title: '序号',
          key: 'index',
          width: 60,
          align: 'center' as const,
          render: (_: any, __: any, index: number) => index + 1,
        },
        {
          title: '公司名称',
          dataIndex: 'name',
          key: 'name',
          width: 200,
          fixed: 'left' as const,
          onCell: (record: any, index?: number) => {
            // 计算相同公司名称的行数，用于合并单元格
            if (!invoiceCountData || invoiceCountData.length === 0 || index === undefined) return {};

            const currentName = record.name;
            let rowSpan = 0;
            let shouldShow = true;

            // 检查当前行是否是该公司名称的第一行
            for (let i = 0; i < index; i++) {
              if (invoiceCountData[i].name === currentName) {
                shouldShow = false;
                break;
              }
            }

            // 如果是第一行，计算需要合并的行数
            if (shouldShow) {
              for (let i = index; i < invoiceCountData.length; i++) {
                if (invoiceCountData[i].name === currentName) {
                  rowSpan++;
                } else {
                  break;
                }
              }
            }

            return {
              rowSpan: shouldShow ? rowSpan : 0,
            };
          },
        },

        {
          title: '年度',
          dataIndex: 'year',
          key: 'year',
          width: 80,
          align: 'center' as const,
        },
        { title: '1月', dataIndex: ['monthly', 'jan'], key: 'jan', width: 80, align: 'right' as const, render: (count: number) => count || 0 },
        { title: '2月', dataIndex: ['monthly', 'feb'], key: 'feb', width: 80, align: 'right' as const, render: (count: number) => count || 0 },
        { title: '3月', dataIndex: ['monthly', 'mar'], key: 'mar', width: 80, align: 'right' as const, render: (count: number) => count || 0 },
        {
          title: '一季度',
          key: 'q1',
          width: 100,
          align: 'right' as const,
          onHeaderCell: () => ({
            style: { backgroundColor: '#bae7ff', fontWeight: 'bold' }
          }),
          onCell: (record: any) => {
            const monthly = record.monthly || {};
            const total = (monthly.jan || 0) + (monthly.feb || 0) + (monthly.mar || 0);
            return {
              style: { backgroundColor: total === 0 ? '#FFFF00' : '#e6f7ff' } // 黄色背景表示0开票
            };
          },
          render: (record: any) => {
            const monthly = record.monthly || {};
            const total = (monthly.jan || 0) + (monthly.feb || 0) + (monthly.mar || 0);
            return <span style={{ fontWeight: 'bold' }}>{total}</span>;
          },
        },
        { title: '4月', dataIndex: ['monthly', 'apr'], key: 'apr', width: 80, align: 'right' as const, render: (count: number) => count || 0 },
        { title: '5月', dataIndex: ['monthly', 'may'], key: 'may', width: 80, align: 'right' as const, render: (count: number) => count || 0 },
        { title: '6月', dataIndex: ['monthly', 'jun'], key: 'jun', width: 80, align: 'right' as const, render: (count: number) => count || 0 },
        {
          title: '二季度',
          key: 'q2',
          width: 100,
          align: 'right' as const,
          onHeaderCell: () => ({
            style: { backgroundColor: '#bae7ff', fontWeight: 'bold' }
          }),
          onCell: (record: any) => {
            const monthly = record.monthly || {};
            const total = (monthly.apr || 0) + (monthly.may || 0) + (monthly.jun || 0);
            return {
              style: { backgroundColor: total === 0 ? '#FFFF00' : '#e6f7ff' } // 黄色背景表示0开票
            };
          },
          render: (record: any) => {
            const monthly = record.monthly || {};
            const total = (monthly.apr || 0) + (monthly.may || 0) + (monthly.jun || 0);
            return <span style={{ fontWeight: 'bold' }}>{total}</span>;
          },
        },
        { title: '7月', dataIndex: ['monthly', 'jul'], key: 'jul', width: 80, align: 'right' as const, render: (count: number) => count || 0 },
        { title: '8月', dataIndex: ['monthly', 'aug'], key: 'aug', width: 80, align: 'right' as const, render: (count: number) => count || 0 },
        { title: '9月', dataIndex: ['monthly', 'sep'], key: 'sep', width: 80, align: 'right' as const, render: (count: number) => count || 0 },
        {
          title: '三季度',
          key: 'q3',
          width: 100,
          align: 'right' as const,
          onHeaderCell: () => ({
            style: { backgroundColor: '#bae7ff', fontWeight: 'bold' }
          }),
          onCell: (record: any) => {
            const monthly = record.monthly || {};
            const total = (monthly.jul || 0) + (monthly.aug || 0) + (monthly.sep || 0);
            return {
              style: { backgroundColor: total === 0 ? '#FFFF00' : '#e6f7ff' } // 黄色背景表示0开票
            };
          },
          render: (record: any) => {
            const monthly = record.monthly || {};
            const total = (monthly.jul || 0) + (monthly.aug || 0) + (monthly.sep || 0);
            return <span style={{ fontWeight: 'bold' }}>{total}</span>;
          },
        },
        { title: '10月', dataIndex: ['monthly', 'oct'], key: 'oct', width: 80, align: 'right' as const, render: (count: number) => count || 0 },
        { title: '11月', dataIndex: ['monthly', 'nov'], key: 'nov', width: 80, align: 'right' as const, render: (count: number) => count || 0 },
        { title: '12月', dataIndex: ['monthly', 'dec'], key: 'dec', width: 80, align: 'right' as const, render: (count: number) => count || 0 },
        {
          title: '四季度',
          key: 'q4',
          width: 100,
          align: 'right' as const,
          onHeaderCell: () => ({
            style: { backgroundColor: '#bae7ff', fontWeight: 'bold' }
          }),
          onCell: (record: any) => {
            const monthly = record.monthly || {};
            const total = (monthly.oct || 0) + (monthly.nov || 0) + (monthly.dec || 0);
            return {
              style: { backgroundColor: total === 0 ? '#FFFF00' : '#e6f7ff' } // 黄色背景表示0开票
            };
          },
          render: (record: any) => {
            const monthly = record.monthly || {};
            const total = (monthly.oct || 0) + (monthly.nov || 0) + (monthly.dec || 0);
            return <span style={{ fontWeight: 'bold' }}>{total}</span>;
          },
        },
        {
          title: '年度总计',
          key: 'yearTotal',
          width: 120,
          align: 'right' as const,
          fixed: 'right' as const,
          onHeaderCell: () => ({
            style: { backgroundColor: '#f6ffed', fontWeight: 'bold' }
          }),
          onCell: () => ({
            style: { backgroundColor: '#f6ffed' }
          }),
          render: (record: any) => {
            const monthly = record.monthly || {};
            const total = Object.values(monthly).reduce((sum: number, val: any) => sum + (Number(val) || 0), 0) as number;
            return <span style={{ fontWeight: 'bold', color: '#52c41a', fontSize: '16px' }}>{total}</span>;
          },
        },
      ];

      return (
        <div>
          {/* 查询条件 */}
          <Card title="查询条件" size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16} align="bottom">
              <Col span={6}>
                <Form.Item label={<Text strong>年度</Text>} style={{ marginBottom: 0 }}>
                  <Select
                    mode="multiple"
                    value={invoiceCountSelectedYears}
                    onChange={setInvoiceCountSelectedYears}
                    style={{ width: '100%' }}
                    placeholder="请选择年度（不选则查询所有年度）"
                    allowClear
                    size="small"
                  >
                    {(invoiceCountAvailableYears.length > 0 ? invoiceCountAvailableYears :
                      Array.from({ length: new Date().getFullYear() - 2019 + 1 }, (_, i) => new Date().getFullYear() - i).filter(year => year >= 2019)
                    ).map(year => (
                      <Select.Option key={year} value={year}>{year}年</Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={10}>
                <Form.Item label={<Text strong>公司</Text>} style={{ marginBottom: 0 }}>
                  <Select
                    mode="multiple"
                    value={invoiceCountSelectedCompanies}
                    onChange={(value) => setInvoiceCountSelectedCompanies(value)}
                    style={{ width: '100%' }}
                    placeholder="请选择公司（不选则查询所有公司）"
                    size="small"
                    maxTagCount="responsive"
                  >
                    {Array.isArray(allCompanies) && allCompanies.map(company => (
                      <Select.Option key={company.id} value={company.id}>{company.name}</Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Space>
                  <Button type="primary" icon={<SearchOutlined />} size="small" onClick={handleInvoiceCountQuery} loading={invoiceCountLoading}>
                    查询
                  </Button>
                  <Button icon={<DownloadOutlined />} size="small" onClick={() => {
                    // TODO: 导出功能
                    message.info('导出功能开发中...');
                  }}>
                    导出
                  </Button>
                </Space>
              </Col>
            </Row>
          </Card>

          <Card style={{ marginBottom: 16, height: 'calc(100vh - 280px)', display: 'flex', flexDirection: 'column' }}>
            <div style={{ flex: 1, overflow: 'hidden' }}>
              <Table
                columns={invoiceCountColumns}
                dataSource={invoiceCountData}
                rowKey={(record) => record.id || `${record.name || 'unknown'}_${record.year || 'unknown'}`}
                pagination={{
                  pageSize: 20,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) => `共 ${total} 条记录，第 ${range[0]}-${range[1]} 条`,
                  pageSizeOptions: ['10', '20', '50', '100'],
                  position: ['bottomCenter'],
                  style: { position: 'sticky', bottom: 0, backgroundColor: 'white', zIndex: 1, padding: '8px 0', borderTop: '1px solid #f0f0f0' }
                }}
                size="small"
                bordered
                loading={invoiceCountLoading}
                locale={{ emptyText: '暂无数据，请先在开票管理中添加发票数据' }}
                scroll={{ x: 1800, y: 'calc(100vh - 400px)' }}
              summary={() => {
                if (!invoiceCountData || invoiceCountData.length === 0) return null;

                // 计算汇总数据
                const totals = {
                  jan: 0, feb: 0, mar: 0, apr: 0, may: 0, jun: 0,
                  jul: 0, aug: 0, sep: 0, oct: 0, nov: 0, dec: 0
                };

                invoiceCountData.forEach(company => {
                  if (company.monthly) {
                    Object.keys(totals).forEach(month => {
                      totals[month as keyof typeof totals] += company.monthly[month as keyof typeof company.monthly] || 0;
                    });
                  }
                });

                const q1Total = totals.jan + totals.feb + totals.mar;
                const q2Total = totals.apr + totals.may + totals.jun;
                const q3Total = totals.jul + totals.aug + totals.sep;
                const q4Total = totals.oct + totals.nov + totals.dec;
                const yearTotal = q1Total + q2Total + q3Total + q4Total;

                return (
                  <Table.Summary.Row style={{ backgroundColor: '#ffe7ba', fontWeight: 'bold' }}>
                    <Table.Summary.Cell index={0} colSpan={3} align="center">
                      <span style={{ fontSize: '16px', color: '#d46b08', fontWeight: 'bold' }}>合计</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={3} align="right">
                      <span style={{ color: '#d46b08' }}>{totals.jan}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={4} align="right">
                      <span style={{ color: '#d46b08' }}>{totals.feb}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={5} align="right">
                      <span style={{ color: '#d46b08' }}>{totals.mar}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={6} align="right">
                      <span style={{ backgroundColor: '#bae7ff', color: '#1890ff', fontWeight: 'bold', padding: '4px' }}>{q1Total}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={7} align="right">
                      <span style={{ color: '#d46b08' }}>{totals.apr}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={8} align="right">
                      <span style={{ color: '#d46b08' }}>{totals.may}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={9} align="right">
                      <span style={{ color: '#d46b08' }}>{totals.jun}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={10} align="right">
                      <span style={{ backgroundColor: '#bae7ff', color: '#1890ff', fontWeight: 'bold', padding: '4px' }}>{q2Total}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={11} align="right">
                      <span style={{ color: '#d46b08' }}>{totals.jul}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={12} align="right">
                      <span style={{ color: '#d46b08' }}>{totals.aug}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={13} align="right">
                      <span style={{ color: '#d46b08' }}>{totals.sep}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={14} align="right">
                      <span style={{ backgroundColor: '#bae7ff', color: '#1890ff', fontWeight: 'bold', padding: '4px' }}>{q3Total}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={15} align="right">
                      <span style={{ color: '#d46b08' }}>{totals.oct}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={16} align="right">
                      <span style={{ color: '#d46b08' }}>{totals.nov}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={17} align="right">
                      <span style={{ color: '#d46b08' }}>{totals.dec}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={18} align="right">
                      <span style={{ backgroundColor: '#bae7ff', color: '#1890ff', fontWeight: 'bold', padding: '4px' }}>{q4Total}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={19} align="right">
                      <span style={{ fontWeight: 'bold', color: '#52c41a', fontSize: '16px', backgroundColor: '#f6ffed', padding: '4px' }}>{yearTotal}</span>
                    </Table.Summary.Cell>
                  </Table.Summary.Row>
                );
              }}
            />
            </div>
          </Card>
        </div>
      );
    }



    if (path.includes('company-summary')) {
      // 公司开票汇总表格列定义 - 和季度开票汇总相同，只是多了年度列
      const companyColumns = [
        {
          title: '序号',
          key: 'index',
          width: 60,
          align: 'center' as const,
          render: (_: any, __: any, index: number) => index + 1,
        },
        {
          title: '公司名称',
          dataIndex: 'name',
          key: 'name',
          width: 200,
          fixed: 'left' as const,
          onCell: (record: any, index?: number) => {
            // 计算相同公司名称的行数，用于合并单元格
            if (!companyData || companyData.length === 0 || index === undefined) return {};

            const currentName = record.name;
            let rowSpan = 0;
            let shouldShow = true;

            // 检查当前行是否是该公司名称的第一行
            for (let i = 0; i < index; i++) {
              if (companyData[i].name === currentName) {
                shouldShow = false;
                break;
              }
            }

            // 如果是第一行，计算需要合并的行数
            if (shouldShow) {
              for (let i = index; i < companyData.length; i++) {
                if (companyData[i].name === currentName) {
                  rowSpan++;
                } else {
                  break;
                }
              }
            }

            return {
              rowSpan: shouldShow ? rowSpan : 0,
            };
          },
        },
        {
          title: '年度',
          dataIndex: 'year',
          key: 'year',
          width: 80,
          align: 'center' as const,
        },
        { title: '1月', dataIndex: ['monthly', 'jan'], key: 'jan', width: 80, align: 'right' as const, render: (amount: number) => formatAmount(amount) },
        { title: '2月', dataIndex: ['monthly', 'feb'], key: 'feb', width: 80, align: 'right' as const, render: (amount: number) => formatAmount(amount) },
        { title: '3月', dataIndex: ['monthly', 'mar'], key: 'mar', width: 80, align: 'right' as const, render: (amount: number) => formatAmount(amount) },
        {
          title: '一季度',
          key: 'q1',
          width: 100,
          align: 'right' as const,
          onHeaderCell: () => ({
            style: { backgroundColor: '#bae7ff', fontWeight: 'bold' }
          }),
          onCell: (record: any) => {
            const monthly = record.monthly || {};
            const total = (monthly.jan || 0) + (monthly.feb || 0) + (monthly.mar || 0);
            let backgroundColor = '#e6f7ff';
            if (total >= 250000 && total < 300000) {
              backgroundColor = 'rgba(255, 165, 0, 0.8)';
            } else if (total >= 300000) {
              backgroundColor = 'rgba(255, 0, 0, 0.7)';
            }
            return { style: { backgroundColor } };
          },
          render: (record: any) => {
            const monthly = record.monthly || {};
            const total = (monthly.jan || 0) + (monthly.feb || 0) + (monthly.mar || 0);
            return <span style={{ fontWeight: 'bold' }}>{formatAmount(total)}</span>;
          },
        },
        { title: '4月', dataIndex: ['monthly', 'apr'], key: 'apr', width: 80, align: 'right' as const, render: (amount: number) => formatAmount(amount) },
        { title: '5月', dataIndex: ['monthly', 'may'], key: 'may', width: 80, align: 'right' as const, render: (amount: number) => formatAmount(amount) },
        { title: '6月', dataIndex: ['monthly', 'jun'], key: 'jun', width: 80, align: 'right' as const, render: (amount: number) => formatAmount(amount) },
        {
          title: '二季度',
          key: 'q2',
          width: 100,
          align: 'right' as const,
          onHeaderCell: () => ({
            style: { backgroundColor: '#bae7ff', fontWeight: 'bold' }
          }),
          onCell: (record: any) => {
            const monthly = record.monthly || {};
            const total = (monthly.apr || 0) + (monthly.may || 0) + (monthly.jun || 0);
            let backgroundColor = '#e6f7ff';
            if (total >= 250000 && total < 300000) {
              backgroundColor = 'rgba(255, 165, 0, 0.8)';
            } else if (total >= 300000) {
              backgroundColor = 'rgba(255, 0, 0, 0.7)';
            }
            return { style: { backgroundColor } };
          },
          render: (record: any) => {
            const monthly = record.monthly || {};
            const total = (monthly.apr || 0) + (monthly.may || 0) + (monthly.jun || 0);
            return <span style={{ fontWeight: 'bold' }}>{formatAmount(total)}</span>;
          },
        },
        { title: '7月', dataIndex: ['monthly', 'jul'], key: 'jul', width: 80, align: 'right' as const, render: (amount: number) => formatAmount(amount) },
        { title: '8月', dataIndex: ['monthly', 'aug'], key: 'aug', width: 80, align: 'right' as const, render: (amount: number) => formatAmount(amount) },
        { title: '9月', dataIndex: ['monthly', 'sep'], key: 'sep', width: 80, align: 'right' as const, render: (amount: number) => formatAmount(amount) },
        {
          title: '三季度',
          key: 'q3',
          width: 100,
          align: 'right' as const,
          onHeaderCell: () => ({
            style: { backgroundColor: '#bae7ff', fontWeight: 'bold' }
          }),
          onCell: (record: any) => {
            const monthly = record.monthly || {};
            const total = (monthly.jul || 0) + (monthly.aug || 0) + (monthly.sep || 0);
            let backgroundColor = '#e6f7ff';
            if (total >= 250000 && total < 300000) {
              backgroundColor = 'rgba(255, 165, 0, 0.8)';
            } else if (total >= 300000) {
              backgroundColor = 'rgba(255, 0, 0, 0.7)';
            }
            return { style: { backgroundColor } };
          },
          render: (record: any) => {
            const monthly = record.monthly || {};
            const total = (monthly.jul || 0) + (monthly.aug || 0) + (monthly.sep || 0);
            return <span style={{ fontWeight: 'bold' }}>{formatAmount(total)}</span>;
          },
        },
        { title: '10月', dataIndex: ['monthly', 'oct'], key: 'oct', width: 80, align: 'right' as const, render: (amount: number) => formatAmount(amount) },
        { title: '11月', dataIndex: ['monthly', 'nov'], key: 'nov', width: 80, align: 'right' as const, render: (amount: number) => formatAmount(amount) },
        { title: '12月', dataIndex: ['monthly', 'dec'], key: 'dec', width: 80, align: 'right' as const, render: (amount: number) => formatAmount(amount) },
        {
          title: '四季度',
          key: 'q4',
          width: 100,
          align: 'right' as const,
          onHeaderCell: () => ({
            style: { backgroundColor: '#bae7ff', fontWeight: 'bold' }
          }),
          onCell: (record: any) => {
            const monthly = record.monthly || {};
            const total = (monthly.oct || 0) + (monthly.nov || 0) + (monthly.dec || 0);
            let backgroundColor = '#e6f7ff';
            if (total >= 250000 && total < 300000) {
              backgroundColor = 'rgba(255, 165, 0, 0.8)';
            } else if (total >= 300000) {
              backgroundColor = 'rgba(255, 0, 0, 0.7)';
            }
            return { style: { backgroundColor } };
          },
          render: (record: any) => {
            const monthly = record.monthly || {};
            const total = (monthly.oct || 0) + (monthly.nov || 0) + (monthly.dec || 0);
            return <span style={{ fontWeight: 'bold' }}>{formatAmount(total)}</span>;
          },
        },
        {
          title: '年度总计',
          key: 'yearTotal',
          width: 120,
          align: 'right' as const,
          fixed: 'right' as const,
          onHeaderCell: () => ({
            style: { backgroundColor: '#f6ffed', fontWeight: 'bold' }
          }),
          onCell: () => ({
            style: { backgroundColor: '#f6ffed' }
          }),
          render: (record: any) => {
            const monthly = record.monthly || {};
            const total = Object.values(monthly).reduce((sum: number, val: any) => sum + (Number(val) || 0), 0) as number;
            return <span style={{ fontWeight: 'bold', color: '#52c41a', fontSize: '16px' }}>{formatAmount(total)}</span>;
          },
        },
      ];

      return (
        <div>
          {/* 查询条件 */}
          <Card title="查询条件" size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16} align="bottom">
              <Col span={6}>
                <Form.Item label={<Text strong>年度</Text>} style={{ marginBottom: 0 }}>
                  <Select
                    mode="multiple"
                    value={selectedYears}
                    onChange={setSelectedYears}
                    style={{ width: '100%' }}
                    placeholder="请选择年度（不选则显示全部）"
                    allowClear
                    size="small"
                  >
                    {Array.from({ length: new Date().getFullYear() - 2019 + 1 }, (_, i) => new Date().getFullYear() - i).filter(year => year >= 2019).map(year => (
                      <Select.Option key={year} value={year}>{year}年</Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={10}>
                <Form.Item label={<span><Text strong>公司</Text> <Text type="danger">*</Text></span>} style={{ marginBottom: 0 }}>
                  <Select
                    mode="multiple"
                    value={companySelectedCompanies}
                    onChange={(value) => setCompanySelectedCompanies(value)}
                    style={{ width: '100%' }}
                    placeholder="请选择公司（必选，支持多选）"
                    size="small"
                    maxTagCount="responsive"
                  >
                    {Array.isArray(allCompanies) && allCompanies.map(company => (
                      <Select.Option key={company.id} value={company.id}>{company.name}</Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Space>
                  <Button type="primary" icon={<SearchOutlined />} size="small" onClick={handleCompanyQuery} loading={companyLoading}>
                    查询
                  </Button>
                  <Button icon={<DownloadOutlined />} size="small" onClick={handleExportCompany}>
                    导出
                  </Button>
                </Space>
              </Col>
            </Row>
          </Card>

          <Card style={{ marginBottom: 16, height: 'calc(100vh - 280px)', display: 'flex', flexDirection: 'column' }}>
            <div style={{ flex: 1, overflow: 'hidden' }}>
              <Table
                columns={companyColumns}
                dataSource={companyData}
                rowKey={(record) => `${record.name}_${record.year}`}
                pagination={{
                  pageSize: 20,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) => `共 ${total} 条记录，第 ${range[0]}-${range[1]} 条`,
                  pageSizeOptions: ['10', '20', '50', '100'],
                  position: ['bottomCenter'],
                  style: { position: 'sticky', bottom: 0, backgroundColor: 'white', zIndex: 1, padding: '8px 0', borderTop: '1px solid #f0f0f0' }
                }}
                size="small"
                bordered
                loading={companyLoading}
                locale={{ emptyText: '暂无数据，请先在开票管理中添加发票数据' }}
                scroll={{ x: 1800, y: 'calc(100vh - 400px)' }}
              summary={() => {
                if (!companyData || companyData.length === 0) return null;

                // 计算汇总数据
                const totals = {
                  jan: 0, feb: 0, mar: 0, apr: 0, may: 0, jun: 0,
                  jul: 0, aug: 0, sep: 0, oct: 0, nov: 0, dec: 0
                };

                companyData.forEach(company => {
                  if (company.monthly) {
                    Object.keys(totals).forEach(month => {
                      totals[month as keyof typeof totals] += company.monthly[month as keyof typeof company.monthly] || 0;
                    });
                  }
                });

                const q1Total = totals.jan + totals.feb + totals.mar;
                const q2Total = totals.apr + totals.may + totals.jun;
                const q3Total = totals.jul + totals.aug + totals.sep;
                const q4Total = totals.oct + totals.nov + totals.dec;
                const yearTotal = q1Total + q2Total + q3Total + q4Total;

                return (
                  <Table.Summary.Row style={{ backgroundColor: '#ffe7ba', fontWeight: 'bold' }}>
                    <Table.Summary.Cell index={0} colSpan={3} align="center">
                      <span style={{ fontSize: '16px', color: '#d46b08', fontWeight: 'bold' }}>合计</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={3} align="right">
                      <span style={{ color: '#d46b08' }}>{formatAmount(totals.jan)}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={4} align="right">
                      <span style={{ color: '#d46b08' }}>{formatAmount(totals.feb)}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={5} align="right">
                      <span style={{ color: '#d46b08' }}>{formatAmount(totals.mar)}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={6} align="right">
                      <span style={{ backgroundColor: '#bae7ff', color: '#1890ff', fontWeight: 'bold', padding: '4px' }}>{formatAmount(q1Total)}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={7} align="right">
                      <span style={{ color: '#d46b08' }}>{formatAmount(totals.apr)}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={8} align="right">
                      <span style={{ color: '#d46b08' }}>{formatAmount(totals.may)}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={9} align="right">
                      <span style={{ color: '#d46b08' }}>{formatAmount(totals.jun)}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={10} align="right">
                      <span style={{ backgroundColor: '#bae7ff', color: '#1890ff', fontWeight: 'bold', padding: '4px' }}>{formatAmount(q2Total)}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={11} align="right">
                      <span style={{ color: '#d46b08' }}>{formatAmount(totals.jul)}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={12} align="right">
                      <span style={{ color: '#d46b08' }}>{formatAmount(totals.aug)}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={13} align="right">
                      <span style={{ color: '#d46b08' }}>{formatAmount(totals.sep)}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={14} align="right">
                      <span style={{ backgroundColor: '#bae7ff', color: '#1890ff', fontWeight: 'bold', padding: '4px' }}>{formatAmount(q3Total)}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={15} align="right">
                      <span style={{ color: '#d46b08' }}>{formatAmount(totals.oct)}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={16} align="right">
                      <span style={{ color: '#d46b08' }}>{formatAmount(totals.nov)}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={17} align="right">
                      <span style={{ color: '#d46b08' }}>{formatAmount(totals.dec)}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={18} align="right">
                      <span style={{ backgroundColor: '#bae7ff', color: '#1890ff', fontWeight: 'bold', padding: '4px' }}>{formatAmount(q4Total)}</span>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={19} align="right">
                      <span style={{ fontWeight: 'bold', color: '#52c41a', fontSize: '16px', backgroundColor: '#f6ffed', padding: '4px' }}>{formatAmount(yearTotal)}</span>
                    </Table.Summary.Cell>
                  </Table.Summary.Row>
                );
              }}
            />
            </div>
          </Card>
        </div>
      );
    }

    if (path.includes('user-summary')) {
      // 用户开票汇总表格列定义
      const userInvoiceSummaryColumns = [
        {
          title: '序号',
          key: 'index',
          width: 60,
          align: 'center' as const,
          render: (_: any, __: any, index: number) => index + 1,
        },
        {
          title: '所属用户',
          key: 'ownerUser',
          width: 150,
          render: (_, record: any) => (
            <div>
              <div style={{ fontWeight: 'bold' }}>{record.ownerUsername}</div>
              <div style={{ fontSize: '12px', color: '#666' }}>{record.ownerEmail}</div>
            </div>
          )
        },
        {
          title: '其他所属用户',
          key: 'otherUser',
          width: 150,
          render: (_, record: any) => (
            <div>
              <div style={{ fontWeight: 'bold' }}>{record.otherUsername}</div>
              <div style={{ fontSize: '12px', color: '#666' }}>{record.otherEmail}</div>
            </div>
          )
        },
        {
          title: '开具发票总金额',
          dataIndex: 'issuedAmount',
          key: 'issuedAmount',
          width: 150,
          align: 'right' as const,
          render: (amount: number) => (
            <span style={{
              fontWeight: 'bold',
              color: amount > 0 ? '#f5222d' : '#999',
              fontSize: '14px'
            }}>
              ¥{amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
            </span>
          )
        },
        {
          title: '取得发票总金额',
          dataIndex: 'receivedAmount',
          key: 'receivedAmount',
          width: 150,
          align: 'right' as const,
          render: (amount: number) => (
            <span style={{
              fontWeight: 'bold',
              color: amount > 0 ? '#52c41a' : '#999',
              fontSize: '14px'
            }}>
              ¥{amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
            </span>
          )
        },
        {
          title: '操作',
          key: 'action',
          width: 160,
          align: 'center' as const,
          render: (_, record: any) => (
            <Space>
              <Button
                type="link"
                size="small"
                onClick={() => {
                  setUserInvoiceSummarySelectedRecord(record);
                  fetchUserInvoiceSummaryDetail(record.ownerUserId, record.otherUserId);
                }}
              >
                查看详细
              </Button>
              <Button
                type="link"
                size="small"
                onClick={() => {
                  setUserInvoiceSummarySelectedRecord(record);
                  fetchUserInvoiceSummaryGroupDetail(record.ownerUserId, record.otherUserId);
                }}
              >
                查看汇总
              </Button>
            </Space>
          )
        }
      ];

      return (
        <div>
          {/* 查询条件 */}
          <Card title="查询条件" size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16} align="bottom">
              <Col span={8}>
                <Form.Item label={<Text strong>所属用户</Text>} style={{ marginBottom: 0 }}>
                  <Select
                    mode="multiple"
                    value={userInvoiceSummarySelectedUsers}
                    onChange={setUserInvoiceSummarySelectedUsers}
                    style={{ width: '100%' }}
                    placeholder="请选择所属用户（不选则查询所有用户）"
                    allowClear
                    size="small"
                    showSearch
                    filterOption={(input, option) =>
                      String(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                    }
                  >
                    {userInvoiceSummaryAllUsers.map(user => (
                      <Select.Option key={user.id} value={user.id} label={user.username}>
                        {user.username} ({user.email})
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Space>
                  <Button type="primary" icon={<SearchOutlined />} size="small" onClick={fetchUserInvoiceSummaryData} loading={userInvoiceSummaryLoading}>
                    查询
                  </Button>
                  <Button icon={<DownloadOutlined />} size="small" onClick={() => {
                    message.info('导出功能开发中...');
                  }}>
                    导出
                  </Button>
                </Space>
              </Col>
            </Row>
          </Card>

          {/* 主表格 */}
          <Card style={{ marginBottom: 16 }}>
            <Table
              columns={userInvoiceSummaryColumns}
              dataSource={userInvoiceSummaryData}
              rowKey={(record) => `${record.ownerUserId}-${record.otherUserId}`}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `共 ${total} 条记录，第 ${range[0]}-${range[1]} 条`,
                pageSizeOptions: ['10', '20', '50', '100'],
                defaultPageSize: 20
              }}
              size="small"
              bordered
              loading={userInvoiceSummaryLoading}
              locale={{ emptyText: '暂无数据，请先在开票管理中添加发票数据' }}
              scroll={{ x: 800 }}
            />
          </Card>

          {/* 用户开票详细信息模态框 */}
          <Modal
            title={
              userInvoiceSummarySelectedRecord ?
              `${userInvoiceSummarySelectedRecord.ownerUsername} ↔ ${userInvoiceSummarySelectedRecord.otherUsername} 开票详情` :
              '开票详情'
            }
            open={userInvoiceSummaryDetailVisible}
            onCancel={() => setUserInvoiceSummaryDetailVisible(false)}
            footer={[
              <Button key="close" onClick={() => setUserInvoiceSummaryDetailVisible(false)}>关闭</Button>
            ]}
            width={1400}
            style={{ top: 20 }}
            bodyStyle={{ height: '70vh', overflow: 'auto' }}
          >
            <Tabs defaultActiveKey="issued" items={[
              {
                key: 'issued',
                label: `📤 开具发票 (总金额: ¥${(userInvoiceSummaryDetailData.issuedInvoices || []).reduce((sum: number, invoice: any) => sum + Number(invoice.totalAmount || 0), 0).toLocaleString('zh-CN', { minimumFractionDigits: 2 })})`,
                children: (
                  <Table
                    columns={[
                      { title: '发票号码', dataIndex: 'invoiceNumber', key: 'invoiceNumber', width: 150 },
                      {
                        title: '开票日期',
                        dataIndex: 'invoiceDate',
                        key: 'invoiceDate',
                        width: 120,
                        render: (date: string) => new Date(date).toLocaleDateString('zh-CN')
                      },
                      { title: '购买方', dataIndex: 'buyerName', key: 'buyerName', width: 200 },
                      { title: '销售方', dataIndex: 'sellerName', key: 'sellerName', width: 200 },
                      {
                        title: '价税合计',
                        dataIndex: 'totalAmount',
                        key: 'totalAmount',
                        width: 120,
                        align: 'right' as const,
                        render: (amount: number) => (
                          <span style={{ fontWeight: 'bold', color: '#f5222d' }}>
                            ¥{amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
                          </span>
                        )
                      },
                      {
                        title: '查看发票',
                        key: 'action',
                        width: 100,
                        align: 'center' as const,
                        render: (_, record: any) => (
                          <Button
                            type="link"
                            size="small"
                            onClick={() => handleViewInvoice(record)}
                          >
                            查看发票
                          </Button>
                        )
                      }
                    ]}
                    dataSource={userInvoiceSummaryDetailData.issuedInvoices}
                    rowKey={(record) => `issued-${record.id}`}
                    pagination={{ pageSize: 20 }}
                    size="small"
                    scroll={{ y: 'calc(60vh - 100px)' }}
                  />
                )
              },
              {
                key: 'received',
                label: `📥 取得发票 (总金额: ¥${(userInvoiceSummaryDetailData.receivedInvoices || []).reduce((sum: number, invoice: any) => sum + Number(invoice.totalAmount || 0), 0).toLocaleString('zh-CN', { minimumFractionDigits: 2 })})`,
                children: (
                  <Table
                    columns={[
                      { title: '发票号码', dataIndex: 'invoiceNumber', key: 'invoiceNumber', width: 150 },
                      {
                        title: '开票日期',
                        dataIndex: 'invoiceDate',
                        key: 'invoiceDate',
                        width: 120,
                        render: (date: string) => new Date(date).toLocaleDateString('zh-CN')
                      },
                      { title: '购买方', dataIndex: 'buyerName', key: 'buyerName', width: 200 },
                      { title: '销售方', dataIndex: 'sellerName', key: 'sellerName', width: 200 },
                      {
                        title: '价税合计',
                        dataIndex: 'totalAmount',
                        key: 'totalAmount',
                        width: 120,
                        align: 'right' as const,
                        render: (amount: number) => (
                          <span style={{ fontWeight: 'bold', color: '#52c41a' }}>
                            ¥{amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
                          </span>
                        )
                      },
                      {
                        title: '查看发票',
                        key: 'action',
                        width: 100,
                        align: 'center' as const,
                        render: (_, record: any) => (
                          <Button
                            type="link"
                            size="small"
                            onClick={() => handleViewReceivedInvoice(record)}
                          >
                            查看发票
                          </Button>
                        )
                      }
                    ]}
                    dataSource={userInvoiceSummaryDetailData.receivedInvoices}
                    rowKey={(record) => `received-${record.id}`}
                    pagination={{ pageSize: 20 }}
                    size="small"
                    scroll={{ y: 'calc(60vh - 200px)' }}
                  />
                )
              }
            ]} />
          </Modal>

          {/* 发票详情模态框 */}
          <Modal
            title={
              userSummarySelectedInvoice ? "开具发票详情" :
              userSummarySelectedReceivedInvoice ? "取得发票详情" : "发票详情"
            }
            open={userSummaryInvoiceDetailVisible}
            onCancel={() => {
              setUserSummaryInvoiceDetailVisible(false);
              setUserSummarySelectedInvoice(null);
              setUserSummarySelectedReceivedInvoice(null);
            }}
            footer={[
              <Button key="close" onClick={() => {
                setUserSummaryInvoiceDetailVisible(false);
                setUserSummarySelectedInvoice(null);
                setUserSummarySelectedReceivedInvoice(null);
              }}>关闭</Button>
            ]}
            width={1200}
          >
            {userSummarySelectedInvoice && (
              <div>
                {/* 基本信息 */}
                <Card title="基本信息" size="small" style={{ marginBottom: 16 }}>
                  <Row gutter={[16, 8]}>
                    <Col span={6}>
                      <div style={{ marginBottom: 8 }}>
                        <Text type="secondary">发票号码</Text>
                        <div style={{ fontWeight: 'bold', fontSize: '16px' }}>{userSummarySelectedInvoice.invoiceNumber}</div>
                      </div>
                    </Col>
                    <Col span={6}>
                      <div style={{ marginBottom: 8 }}>
                        <Text type="secondary">发票代码</Text>
                        <div style={{ fontWeight: 'bold', fontSize: '16px' }}>{userSummarySelectedInvoice.invoiceCode}</div>
                      </div>
                    </Col>
                    <Col span={6}>
                      <div style={{ marginBottom: 8 }}>
                        <Text type="secondary">开票日期</Text>
                        <div style={{ fontWeight: 'bold', fontSize: '16px' }}>{userSummarySelectedInvoice.invoiceDate?.split('T')[0]}</div>
                      </div>
                    </Col>
                    <Col span={6}>
                      <div style={{ marginBottom: 8 }}>
                        <Text type="secondary">开票人</Text>
                        <div style={{ fontWeight: 'bold', fontSize: '16px' }}>{userSummarySelectedInvoice.drawer || '-'}</div>
                      </div>
                    </Col>
                    <Col span={24}>
                      <div style={{ marginBottom: 8 }}>
                        <Text type="secondary">备注</Text>
                        <div style={{ marginTop: 4, padding: '8px', backgroundColor: '#f5f5f5', borderRadius: '4px', minHeight: '32px' }}>
                          {userSummarySelectedInvoice.remarks || ''}
                        </div>
                      </div>
                    </Col>
                  </Row>
                </Card>

                {/* 购买方和销售方信息 */}
                <Row gutter={16} style={{ marginBottom: 16 }}>
                  <Col span={12}>
                    <Card title="购买方信息" size="small">
                      <div style={{ marginBottom: 12 }}>
                        <Text type="secondary">公司名称</Text>
                        <div style={{ fontWeight: 'bold', fontSize: '16px', marginTop: 4 }}>{userSummarySelectedInvoice.buyerName}</div>
                      </div>
                      <div style={{ marginBottom: 12 }}>
                        <Text type="secondary">纳税人识别号</Text>
                        <div style={{ fontWeight: 'bold', marginTop: 4 }}>{userSummarySelectedInvoice.buyerTaxId}</div>
                      </div>
                      <div style={{ marginBottom: 8 }}>
                        <Text type="secondary">地址电话</Text>
                        <div style={{ marginTop: 4 }}>{userSummarySelectedInvoice.buyerAddress} {userSummarySelectedInvoice.buyerPhone}</div>
                      </div>
                    </Card>
                  </Col>
                  <Col span={12}>
                    <Card title="销售方信息" size="small">
                      <div style={{ marginBottom: 12 }}>
                        <Text type="secondary">公司名称</Text>
                        <div style={{ fontWeight: 'bold', fontSize: '16px', marginTop: 4 }}>{userSummarySelectedInvoice.sellerName}</div>
                      </div>
                      <div style={{ marginBottom: 12 }}>
                        <Text type="secondary">纳税人识别号</Text>
                        <div style={{ fontWeight: 'bold', marginTop: 4 }}>{userSummarySelectedInvoice.sellerTaxId}</div>
                      </div>
                      <div style={{ marginBottom: 8 }}>
                        <Text type="secondary">地址电话</Text>
                        <div style={{ marginTop: 4 }}>{userSummarySelectedInvoice.sellerAddress} {userSummarySelectedInvoice.sellerPhone}</div>
                      </div>
                    </Card>
                  </Col>
                </Row>

                {/* 金额信息 */}
                <Card title="金额信息" size="small" style={{ marginBottom: 16 }}>
                  <Row gutter={16}>
                    <Col span={8}>
                      <Statistic
                        title="金额"
                        value={userSummarySelectedInvoice.amount || 0}
                        precision={2}
                        prefix="¥"
                        valueStyle={{ color: '#1890ff' }}
                      />
                    </Col>
                    <Col span={8}>
                      <Statistic
                        title="税额"
                        value={userSummarySelectedInvoice.taxAmount || 0}
                        precision={2}
                        prefix="¥"
                        valueStyle={{ color: '#faad14' }}
                      />
                    </Col>
                    <Col span={8}>
                      <Statistic
                        title="价税合计"
                        value={userSummarySelectedInvoice.totalAmount || 0}
                        precision={2}
                        prefix="¥"
                        valueStyle={{ color: '#52c41a', fontSize: '20px', fontWeight: 'bold' }}
                      />
                    </Col>
                  </Row>
                </Card>

                {/* 发票明细 */}
                {userSummarySelectedInvoice.invoiceItems && userSummarySelectedInvoice.invoiceItems.length > 0 && (
                  <Card title="发票明细" size="small" style={{ marginBottom: 16 }}>
                    <Table
                      dataSource={userSummarySelectedInvoice.invoiceItems}
                      rowKey="id"
                      size="small"
                      pagination={false}
                      columns={[
                        { title: '商品名称', dataIndex: 'itemName', key: 'itemName' },
                        { title: '规格型号', dataIndex: 'specification', key: 'specification' },
                        { title: '单位', dataIndex: 'unit', key: 'unit', width: 80 },
                        { title: '数量', dataIndex: 'quantity', key: 'quantity', width: 100, align: 'right' as const },
                        { title: '单价', dataIndex: 'unitPrice', key: 'unitPrice', width: 120, align: 'right' as const, render: (price: number) => `¥${price?.toLocaleString()}` },
                        { title: '金额', dataIndex: 'amount', key: 'amount', width: 120, align: 'right' as const, render: (amount: number) => `¥${amount?.toLocaleString()}` },
                        { title: '税率', dataIndex: 'taxRate', key: 'taxRate', width: 100, align: 'right' as const, render: (rate: number) => `${(rate * 100).toFixed(2)}%` },
                        { title: '税额', dataIndex: 'taxAmount', key: 'taxAmount', width: 120, align: 'right' as const, render: (tax: number) => `¥${tax?.toLocaleString()}` },
                        { title: '价税合计', dataIndex: 'totalAmount', key: 'totalAmount', width: 130, align: 'right' as const, render: (total: number) => `¥${total?.toLocaleString() || 0}` },
                      ]}
                    />
                  </Card>
                )}
              </div>
            )}
            {userSummarySelectedReceivedInvoice && (
              <div>
                {/* 基本信息 */}
                <Card title="基本信息" size="small" style={{ marginBottom: 16 }}>
                  <Row gutter={[16, 8]}>
                    <Col span={6}>
                      <div style={{ marginBottom: 8 }}>
                        <Text type="secondary">发票号码</Text>
                        <div style={{ fontWeight: 'bold', fontSize: '16px' }}>{userSummarySelectedReceivedInvoice.invoiceNumber}</div>
                      </div>
                    </Col>
                    <Col span={6}>
                      <div style={{ marginBottom: 8 }}>
                        <Text type="secondary">发票代码</Text>
                        <div style={{ fontWeight: 'bold', fontSize: '16px' }}>{userSummarySelectedReceivedInvoice.invoiceCode}</div>
                      </div>
                    </Col>
                    <Col span={6}>
                      <div style={{ marginBottom: 8 }}>
                        <Text type="secondary">开票日期</Text>
                        <div style={{ fontWeight: 'bold', fontSize: '16px' }}>{userSummarySelectedReceivedInvoice.invoiceDate?.split('T')[0]}</div>
                      </div>
                    </Col>
                    <Col span={6}>
                      <div style={{ marginBottom: 8 }}>
                        <Text type="secondary">开票人</Text>
                        <div style={{ fontWeight: 'bold', fontSize: '16px' }}>{userSummarySelectedReceivedInvoice.drawer || '-'}</div>
                      </div>
                    </Col>
                    <Col span={24}>
                      <div style={{ marginBottom: 8 }}>
                        <Text type="secondary">备注</Text>
                        <div style={{ marginTop: 4, padding: '8px', backgroundColor: '#f5f5f5', borderRadius: '4px', minHeight: '32px' }}>
                          {userSummarySelectedReceivedInvoice.remarks || ''}
                        </div>
                      </div>
                    </Col>
                  </Row>
                </Card>

                {/* 购买方和销售方信息 */}
                <Row gutter={16} style={{ marginBottom: 16 }}>
                  <Col span={12}>
                    <Card title="购买方信息" size="small">
                      <div style={{ marginBottom: 12 }}>
                        <Text type="secondary">公司名称</Text>
                        <div style={{ fontWeight: 'bold', fontSize: '16px', marginTop: 4 }}>{userSummarySelectedReceivedInvoice.buyerName}</div>
                      </div>
                      <div style={{ marginBottom: 12 }}>
                        <Text type="secondary">纳税人识别号</Text>
                        <div style={{ fontWeight: 'bold', marginTop: 4 }}>{userSummarySelectedReceivedInvoice.buyerTaxId}</div>
                      </div>
                      <div style={{ marginBottom: 8 }}>
                        <Text type="secondary">地址电话</Text>
                        <div style={{ marginTop: 4 }}>{userSummarySelectedReceivedInvoice.buyerAddress} {userSummarySelectedReceivedInvoice.buyerPhone}</div>
                      </div>
                    </Card>
                  </Col>
                  <Col span={12}>
                    <Card title="销售方信息" size="small">
                      <div style={{ marginBottom: 12 }}>
                        <Text type="secondary">公司名称</Text>
                        <div style={{ fontWeight: 'bold', fontSize: '16px', marginTop: 4 }}>{userSummarySelectedReceivedInvoice.sellerName}</div>
                      </div>
                      <div style={{ marginBottom: 12 }}>
                        <Text type="secondary">纳税人识别号</Text>
                        <div style={{ fontWeight: 'bold', marginTop: 4 }}>{userSummarySelectedReceivedInvoice.sellerTaxId}</div>
                      </div>
                      <div style={{ marginBottom: 8 }}>
                        <Text type="secondary">地址电话</Text>
                        <div style={{ marginTop: 4 }}>{userSummarySelectedReceivedInvoice.sellerAddress} {userSummarySelectedReceivedInvoice.sellerPhone}</div>
                      </div>
                    </Card>
                  </Col>
                </Row>

                {/* 金额信息 */}
                <Card title="金额信息" size="small" style={{ marginBottom: 16 }}>
                  <Row gutter={16}>
                    <Col span={8}>
                      <Statistic
                        title="金额"
                        value={userSummarySelectedReceivedInvoice.amount || 0}
                        precision={2}
                        prefix="¥"
                        valueStyle={{ color: '#1890ff' }}
                      />
                    </Col>
                    <Col span={8}>
                      <Statistic
                        title="税额"
                        value={userSummarySelectedReceivedInvoice.taxAmount || 0}
                        precision={2}
                        prefix="¥"
                        valueStyle={{ color: '#faad14' }}
                      />
                    </Col>
                    <Col span={8}>
                      <Statistic
                        title="价税合计"
                        value={userSummarySelectedReceivedInvoice.totalAmount || 0}
                        precision={2}
                        prefix="¥"
                        valueStyle={{ color: '#52c41a', fontSize: '20px', fontWeight: 'bold' }}
                      />
                    </Col>
                  </Row>
                </Card>

                {/* 发票明细 */}
                {userSummarySelectedReceivedInvoice.invoiceItems && userSummarySelectedReceivedInvoice.invoiceItems.length > 0 && (
                  <Card title="发票明细" size="small" style={{ marginBottom: 16 }}>
                    <Table
                      dataSource={userSummarySelectedReceivedInvoice.invoiceItems}
                      rowKey="id"
                      size="small"
                      pagination={false}
                      columns={[
                        { title: '商品名称', dataIndex: 'itemName', key: 'itemName' },
                        { title: '规格型号', dataIndex: 'specification', key: 'specification' },
                        { title: '单位', dataIndex: 'unit', key: 'unit', width: 80 },
                        { title: '数量', dataIndex: 'quantity', key: 'quantity', width: 100, align: 'right' as const },
                        { title: '单价', dataIndex: 'unitPrice', key: 'unitPrice', width: 120, align: 'right' as const, render: (price: number) => `¥${price?.toLocaleString()}` },
                        { title: '金额', dataIndex: 'amount', key: 'amount', width: 120, align: 'right' as const, render: (amount: number) => `¥${amount?.toLocaleString()}` },
                        { title: '税率', dataIndex: 'taxRate', key: 'taxRate', width: 100, align: 'right' as const, render: (rate: number) => `${(rate * 100).toFixed(2)}%` },
                        { title: '税额', dataIndex: 'taxAmount', key: 'taxAmount', width: 120, align: 'right' as const, render: (tax: number) => `¥${tax?.toLocaleString()}` },
                        { title: '价税合计', dataIndex: 'totalAmount', key: 'totalAmount', width: 130, align: 'right' as const, render: (total: number) => `¥${total?.toLocaleString() || 0}` },
                      ]}
                    />
                  </Card>
                )}
              </div>
            )}
          </Modal>

          {/* 用户开票汇总分组模态框 */}
          <Modal
            title={
              userInvoiceSummarySelectedRecord ?
              `${userInvoiceSummarySelectedRecord.ownerUsername} ↔ ${userInvoiceSummarySelectedRecord.otherUsername} 开票汇总` :
              '开票汇总'
            }
            open={userInvoiceSummaryGroupVisible}
            onCancel={() => setUserInvoiceSummaryGroupVisible(false)}
            footer={[
              <Button key="close" onClick={() => setUserInvoiceSummaryGroupVisible(false)}>关闭</Button>
            ]}
            width={1400}
            style={{ top: 20 }}
            bodyStyle={{ height: '70vh', overflow: 'auto' }}
          >
            <Tabs defaultActiveKey="issued" items={[
              {
                key: 'issued',
                label: `📤 开具发票汇总 (总金额: ¥${(userInvoiceSummaryGroupData.issuedGroups || []).reduce((sum: number, group: any) => sum + Number(group.totalAmount || 0), 0).toLocaleString('zh-CN', { minimumFractionDigits: 2 })})`,
                children: (
                  <Table
                    columns={[
                      { title: '购买方', dataIndex: 'buyerName', key: 'buyerName', width: 200 },
                      { title: '销售方', dataIndex: 'sellerName', key: 'sellerName', width: 200 },
                      {
                        title: '价税合计',
                        dataIndex: 'totalAmount',
                        key: 'totalAmount',
                        width: 150,
                        align: 'right' as const,
                        render: (amount: number) => (
                          <span style={{ fontWeight: 'bold', color: '#f5222d' }}>
                            ¥{amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
                          </span>
                        )
                      },
                      {
                        title: '发票列表',
                        key: 'action',
                        width: 100,
                        align: 'center' as const,
                        render: (_, record: any) => (
                          <Button
                            type="link"
                            size="small"
                            onClick={() => fetchUserInvoiceSummaryGroupList(
                              userInvoiceSummaryGroupData.ownerUserId,
                              userInvoiceSummaryGroupData.otherUserId,
                              record.buyerName,
                              record.sellerName,
                              'issued'
                            )}
                          >
                            查看列表 ({record.count})
                          </Button>
                        )
                      }
                    ]}
                    dataSource={userInvoiceSummaryGroupData.issuedGroups}
                    rowKey={(record) => `issued-${record.buyerName}-${record.sellerName}`}
                    pagination={{ pageSize: 20 }}
                    size="small"
                    scroll={{ y: 'calc(60vh - 200px)' }}
                  />
                )
              },
              {
                key: 'received',
                label: `📥 取得发票汇总 (总金额: ¥${(userInvoiceSummaryGroupData.receivedGroups || []).reduce((sum: number, group: any) => sum + Number(group.totalAmount || 0), 0).toLocaleString('zh-CN', { minimumFractionDigits: 2 })})`,
                children: (
                  <Table
                    columns={[
                      { title: '购买方', dataIndex: 'buyerName', key: 'buyerName', width: 200 },
                      { title: '销售方', dataIndex: 'sellerName', key: 'sellerName', width: 200 },
                      {
                        title: '价税合计',
                        dataIndex: 'totalAmount',
                        key: 'totalAmount',
                        width: 150,
                        align: 'right' as const,
                        render: (amount: number) => (
                          <span style={{ fontWeight: 'bold', color: '#52c41a' }}>
                            ¥{amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
                          </span>
                        )
                      },
                      {
                        title: '发票列表',
                        key: 'action',
                        width: 100,
                        align: 'center' as const,
                        render: (_, record: any) => (
                          <Button
                            type="link"
                            size="small"
                            onClick={() => fetchUserInvoiceSummaryGroupList(
                              userInvoiceSummaryGroupData.ownerUserId,
                              userInvoiceSummaryGroupData.otherUserId,
                              record.buyerName,
                              record.sellerName,
                              'received'
                            )}
                          >
                            查看列表 ({record.count})
                          </Button>
                        )
                      }
                    ]}
                    dataSource={userInvoiceSummaryGroupData.receivedGroups}
                    rowKey={(record) => `received-${record.buyerName}-${record.sellerName}`}
                    pagination={{ pageSize: 20 }}
                    size="small"
                    scroll={{ y: 'calc(60vh - 200px)' }}
                  />
                )
              }
            ]} />
          </Modal>

          {/* 用户开票汇总分组发票列表模态框 */}
          <Modal
            title={userInvoiceSummaryGroupListTitle}
            open={userInvoiceSummaryGroupListVisible}
            onCancel={() => setUserInvoiceSummaryGroupListVisible(false)}
            footer={[
              <Button key="close" onClick={() => setUserInvoiceSummaryGroupListVisible(false)}>关闭</Button>
            ]}
            width={1200}
            style={{ top: 20 }}
          >
            <Table
              columns={[
                { title: '发票号码', dataIndex: 'invoiceNumber', key: 'invoiceNumber', width: 150 },
                {
                  title: '开票日期',
                  dataIndex: 'invoiceDate',
                  key: 'invoiceDate',
                  width: 120,
                  render: (date: string) => new Date(date).toLocaleDateString('zh-CN')
                },
                { title: '购买方', dataIndex: 'buyerName', key: 'buyerName', width: 200 },
                { title: '销售方', dataIndex: 'sellerName', key: 'sellerName', width: 200 },
                {
                  title: '价税合计',
                  dataIndex: 'totalAmount',
                  key: 'totalAmount',
                  width: 120,
                  align: 'right' as const,
                  render: (amount: number) => (
                    <span style={{ fontWeight: 'bold', color: userInvoiceSummaryGroupListTitle.includes('开具发票') ? '#f5222d' : '#52c41a' }}>
                      ¥{amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
                    </span>
                  )
                },
                {
                  title: '查看发票',
                  key: 'action',
                  width: 100,
                  align: 'center' as const,
                  render: (_, record: any) => (
                    <Button
                      type="link"
                      size="small"
                      onClick={() => {
                        if (userInvoiceSummaryGroupListTitle.includes('开具发票')) {
                          handleViewInvoice(record);
                        } else {
                          handleViewReceivedInvoice(record);
                        }
                      }}
                    >
                      查看发票
                    </Button>
                  )
                }
              ]}
              dataSource={userInvoiceSummaryGroupListData}
              rowKey="id"
              pagination={{ pageSize: 20 }}
              size="small"
              scroll={{ y: 600 }}
            />
          </Modal>
        </div>
      );
    }

    if (path.includes('relations')) {
      return (
        <div style={{ height: 'calc(100vh - 150px)', overflow: 'hidden', display: 'flex', flexDirection: 'column' }}>

          {/* 筛选条件 */}
          <Card title="筛选条件" size="small" style={{ marginBottom: 12 }}>
            <Row gutter={[12, 8]} align="bottom">
              <Col span={6}>
                <div>
                  <Text strong style={{ fontSize: '12px' }}>公司名称</Text>
                  <Input
                    placeholder="输入公司名称进行筛选"
                    value={penetrationFilters.companyName}
                    onChange={(e) => setPenetrationFilters(prev => ({ ...prev, companyName: e.target.value }))}
                    size="small"
                    style={{ marginTop: 4 }}
                  />
                </div>
              </Col>
              <Col span={4}>
                <div>
                  <Text strong style={{ fontSize: '12px' }}>开始日期</Text>
                  <DatePicker
                    value={penetrationFilters.startDate ? dayjs(penetrationFilters.startDate) : null}
                    onChange={(date) => setPenetrationFilters(prev => ({ ...prev, startDate: date ? date.format('YYYY-MM-DD') : '' }))}
                    size="small"
                    style={{ width: '100%', marginTop: 4 }}
                  />
                </div>
              </Col>
              <Col span={4}>
                <div>
                  <Text strong style={{ fontSize: '12px' }}>结束日期</Text>
                  <DatePicker
                    value={penetrationFilters.endDate ? dayjs(penetrationFilters.endDate) : null}
                    onChange={(date) => setPenetrationFilters(prev => ({ ...prev, endDate: date ? date.format('YYYY-MM-DD') : '' }))}
                    size="small"
                    style={{ width: '100%', marginTop: 4 }}
                  />
                </div>
              </Col>
              <Col span={4}>
                <div>
                  <Text strong style={{ fontSize: '12px' }}>最大深度</Text>
                  <InputNumber
                    min={1}
                    max={10}
                    value={penetrationFilters.maxDepth}
                    onChange={(value) => setPenetrationFilters(prev => ({ ...prev, maxDepth: value || 5 }))}
                    size="small"
                    style={{ width: '100%', marginTop: 4 }}
                  />
                </div>
              </Col>
              <Col span={6}>
                <div>
                  <Button type="primary" onClick={fetchPenetrationData} loading={penetrationLoading} size="small">
                    查询
                  </Button>
                </div>
              </Col>
            </Row>
          </Card>

          {/* 统计信息 */}
          <Card title="穿透关系统计" size="small" style={{ marginBottom: 12 }}>
            <Row gutter={12}>
              <Col span={6}>
                <Card size="small" style={{ textAlign: 'center', backgroundColor: '#f0f9ff', padding: '8px' }}>
                  <Statistic
                    title="穿透链条数"
                    value={penetrationData.pagination?.totalChains || penetrationData.stats?.totalChains || 0}
                    prefix={<ThunderboltOutlined style={{ color: '#1890ff' }} />}
                    valueStyle={{ color: '#1890ff', fontSize: '25px', fontWeight: 'bold' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card size="small" style={{ textAlign: 'center', backgroundColor: '#f6ffed', padding: '8px' }}>
                  <Statistic
                    title="涉及公司数"
                    value={penetrationData.pagination?.totalNodes || penetrationData.stats?.totalNodes || 0}
                    prefix={<BankOutlined style={{ color: '#52c41a' }} />}
                    valueStyle={{ color: '#52c41a', fontSize: '25px', fontWeight: 'bold' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card size="small" style={{ textAlign: 'center', backgroundColor: '#fff7e6', padding: '8px' }}>
                  <Statistic
                    title="关系连接数"
                    value={penetrationData.pagination?.totalEdges || penetrationData.stats?.totalEdges || 0}
                    prefix={<SearchOutlined style={{ color: '#fa8c16' }} />}
                    valueStyle={{ color: '#fa8c16', fontSize: '25px', fontWeight: 'bold' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card size="small" style={{ textAlign: 'center', backgroundColor: '#fff1f0', padding: '8px' }}>
                  <Statistic
                    title="总金额(万元)"
                    value={(penetrationData.pagination?.totalAmount || penetrationData.stats?.totalAmount || 0) / 10000}
                    prefix="¥"
                    precision={2}
                    formatter={value => `${Number(value).toLocaleString()}`}
                    valueStyle={{ color: '#f5222d', fontSize: '25px', fontWeight: 'bold' }}
                  />
                </Card>
              </Col>
            </Row>
          </Card>

          {/* 穿透关系链列表 */}
          <Card
            title="穿透关系链"
            loading={penetrationLoading}
            style={{ flex: 1, display: 'flex', flexDirection: 'column', minHeight: 0 }}
            styles={{ body: { flex: 1, display: 'flex', flexDirection: 'column', padding: '16px', overflow: 'hidden' } }}
          >
            {penetrationData.chains && penetrationData.chains.length > 0 ? (
              <div style={{ display: 'flex', flexDirection: 'column', height: '100%', flex: 1 }}>
                <div style={{ flex: 1, overflowY: 'auto', marginBottom: 16 }}>
                  <List
                    dataSource={penetrationData.chains.slice(
                      (penetrationPagination.current - 1) * penetrationPagination.pageSize,
                      penetrationPagination.current * penetrationPagination.pageSize
                    )}
                    renderItem={(chain: any, index: number) => (
                    <List.Item>
                      <Card size="small" style={{ width: '100%' }}>
                        <Row gutter={16}>
                          <Col span={16}>
                            <div style={{ marginBottom: 8 }}>
                              <Text strong>链条 {(penetrationPagination.current - 1) * penetrationPagination.pageSize + index + 1}：</Text>
                              <Tag color="blue" style={{ marginLeft: 8 }}>深度: {chain.depth}</Tag>
                              <Tag color="green">金额: ¥{chain.totalAmount?.toLocaleString()}</Tag>
                            </div>
                            <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#1890ff' }}>
                              {chain.companies?.join(' → ')}
                              {chain.isCircular && <span style={{ color: '#52c41a' }}> → {chain.companies[0]} (循环)</span>}
                            </div>
                          </Col>
                          <Col span={8}>
                            <Button
                              type="link"
                              onClick={() => {
                                Modal.info({
                                  title: `链条详情 - 链条 ${(penetrationPagination.current - 1) * penetrationPagination.pageSize + index + 1}`,
                                  width: 800,
                                  content: (
                                    <div>
                                      <Table
                                        size="small"
                                        dataSource={chain.details || []}
                                        columns={[
                                          { title: '销售方', dataIndex: 'from', key: 'from' },
                                          { title: '购买方', dataIndex: 'to', key: 'to' },
                                          { title: '发票数量', dataIndex: 'count', key: 'count' },
                                          {
                                            title: '总金额',
                                            dataIndex: 'totalAmount',
                                            key: 'totalAmount',
                                            render: (amount: number) => `¥${amount?.toLocaleString()}`
                                          },
                                        {
                                          title: '操作',
                                          key: 'action',
                                          render: (_, record: any) => (
                                            <Button
                                              type="link"
                                              size="small"
                                              onClick={() => {
                                                // 显示发票列表
                                                Modal.info({
                                                  title: `${record.from} → ${record.to} 发票列表`,
                                                  width: 1000,
                                                  content: (
                                                    <div>
                                                      <Table
                                                        size="small"
                                                        dataSource={record.invoices || []}
                                                        rowKey={(invoice: any) => `penetration-invoice-${invoice.id}-${invoice.source}`}
                                                        columns={[
                                                          { title: '发票号码', dataIndex: 'invoiceNumber', key: 'invoiceNumber', width: 180, render: (text: string) => <span style={{ whiteSpace: 'nowrap' }}>{text}</span> },
                                                          { title: '发票日期', dataIndex: 'invoiceDate', key: 'invoiceDate', render: (date: string) => date?.split('T')[0] },
                                                          { title: '金额', dataIndex: 'totalAmount', key: 'totalAmount', render: (amount: number) => `¥${amount?.toLocaleString()}` },
                                                          {
                                                            title: '发票来源',
                                                            dataIndex: 'source',
                                                            key: 'source',
                                                            width: 100,
                                                            render: (source: string) => (
                                                              <Tag color={source === 'issued' ? 'blue' : 'green'}>
                                                                {source === 'issued' ? '开具发票' : '取得发票'}
                                                              </Tag>
                                                            )
                                                          },
                                                          {
                                                            title: '发票状态',
                                                            dataIndex: 'status',
                                                            key: 'status',
                                                            width: 100,
                                                            render: (status: string) => {
                                                              const statusMap: { [key: string]: { text: string, color: string } } = {
                                                                'NORMAL': { text: '正常', color: 'success' },
                                                                'CANCELLED': { text: '作废', color: 'error' }
                                                              };
                                                              const statusInfo = statusMap[status] || { text: status || '未知', color: 'default' };
                                                              return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
                                                            }
                                                          },
                                                          {
                                                            title: '操作',
                                                            key: 'action',
                                                            render: (_, invoiceRecord: any) => (
                                                              <Button
                                                                type="link"
                                                                size="small"
                                                                onClick={async () => {
                                                                  try {
                                                                    // 根据发票来源类型调用不同的API
                                                                    const apiUrl = invoiceRecord.source === 'received'
                                                                      ? `/api/received-invoices/${invoiceRecord.id}`
                                                                      : `/api/invoices/${invoiceRecord.id}`;
                                                                    const response = await api.get(apiUrl);
                                                                    const invoiceDetail = response.data.data;

                                                                    // 显示发票详情页面（参考开具发票详情界面）
                                                                    Modal.info({
                                                                      title: `发票详情 - ${invoiceDetail.invoiceNumber}`,
                                                                      width: 1200,
                                                                      content: (
                                                                        <div>
                                                                          {/* 基本信息 */}
                                                                          <Card title="基本信息" size="small" style={{ marginBottom: 16 }}>
                                                                            <Row gutter={[16, 8]}>
                                                                              <Col span={6}>
                                                                                <div style={{ marginBottom: 8 }}>
                                                                                  <Text type="secondary">发票号码</Text>
                                                                                  <div style={{ fontWeight: 'bold', fontSize: '16px' }}>{invoiceDetail.invoiceNumber}</div>
                                                                                </div>
                                                                              </Col>
                                                                              <Col span={6}>
                                                                                <div style={{ marginBottom: 8 }}>
                                                                                  <Text type="secondary">发票代码</Text>
                                                                                  <div style={{ fontWeight: 'bold', fontSize: '16px' }}>{invoiceDetail.invoiceCode}</div>
                                                                                </div>
                                                                              </Col>
                                                                              <Col span={6}>
                                                                                <div style={{ marginBottom: 8 }}>
                                                                                  <Text type="secondary">开票日期</Text>
                                                                                  <div style={{ fontWeight: 'bold', fontSize: '16px' }}>{invoiceDetail.invoiceDate?.split('T')[0]}</div>
                                                                                </div>
                                                                              </Col>
                                                                              <Col span={6}>
                                                                                <div style={{ marginBottom: 8 }}>
                                                                                  <Text type="secondary">开票人</Text>
                                                                                  <div style={{ fontWeight: 'bold', fontSize: '16px' }}>{invoiceDetail.drawer || '-'}</div>
                                                                                </div>
                                                                              </Col>
                                                                              {invoiceDetail.remarks && (
                                                                                <Col span={24}>
                                                                                  <div style={{ marginBottom: 8 }}>
                                                                                    <Text type="secondary">备注</Text>
                                                                                    <div style={{ marginTop: 4, padding: '8px', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>
                                                                                      {invoiceDetail.remarks}
                                                                                    </div>
                                                                                  </div>
                                                                                </Col>
                                                                              )}
                                                                            </Row>
                                                                          </Card>

                                                                          {/* 购买方和销售方信息 */}
                                                                          <Row gutter={16} style={{ marginBottom: 16 }}>
                                                                            <Col span={12}>
                                                                              <Card title="购买方信息" size="small">
                                                                                <div style={{ marginBottom: 12 }}>
                                                                                  <Text type="secondary">公司名称</Text>
                                                                                  <div style={{ fontWeight: 'bold', fontSize: '16px', marginTop: 4 }}>{invoiceDetail.buyerName}</div>
                                                                                </div>
                                                                                <div style={{ marginBottom: 12 }}>
                                                                                  <Text type="secondary">纳税人识别号</Text>
                                                                                  <div style={{ fontWeight: 'bold', marginTop: 4 }}>{invoiceDetail.buyerTaxId}</div>
                                                                                </div>
                                                                                <div style={{ marginBottom: 8 }}>
                                                                                  <Text type="secondary">地址电话</Text>
                                                                                  <div style={{ marginTop: 4 }}>{invoiceDetail.buyerAddress} {invoiceDetail.buyerPhone}</div>
                                                                                </div>
                                                                              </Card>
                                                                            </Col>
                                                                            <Col span={12}>
                                                                              <Card title="销售方信息" size="small">
                                                                                <div style={{ marginBottom: 12 }}>
                                                                                  <Text type="secondary">公司名称</Text>
                                                                                  <div style={{ fontWeight: 'bold', fontSize: '16px', marginTop: 4 }}>{invoiceDetail.sellerName}</div>
                                                                                </div>
                                                                                <div style={{ marginBottom: 12 }}>
                                                                                  <Text type="secondary">纳税人识别号</Text>
                                                                                  <div style={{ fontWeight: 'bold', marginTop: 4 }}>{invoiceDetail.sellerTaxId}</div>
                                                                                </div>
                                                                                <div style={{ marginBottom: 8 }}>
                                                                                  <Text type="secondary">地址电话</Text>
                                                                                  <div style={{ marginTop: 4 }}>{invoiceDetail.sellerAddress} {invoiceDetail.sellerPhone}</div>
                                                                                </div>
                                                                              </Card>
                                                                            </Col>
                                                                          </Row>

                                                                          {/* 金额信息 */}
                                                                          <Card title="金额信息" size="small" style={{ marginBottom: 16 }}>
                                                                            <Row gutter={16}>
                                                                              <Col span={8}>
                                                                                <Statistic
                                                                                  title="金额"
                                                                                  value={invoiceDetail.amount || 0}
                                                                                  precision={2}
                                                                                  prefix="¥"
                                                                                  valueStyle={{ color: '#1890ff' }}
                                                                                />
                                                                              </Col>
                                                                              <Col span={8}>
                                                                                <Statistic
                                                                                  title="税额"
                                                                                  value={invoiceDetail.taxAmount || 0}
                                                                                  precision={2}
                                                                                  prefix="¥"
                                                                                  valueStyle={{ color: '#faad14' }}
                                                                                />
                                                                              </Col>
                                                                              <Col span={8}>
                                                                                <Statistic
                                                                                  title="价税合计"
                                                                                  value={invoiceDetail.totalAmount || 0}
                                                                                  precision={2}
                                                                                  prefix="¥"
                                                                                  valueStyle={{ color: '#52c41a', fontSize: '20px', fontWeight: 'bold' }}
                                                                                />
                                                                              </Col>
                                                                            </Row>
                                                                          </Card>

                                                                          {/* 发票明细 */}
                                                                          {((invoiceDetail.invoiceItems && invoiceDetail.invoiceItems.length > 0) ||
                                                                            (invoiceDetail.receivedInvoiceItems && invoiceDetail.receivedInvoiceItems.length > 0)) && (
                                                                            <Card title="发票明细" size="small" style={{ marginBottom: 16 }}>
                                                                              <Table
                                                                                dataSource={invoiceDetail.invoiceItems || invoiceDetail.receivedInvoiceItems || []}
                                                                                rowKey={(record: any) => `invoice-detail-${record.id}`}
                                                                                size="small"
                                                                                pagination={false}
                                                                                columns={[
                                                                                  { title: '商品名称', dataIndex: 'itemName', key: 'itemName' },
                                                                                  { title: '规格型号', dataIndex: 'specification', key: 'specification' },
                                                                                  { title: '单位', dataIndex: 'unit', key: 'unit', width: 80 },
                                                                                  { title: '数量', dataIndex: 'quantity', key: 'quantity', width: 100, align: 'right' as const },
                                                                                  { title: '单价', dataIndex: 'unitPrice', key: 'unitPrice', width: 120, align: 'right' as const, render: (price: number) => `¥${price?.toLocaleString()}` },
                                                                                  { title: '金额', dataIndex: 'amount', key: 'amount', width: 120, align: 'right' as const, render: (amount: number) => `¥${amount?.toLocaleString()}` },
                                                                                  { title: '税率', dataIndex: 'taxRate', key: 'taxRate', width: 100, align: 'right' as const, render: (rate: number) => `${(rate * 100).toFixed(2)}%` },
                                                                                  { title: '税额', dataIndex: 'taxAmount', key: 'taxAmount', width: 120, align: 'right' as const, render: (tax: number) => `¥${tax?.toLocaleString()}` },
                                                                                  { title: '价税合计', dataIndex: 'totalAmount', key: 'totalAmount', width: 130, align: 'right' as const, render: (total: number) => `¥${total?.toLocaleString() || 0}` },
                                                                                ]}
                                                                              />
                                                                            </Card>
                                                                          )}
                                                                        </div>
                                                                      )
                                                                    });
                                                                  } catch (error) {
                                                                    console.error('获取发票详情失败:', error);
                                                                    message.error('获取发票详情失败');
                                                                  }
                                                                }}
                                                              >
                                                                查看发票
                                                              </Button>
                                                            )
                                                          }
                                                        ]}
                                                        pagination={false}
                                                        scroll={{ y: 300 }}
                                                        summary={() => {
                                                          const totalAmount = (record.invoices || []).reduce((sum: number, invoice: any) => sum + (parseFloat(invoice.totalAmount) || 0), 0);
                                                          const invoiceCount = (record.invoices || []).length;
                                                          return (
                                                            <Table.Summary.Row style={{ backgroundColor: '#fafafa', fontWeight: 'bold' }}>
                                                              <Table.Summary.Cell index={0}>
                                                                <span style={{ fontSize: '14px', color: '#1890ff', fontWeight: 'bold' }}>合计 ({invoiceCount}张)</span>
                                                              </Table.Summary.Cell>
                                                              <Table.Summary.Cell index={1}></Table.Summary.Cell>
                                                              <Table.Summary.Cell index={2}>
                                                                <span style={{ fontSize: '14px', color: '#52c41a', fontWeight: 'bold' }}>¥{totalAmount?.toLocaleString()}</span>
                                                              </Table.Summary.Cell>
                                                              <Table.Summary.Cell index={3}></Table.Summary.Cell>
                                                              <Table.Summary.Cell index={4}></Table.Summary.Cell>
                                                              <Table.Summary.Cell index={5}></Table.Summary.Cell>
                                                            </Table.Summary.Row>
                                                          );
                                                        }}
                                                      />
                                                    </div>
                                                  )
                                                });
                                              }}
                                            >
                                              查看发票列表
                                            </Button>
                                          )
                                        }
                                      ]}
                                      pagination={false}
                                      summary={() => {
                                        const totalAmount = (chain.details || []).reduce((sum: number, detail: any) => sum + (detail.totalAmount || 0), 0);
                                        const totalCount = (chain.details || []).reduce((sum: number, detail: any) => sum + (detail.count || 0), 0);
                                        return (
                                          <Table.Summary.Row style={{ backgroundColor: '#fafafa', fontWeight: 'bold' }}>
                                            <Table.Summary.Cell index={0} colSpan={2}>
                                              <span style={{ fontSize: '14px', color: '#1890ff', fontWeight: 'bold' }}>合计</span>
                                            </Table.Summary.Cell>
                                            <Table.Summary.Cell index={2}>
                                              <span style={{ fontSize: '14px', color: '#1890ff', fontWeight: 'bold' }}>{totalCount}</span>
                                            </Table.Summary.Cell>
                                            <Table.Summary.Cell index={3}>
                                              <span style={{ fontSize: '14px', color: '#52c41a', fontWeight: 'bold' }}>¥{totalAmount?.toLocaleString()}</span>
                                            </Table.Summary.Cell>
                                            <Table.Summary.Cell index={4}></Table.Summary.Cell>
                                          </Table.Summary.Row>
                                        );
                                      }}
                                    />
                                  </div>
                                )
                              });
                            }}
                          >
                            查看详情
                          </Button>
                        </Col>
                      </Row>
                    </Card>
                  </List.Item>
                  )}
                    pagination={false}
                  />
                </div>
                <div style={{
                  borderTop: '1px solid #f0f0f0',
                  paddingTop: 16,
                  backgroundColor: '#fafafa',
                  flexShrink: 0  // 防止分页组件被压缩
                }}>
                  <Pagination
                    current={penetrationPagination.current}
                    total={penetrationPagination.total}
                    pageSize={penetrationPagination.pageSize}
                    showSizeChanger={true}
                    showQuickJumper={true}
                    showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`}
                    pageSizeOptions={['5', '10', '20', '50']}
                    style={{ textAlign: 'center' }}
                    onChange={(page, pageSize) => {
                      setPenetrationPagination(prev => ({
                        ...prev,
                        current: page,
                        pageSize: pageSize || prev.pageSize
                      }));
                    }}
                    onShowSizeChange={(current, size) => {
                      setPenetrationPagination(prev => ({
                        ...prev,
                        current: 1,
                        pageSize: size
                      }));
                    }}
                  />
                </div>
              </div>
            ) : (
              <Empty
                description="暂无穿透关系链数据"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            )}
          </Card>
        </div>
      );
    }

    return (
      <div>
        <Card title="报表中心" style={{ marginBottom: 16 }}>
          <Text type="secondary">请从左侧菜单选择具体的报表类型</Text>
        </Card>
        <Row gutter={[16, 16]}>
          <Col span={6}>
            <Card hoverable onClick={() => navigate('/reports/quarterly-summary')}>
              <Card.Meta
                avatar={<Avatar style={{ backgroundColor: '#722ed1' }}>季</Avatar>}
                title="季度开票汇总"
                description="查看季度开票统计汇总"
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card hoverable onClick={() => navigate('/reports/company-summary')}>
              <Card.Meta
                avatar={<Avatar style={{ backgroundColor: '#52c41a' }}>公</Avatar>}
                title="公司开票汇总"
                description="查看公司按年度开票汇总"
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card hoverable onClick={() => navigate('/reports/invoice-count-summary')}>
              <Card.Meta
                avatar={<Avatar style={{ backgroundColor: '#fa8c16' }}>张</Avatar>}
                title="发票张数汇总"
                description="查看发票张数统计汇总"
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card hoverable onClick={() => navigate('/reports/user-summary')}>
              <Card.Meta
                avatar={<Avatar style={{ backgroundColor: '#13c2c2' }}>用</Avatar>}
                title="用户开票汇总"
                description="查看用户间开票往来汇总"
              />
            </Card>
          </Col>
        </Row>
        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          <Col span={6}>
            <Card hoverable onClick={() => navigate('/reports/relations')}>
              <Card.Meta
                avatar={<Avatar style={{ backgroundColor: '#1890ff' }}>关</Avatar>}
                title="开票关系"
                description="查看公司间开票关系穿透图"
              />
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  return (
    <div>
      {getReportContent()}
    </div>
  );
};



// Excel导入组件
const ImportInvoices: React.FC = () => {
  const [uploading, setUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState<any>(null);
  const [receivedUploading, setReceivedUploading] = useState(false);
  const [receivedUploadResult, setReceivedUploadResult] = useState<any>(null);
  const [activeKey, setActiveKey] = useState<string[]>(['1', '2']); // 默认展开两个面板

  // 开具发票相关函数
  const handleDownloadTemplate = async () => {
    try {
      const response = await api.get('/api/invoices/template', { responseType: 'blob' });
      const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = '开具发票导入模板.xlsx';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success('开具发票模板下载成功！');
    } catch (error) {
      console.error('下载模板失败:', error);
      message.error('下载模板失败');
    }
  };

  const handleUpload = async (files: FileList) => {
    setUploading(true);
    setUploadResult(null);

    const results = [];
    let totalSuccess = 0;
    let totalFailure = 0;
    let totalDuplicate = 0;

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const formData = new FormData();
        formData.append('file', file);

        try {
          const response = await api.post('/api/invoices/import', formData, {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          });

          const result = response.data.data;
          results.push({
            fileName: file.name,
            success: true,
            ...result
          });

          totalSuccess += result.successCount;
          totalFailure += result.failureCount;
          totalDuplicate += result.duplicateCount;
        } catch (error: any) {
          console.error(`文件 ${file.name} 导入失败:`, error);
          results.push({
            fileName: file.name,
            success: false,
            error: error.response?.data?.message || '导入失败'
          });
        }
      }

      setUploadResult({
        files: results,
        totalSuccess,
        totalFailure,
        totalDuplicate,
        totalFiles: files.length
      });

      message.success(`批量导入完成！共处理 ${files.length} 个文件，成功导入 ${totalSuccess} 条记录`);
    } catch (error: any) {
      console.error('批量导入失败:', error);
      message.error('批量导入失败');
    } finally {
      setUploading(false);
    }
  };

  // 取得发票相关函数
  const handleDownloadReceivedTemplate = async () => {
    try {
      const response = await api.get('/api/received-invoices/template', { responseType: 'blob' });
      const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = '取得发票导入模板.xlsx';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success('取得发票模板下载成功！');
    } catch (error) {
      console.error('下载取得发票模板失败:', error);
      message.error('下载取得发票模板失败');
    }
  };

  const handleReceivedUpload = async (files: FileList) => {
    setReceivedUploading(true);
    setReceivedUploadResult(null);

    const results = [];
    let totalSuccess = 0;
    let totalFailure = 0;
    let totalDuplicate = 0;

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const formData = new FormData();
        formData.append('file', file);

        try {
          const response = await api.post('/api/received-invoices/import', formData, {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          });

          const result = response.data.data;
          results.push({
            fileName: file.name,
            success: true,
            ...result
          });

          totalSuccess += result.successCount;
          totalFailure += result.failureCount;
          totalDuplicate += result.duplicateCount;
        } catch (error: any) {
          console.error(`文件 ${file.name} 导入失败:`, error);
          results.push({
            fileName: file.name,
            success: false,
            error: error.response?.data?.message || '导入失败'
          });
        }
      }

      setReceivedUploadResult({
        files: results,
        totalSuccess,
        totalFailure,
        totalDuplicate,
        totalFiles: files.length
      });

      message.success(`批量导入完成！共处理 ${files.length} 个文件，成功导入 ${totalSuccess} 条记录`);
    } catch (error: any) {
      console.error('批量导入失败:', error);
      message.error('批量导入失败');
    } finally {
      setReceivedUploading(false);
    }
  };

  // 批量上传发票文件的处理函数
  const handleBatchInvoiceFileUpload = async (files: FileList, invoiceType: 'issued' | 'received') => {
    try {
      let successCount = 0;
      let failureCount = 0;
      let duplicateCount = 0;
      const results: any[] = [];

      for (let i = 0; i < files.length; i++) {
        const file = files[i];

        try {
          // 从文件名中提取税号
          const fileName = file.name;
          const taxIdMatch = fileName.match(/(\d{15,20})/); // 匹配15-20位数字作为税号

          if (!taxIdMatch) {
            results.push({
              fileName,
              success: false,
              error: '文件名中未找到税号（需要15-20位数字）'
            });
            failureCount++;
            continue;
          }

          const taxId = taxIdMatch[1];

          // 根据税号查找发票
          const searchResponse = await api.get(`/api/${invoiceType === 'issued' ? 'invoices' : 'received-invoices'}?search=${taxId}&pageSize=1`);
          const invoices = searchResponse.data.data?.data || [];

          if (invoices.length === 0) {
            results.push({
              fileName,
              success: false,
              error: `未找到税号为 ${taxId} 的发票`
            });
            failureCount++;
            continue;
          }

          const invoice = invoices[0];

          // 检查是否已经有文件上传（重复检查）
          const attachmentCheckResponse = await api.get(`/api/${invoiceType === 'issued' ? 'invoices' : 'received-invoices'}/${invoice.id}`);
          const invoiceDetail = attachmentCheckResponse.data.data;

          if (invoiceDetail.attachments && invoiceDetail.attachments.length > 0) {
            // 检查是否有相同文件名的附件
            const existingFile = invoiceDetail.attachments.find((att: any) =>
              att.originalName === fileName || att.fileName === fileName
            );

            if (existingFile) {
              results.push({
                fileName,
                success: false,
                error: '该发票已有文件上传，跳过重复上传',
                isDuplicate: true
              });
              duplicateCount++;
              continue;
            }
          }

          // 上传文件到发票
          const formData = new FormData();
          formData.append('file', file);
          formData.append('invoiceId', invoice.id);
          formData.append('fileType', file.type.startsWith('image/') ? 'IMAGE' : 'PDF');
          formData.append('isOriginal', 'true');
          formData.append('invoiceType', invoiceType);

          await api.post('/api/upload/invoice-attachment', formData, {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          });

          results.push({
            fileName,
            success: true,
            invoiceNumber: invoice.invoiceNumber,
            taxId: taxId
          });
          successCount++;

        } catch (error: any) {
          console.error(`文件 ${file.name} 上传失败:`, error);
          results.push({
            fileName: file.name,
            success: false,
            error: error.response?.data?.message || '上传失败'
          });
          failureCount++;
        }
      }

      // 显示结果
      Modal.info({
        title: `批量上传发票文件结果`,
        width: 800,
        content: (
          <div>
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={8}>
                <Statistic title="成功上传" value={successCount} valueStyle={{ color: '#3f8600' }} />
              </Col>
              <Col span={8}>
                <Statistic title="失败上传" value={failureCount} valueStyle={{ color: '#cf1322' }} />
              </Col>
              <Col span={8}>
                <Statistic title="重复跳过" value={duplicateCount} valueStyle={{ color: '#fa8c16' }} />
              </Col>
            </Row>

            <Title level={5}>详细结果</Title>
            <List
              size="small"
              dataSource={results}
              style={{ maxHeight: 400, overflow: 'auto' }}
              renderItem={(result: any) => (
                <List.Item>
                  <List.Item.Meta
                    title={
                      <Space>
                        <span>{result.fileName}</span>
                        {result.success ? (
                          <Tag color="green">成功</Tag>
                        ) : result.isDuplicate ? (
                          <Tag color="orange">重复跳过</Tag>
                        ) : (
                          <Tag color="red">失败</Tag>
                        )}
                      </Space>
                    }
                    description={
                      result.success ? (
                        <Text>
                          匹配发票: {result.invoiceNumber} (税号: {result.taxId})
                        </Text>
                      ) : (
                        <Text type="danger">{result.error}</Text>
                      )
                    }
                  />
                </List.Item>
              )}
            />
          </div>
        ),
      });

      message.success(`批量上传完成！成功 ${successCount} 个，失败 ${failureCount} 个，重复跳过 ${duplicateCount} 个`);

    } catch (error: any) {
      console.error('批量上传发票文件失败:', error);
      message.error('批量上传发票文件失败');
    }
  };

  return (
    <div>
      <Title level={2}>Excel导入</Title>

      <Collapse
        activeKey={activeKey}
        onChange={setActiveKey}
        size="large"
        items={[
          {
            key: '1',
            label: (
              <span style={{ fontSize: '16px', fontWeight: 'bold' }}>
                📄 Excel导入开具发票
              </span>
            ),
            children: (
              <Space direction="vertical" size="large" style={{ width: '100%' }}>
                <div>
                  <Title level={4}>导入步骤</Title>
                  <ol>
                    <li>下载开具发票Excel模板</li>
                    <li>按照模板格式填写开具发票数据</li>
                    <li>上传填写好的Excel文件（支持同时选择多个文件进行批量导入）</li>
                  </ol>
                </div>

          <div>
            <Space>
              <Button
                type="primary"
                icon={<DownloadOutlined />}
                onClick={handleDownloadTemplate}
                style={{
                  background: 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
                  border: 'none'
                }}
              >
                下载开具发票模板
              </Button>
              <input
                type="file"
                accept=".xlsx,.xls"
                multiple
                onChange={(e) => {
                  const files = e.target.files;
                  if (files && files.length > 0) {
                    // 检查文件名是否包含"开具"
                     const validFiles = Array.from(files); // Array.from(files).filter(file => file.name.includes('开具'));
                    // const invalidFiles = Array.from(files).filter(file => !file.name.includes('开具'));

                    // if (invalidFiles.length > 0) {
                    //   message.error(`文件名称必须包含"开具"，请选择正确的开具发票文件。以下文件已跳过：${invalidFiles.map(f => f.name).join(', ')}`);
                    // }
                    // 
                    // 只处理符合要求的文件
                    const fileList = new DataTransfer();
                    validFiles.forEach(file => fileList.items.add(file));
                    handleUpload(fileList.files);
                  }
                  e.target.value = ''; // 清空文件选择，允许重新选择相同文件
                }}
                style={{ display: 'none' }}
                id="excel-upload"
              />
              <Button
                type="primary"
                icon={<UploadOutlined />}
                loading={uploading}
                onClick={() => document.getElementById('excel-upload')?.click()}
                style={{
                  background: 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
                  border: 'none'
                }}
              >
                {uploading ? '批量上传中...' : '上传开具发票文件（可多选）'}
              </Button>
              <input
                type="file"
                accept="image/*,.pdf"
                multiple
                onChange={(e) => { const files = e.target.files; if (files && files.length > 0) handleBatchInvoiceFileUpload(files, 'issued'); }}
                style={{ display: 'none' }}
                id="issued-invoice-files-upload"
              />
              <Button
                type="default"
                icon={<FileImageOutlined />}
                onClick={() => document.getElementById('issued-invoice-files-upload')?.click()}
                style={{
                  background: 'linear-gradient(135deg, #fa8c16 0%, #d46b08 100%)',
                  border: 'none',
                  color: 'white'
                }}
              >
                上传发票文件(可多选)
              </Button>
            </Space>
          </div>

          {uploadResult && (
            <Card title="开具发票批量导入结果" style={{ marginTop: 16 }}>
              <Row gutter={16} style={{ marginBottom: 16 }}>
                <Col span={6}>
                  <Statistic title="处理文件数" value={uploadResult.totalFiles} />
                </Col>
                <Col span={6}>
                  <Statistic title="成功导入" value={uploadResult.totalSuccess} valueStyle={{ color: '#3f8600' }} />
                </Col>
                <Col span={6}>
                  <Statistic title="失败记录" value={uploadResult.totalFailure} valueStyle={{ color: '#cf1322' }} />
                </Col>
                <Col span={6}>
                  <Statistic title="重复记录" value={uploadResult.totalDuplicate} valueStyle={{ color: '#fa8c16' }} />
                </Col>
              </Row>

              <Title level={5}>文件处理详情</Title>
              <List
                size="small"
                dataSource={uploadResult.files}
                renderItem={(fileResult: any) => (
                  <List.Item
                    actions={[
                      fileResult.success && fileResult.errors && fileResult.errors.length > 0 && (
                        <Button
                          type="link"
                          size="small"
                          onClick={() => {
                            Modal.info({
                              title: `${fileResult.fileName} - 详细错误信息`,
                              width: 800,
                              content: (
                                <div>
                                  <Text type="secondary">共 {fileResult.errors.length} 条错误记录：</Text>
                                  <List
                                    size="small"
                                    dataSource={fileResult.errors}
                                    style={{ marginTop: 16, maxHeight: 400, overflow: 'auto' }}
                                    renderItem={(error: any, index: number) => (
                                      <List.Item>
                                        <div>
                                          <Text type="danger">
                                            <strong>第 {error.row} 行:</strong> {error.message}
                                          </Text>
                                          {error.field && error.value && (
                                            <div style={{ marginTop: 4, fontSize: '12px', color: '#666' }}>
                                              <Text type="secondary">
                                                字段: {error.field} | 值: {error.value}
                                              </Text>
                                            </div>
                                          )}
                                        </div>
                                      </List.Item>
                                    )}
                                  />
                                </div>
                              )
                            });
                          }}
                        >
                          查看错误详情
                        </Button>
                      )
                    ].filter(Boolean)}
                  >
                    <List.Item.Meta
                      title={
                        <Space>
                          <span>{fileResult.fileName}</span>
                          {fileResult.success ? (
                            <Tag color="green">成功</Tag>
                          ) : (
                            <Tag color="red">失败</Tag>
                          )}
                        </Space>
                      }
                      description={
                        fileResult.success ? (
                          <Space>
                            <Text>成功: {fileResult.successCount}</Text>
                            <Text>失败: {fileResult.failureCount}</Text>
                            <Text>重复: {fileResult.duplicateCount}</Text>
                            {fileResult.errors && fileResult.errors.length > 0 && (
                              <Text type="warning">有 {fileResult.errors.length} 条错误记录</Text>
                            )}
                          </Space>
                        ) : (
                          <Text type="danger">{fileResult.error}</Text>
                        )
                      }
                    />
                  </List.Item>
                )}
              />
            </Card>
          )}
              </Space>
            )
          },
          {
            key: '2',
            label: (
              <span style={{ fontSize: '16px', fontWeight: 'bold' }}>
                📋 Excel导入取得发票
              </span>
            ),
            children: (
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <Title level={4}>导入步骤</Title>
            <ol>
              <li>下载取得发票Excel模板</li>
              <li>按照模板格式填写取得发票数据</li>
              <li>上传填写好的Excel文件（支持同时选择多个文件进行批量导入）</li>
            </ol>
          </div>

          <div>
            <Space>
              <Button
                type="primary"
                icon={<DownloadOutlined />}
                onClick={handleDownloadReceivedTemplate}
                style={{
                  background: 'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)',
                  border: 'none'
                }}
              >
                下载取得发票模板
              </Button>
              <input
                type="file"
                accept=".xlsx,.xls"
                multiple
                onChange={(e) => {
                  const files = e.target.files;
                  if (files && files.length > 0) {
                    // 检查文件名是否包含"取得"
                    const validFiles = Array.from(files) ;// Array.from(files).filter(file => file.name.includes('取得'));
                    // const invalidFiles = Array.from(files).filter(file => !file.name.includes('取得'));

                    // if (invalidFiles.length > 0) {
                    //   message.error(`文件名称必须包含"取得"，请选择正确的取得发票文件。以下文件已跳过：${invalidFiles.map(f => f.name).join(', ')}`);
                    // }
                    //
                    // 只处理符合要求的文件
                    const fileList = new DataTransfer();
                    validFiles.forEach(file => fileList.items.add(file));
                    handleReceivedUpload(fileList.files);
                  }
                  e.target.value = ''; // 清空文件选择，允许重新选择相同文件
                }}
                style={{ display: 'none' }}
                id="received-excel-upload"
              />
              <Button
                type="primary"
                icon={<UploadOutlined />}
                loading={receivedUploading}
                onClick={() => document.getElementById('received-excel-upload')?.click()}
                style={{
                  background: 'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)',
                  border: 'none'
                }}
              >
                {receivedUploading ? '批量上传中...' : '上传取得发票文件（可多选）'}
              </Button>
              <input
                type="file"
                accept="image/*,.pdf"
                multiple
                onChange={(e) => { const files = e.target.files; if (files && files.length > 0) handleBatchInvoiceFileUpload(files, 'received'); }}
                style={{ display: 'none' }}
                id="received-invoice-files-upload"
              />
              <Button
                type="default"
                icon={<FileImageOutlined />}
                onClick={() => document.getElementById('received-invoice-files-upload')?.click()}
                style={{
                  background: 'linear-gradient(135deg, #fa8c16 0%, #d46b08 100%)',
                  border: 'none',
                  color: 'white'
                }}
              >
                上传发票文件(可多选)
              </Button>
            </Space>
          </div>

          {receivedUploadResult && (
            <Card title="取得发票批量导入结果" style={{ marginTop: 16 }}>
              <Row gutter={16} style={{ marginBottom: 16 }}>
                <Col span={6}>
                  <Statistic title="处理文件数" value={receivedUploadResult.totalFiles} />
                </Col>
                <Col span={6}>
                  <Statistic title="成功导入" value={receivedUploadResult.totalSuccess} valueStyle={{ color: '#3f8600' }} />
                </Col>
                <Col span={6}>
                  <Statistic title="失败记录" value={receivedUploadResult.totalFailure} valueStyle={{ color: '#cf1322' }} />
                </Col>
                <Col span={6}>
                  <Statistic title="重复记录" value={receivedUploadResult.totalDuplicate} valueStyle={{ color: '#fa8c16' }} />
                </Col>
              </Row>

              <Title level={5}>文件处理详情</Title>
              <List
                size="small"
                dataSource={receivedUploadResult.files}
                renderItem={(fileResult: any) => (
                  <List.Item
                    actions={[
                      fileResult.success && fileResult.errors && fileResult.errors.length > 0 && (
                        <Button
                          type="link"
                          size="small"
                          onClick={() => {
                            Modal.info({
                              title: `${fileResult.fileName} - 详细错误信息`,
                              width: 800,
                              content: (
                                <div>
                                  <Text type="secondary">共 {fileResult.errors.length} 条错误记录：</Text>
                                  <List
                                    size="small"
                                    dataSource={fileResult.errors}
                                    style={{ marginTop: 16, maxHeight: 400, overflow: 'auto' }}
                                    renderItem={(error: any, index: number) => (
                                      <List.Item>
                                        <div>
                                          <Text type="danger">
                                            <strong>第 {error.row} 行:</strong> {error.message}
                                          </Text>
                                          {error.field && error.value && (
                                            <div style={{ marginTop: 4, fontSize: '12px', color: '#666' }}>
                                              <Text type="secondary">
                                                字段: {error.field} | 值: {error.value}
                                              </Text>
                                            </div>
                                          )}
                                        </div>
                                      </List.Item>
                                    )}
                                  />
                                </div>
                              )
                            });
                          }}
                        >
                          查看错误详情
                        </Button>
                      )
                    ].filter(Boolean)}
                  >
                    <List.Item.Meta
                      title={
                        <Space>
                          <span>{fileResult.fileName}</span>
                          {fileResult.success ? (
                            <Tag color="green">成功</Tag>
                          ) : (
                            <Tag color="red">失败</Tag>
                          )}
                        </Space>
                      }
                      description={
                        fileResult.success ? (
                          <Space>
                            <Text>成功: {fileResult.successCount}</Text>
                            <Text>失败: {fileResult.failureCount}</Text>
                            <Text>重复: {fileResult.duplicateCount}</Text>
                            {fileResult.errors && fileResult.errors.length > 0 && (
                              <Text type="warning">有 {fileResult.errors.length} 条错误记录</Text>
                            )}
                          </Space>
                        ) : (
                          <Text type="danger">{fileResult.error}</Text>
                        )
                      }
                    />
                  </List.Item>
                )}
              />
            </Card>
          )}
              </Space>
            )
          }
        ]}
      />
    </div>
  );
};

// 生成随机密码的工具函数
const generateRandomPassword = () => {
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const numbers = '0123456789';
  const allChars = uppercase + lowercase + numbers;

  let password = '';
  // 确保至少包含一个大写字母、一个小写字母和一个数字
  password += uppercase[Math.floor(Math.random() * uppercase.length)];
  password += lowercase[Math.floor(Math.random() * lowercase.length)];
  password += numbers[Math.floor(Math.random() * numbers.length)];

  // 生成剩余的13个字符
  for (let i = 3; i < 16; i++) {
    password += allChars[Math.floor(Math.random() * allChars.length)];
  }

  // 打乱密码字符顺序
  return password.split('').sort(() => Math.random() - 0.5).join('');
};

// 主布局组件
const MainLayout: React.FC<{ onLogout: () => void }> = ({ onLogout }) => {
  const [collapsed, setCollapsed] = useState(false);
  const [userInfo, setUserInfo] = useState<any>(null);
  const [userModalVisible, setUserModalVisible] = useState(false);
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [menuItems, setMenuItems] = useState<any[]>([]);
  const navigate = useNavigate();
  const location = useLocation();
  const [passwordForm] = Form.useForm();

  // 页面标题映射
  const pageTitleMap: { [key: string]: string } = {
    '/': '仪表板',
    '/dashboard': '仪表板',
    '/companies': '公司管理',
    '/invoices': '开具发票',
    '/received-invoices': '取得发票',
    '/import': 'Excel导入',
    '/users': '用户管理',
    '/operation-logs': '操作日志',
    '/reports': '报表中心',
    '/reports/quarterly-summary': '季度开票汇总',
    '/reports/company-summary': '公司开票汇总',
    '/reports/invoice-count-summary': '发票张数汇总',
    '/reports/user-summary': '用户开票汇总',
    '/reports/relations': '开票关系'
  };

  // 更新页面标题
  const updatePageTitle = (pathname: string) => {
    const pageTitle = pageTitleMap[pathname] || '发票管理系统';
    document.title = `发票管理-${pageTitle}`;
  };

  useEffect(() => {
    fetchUserInfo();
    fetchUserMenus();
  }, []);

  // 监听路由变化，更新页面标题
  useEffect(() => {
    updatePageTitle(location.pathname);
  }, [location.pathname]);

  const fetchUserInfo = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        console.error('未找到登录令牌');
        return;
      }

      const response = await api.get('/api/auth/me');
      if (response.data.success) {
        const userData = response.data.data;
        setUserInfo({
          id: userData.id,
          name: userData.name,
          email: userData.email,
          role: getRoleDisplayName(userData.role),
          department: userData.department || '未设置',
          phone: userData.phone || '未设置',
          lastLogin: userData.lastLogin || new Date().toISOString(),
          createdAt: userData.createdAt
        });
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      // 如果获取失败，可能是token过期，跳转到登录页
      if (error.response?.status === 401) {
        localStorage.removeItem('token');
        onLogout();
      }
    }
  };

  const getRoleDisplayName = (role: string) => {
    const roleMap = {
      'ADMIN': '系统管理员',
      'FINANCE': '财务人员',
      'BUSINESS': '业务人员',
      'AUDITOR': '审计人员',
      'USER': '普通用户'
    };
    return roleMap[role] || role;
  };

  const fetchUserMenus = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        console.error('未找到登录令牌');
        return;
      }

      const response = await api.get('/api/menus/user/current/accessible');

      if (response.data.success) {
        const menus = response.data.data;
        const formattedMenus = formatMenusForAntd(menus);
        setMenuItems(formattedMenus);
      }
    } catch (error) {
      console.error('获取用户菜单失败:', error);
      // 如果获取失败，只显示基本菜单（仪表板）
      setMenuItems([
        { key: '/dashboard', icon: <DashboardOutlined />, label: '仪表板' }
      ]);
    }
  };

  // 处理生成随机密码
  const handleGeneratePassword = () => {
    const randomPassword = generateRandomPassword();
    passwordForm.setFieldsValue({
      newPassword: randomPassword,
      confirmPassword: randomPassword
    });
    message.success(`已生成16位随机密码`);
  };

  // 修改密码处理函数
  const handleChangePassword = async () => {
    try {
      const values = await passwordForm.validateFields();
      await api.put('/api/auth/change-password', {
        currentPassword: values.currentPassword,
        newPassword: values.newPassword
      });
      message.success('密码修改成功！');
      setPasswordModalVisible(false);
      passwordForm.resetFields();
    } catch (error: any) {
      console.error('修改密码失败:', error);
      message.error(error.response?.data?.message || '修改密码失败');
    }
  };

  const formatMenusForAntd = (menus: any[]): any[] => {
    const iconMap = {
      'DashboardOutlined': <DashboardOutlined />,
      'BankOutlined': <BankOutlined />,
      'FileTextOutlined': <FileTextOutlined />,
      'ImportOutlined': <ImportOutlined />,
      'UserOutlined': <UserOutlined />,
      'BarChartOutlined': <BarChartOutlined />,
      'NodeIndexOutlined': <SearchOutlined />,
    };

    return menus.map(menu => {
      const menuItem: any = {
        key: menu.path || `/${menu.key}`,
        icon: iconMap[menu.icon] || <FileTextOutlined />,
        label: menu.name,
      };

      // 只有当确实有子菜单时才添加 children 属性
      if (menu.children && menu.children.length > 0) {
        menuItem.children = formatMenusForAntd(menu.children);
      }

      return menuItem;
    });
  };

  const getDefaultMenus = () => [
    { key: '/dashboard', icon: <DashboardOutlined />, label: '仪表板' },
    { key: '/companies', icon: <BankOutlined />, label: '公司管理' },
    { key: '/invoices', icon: <FileTextOutlined />, label: '开具发票' },
    { key: '/received-invoices', icon: <FileTextOutlined />, label: '取得发票' },
    { key: '/import', icon: <ImportOutlined />, label: 'Excel导入' },
    { key: '/users', icon: <UserOutlined />, label: '用户管理' },
    {
      key: '/reports',
      icon: <BarChartOutlined />,
      label: '报表',
      children: [
        { key: '/reports/quarterly-summary', label: '季度开票汇总' },
        { key: '/reports/company-summary', label: '公司开票汇总' },
        { key: '/reports/invoice-count-summary', label: '发票张数汇总' },
        { key: '/reports/user-summary', label: '用户开票汇总' },
        { key: '/reports/relations', label: '开票关系' },
      ],
    },
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider trigger={null} collapsible collapsed={collapsed}>
        <div style={{ height: 64, display: 'flex', alignItems: 'center', justifyContent: 'center', color: 'white', fontSize: '18px', fontWeight: 'bold' }}>
          {collapsed ? '发票' : '发票管理系统'}
        </div>
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[location.pathname]}
          defaultOpenKeys={location.pathname.startsWith('/reports') ? ['/reports'] : []}
          items={menuItems}
          onClick={({ key }) => navigate(key)}
        />
      </Sider>
      <Layout>
        <Header style={{ padding: '0 16px', background: '#fff', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Button type="text" onClick={() => setCollapsed(!collapsed)} style={{ fontSize: '16px', width: 64, height: 64 }}>
            {collapsed ? '>' : '<'}
          </Button>
          <Space>
            <Dropdown
              menu={{
                items: [
                  {
                    key: 'userInfo',
                    label: '查看详细信息',
                    icon: <UserOutlined />,
                    onClick: () => setUserModalVisible(true)
                  },
                  {
                    key: 'changePassword',
                    label: '修改密码',
                    icon: <LockOutlined />,
                    onClick: () => setPasswordModalVisible(true)
                  },
                  {
                    type: 'divider'
                  },
                  {
                    key: 'logout',
                    label: '退出登录',
                    icon: <LockOutlined />,
                    onClick: onLogout
                  }
                ]
              }}
              trigger={['click']}
            >
              <Button type="text" style={{ height: 'auto', padding: '8px 12px' }}>
                <Space>
                  <UserOutlined />
                  <span>{userInfo?.name || '用户'}</span>
                </Space>
              </Button>
            </Dropdown>
          </Space>
        </Header>
        <Content style={{ margin: '16px', padding: 24, background: '#fff' }}>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/companies" element={<Companies userInfo={userInfo} />} />
            <Route path="/invoices" element={<Invoices />} />
            <Route path="/received-invoices" element={<ReceivedInvoices />} />
            <Route path="/import" element={<ImportInvoices />} />
            <Route path="/users" element={<UserManagement />} />
            <Route path="/operation-logs" element={<OperationLogs />} />
            <Route path="/reports/*" element={<Reports />} />
          </Routes>
        </Content>
      </Layout>

      {/* 用户详细信息模态框 */}
      <Modal
        title="用户详细信息"
        open={userModalVisible}
        onCancel={() => setUserModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setUserModalVisible(false)}>关闭</Button>
        ]}
        width={800}
      >
        {userInfo && (
          <div>
            {/* 基本信息 */}
            <Card title="基本信息" size="small" style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <div style={{ display: 'flex', marginBottom: 12 }}>
                    <Text type="secondary" style={{ width: '80px', flexShrink: 0 }}>用户名称:</Text>
                    <Text strong style={{ fontSize: '16px' }}>{userInfo.name}</Text>
                  </div>
                </Col>
                <Col span={12}>
                  <div style={{ display: 'flex', marginBottom: 12 }}>
                    <Text type="secondary" style={{ width: '80px', flexShrink: 0 }}>邮箱地址:</Text>
                    <Text strong>{userInfo.email}</Text>
                  </div>
                </Col>
                <Col span={12}>
                  <div style={{ display: 'flex', marginBottom: 12 }}>
                    <Text type="secondary" style={{ width: '80px', flexShrink: 0 }}>用户角色:</Text>
                    <Tag color="blue">{userInfo.role}</Tag>
                  </div>
                </Col>
                <Col span={12}>
                  <div style={{ display: 'flex', marginBottom: 12 }}>
                    <Text type="secondary" style={{ width: '80px', flexShrink: 0 }}>所属部门:</Text>
                    <Text>{userInfo.department}</Text>
                  </div>
                </Col>
              </Row>
            </Card>

            {/* 联系信息 */}
            <Card title="联系信息" size="small" style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <div style={{ display: 'flex', marginBottom: 12 }}>
                    <Text type="secondary" style={{ width: '80px', flexShrink: 0 }}>联系电话:</Text>
                    <Text>{userInfo.phone}</Text>
                  </div>
                </Col>
                <Col span={12}>
                  <div style={{ display: 'flex', marginBottom: 12 }}>
                    <Text type="secondary" style={{ width: '80px', flexShrink: 0 }}>用户ID:</Text>
                    <Text type="secondary" style={{ wordBreak: 'break-all', fontSize: '12px' }}>{userInfo.id}</Text>
                  </div>
                </Col>
              </Row>
            </Card>

            {/* 登录信息 */}
            <Card title="登录信息" size="small">
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <div style={{ display: 'flex', marginBottom: 12 }}>
                    <Text type="secondary" style={{ width: '80px', flexShrink: 0 }}>最后登录:</Text>
                    <Text>{new Date(userInfo.lastLogin).toLocaleString()}</Text>
                  </div>
                </Col>
                <Col span={12}>
                  <div style={{ display: 'flex', marginBottom: 12 }}>
                    <Text type="secondary" style={{ width: '80px', flexShrink: 0 }}>注册时间:</Text>
                    <Text>{new Date(userInfo.createdAt).toLocaleDateString()}</Text>
                  </div>
                </Col>
              </Row>
            </Card>
          </div>
        )}
      </Modal>

      {/* 修改密码弹窗 */}
      <Modal
        title="修改密码"
        open={passwordModalVisible}
        onCancel={() => {
          setPasswordModalVisible(false);
          passwordForm.resetFields();
        }}
        width={400}
        footer={[
          <Button
            key="generate"
            type="default"
            onClick={handleGeneratePassword}
            style={{ float: 'left' }}
          >
            随机密码
          </Button>,
          <Button
            key="cancel"
            onClick={() => {
              setPasswordModalVisible(false);
              passwordForm.resetFields();
            }}
          >
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={handleChangePassword}
          >
            确定
          </Button>
        ]}
      >
        <Form
          form={passwordForm}
          layout="vertical"
          style={{ marginTop: 16 }}
        >
          <Form.Item
            label="当前密码"
            name="currentPassword"
            rules={[
              { required: true, message: '请输入当前密码' }
            ]}
          >
            <Input.Password placeholder="请输入当前密码" />
          </Form.Item>
          <Form.Item
            label="新密码"
            name="newPassword"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 6, message: '密码至少6个字符' }
            ]}
          >
            <Input.Password
              placeholder="请输入新密码（至少6个字符）"
            />
          </Form.Item>
          <Form.Item
            label="确认新密码"
            name="confirmPassword"
            dependencies={['newPassword']}
            rules={[
              { required: true, message: '请确认新密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('newPassword') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input.Password
              placeholder="请再次输入新密码"
            />
          </Form.Item>
        </Form>
      </Modal>
    </Layout>
  );
};

export default function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      setIsAuthenticated(true);
    }
    setIsLoading(false); // 认证检查完成
  }, []);

  // 在认证检查完成之前显示加载状态
  if (isLoading) {
    return (
      <ConfigProvider locale={zhCN}>
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          fontSize: '16px'
        }}>
          加载中...
        </div>
      </ConfigProvider>
    );
  }

  return (
    <ConfigProvider locale={zhCN}>
      <AntApp>
        <Router>
          <Routes>
            <Route
              path="/login"
              element={
                isAuthenticated ?
                <Navigate to="/dashboard" replace /> :
                <Login onLogin={() => setIsAuthenticated(true)} />
              }
            />
            <Route
              path="/*"
              element={
                isAuthenticated ?
                <MainLayout onLogout={() => { localStorage.removeItem('token'); setIsAuthenticated(false); }} /> :
                <Navigate to="/login" replace />
              }
            />
          </Routes>
        </Router>
      </AntApp>
    </ConfigProvider>
  );
}
