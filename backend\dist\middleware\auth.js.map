{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": ";;;;;;AACA,gEAA+B;AAC/B,2CAAwD;AACxD,iDAA0C;AAE1C,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;AAWlC,iBAAiB;AACV,MAAM,iBAAiB,GAAG,KAAK,EAAE,MAAc,EAAqB,EAAE;IAC3E,IAAI,CAAC;QACH,UAAU;QACV,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;YACrB,MAAM,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;SACvB,CAAC,CAAC;QAEH,kBAAkB;QAClB,IAAI,IAAI,EAAE,IAAI,KAAK,OAAO,EAAE,CAAC;YAC3B,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;gBACjD,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;gBACzB,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE;aACrB,CAAC,CAAC;YACH,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACrC,CAAC;QAED,uBAAuB;QACvB,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;gBACtD,KAAK,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE;gBACzB,MAAM,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;aAC5B,CAAC,CAAC;YAEH,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC7B,OAAO,aAAa,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;YAC/C,CAAC;QACH,CAAC;QAAC,OAAO,UAAU,EAAE,CAAC;YACpB,aAAa;QACf,CAAC;QAED,4BAA4B;QAC5B,OAAO,EAAE,CAAC;IACZ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;QACpC,OAAO,EAAE,CAAC;IACZ,CAAC;AACH,CAAC,CAAC;AArCW,QAAA,iBAAiB,qBAqC5B;AAEF,WAAW;AACJ,MAAM,YAAY,GAAG,KAAK,EAC/B,GAAyB,EACzB,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QAElE,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,uBAAQ,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QACpC,CAAC;QAED,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,UAAW,CAAQ,CAAC;QAElE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,MAAM,EAAE;YAC7B,MAAM,EAAE;gBACN,EAAE,EAAE,IAAI;gBACR,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;gBACX,IAAI,EAAE,IAAI;gBACV,IAAI,EAAE,IAAI;gBACV,MAAM,EAAE,IAAI;aACb;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YACtC,MAAM,IAAI,uBAAQ,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;QACxC,CAAC;QAED,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;QAChB,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AAnCW,QAAA,YAAY,gBAmCvB;AAEF,UAAU;AACH,MAAM,SAAS,GAAG,CAAC,GAAG,KAAiB,EAAE,EAAE;IAChD,OAAO,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACtE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,OAAO,IAAI,CAAC,IAAI,uBAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC,IAAI,uBAAQ,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAZW,QAAA,SAAS,aAYpB;AAEF,0BAA0B;AACnB,MAAM,kBAAkB,GAAG,CAAC,gBAAwB,QAAQ,EAAE,EAAE;IACrE,OAAO,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QAC5E,IAAI,CAAC;YACH,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;gBACd,OAAO,IAAI,CAAC,IAAI,uBAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;YAC1C,CAAC;YAED,cAAc;YACd,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBAC9B,OAAO,IAAI,EAAE,CAAC;YAChB,CAAC;YAED,MAAM,UAAU,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,OAAO,IAAI,CAAC,IAAI,uBAAQ,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;YAC3C,CAAC;YAED,sBAAsB;YACtB,qBAAqB;YACrB,iBAAiB;YAEjB,IAAI,EAAE,CAAC;QACT,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,CAAC;QACd,CAAC;IACH,CAAC,CAAC;AACJ,CAAC,CAAC;AA1BW,QAAA,kBAAkB,sBA0B7B"}