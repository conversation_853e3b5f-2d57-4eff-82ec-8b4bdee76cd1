const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

async function createTables() {
  let connection;

  try {
    // 从环境变量解析数据库连接信息
    const databaseUrl = process.env.DATABASE_URL;
    if (!databaseUrl) {
      throw new Error('DATABASE_URL environment variable is not set');
    }

    // 解析数据库URL
    const url = new URL(databaseUrl);
    const connectionConfig = {
      host: url.hostname,
      port: parseInt(url.port) || 3306,
      user: url.username,
      password: url.password,
      database: url.pathname.slice(1), // 移除开头的 '/'
      multipleStatements: true // 允许执行多条SQL语句
    };

    console.log('正在连接数据库...');
    console.log(`主机: ${connectionConfig.host}:${connectionConfig.port}`);
    console.log(`数据库: ${connectionConfig.database}`);

    // 创建数据库连接
    connection = await mysql.createConnection(connectionConfig);
    console.log('数据库连接成功！');

    // 读取SQL文件
    const sqlFilePath = path.join(__dirname, 'create_received_invoice_tables.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

    console.log('正在创建取得发票相关表...');

    // 直接执行完整的SQL脚本
    try {
      await connection.execute(sqlContent);
      console.log('✓ SQL脚本执行成功');
    } catch (error) {
      console.error('✗ SQL脚本执行失败:', error.message);

      // 如果整体执行失败，尝试分别执行每个CREATE TABLE语句
      console.log('尝试分别执行每个CREATE TABLE语句...');

      const createTableStatements = sqlContent.match(/CREATE TABLE[^;]+;/gi) || [];

      for (let i = 0; i < createTableStatements.length; i++) {
        const statement = createTableStatements[i];
        try {
          await connection.execute(statement);
          console.log(`✓ 创建表 ${i + 1} 成功`);
        } catch (createError) {
          console.error(`✗ 创建表 ${i + 1} 失败:`, createError.message);
        }
      }
    }

    console.log('✅ 取得发票相关表创建完成！');
    console.log('已创建以下表：');
    console.log('  - received_invoices (取得发票主表)');
    console.log('  - received_invoice_items (取得发票明细表)');
    console.log('  - received_invoice_attachments (取得发票附件表)');
    console.log('  - received_invoice_audit_logs (取得发票审计日志表)');

  } catch (error) {
    console.error('❌ 执行失败:', error.message);

    if (error.code === 'ECONNREFUSED') {
      console.error('无法连接到数据库，请检查：');
      console.error('1. 数据库服务是否正在运行');
      console.error('2. 连接参数是否正确');
      console.error('3. 网络连接是否正常');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('数据库访问被拒绝，请检查用户名和密码');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.error('数据库不存在，请检查数据库名称');
    }

    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

// 执行脚本
createTables();
