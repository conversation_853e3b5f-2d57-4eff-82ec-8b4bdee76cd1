"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const client_1 = require("@prisma/client");
const joi_1 = __importDefault(require("joi"));
const errorHandler_1 = require("../middleware/errorHandler");
const auth_1 = require("../middleware/auth");
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const multer_1 = __importDefault(require("multer"));
const XLSX = __importStar(require("xlsx"));
const router = express_1.default.Router();
const prisma = new client_1.PrismaClient();
// 应用认证中间件到所有路由
router.use(auth_1.authenticate);
// 验证schemas
const createInvoiceSchema = joi_1.default.object({
    invoiceNumber: joi_1.default.string().required().messages({
        'any.required': '发票号码是必填项',
    }),
    invoiceCode: joi_1.default.string().required().messages({
        'any.required': '发票代码是必填项',
    }),
    invoiceDate: joi_1.default.date().required().messages({
        'any.required': '开票日期是必填项',
    }),
    amount: joi_1.default.number().positive().required().messages({
        'number.positive': '金额必须大于0',
        'any.required': '金额是必填项',
    }),
    taxAmount: joi_1.default.number().min(0).required().messages({
        'number.min': '税额不能为负数',
        'any.required': '税额是必填项',
    }),
    totalAmount: joi_1.default.number().positive().required().messages({
        'number.positive': '价税合计必须大于0',
        'any.required': '价税合计是必填项',
    }),
    buyerName: joi_1.default.string().required().messages({
        'any.required': '购买方名称是必填项',
    }),
    buyerTaxId: joi_1.default.string().required().messages({
        'any.required': '购买方税号是必填项',
    }),
    buyerAddress: joi_1.default.string().optional(),
    buyerPhone: joi_1.default.string().optional(),
    buyerBank: joi_1.default.string().optional(),
    sellerName: joi_1.default.string().required().messages({
        'any.required': '销售方名称是必填项',
    }),
    sellerTaxId: joi_1.default.string().required().messages({
        'any.required': '销售方税号是必填项',
    }),
    sellerAddress: joi_1.default.string().optional(),
    sellerPhone: joi_1.default.string().optional(),
    sellerBank: joi_1.default.string().optional(),
    invoiceType: joi_1.default.string().valid(...Object.values(client_1.InvoiceType)).required(),
    status: joi_1.default.string().valid(...Object.values(client_1.InvoiceStatus)).default('NORMAL'),
    remark: joi_1.default.string().optional(),
    projectName: joi_1.default.string().optional(),
    department: joi_1.default.string().optional(),
    costCenter: joi_1.default.string().optional(),
    companyId: joi_1.default.string().required().messages({
        'any.required': '公司ID是必填项',
    }),
    invoiceItems: joi_1.default.array().items(joi_1.default.object({
        itemName: joi_1.default.string().required(),
        specification: joi_1.default.string().optional(),
        unit: joi_1.default.string().optional(),
        quantity: joi_1.default.number().positive().required(),
        unitPrice: joi_1.default.number().min(0).required(),
        amount: joi_1.default.number().min(0).required(),
        taxRate: joi_1.default.number().min(0).max(1).required(),
        taxAmount: joi_1.default.number().min(0).required(),
    })).optional(),
});
const updateInvoiceSchema = createInvoiceSchema.fork(['invoiceNumber', 'invoiceCode', 'invoiceDate', 'amount', 'taxAmount', 'totalAmount',
    'buyerName', 'buyerTaxId', 'sellerName', 'sellerTaxId', 'invoiceType', 'companyId'], (schema) => schema.optional());
const querySchema = joi_1.default.object({
    page: joi_1.default.number().integer().min(1).default(1),
    pageSize: joi_1.default.number().integer().min(1).max(100).default(10),
    search: joi_1.default.string().optional(),
    companyId: joi_1.default.string().optional(),
    year: joi_1.default.number().integer().min(2000).max(2100).optional(),
    invoiceType: joi_1.default.string().valid(...Object.values(client_1.InvoiceType)).optional(),
    status: joi_1.default.string().valid(...Object.values(client_1.InvoiceStatus)).optional(),
    verificationStatus: joi_1.default.string().valid(...Object.values(client_1.VerificationStatus)).optional(),
    startDate: joi_1.default.date().optional(),
    endDate: joi_1.default.date().optional(),
    sellerCompanies: joi_1.default.alternatives().try(joi_1.default.array().items(joi_1.default.string()), joi_1.default.string()).optional(),
    onlyShowPartnerCompanies: joi_1.default.string().valid('true', 'false').optional(),
    sortBy: joi_1.default.string().valid('invoiceDate', 'totalAmount', 'createdAt').default('createdAt'),
    sortOrder: joi_1.default.string().valid('asc', 'desc').default('desc'),
});
// 配置multer用于文件上传
const upload = (0, multer_1.default)({
    storage: multer_1.default.memoryStorage(),
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
    },
    fileFilter: (req, file, cb) => {
        if (file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
            file.mimetype === 'application/vnd.ms-excel') {
            cb(null, true);
        }
        else {
            cb(new Error('只支持Excel文件格式'));
        }
    },
});
// 修复发票公司ID - 必须在动态路由之前
router.post('/fix-company-ids', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        // 获取所有发票
        const invoices = await prisma.invoice.findMany({
            select: {
                id: true,
                sellerName: true,
                sellerTaxId: true,
                companyId: true,
            },
        });
        let updatedCount = 0;
        let errorCount = 0;
        const errors = [];
        for (const invoice of invoices) {
            try {
                // 根据销售方信息查找正确的公司
                const company = await prisma.company.findFirst({
                    where: {
                        OR: [
                            { name: invoice.sellerName },
                            { taxId: invoice.sellerTaxId }
                        ]
                    }
                });
                if (company && company.id !== invoice.companyId) {
                    // 更新发票的公司ID
                    await prisma.invoice.update({
                        where: { id: invoice.id },
                        data: { companyId: company.id }
                    });
                    updatedCount++;
                }
            }
            catch (error) {
                errorCount++;
                errors.push(`发票 ${invoice.id} 更新失败: ${error instanceof Error ? error.message : '未知错误'}`);
            }
        }
        res.json({
            success: true,
            data: {
                totalInvoices: invoices.length,
                updatedCount,
                errorCount,
                errors: errors.slice(0, 10), // 最多返回10个错误
            },
            message: `修复完成：更新了${updatedCount}条发票，${errorCount}条失败`,
        });
    }
    catch (error) {
        console.error('修复发票公司ID失败:', error);
        throw new errorHandler_1.AppError('修复发票公司ID失败', 500);
    }
}));
// 下载Excel模板 - 必须在动态路由之前
router.get('/template', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const templatePath = path_1.default.join(__dirname, '../../templates/invoice-template.xlsx');
    if (!fs_1.default.existsSync(templatePath)) {
        throw new errorHandler_1.AppError('模板文件不存在', 404);
    }
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename="invoice-template.xlsx"');
    const fileStream = fs_1.default.createReadStream(templatePath);
    fileStream.pipe(res);
}));
// Excel导入 - 必须在动态路由之前
router.post('/import', upload.single('file'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.file) {
        throw new errorHandler_1.AppError('请选择要上传的Excel文件', 400);
    }
    console.log('原始文件名:', req.file.filename);
    // 处理中文文件名编码问题
    const decodeFileName = (filename) => {
        try {
            // 方法1: 尝试从latin1编码转换为utf8
            const decoded1 = Buffer.from(filename, 'latin1').toString('utf8');
            if (decoded1.includes('开具') || decoded1.includes('取得')) {
                return decoded1;
            }
            // 方法2: 尝试URL解码
            const decoded2 = decodeURIComponent(filename);
            if (decoded2.includes('开具') || decoded2.includes('取得')) {
                return decoded2;
            }
            // 方法3: 尝试直接使用原始文件名
            if (filename.includes('开具') || filename.includes('取得')) {
                return filename;
            }
            // 如果都不包含中文，返回第一个解码结果
            return decoded1;
        }
        catch (error) {
            console.error('文件名解码失败:', error);
            return filename;
        }
    };
    const originalFileName = decodeFileName(req.file.originalname);
    console.log('原始文件名:', req.file.originalname);
    console.log('处理后文件名:', originalFileName);
    console.log('原始文件名字节:', Buffer.from(req.file.originalname).toString('hex'));
    console.log('处理后文件名字节:', Buffer.from(originalFileName).toString('hex'));
    if (!originalFileName || !originalFileName.includes("开具")) {
        throw new errorHandler_1.AppError('Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息', 400);
    }
    try {
        // 读取Excel文件
        const workbook = XLSX.read(req.file.buffer, { type: 'buffer' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        // 将工作表转换为JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        if (jsonData.length < 2) {
            throw new errorHandler_1.AppError('Excel文件格式不正确或没有数据', 400);
        }
        // 获取表头
        const headers = jsonData[0];
        const dataRows = jsonData.slice(1);
        // 映射Excel列到数据库字段 - 根据实际模板更新
        const fieldMapping = {
            // 基本发票信息
            '发票号码': 'invoiceNumber',
            '数电发票号码': 'invoiceCode', // 数电发票号码映射到发票代码字段
            '发票代码': 'invoiceCode',
            '开票日期': 'invoiceDate',
            // 购买方信息
            '购买方名称': 'buyerName',
            '购方识别号': 'buyerTaxId', // 实际模板中的字段名
            '购买方纳税人识别号': 'buyerTaxId',
            '购买方地址、电话': 'buyerAddress',
            // 销售方信息
            '销方名称': 'sellerName', // 实际模板中的字段名
            '销售方名称': 'sellerName',
            '销方识别号': 'sellerTaxId', // 实际模板中的字段名
            '销售方纳税人识别号': 'sellerTaxId',
            '销售方地址、电话': 'sellerAddress',
            // 金额信息
            '金额': 'amount',
            '税额': 'taxAmount',
            '价税合计': 'totalAmount',
            '发票状态': 'status',
            // 商品信息
            '货物或应税劳务名称': 'itemName', // 实际模板中的字段名
            '商品名称': 'itemName',
            '规格型号': 'specification',
            '单位': 'unit',
            '数量': 'quantity',
            '单价': 'unitPrice',
            '税率': 'taxRate',
            // 其他信息
            '开票人': 'drawer',
            '复核': 'reviewer',
            '收款人': 'payee',
            '备注': 'remarks'
        };
        // 添加调试信息
        console.log('Excel表头:', headers);
        console.log('字段映射:', fieldMapping);
        let successCount = 0;
        let failureCount = 0;
        let duplicateCount = 0;
        const errors = [];
        const newCompanyIds = []; // 记录新创建的公司ID
        // 处理每一行数据
        for (let i = 0; i < dataRows.length; i++) {
            const row = dataRows[i];
            const rowNumber = i + 2; // Excel行号（从2开始，因为第1行是表头）
            try {
                if (!row || row.length === 0 || !row.some(cell => cell !== null && cell !== undefined && cell !== '')) {
                    continue; // 跳过空行
                }
                // // 获取默认公司（如果没有指定公司）
                // const defaultCompany = await prisma.company.findFirst({
                //     where: { isActive: true },
                // });
                // if (!defaultCompany) {
                //     throw new errorHandler_1.AppError('系统中没有可用的公司，请先创建公司', 400);
                // }
                // 构建发票数据
                const invoiceData = {
                    // companyId: defaultCompany.id,
                    invoiceType: 'ORDINARY_VAT', // 默认普通发票
                    status: 'NORMAL',
                    verificationStatus: 'UNVERIFIED',
                };
                // 映射Excel数据到发票字段
                headers.forEach((header, index) => {
                    const field = fieldMapping[header];
                    if (field && row[index] !== null && row[index] !== undefined && row[index] !== '') {
                        let value = row[index];
                        // 特殊处理不同类型的字段
                        if (field === 'invoiceDate') {
                            // 处理日期格式
                            if (typeof value === 'number') {
                                // Excel日期序列号
                                const date = XLSX.SSF.parse_date_code(value);
                                value = new Date(date.y, date.m - 1, date.d);
                            }
                            else if (typeof value === 'string') {
                                value = new Date(value);
                            }
                        }
                        else if (['amount', 'taxAmount', 'totalAmount', 'quantity', 'unitPrice', 'taxRate'].includes(field)) {
                            // 处理数字字段
                            value = parseFloat(value.toString().replace(/[,，]/g, '')) || 0;
                        }
                        else if (field === 'status') {
                            // 处理状态字段
                            value = value === '正常' ? 'NORMAL' : value === '作废' ? 'CANCELLED' : 'NORMAL';
                        }
                        // 对于发票代码字段，如果已经有值就不覆盖（支持数电发票号码和发票代码二选一）
                        if (field === 'invoiceCode' && invoiceData[field]) {
                            return; // 已经有发票代码了，跳过
                        }
                        invoiceData[field] = value;
                    }
                });
                // 验证必填字段 - 发票号码和数电发票号码(发票代码)只需要有一个即可
                const hasInvoiceNumber = invoiceData.invoiceNumber && invoiceData.invoiceNumber.toString().trim();
                const hasInvoiceCode = invoiceData.invoiceCode && invoiceData.invoiceCode.toString().trim();
                if (!hasInvoiceNumber && !hasInvoiceCode) {
                    errors.push({ row: rowNumber, message: '发票号码和数电发票号码至少需要填写一个' });
                    failureCount++;
                    continue;
                }
                if (!invoiceData.buyerName) {
                    errors.push({ row: rowNumber, message: '购买方名称不能为空' });
                    failureCount++;
                    continue;
                }
                if (!invoiceData.sellerName) {
                    errors.push({ row: rowNumber, message: '销售方名称不能为空' });
                    failureCount++;
                    continue;
                }
                // 检查开具发票是否已存在 - 只在开具发票表(invoice)中检查重复
                // 注意：这里只检查开具发票表，不检查取得发票表
                let existingInvoice = null;
                if (hasInvoiceNumber && hasInvoiceCode) {
                    // 两个字段都有值，检查两个字段的组合
                    existingInvoice = await prisma.invoice.findFirst({
                        where: {
                            invoiceNumber: invoiceData.invoiceNumber,
                            invoiceCode: invoiceData.invoiceCode,
                        },
                    });
                }
                else if (hasInvoiceNumber) {
                    // 只有发票号码，检查发票号码
                    existingInvoice = await prisma.invoice.findFirst({
                        where: {
                            invoiceNumber: invoiceData.invoiceNumber,
                        },
                    });
                }
                else if (hasInvoiceCode) {
                    // 只有发票代码，检查发票代码
                    existingInvoice = await prisma.invoice.findFirst({
                        where: {
                            invoiceCode: invoiceData.invoiceCode,
                        },
                    });
                }
                if (existingInvoice) {
                    duplicateCount++;
                    continue;
                }
                // 确保必填字段有值 - 如果没有发票号码但有发票代码，使用发票代码作为发票号码
                if (!invoiceData.invoiceNumber && invoiceData.invoiceCode) {
                    invoiceData.invoiceNumber = invoiceData.invoiceCode;
                }
                // 确保有公司ID - 总是根据销售方信息查找公司
                // 尝试根据销售方信息找到对应的公司
                let company = null;
                // 首先尝试精确匹配
                if (invoiceData.sellerTaxId) {
                    company = await prisma.company.findFirst({
                        where: { taxId: invoiceData.sellerTaxId }
                    });
                }
                // 如果税号没找到，尝试公司名称匹配
                if (!company && invoiceData.sellerName) {
                    company = await prisma.company.findFirst({
                        where: { name: invoiceData.sellerName }
                    });
                }
                // // 如果还是没找到，尝试模糊匹配公司名称
                // if (!company && invoiceData.sellerName) {
                //   company = await prisma.company.findFirst({
                //     where: {
                //       name: {
                //         contains: invoiceData.sellerName.replace(/有限公司|股份有限公司|集团|公司$/g, '').trim()
                //       }
                //     }
                //   });
                // }
                if (company) {
                    invoiceData.companyId = company.id;
                    console.log(`发票 ${invoiceData.invoiceNumber} 匹配到公司: ${company.name} (${company.id})`);
                }
                else {
                    // 如果找不到公司，返回错误
                    errors.push({ row: rowNumber, message: `销售方公司未在公司管理维护，请维护${invoiceData.sellerName} 的公司信息` });
                    failureCount++;
                    continue;
                }
                // 处理税率：确保税率在正确的范围内 (0-1之间)
                let taxRate = invoiceData.taxRate || 0;
                if (taxRate > 1) {
                    // 如果税率大于1，假设是百分比形式，转换为小数
                    taxRate = taxRate / 100;
                }
                // 确保税率不超过数据库字段限制 (最大9.9999)
                if (taxRate > 9.9999) {
                    taxRate = 0.13; // 默认13%税率
                }
                // 处理必需的数值字段，确保它们不为null或undefined
                const amount = Number(invoiceData.amount) || 0;
                const taxAmount = Number(invoiceData.taxAmount) || 0;
                let quantity = Number(invoiceData.quantity) || 1;
                let unitPrice = Number(invoiceData.unitPrice) || amount; // 如果没有单价，使用金额作为单价
                const totalAmount = Number(invoiceData.totalAmount) || (amount + taxAmount);
                // 确保quantity在数据库字段范围内 (Decimal(10,4) 最大值约为999999.9999)
                if (quantity > 999999.9999) {
                    quantity = 1; // 超出范围时使用默认值1
                }
                if (quantity < 0) {
                    quantity = 1; // 负数时使用默认值1
                }
                // 确保unitPrice在数据库字段范围内 (Decimal(15,4) 最大值约为99999999999.9999)
                if (unitPrice > 99999999999.9999) {
                    unitPrice = amount; // 超出范围时使用金额
                }
                if (unitPrice < 0) {
                    unitPrice = Math.abs(unitPrice); // 负数时取绝对值
                }
                // 分离发票项目数据，为必需字段提供默认值
                const invoiceItemData = {
                    itemName: invoiceData.itemName || '未知商品',
                    specification: invoiceData.specification || null,
                    unit: invoiceData.unit || null,
                    quantity: quantity,
                    unitPrice: unitPrice,
                    amount: amount,
                    taxRate: taxRate,
                    taxAmount: taxAmount,
                    totalAmount: totalAmount
                };
                // 从发票数据中移除项目相关字段，并确保主记录的数值字段正确
                const cleanInvoiceData = { ...invoiceData };
                delete cleanInvoiceData.itemName;
                delete cleanInvoiceData.specification;
                delete cleanInvoiceData.unit;
                delete cleanInvoiceData.quantity;
                delete cleanInvoiceData.unitPrice;
                delete cleanInvoiceData.taxRate; // taxRate只保存在InvoiceItem中
                // 确保主记录的数值字段正确
                cleanInvoiceData.amount = amount;
                cleanInvoiceData.taxAmount = taxAmount;
                cleanInvoiceData.totalAmount = totalAmount;
                // 确保使用正确的公司ID（这是最重要的）
                cleanInvoiceData.companyId = invoiceData.companyId;
                // 创建发票和发票项目
                await prisma.invoice.create({
                    data: {
                        ...cleanInvoiceData,
                        invoiceItems: invoiceItemData.itemName ? {
                            create: [invoiceItemData]
                        } : undefined
                    }
                });
                successCount++;
            }
            catch (error) {
                console.error(`处理第${rowNumber}行时出错:`, error);
                errors.push({
                    row: rowNumber,
                    message: error instanceof Error ? error.message : '未知错误'
                });
                failureCount++;
            }
        }
        // 为当前登录用户分配新创建公司的权限
        if (newCompanyIds.length > 0 && req.user) {
            try {
                console.log(`开始为用户 ${req.user.id} (${req.user.name}) 分配 ${newCompanyIds.length} 个新创建公司的权限`);
                // 为当前用户分配新创建公司的权限
                let assignedCount = 0;
                for (const companyId of newCompanyIds) {
                    try {
                        // 检查权限是否已存在
                        const existingPermission = await prisma.userCompany.findUnique({
                            where: {
                                userId_companyId: {
                                    userId: req.user.id,
                                    companyId: companyId
                                }
                            }
                        });
                        if (!existingPermission) {
                            // 创建新的权限记录
                            await prisma.userCompany.create({
                                data: {
                                    userId: req.user.id,
                                    companyId: companyId
                                }
                            });
                            assignedCount++;
                            console.log(`✅ 成功为用户 ${req.user.id} 分配公司 ${companyId} 的权限`);
                        }
                        else {
                            console.log(`⚠️ 用户 ${req.user.id} 已有公司 ${companyId} 的权限，跳过`);
                        }
                    }
                    catch (permError) {
                        console.error(`❌ 为用户 ${req.user.id} 分配公司 ${companyId} 权限失败:`, permError);
                    }
                }
                console.log(`✅ 为用户 ${req.user.id} 成功分配了 ${assignedCount} 个新创建公司的权限`);
            }
            catch (error) {
                console.error('❌ 分配公司权限失败:', error);
                // 不影响导入结果，只记录错误
            }
        }
        res.json({
            success: true,
            data: {
                totalCount: dataRows.length,
                successCount,
                failureCount,
                duplicateCount,
                errors: errors.slice(0, 50), // 最多返回50个错误
                newCompaniesAssigned: newCompanyIds.length, // 分配给当前用户的新公司数量
            },
            message: `导入完成：成功${successCount}条，失败${failureCount}条，重复${duplicateCount}条${newCompanyIds.length > 0 ? `，已为您分配${newCompanyIds.length}个新公司的权限` : ''}`,
        });
    }
    catch (error) {
        console.error('Excel导入失败:', error);
        throw new errorHandler_1.AppError('Excel文件解析失败，请检查文件格式', 400);
    }
}));
// 获取发票列表
router.get('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    // 预处理查询参数，特别是sellerCompanies
    const queryParams = { ...req.query };
    // 如果sellerCompanies是字符串，将其转换为数组
    if (queryParams.sellerCompanies && typeof queryParams.sellerCompanies === 'string') {
        queryParams.sellerCompanies = [queryParams.sellerCompanies];
    }
    const { error, value } = querySchema.validate(queryParams);
    if (error) {
        console.error('查询参数验证失败:', error.details[0].message, '原始参数:', req.query);
        throw new errorHandler_1.AppError(error.details[0].message, 400);
    }
    const { page, pageSize, search, companyId, year, invoiceType, status, verificationStatus, startDate, endDate, sellerCompanies, onlyShowPartnerCompanies, sortBy, sortOrder } = value;
    const skip = (page - 1) * pageSize;
    // 构建查询条件
    const where = {
        // 只查询激活公司的发票
        company: {
            isActive: true,
        },
    };
    // 如果是普通用户，只能查看有权限的公司数据
    if (req.user?.role !== 'ADMIN') {
        const userCompanyIds = await (0, auth_1.getUserCompanyIds)(req.user.id);
        if (userCompanyIds.length === 0) {
            // 用户没有任何公司权限，返回空结果
            return res.json({
                success: true,
                data: {
                    data: [],
                    total: 0,
                    page,
                    pageSize,
                },
            });
        }
        where.companyId = { in: userCompanyIds };
    }
    if (search) {
        // 支持多公司搜索，用逗号分隔
        const searchTerms = search.split(',').map((term) => term.trim()).filter((term) => term);
        if (searchTerms.length > 1) {
            // 多个搜索词，每个词都要匹配 - 只搜索购买方、发票号码、税号、金额
            where.OR = searchTerms.flatMap((term) => {
                const conditions = [
                    { invoiceNumber: { contains: term } },
                    { buyerName: { contains: term } },
                    { buyerTaxId: { contains: term } }
                ];
                // 如果搜索词是数字，也搜索金额
                if (!isNaN(Number(term))) {
                    conditions.push({ totalAmount: { equals: Number(term) } });
                }
                return conditions;
            });
        }
        else {
            // 单个搜索词 - 只搜索购买方、发票号码、税号、金额
            const conditions = [
                { invoiceNumber: { contains: search } },
                { buyerName: { contains: search } },
                { buyerTaxId: { contains: search } }
            ];
            // 如果搜索词是数字，也搜索金额
            if (!isNaN(Number(search))) {
                conditions.push({ totalAmount: { equals: Number(search) } });
            }
            where.OR = conditions;
        }
    }
    if (companyId)
        where.companyId = companyId;
    if (invoiceType)
        where.invoiceType = invoiceType;
    if (status)
        where.status = status;
    if (verificationStatus)
        where.verificationStatus = verificationStatus;
    // 处理年度过滤
    if (year) {
        const yearStart = new Date(year, 0, 1); // 年初
        const yearEnd = new Date(year, 11, 31, 23, 59, 59); // 年末
        where.invoiceDate = {
            gte: yearStart,
            lte: yearEnd,
        };
    }
    else if (startDate || endDate) {
        where.invoiceDate = {};
        if (startDate)
            where.invoiceDate.gte = new Date(startDate);
        if (endDate)
            where.invoiceDate.lte = new Date(endDate);
    }
    // "只显示往来公司"过滤：购买方按照公司管理的公司列表进行过滤
    if (onlyShowPartnerCompanies === 'true') {
        // 获取公司管理中的所有公司名称
        const allCompanies = await prisma.company.findMany({
            where: {
                isActive: true
            },
            select: {
                name: true
            }
        });
        const companyNames = allCompanies.map(company => company.name);
        where.buyerName = { in: companyNames };
    }
    // 销售方公司过滤 - 支持公司名称过滤
    if (sellerCompanies) {
        // 确保sellerCompanies是数组
        const companiesArray = Array.isArray(sellerCompanies) ? sellerCompanies : [sellerCompanies];
        console.log('销售方过滤参数:', companiesArray);
        if (companiesArray.length > 0) {
            // 检查是否是公司ID（数字字符串或UUID）还是公司名称
            const isCompanyIds = companiesArray.every(item => /^\d+$/.test(item) || // 纯数字
                typeof item === 'number' || // 数字类型
                /^[a-f0-9-]{36}$/i.test(item) || // UUID格式
                /^[a-z0-9]{25}$/i.test(item) // cuid格式
            );
            console.log('是否为公司ID:', isCompanyIds);
            if (isCompanyIds) {
                // 如果是公司ID，按原逻辑处理
                if (where.companyId) {
                    // 如果已经有companyId条件（来自用户权限），取交集
                    const existingIds = Array.isArray(where.companyId.in) ? where.companyId.in : [where.companyId];
                    const filteredIds = companiesArray.filter(id => existingIds.includes(String(id)));
                    where.companyId = { in: filteredIds };
                    console.log('使用公司ID过滤（有权限限制）:', filteredIds);
                }
                else {
                    // 没有现有的companyId条件，直接设置
                    where.companyId = { in: companiesArray.map(id => String(id)) };
                    console.log('使用公司ID过滤（无权限限制）:', companiesArray);
                }
            }
            else {
                // 如果是公司名称，支持模糊查询
                if (companiesArray.length === 1) {
                    // 单个条件，使用模糊查询
                    where.sellerName = { contains: companiesArray[0] };
                    console.log('使用公司名称模糊查询:', companiesArray[0]);
                }
                else {
                    // 多个条件，使用OR组合模糊查询
                    where.OR = companiesArray.map(name => ({
                        sellerName: { contains: name }
                    }));
                    console.log('使用多个公司名称模糊查询:', companiesArray);
                }
            }
        }
    }
    // 获取总数
    const total = await prisma.invoice.count({ where });
    console.log(`发票查询结果: 总数=${total}, 页码=${page}, 每页=${pageSize}, 总页数=${Math.ceil(total / pageSize)}`);
    // 获取数据 - 只查询激活公司的发票
    const invoices = await prisma.invoice.findMany({
        where,
        skip,
        take: pageSize,
        orderBy: { [sortBy]: sortOrder },
        include: {
            company: {
                select: {
                    id: true,
                    name: true,
                    taxId: true,
                    isActive: true,
                },
            },
            _count: {
                select: {
                    invoiceItems: true,
                    attachments: true,
                },
            },
        },
    });
    console.log(`实际返回发票数量: ${invoices.length}`);
    res.json({
        success: true,
        data: {
            data: invoices,
            pagination: {
                page,
                pageSize,
                total,
                totalPages: Math.ceil(total / pageSize),
            },
        },
    });
}));
// 导出开具发票数据
router.get('/export', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const { search, startDate, endDate, status, sellerCompanies, onlyShowPartnerCompanies } = req.query;
        // 获取用户有权限的公司ID
        const userCompanyIds = await (0, auth_1.getUserCompanyIds)(req.user.id);
        if (userCompanyIds.length === 0) {
            throw new errorHandler_1.AppError('没有权限访问公司数据', 403);
        }
        // 构建查询条件（与列表查询相同的逻辑）
        const where = {
            companyId: { in: userCompanyIds },
            company: {
                isActive: true,
            },
        };
        // 搜索条件 - 只搜索购买方、发票号码、税号、金额
        if (search) {
            const conditions = [
                { invoiceNumber: { contains: search, mode: 'insensitive' } },
                { buyerName: { contains: search, mode: 'insensitive' } },
                { buyerTaxId: { contains: search, mode: 'insensitive' } }
            ];
            // 如果搜索词是数字，也搜索金额
            if (!isNaN(Number(search))) {
                conditions.push({ totalAmount: { equals: Number(search) } });
            }
            where.OR = conditions;
        }
        // 日期范围
        if (startDate || endDate) {
            where.invoiceDate = {};
            if (startDate) {
                where.invoiceDate.gte = new Date(startDate);
            }
            if (endDate) {
                const endDateTime = new Date(endDate);
                endDateTime.setHours(23, 59, 59, 999);
                where.invoiceDate.lte = endDateTime;
            }
        }
        // 状态过滤
        if (status) {
            where.status = status;
        }
        // "只显示往来公司"过滤
        if (onlyShowPartnerCompanies === 'true') {
            const allCompanies = await prisma.company.findMany({
                where: {
                    isActive: true
                },
                select: {
                    name: true
                }
            });
            const companyNames = allCompanies.map(company => company.name);
            where.buyerName = { in: companyNames };
        }
        // 销售方公司过滤
        if (sellerCompanies) {
            const sellerCompanyArray = Array.isArray(sellerCompanies) ? sellerCompanies : [sellerCompanies];
            where.sellerName = { in: sellerCompanyArray };
        }
        // 查询所有符合条件的开具发票数据
        const invoices = await prisma.invoice.findMany({
            where,
            include: {
                company: {
                    select: {
                        name: true,
                        organization: true
                    }
                },
                invoiceItems: true
            },
            orderBy: { invoiceDate: 'desc' }
        });
        // 读取Excel模板
        const templatePath = path_1.default.join(__dirname, '../../templates/invoice-template.xlsx');
        console.log('模板文件路径:', templatePath);
        console.log('模板文件是否存在:', fs_1.default.existsSync(templatePath));
        if (!fs_1.default.existsSync(templatePath)) {
            throw new errorHandler_1.AppError('Excel模板文件不存在', 404);
        }
        const workbook = XLSX.readFile(templatePath);
        const worksheet = workbook.Sheets[workbook.SheetNames[0]];
        console.log('成功读取模板，工作表名称:', workbook.SheetNames[0]);
        console.log('模板原始范围:', worksheet['!ref']);
        // 准备导出数据 - 按照模板的列顺序
        const exportData = invoices.map(invoice => {
            const firstItem = invoice.invoiceItems?.[0];
            return [
                invoice.invoiceNumber || '', // A: 发票号码
                invoice.invoiceCode || '', // B: 发票代码
                invoice.invoiceDate ? new Date(invoice.invoiceDate).toLocaleDateString('zh-CN') : '', // C: 开票日期
                invoice.buyerName || '', // D: 购买方名称
                invoice.buyerTaxId || '', // E: 购方识别号
                invoice.sellerName || '', // F: 销方名称
                invoice.sellerTaxId || '', // G: 销方识别号
                Number(invoice.amount) || 0, // H: 金额
                Number(invoice.taxAmount) || 0, // I: 税额
                Number(invoice.totalAmount) || 0, // J: 价税合计
                firstItem?.itemName || '', // K: 货物或应税劳务名称
                firstItem?.specification || '', // L: 规格型号
                firstItem?.unit || '', // M: 单位
                Number(firstItem?.quantity) || '', // N: 数量
                Number(firstItem?.unitPrice) || '', // O: 单价
                firstItem?.taxRate ? (Number(firstItem.taxRate) * 100).toFixed(2) + '%' : '', // P: 税率
                invoice.status === 'NORMAL' ? '正常' : '作废', // Q: 发票状态
                invoice.drawer || '', // R: 开票人
                invoice.remarks || '' // S: 备注
            ];
        });
        // 将数据填入模板，从第2行开始（第1行是表头）
        const startRow = 2;
        console.log('准备填入数据，发票数量:', exportData.length);
        exportData.forEach((rowData, index) => {
            const rowIndex = startRow + index;
            console.log(`填入第${rowIndex}行数据:`, rowData.slice(0, 3)); // 只显示前3列避免日志过长
            rowData.forEach((cellValue, colIndex) => {
                const cellAddress = XLSX.utils.encode_cell({ r: rowIndex - 1, c: colIndex });
                if (!worksheet[cellAddress]) {
                    worksheet[cellAddress] = {};
                }
                worksheet[cellAddress].v = cellValue;
                worksheet[cellAddress].t = typeof cellValue === 'number' ? 'n' : 's';
            });
        });
        // 更新工作表的范围
        const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');
        if (exportData.length > 0) {
            range.e.r = Math.max(range.e.r, startRow + exportData.length - 2);
            range.e.c = Math.max(range.e.c, exportData[0].length - 1);
            worksheet['!ref'] = XLSX.utils.encode_range(range);
        }
        console.log('更新后的工作表范围:', worksheet['!ref']);
        // 生成Excel文件
        const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
        console.log('Excel文件生成完成，大小:', buffer.length, '字节');
        // 设置响应头
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', 'attachment; filename="invoices-export-with-template.xlsx"');
        // 发送文件
        res.send(buffer);
    }
    catch (error) {
        console.error('导出开具发票失败:', error);
        res.status(500).json({
            success: false,
            message: '导出失败'
        });
    }
}));
// 获取单个发票详情
router.get('/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    console.log('获取发票详情，ID:', id);
    const invoice = await prisma.invoice.findUnique({
        where: { id },
        include: {
            company: true,
            invoiceItems: true,
            attachments: true, // 恢复附件查询
            auditLogs: {
                include: {
                    user: {
                        select: {
                            id: true,
                            name: true,
                            email: true,
                        },
                    },
                },
                orderBy: { createdAt: 'desc' },
                take: 10,
            },
        },
    });
    if (!invoice) {
        throw new errorHandler_1.AppError('发票不存在', 404);
    }
    console.log('发票详情查询结果:');
    console.log('- 发票号码:', invoice.invoiceNumber);
    console.log('- 附件数量:', invoice.attachments?.length || 0);
    if (invoice.attachments && invoice.attachments.length > 0) {
        console.log('- 附件详情:');
        invoice.attachments.forEach((attachment, index) => {
            console.log(`  附件 ${index + 1}:`, {
                id: attachment.id,
                fileName: attachment.fileName,
                filePath: attachment.filePath,
                fileType: attachment.fileType
            });
        });
    }
    // 额外查询：直接查询附件表验证
    const directAttachments = await prisma.invoiceAttachment.findMany({
        where: { invoiceId: id }
    });
    console.log('直接查询附件表结果:', directAttachments.length, '个附件');
    res.json({
        success: true,
        data: invoice,
    });
}));
// 创建发票
router.post('/', (0, auth_1.authorize)('ADMIN', 'FINANCE', 'BUSINESS'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { error, value } = createInvoiceSchema.validate(req.body);
    if (error) {
        throw new errorHandler_1.AppError(error.details[0].message, 400);
    }
    const { invoiceItems, ...invoiceData } = value;
    // 检查发票号码是否已存在
    const existingInvoice = await prisma.invoice.findUnique({
        where: { invoiceNumber: value.invoiceNumber },
    });
    if (existingInvoice) {
        throw new errorHandler_1.AppError('发票号码已存在', 409);
    }
    // 检查公司是否存在
    const company = await prisma.company.findFirst({
        where: {
            id: value.companyId,
            isActive: true,
        },
    });
    if (!company) {
        throw new errorHandler_1.AppError('公司不存在', 404);
    }
    // 验证金额逻辑
    const calculatedTotal = Number(value.amount) + Number(value.taxAmount);
    if (Math.abs(calculatedTotal - Number(value.totalAmount)) > 0.01) {
        throw new errorHandler_1.AppError('价税合计金额不正确', 400);
    }
    // 检查开具发票重复 - 只在开具发票表(invoice)中检查
    // 注意：这里只检查开具发票表，不检查取得发票表
    const duplicateInvoice = await prisma.invoice.findFirst({
        where: {
            invoiceNumber: value.invoiceNumber,
            invoiceCode: value.invoiceCode,
        },
    });
    const isDuplicate = !!duplicateInvoice;
    // 创建发票
    const invoice = await prisma.invoice.create({
        data: {
            ...invoiceData,
            isDuplicate,
            verificationStatus: isDuplicate ? 'DUPLICATE' : 'UNVERIFIED',
            invoiceItems: invoiceItems ? {
                create: invoiceItems,
            } : undefined,
        },
        include: {
            company: true,
            invoiceItems: true,
        },
    });
    res.status(201).json({
        success: true,
        data: invoice,
        message: isDuplicate ? '发票创建成功，但检测到可能重复' : '发票创建成功',
    });
}));
// 更新发票
router.put('/:id', (0, auth_1.authorize)('ADMIN', 'FINANCE'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const { error, value } = updateInvoiceSchema.validate(req.body);
    if (error) {
        throw new errorHandler_1.AppError(error.details[0].message, 400);
    }
    const { invoiceItems, ...invoiceData } = value;
    // 检查发票是否存在
    const existingInvoice = await prisma.invoice.findUnique({
        where: { id },
    });
    if (!existingInvoice) {
        throw new errorHandler_1.AppError('发票不存在', 404);
    }
    // 如果发票已归档，不允许修改
    if (existingInvoice.isArchived) {
        throw new errorHandler_1.AppError('已归档的发票不能修改', 400);
    }
    // 如果更新发票号码，检查开具发票表中是否重复
    // 注意：这里只检查开具发票表，不检查取得发票表
    if (value.invoiceNumber && value.invoiceNumber !== existingInvoice.invoiceNumber) {
        const duplicateNumber = await prisma.invoice.findFirst({
            where: {
                invoiceNumber: value.invoiceNumber,
                id: { not: id },
            },
        });
        if (duplicateNumber) {
            throw new errorHandler_1.AppError('发票号码已存在', 409);
        }
    }
    // 更新发票 - 使用事务处理发票明细
    const invoice = await prisma.$transaction(async (tx) => {
        // 先删除现有的发票明细
        await tx.invoiceItem.deleteMany({
            where: { invoiceId: id },
        });
        // 更新发票基本信息
        const updatedInvoice = await tx.invoice.update({
            where: { id },
            data: invoiceData,
            include: {
                company: true,
                invoiceItems: true,
            },
        });
        // 如果有发票明细，创建新的明细
        if (invoiceItems && invoiceItems.length > 0) {
            await tx.invoiceItem.createMany({
                data: invoiceItems.map((item) => ({
                    ...item,
                    invoiceId: id,
                })),
            });
            // 重新查询包含明细的发票
            return await tx.invoice.findUnique({
                where: { id },
                include: {
                    company: true,
                    invoiceItems: true,
                },
            });
        }
        return updatedInvoice;
    });
    res.json({
        success: true,
        data: invoice,
        message: '发票更新成功',
    });
}));
// 删除发票
router.delete('/:id', (0, auth_1.authorize)('ADMIN'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const invoice = await prisma.invoice.findUnique({
        where: { id },
    });
    if (!invoice) {
        throw new errorHandler_1.AppError('发票不存在', 404);
    }
    if (invoice.isArchived) {
        throw new errorHandler_1.AppError('已归档的发票不能删除', 400);
    }
    await prisma.invoice.delete({
        where: { id },
    });
    res.json({
        success: true,
        message: '发票删除成功',
    });
}));
// 发票查验
router.post('/:id/verify', (0, auth_1.authorize)('ADMIN', 'FINANCE'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const invoice = await prisma.invoice.findUnique({
        where: { id },
    });
    if (!invoice) {
        throw new errorHandler_1.AppError('发票不存在', 404);
    }
    // 模拟税务局查验过程
    // 在实际应用中，这里会调用税务局的API
    let verificationStatus = 'VERIFIED';
    let statusMessage = '查验通过';
    // 简单的验证逻辑
    if (invoice.isDuplicate) {
        verificationStatus = 'DUPLICATE';
        statusMessage = '发现重复发票';
    }
    else {
        // 模拟随机验证结果（实际应用中会调用真实的税务API）
        const random = Math.random();
        if (random < 0.1) {
            verificationStatus = 'FAILED';
            statusMessage = '查验失败';
        }
    }
    const updatedInvoice = await prisma.invoice.update({
        where: { id },
        data: {
            verificationStatus,
            status: verificationStatus === 'VERIFIED' ? 'NORMAL' : 'CANCELLED',
        },
        include: {
            company: true,
        },
    });
    res.json({
        success: true,
        data: updatedInvoice,
        message: statusMessage,
    });
}));
// 批量操作
router.post('/batch', (0, auth_1.authorize)('ADMIN', 'FINANCE'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const schema = joi_1.default.object({
        action: joi_1.default.string().valid('verify', 'archive', 'delete').required(),
        invoiceIds: joi_1.default.array().items(joi_1.default.string()).min(1).required(),
    });
    const { error, value } = schema.validate(req.body);
    if (error) {
        throw new errorHandler_1.AppError(error.details[0].message, 400);
    }
    const { action, invoiceIds } = value;
    let updateData = {};
    let message = '';
    switch (action) {
        case 'verify':
            updateData = { verificationStatus: 'VERIFIED', status: 'NORMAL' };
            message = '批量查验完成';
            break;
        case 'archive':
            updateData = { isArchived: true };
            message = '批量归档完成';
            break;
        case 'delete':
            await prisma.invoice.deleteMany({
                where: {
                    id: { in: invoiceIds },
                    isArchived: false,
                },
            });
            return res.json({
                success: true,
                message: '批量删除完成',
            });
    }
    const result = await prisma.invoice.updateMany({
        where: {
            id: { in: invoiceIds },
            isArchived: action === 'archive' ? false : undefined,
        },
        data: updateData,
    });
    res.json({
        success: true,
        data: { count: result.count },
        message,
    });
}));
// 获取发票统计
router.get('/stats/summary', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { year } = req.query;
    // 构建查询条件
    const whereCondition = {};
    // 如果指定了年度，添加年度过滤条件
    if (year) {
        const targetYear = parseInt(year);
        whereCondition.invoiceDate = {
            gte: new Date(`${targetYear}-01-01`),
            lt: new Date(`${targetYear + 1}-01-01`),
        };
    }
    // 如果是普通用户，只能查看有权限的公司数据
    if (req.user?.role !== 'ADMIN') {
        const userCompanyIds = await (0, auth_1.getUserCompanyIds)(req.user.id);
        if (userCompanyIds.length === 0) {
            // 用户没有任何公司权限，返回空结果
            return res.json({
                success: true,
                data: {
                    totalInvoices: 0,
                    totalAmount: 0,
                    statusStats: [],
                    typeStats: [],
                    verificationStats: [],
                },
            });
        }
        whereCondition.companyId = { in: userCompanyIds };
    }
    const totalInvoices = await prisma.invoice.count({
        where: whereCondition,
    });
    const totalAmount = await prisma.invoice.aggregate({
        where: whereCondition,
        _sum: { totalAmount: true },
    });
    const statusStats = await prisma.invoice.groupBy({
        by: ['status'],
        where: whereCondition,
        _count: { _all: true },
        _sum: { totalAmount: true },
    });
    const typeStats = await prisma.invoice.groupBy({
        by: ['invoiceType'],
        where: whereCondition,
        _count: { _all: true },
        _sum: { totalAmount: true },
    });
    const verificationStats = await prisma.invoice.groupBy({
        by: ['verificationStatus'],
        where: whereCondition,
        _count: { _all: true },
    });
    res.json({
        success: true,
        data: {
            totalInvoices,
            totalAmount: totalAmount._sum.totalAmount || 0,
            statusStats,
            typeStats,
            verificationStats,
        },
    });
}));
// 获取季度开票汇总统计
router.get('/stats/quarterly', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { year, companyIds } = req.query;
    const targetYear = year ? parseInt(year) : new Date().getFullYear();
    try {
        // 构建查询条件
        const whereCondition = {
            invoiceDate: {
                gte: new Date(`${targetYear}-01-01`),
                lt: new Date(`${targetYear + 1}-01-01`),
            },
            status: 'NORMAL', // 只统计正常发票
        };
        // 如果是普通用户，只能查看有权限的公司数据
        if (req.user?.role !== 'ADMIN') {
            const userCompanyIds = await (0, auth_1.getUserCompanyIds)(req.user.id);
            if (userCompanyIds.length === 0) {
                // 用户没有任何公司权限，返回空结果
                return res.json({
                    success: true,
                    data: {
                        year: targetYear,
                        companies: [],
                    },
                });
            }
            whereCondition.companyId = { in: userCompanyIds };
        }
        // 如果指定了公司ID，按公司ID过滤
        if (companyIds) {
            // 确保companyIds是数组
            const companiesArray = Array.isArray(companyIds) ? companyIds : [companyIds];
            console.log('季度汇总公司过滤参数:', companiesArray);
            if (companiesArray.length > 0) {
                const requestedCompanyIds = companiesArray.map(id => String(id));
                console.log('处理后的公司ID数组:', requestedCompanyIds);
                // 如果已经有用户权限限制，需要取交集
                if (whereCondition.companyId && whereCondition.companyId.in) {
                    const allowedCompanyIds = whereCondition.companyId.in;
                    const filteredIds = requestedCompanyIds.filter(id => allowedCompanyIds.includes(id));
                    whereCondition.companyId = { in: filteredIds };
                    console.log('使用权限交集过滤:', filteredIds);
                }
                else {
                    // 没有用户权限限制，直接使用请求的公司ID
                    whereCondition.companyId = { in: requestedCompanyIds };
                    console.log('直接使用公司ID过滤:', requestedCompanyIds);
                }
            }
        }
        // 获取发票数据，按销售方分组统计 - 只查询激活公司的发票
        const invoices = await prisma.invoice.findMany({
            where: {
                ...whereCondition,
                company: {
                    isActive: true, // 只查询激活公司的发票
                },
            },
            select: {
                sellerName: true,
                sellerTaxId: true,
                totalAmount: true,
                invoiceDate: true,
            },
        });
        // 按销售方分组
        const sellerGroups = new Map();
        invoices.forEach(invoice => {
            const sellerKey = invoice.sellerTaxId || invoice.sellerName || '未知销售方';
            if (!sellerGroups.has(sellerKey)) {
                sellerGroups.set(sellerKey, {
                    name: invoice.sellerName || '未知销售方',
                    taxId: invoice.sellerTaxId || '',
                    monthly: {
                        jan: 0, feb: 0, mar: 0, apr: 0, may: 0, jun: 0,
                        jul: 0, aug: 0, sep: 0, oct: 0, nov: 0, dec: 0,
                    },
                });
            }
            // 统计每月金额
            const month = new Date(invoice.invoiceDate).getMonth(); // 0-11
            const monthNames = ['jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'];
            const monthKey = monthNames[month];
            const seller = sellerGroups.get(sellerKey);
            seller.monthly[monthKey] += Number(invoice.totalAmount);
        });
        // 转换为数组并添加序号
        const companyStats = Array.from(sellerGroups.values()).map((seller, index) => ({
            id: index + 1,
            name: seller.name,
            taxId: seller.taxId,
            monthly: seller.monthly,
        }));
        res.json({
            success: true,
            data: {
                year: targetYear,
                companies: companyStats,
            },
        });
    }
    catch (error) {
        console.error('获取季度统计失败:', error);
        res.status(500).json({
            success: false,
            message: '获取季度统计失败',
        });
    }
}));
// 获取公司开票汇总统计
router.get('/stats/company-summary', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { years, companyIds } = req.query;
    try {
        // 处理年度参数（多选）
        let targetYears = [];
        if (years) {
            if (Array.isArray(years)) {
                targetYears = years.map(year => parseInt(year));
            }
            else {
                targetYears = [parseInt(years)];
            }
        }
        else {
            // 如果没有指定年度，查询所有年度
            const allYearsResult = await prisma.invoice.findMany({
                select: {
                    invoiceDate: true,
                },
                where: {
                    status: 'NORMAL',
                },
            });
            const uniqueYears = [...new Set(allYearsResult.map(invoice => new Date(invoice.invoiceDate).getFullYear()))].sort((a, b) => b - a); // 降序排列
            targetYears = uniqueYears;
        }
        // 构建查询条件
        const whereCondition = {
            status: 'NORMAL', // 只统计正常发票
        };
        // 年度过滤
        if (targetYears.length > 0) {
            whereCondition.invoiceDate = {
                gte: new Date(`${Math.min(...targetYears)}-01-01`),
                lt: new Date(`${Math.max(...targetYears) + 1}-01-01`),
            };
        }
        // 如果是普通用户，只能查看有权限的公司数据
        if (req.user?.role !== 'ADMIN') {
            const userCompanyIds = await (0, auth_1.getUserCompanyIds)(req.user.id);
            if (userCompanyIds.length === 0) {
                return res.json({
                    success: true,
                    data: {
                        years: targetYears,
                        companies: [],
                    },
                });
            }
            whereCondition.companyId = { in: userCompanyIds };
        }
        // 公司过滤
        if (companyIds) {
            const companyIdArray = Array.isArray(companyIds) ? companyIds : [companyIds];
            if (req.user?.role === 'ADMIN') {
                whereCondition.companyId = { in: companyIdArray };
            }
            else {
                // 普通用户只能查看有权限的公司
                const userCompanyIds = await (0, auth_1.getUserCompanyIds)(req.user.id);
                const allowedCompanyIds = companyIdArray.filter(id => userCompanyIds.includes(id));
                if (allowedCompanyIds.length > 0) {
                    whereCondition.companyId = { in: allowedCompanyIds };
                }
                else {
                    // 如果没有匹配的公司权限，返回空结果
                    return res.json({
                        success: true,
                        data: {
                            years: targetYears,
                            companies: [],
                        },
                    });
                }
            }
        }
        // 查询发票数据 - 只查询激活公司的发票
        const invoices = await prisma.invoice.findMany({
            where: {
                ...whereCondition,
                company: {
                    isActive: true, // 只查询激活公司的发票
                },
            },
            select: {
                sellerName: true,
                sellerTaxId: true,
                totalAmount: true,
                invoiceDate: true,
            },
        });
        // 按销售方和年度分组，并按月份统计
        const companyYearGroups = new Map();
        invoices.forEach(invoice => {
            const invoiceDate = new Date(invoice.invoiceDate);
            const year = invoiceDate.getFullYear();
            const month = invoiceDate.getMonth() + 1; // 月份从1开始
            const sellerKey = invoice.sellerTaxId || invoice.sellerName || '未知销售方';
            const companyYearKey = `${sellerKey}_${year}`;
            if (!companyYearGroups.has(companyYearKey)) {
                companyYearGroups.set(companyYearKey, {
                    name: invoice.sellerName || '未知销售方',
                    year: year,
                    monthly: {
                        jan: 0, feb: 0, mar: 0, apr: 0, may: 0, jun: 0,
                        jul: 0, aug: 0, sep: 0, oct: 0, nov: 0, dec: 0
                    }
                });
            }
            const group = companyYearGroups.get(companyYearKey);
            const monthNames = ['jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'];
            const monthKey = monthNames[month - 1];
            group.monthly[monthKey] += Number(invoice.totalAmount);
        });
        // 转换为数组并添加序号
        const companyStats = Array.from(companyYearGroups.values()).map((company, index) => ({
            id: index + 1,
            name: company.name,
            year: company.year,
            monthly: company.monthly,
        }));
        // 按公司名称和年度排序
        companyStats.sort((a, b) => {
            if (a.name !== b.name) {
                return a.name.localeCompare(b.name);
            }
            return b.year - a.year; // 年度降序
        });
        res.json({
            success: true,
            data: {
                years: targetYears,
                companies: companyStats,
            },
        });
    }
    catch (error) {
        console.error('获取公司开票汇总失败:', error);
        res.status(500).json({
            success: false,
            message: '获取公司开票汇总失败',
        });
    }
}));
// 用户开票汇总统计
router.get('/stats/user-invoice-summary', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const { userIds } = req.query;
        // 获取用户有权限的公司ID
        const userCompanyIds = await (0, auth_1.getUserCompanyIds)(req.user.id);
        if (userCompanyIds.length === 0) {
            return res.json({
                success: true,
                data: { userSummary: [], allUsers: [] },
                message: '没有权限访问公司数据'
            });
        }
        // 获取所有公司及其所属用户信息
        const companies = await prisma.company.findMany({
            where: {
                id: { in: userCompanyIds },
                isActive: true
            },
            select: {
                id: true,
                name: true,
                organization: true
            }
        });
        // 获取所有去重的所属用户列表
        const allUsers = Array.from(new Set(companies.map(c => c.organization).filter(Boolean)))
            .map(org => ({
            id: org,
            username: org,
            email: `${org}@company.com` // 临时邮箱，实际可以从用户表获取
        }));
        // 解析用户ID过滤参数
        let selectedUserIds = [];
        if (userIds && typeof userIds === 'string') {
            selectedUserIds = userIds.split(',').map(id => id.trim()).filter(Boolean);
        }
        // 构建公司组织映射
        const companyOrgMap = new Map();
        companies.forEach(company => {
            if (company.organization) {
                companyOrgMap.set(company.id, company.organization);
            }
        });
        // 获取所有开具发票数据（不限制公司权限，用于跨用户统计）
        const allInvoices = await prisma.invoice.findMany({
            select: {
                id: true,
                companyId: true,
                buyerName: true,
                sellerName: true,
                totalAmount: true,
                company: {
                    select: {
                        organization: true
                    }
                }
            }
        });
        // 获取所有取得发票数据（不限制公司权限，用于跨用户统计）
        const allReceivedInvoices = await prisma.receivedInvoice.findMany({
            select: {
                id: true,
                companyId: true,
                buyerName: true,
                sellerName: true,
                totalAmount: true,
                company: {
                    select: {
                        organization: true
                    }
                }
            }
        });
        // 获取所有公司的所属用户映射（用于查找购买方/销售方的所属用户）
        const allCompanies = await prisma.company.findMany({
            where: {
                isActive: true
            },
            select: {
                id: true,
                name: true,
                organization: true
            }
        });
        // 创建公司名称到所属用户的映射
        const companyToUserMap = new Map();
        allCompanies.forEach(company => {
            if (company.organization) {
                companyToUserMap.set(company.name, company.organization);
            }
        });
        // 统计用户间的发票往来
        const userSummaryMap = new Map();
        // 处理开具发票：销售方公司.所属用户 = 当前用户，购买方公司.所属用户 = 其他用户
        allInvoices.forEach(invoice => {
            const sellerOrg = invoice.company.organization; // 销售方所属用户
            const buyerOrg = companyToUserMap.get(invoice.buyerName); // 购买方所属用户
            if (!sellerOrg || !buyerOrg || sellerOrg === buyerOrg)
                return;
            // 只统计当前用户有权限的公司作为销售方的情况
            const sellerCompany = companies.find(c => c.name === invoice.sellerName);
            if (!sellerCompany)
                return;
            const key = `${sellerOrg}-${buyerOrg}`;
            if (!userSummaryMap.has(key)) {
                userSummaryMap.set(key, {
                    ownerUserId: sellerOrg,
                    ownerUsername: sellerOrg,
                    ownerEmail: `${sellerOrg}@company.com`,
                    otherUserId: buyerOrg,
                    otherUsername: buyerOrg,
                    otherEmail: `${buyerOrg}@company.com`,
                    issuedAmount: 0,
                    receivedAmount: 0
                });
            }
            userSummaryMap.get(key).issuedAmount += Number(invoice.totalAmount);
        });
        // 处理取得发票：购买方公司.所属用户 = 当前用户，销售方公司.所属用户 = 其他用户
        allReceivedInvoices.forEach(invoice => {
            const buyerOrg = invoice.company.organization; // 购买方所属用户
            const sellerOrg = companyToUserMap.get(invoice.sellerName); // 销售方所属用户
            if (!buyerOrg || !sellerOrg || buyerOrg === sellerOrg)
                return;
            // 只统计当前用户有权限的公司作为购买方的情况
            const buyerCompany = companies.find(c => c.name === invoice.buyerName);
            if (!buyerCompany)
                return;
            const key = `${buyerOrg}-${sellerOrg}`;
            if (!userSummaryMap.has(key)) {
                userSummaryMap.set(key, {
                    ownerUserId: buyerOrg,
                    ownerUsername: buyerOrg,
                    ownerEmail: `${buyerOrg}@company.com`,
                    otherUserId: sellerOrg,
                    otherUsername: sellerOrg,
                    otherEmail: `${sellerOrg}@company.com`,
                    issuedAmount: 0,
                    receivedAmount: 0
                });
            }
            userSummaryMap.get(key).receivedAmount += Number(invoice.totalAmount);
        });
        // 转换为数组并过滤
        let userSummary = Array.from(userSummaryMap.values());
        // 如果指定了用户过滤，则进行过滤
        if (selectedUserIds.length > 0) {
            userSummary = userSummary.filter(item => selectedUserIds.includes(item.ownerUserId));
        }
        // 按所属用户名称排序
        userSummary.sort((a, b) => {
            const ownerCompare = a.ownerUsername.localeCompare(b.ownerUsername);
            if (ownerCompare !== 0)
                return ownerCompare;
            return a.otherUsername.localeCompare(b.otherUsername);
        });
        res.json({
            success: true,
            data: {
                userSummary,
                allUsers
            },
        });
    }
    catch (error) {
        console.error('获取用户开票汇总失败:', error);
        res.status(500).json({
            success: false,
            message: '获取用户开票汇总失败',
        });
    }
}));
// 获取用户开票详细信息
router.get('/stats/user-invoice-detail', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const { ownerUserId, otherUserId } = req.query;
        if (!ownerUserId || !otherUserId) {
            throw new errorHandler_1.AppError('所属用户和其他用户参数不能为空', 400);
        }
        // 获取用户有权限的公司ID
        const userCompanyIds = await (0, auth_1.getUserCompanyIds)(req.user.id);
        if (userCompanyIds.length === 0) {
            return res.json({
                success: true,
                data: { issuedInvoices: [], receivedInvoices: [] },
                message: '没有权限访问公司数据'
            });
        }
        // 获取所有公司及其所属用户信息
        const companies = await prisma.company.findMany({
            where: {
                id: { in: userCompanyIds },
                isActive: true
            },
            select: {
                id: true,
                name: true,
                organization: true
            }
        });
        // 获取所属用户的公司列表
        const ownerCompanies = companies.filter(c => c.organization === ownerUserId);
        const otherCompanies = companies.filter(c => c.organization === otherUserId);
        const ownerCompanyNames = ownerCompanies.map(c => c.name);
        const otherCompanyNames = otherCompanies.map(c => c.name);
        // 获取所有公司的所属用户映射
        const allCompanies = await prisma.company.findMany({
            where: {
                isActive: true
            },
            select: {
                id: true,
                name: true,
                organization: true
            }
        });
        // 创建公司名称到所属用户的映射
        const companyToUserMap = new Map();
        allCompanies.forEach(company => {
            if (company.organization) {
                companyToUserMap.set(company.name, company.organization);
            }
        });
        // 查询开具发票：销售方公司.所属用户 = ownerUserId，购买方公司.所属用户 = otherUserId
        const allIssuedInvoices = await prisma.invoice.findMany({
            select: {
                id: true,
                invoiceNumber: true,
                invoiceDate: true,
                buyerName: true,
                sellerName: true,
                totalAmount: true,
                status: true,
                company: {
                    select: {
                        name: true,
                        organization: true
                    }
                }
            },
            orderBy: {
                invoiceDate: 'desc'
            }
        });
        // 过滤开具发票：销售方所属用户 = ownerUserId，购买方所属用户 = otherUserId
        const issuedInvoices = allIssuedInvoices.filter(invoice => {
            const sellerOrg = invoice.company.organization;
            const buyerOrg = companyToUserMap.get(invoice.buyerName);
            return sellerOrg === ownerUserId && buyerOrg === otherUserId;
        });
        // 查询取得发票：购买方公司.所属用户 = ownerUserId，销售方公司.所属用户 = otherUserId
        const allReceivedInvoices = await prisma.receivedInvoice.findMany({
            select: {
                id: true,
                invoiceNumber: true,
                invoiceDate: true,
                buyerName: true,
                sellerName: true,
                totalAmount: true,
                status: true,
                company: {
                    select: {
                        name: true,
                        organization: true
                    }
                }
            },
            orderBy: {
                invoiceDate: 'desc'
            }
        });
        // 过滤取得发票：购买方所属用户 = ownerUserId，销售方所属用户 = otherUserId
        const receivedInvoices = allReceivedInvoices.filter(invoice => {
            const buyerOrg = invoice.company.organization;
            const sellerOrg = companyToUserMap.get(invoice.sellerName);
            return buyerOrg === ownerUserId && sellerOrg === otherUserId;
        });
        res.json({
            success: true,
            data: {
                ownerUserId,
                otherUserId,
                issuedInvoices,
                receivedInvoices
            },
        });
    }
    catch (error) {
        console.error('获取用户开票详细信息失败:', error);
        res.status(500).json({
            success: false,
            message: '获取用户开票详细信息失败',
        });
    }
}));
// 获取用户开票汇总分组信息
router.get('/stats/user-invoice-group', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const { ownerUserId, otherUserId } = req.query;
        if (!ownerUserId || !otherUserId) {
            throw new errorHandler_1.AppError('所属用户和其他用户参数不能为空', 400);
        }
        // 获取用户有权限的公司ID
        const userCompanyIds = await (0, auth_1.getUserCompanyIds)(req.user.id);
        if (userCompanyIds.length === 0) {
            return res.json({
                success: true,
                data: { issuedGroups: [], receivedGroups: [] },
                message: '没有权限访问公司数据'
            });
        }
        // 获取所有公司及其所属用户信息
        const companies = await prisma.company.findMany({
            where: {
                id: { in: userCompanyIds },
                isActive: true
            },
            select: {
                id: true,
                name: true,
                organization: true
            }
        });
        // 获取所属用户的公司列表
        const ownerCompanies = companies.filter(c => c.organization === ownerUserId);
        const otherCompanies = companies.filter(c => c.organization === otherUserId);
        const ownerCompanyNames = ownerCompanies.map(c => c.name);
        const otherCompanyNames = otherCompanies.map(c => c.name);
        // 获取所有公司的所属用户映射
        const allCompanies = await prisma.company.findMany({
            where: {
                isActive: true
            },
            select: {
                id: true,
                name: true,
                organization: true
            }
        });
        // 创建公司名称到所属用户的映射
        const companyToUserMap = new Map();
        allCompanies.forEach(company => {
            if (company.organization) {
                companyToUserMap.set(company.name, company.organization);
            }
        });
        // 查询所有开具发票
        const allIssuedInvoices = await prisma.invoice.findMany({
            select: {
                buyerName: true,
                sellerName: true,
                totalAmount: true,
                company: {
                    select: {
                        organization: true
                    }
                }
            }
        });
        // 过滤开具发票：销售方所属用户 = ownerUserId，购买方所属用户 = otherUserId
        const issuedInvoices = allIssuedInvoices.filter(invoice => {
            const sellerOrg = invoice.company.organization;
            const buyerOrg = companyToUserMap.get(invoice.buyerName);
            return sellerOrg === ownerUserId && buyerOrg === otherUserId;
        });
        // 查询所有取得发票
        const allReceivedInvoices = await prisma.receivedInvoice.findMany({
            select: {
                buyerName: true,
                sellerName: true,
                totalAmount: true,
                company: {
                    select: {
                        organization: true
                    }
                }
            }
        });
        // 过滤取得发票：购买方所属用户 = ownerUserId，销售方所属用户 = otherUserId
        const receivedInvoices = allReceivedInvoices.filter(invoice => {
            const buyerOrg = invoice.company.organization;
            const sellerOrg = companyToUserMap.get(invoice.sellerName);
            return buyerOrg === ownerUserId && sellerOrg === otherUserId;
        });
        // 开具发票分组汇总
        const issuedGroupMap = new Map();
        issuedInvoices.forEach(invoice => {
            const key = `${invoice.buyerName}-${invoice.sellerName}`;
            if (!issuedGroupMap.has(key)) {
                issuedGroupMap.set(key, {
                    buyerName: invoice.buyerName,
                    sellerName: invoice.sellerName,
                    totalAmount: 0,
                    count: 0
                });
            }
            const group = issuedGroupMap.get(key);
            group.totalAmount += Number(invoice.totalAmount);
            group.count += 1;
        });
        // 取得发票分组汇总
        const receivedGroupMap = new Map();
        receivedInvoices.forEach(invoice => {
            const key = `${invoice.buyerName}-${invoice.sellerName}`;
            if (!receivedGroupMap.has(key)) {
                receivedGroupMap.set(key, {
                    buyerName: invoice.buyerName,
                    sellerName: invoice.sellerName,
                    totalAmount: 0,
                    count: 0
                });
            }
            const group = receivedGroupMap.get(key);
            group.totalAmount += Number(invoice.totalAmount);
            group.count += 1;
        });
        const issuedGroups = Array.from(issuedGroupMap.values()).sort((a, b) => b.totalAmount - a.totalAmount);
        const receivedGroups = Array.from(receivedGroupMap.values()).sort((a, b) => b.totalAmount - a.totalAmount);
        res.json({
            success: true,
            data: {
                ownerUserId,
                otherUserId,
                issuedGroups,
                receivedGroups
            },
        });
    }
    catch (error) {
        console.error('获取用户开票汇总分组信息失败:', error);
        res.status(500).json({
            success: false,
            message: '获取用户开票汇总分组信息失败',
        });
    }
}));
// 获取用户开票汇总分组发票列表
router.get('/stats/user-invoice-group-list', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const { ownerUserId, otherUserId, buyerName, sellerName, invoiceType } = req.query;
        if (!ownerUserId || !otherUserId || !buyerName || !sellerName || !invoiceType) {
            throw new errorHandler_1.AppError('参数不完整', 400);
        }
        // 获取用户有权限的公司ID
        const userCompanyIds = await (0, auth_1.getUserCompanyIds)(req.user.id);
        if (userCompanyIds.length === 0) {
            return res.json({
                success: true,
                data: [],
                message: '没有权限访问公司数据'
            });
        }
        // 获取所有公司及其所属用户信息
        const companies = await prisma.company.findMany({
            where: {
                id: { in: userCompanyIds },
                isActive: true
            },
            select: {
                id: true,
                name: true,
                organization: true
            }
        });
        // 获取所属用户的公司列表
        const ownerCompanies = companies.filter(c => c.organization === ownerUserId);
        // 获取所有公司的所属用户映射
        const allCompanies = await prisma.company.findMany({
            where: {
                isActive: true
            },
            select: {
                id: true,
                name: true,
                organization: true
            }
        });
        // 创建公司名称到所属用户的映射
        const companyToUserMap = new Map();
        allCompanies.forEach(company => {
            if (company.organization) {
                companyToUserMap.set(company.name, company.organization);
            }
        });
        let invoices = [];
        if (invoiceType === 'issued') {
            // 查询所有开具发票
            const allIssuedInvoices = await prisma.invoice.findMany({
                where: {
                    buyerName: buyerName,
                    sellerName: sellerName
                },
                select: {
                    id: true,
                    invoiceNumber: true,
                    invoiceDate: true,
                    buyerName: true,
                    sellerName: true,
                    totalAmount: true,
                    status: true,
                    company: {
                        select: {
                            organization: true
                        }
                    }
                },
                orderBy: {
                    invoiceDate: 'desc'
                }
            });
            // 过滤：销售方所属用户 = ownerUserId，购买方所属用户 = otherUserId
            invoices = allIssuedInvoices.filter(invoice => {
                const sellerOrg = invoice.company.organization;
                const buyerOrg = companyToUserMap.get(invoice.buyerName);
                return sellerOrg === ownerUserId && buyerOrg === otherUserId;
            });
        }
        else if (invoiceType === 'received') {
            // 查询所有取得发票
            const allReceivedInvoices = await prisma.receivedInvoice.findMany({
                where: {
                    buyerName: buyerName,
                    sellerName: sellerName
                },
                select: {
                    id: true,
                    invoiceNumber: true,
                    invoiceDate: true,
                    buyerName: true,
                    sellerName: true,
                    totalAmount: true,
                    status: true,
                    company: {
                        select: {
                            organization: true
                        }
                    }
                },
                orderBy: {
                    invoiceDate: 'desc'
                }
            });
            // 过滤：购买方所属用户 = ownerUserId，销售方所属用户 = otherUserId
            invoices = allReceivedInvoices.filter(invoice => {
                const buyerOrg = invoice.company.organization;
                const sellerOrg = companyToUserMap.get(invoice.sellerName);
                return buyerOrg === ownerUserId && sellerOrg === otherUserId;
            });
        }
        res.json({
            success: true,
            data: invoices,
        });
    }
    catch (error) {
        console.error('获取用户开票汇总分组发票列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取用户开票汇总分组发票列表失败',
        });
    }
}));
// 测试API - 直接返回测试数据
router.get('/stats/invoice-count-summary-test', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    console.log('🧪 === 测试API调用 ===');
    const testData = [{
            id: 'test-2024',
            companyId: 'test-company',
            name: '测试公司A',
            taxId: '123456789012345',
            year: 2024,
            monthly: {
                jan: 5, feb: 3, mar: 8, apr: 4, may: 6, jun: 2,
                jul: 3, aug: 1, sep: 4, oct: 7, nov: 5, dec: 2
            },
            quarterly: {
                Q1: 16, Q2: 12, Q3: 8, Q4: 14
            },
            yearlyTotal: 50
        }];
    res.json({
        success: true,
        data: {
            years: [2024],
            companies: testData,
            summary: {
                totalInvoices: 50,
                monthlyTotals: [5, 3, 8, 4, 6, 2, 3, 1, 4, 7, 5, 2],
                quarterlyTotals: { Q1: 16, Q2: 12, Q3: 8, Q4: 14 },
                yearlyTotal: 50
            },
            availableYears: [2024, 2023, 2022],
        },
    });
}));
// 获取发票张数汇总统计
router.get('/stats/invoice-count-summary', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { years, companyIds } = req.query;
    try {
        // 处理年度参数（多选）
        let targetYears = [];
        if (years) {
            if (Array.isArray(years)) {
                targetYears = years.map(year => parseInt(year));
            }
            else {
                targetYears = [parseInt(years)];
            }
        }
        // 获取用户有权限的公司ID列表
        let allowedCompanyIds = [];
        if (req.user?.role !== 'ADMIN') {
            allowedCompanyIds = await (0, auth_1.getUserCompanyIds)(req.user.id);
            if (allowedCompanyIds.length === 0) {
                return res.json({
                    success: true,
                    data: {
                        years: targetYears,
                        companies: [],
                        summary: {
                            totalInvoices: 0,
                            quarterlyTotals: { Q1: 0, Q2: 0, Q3: 0, Q4: 0 },
                            monthlyTotals: Array(12).fill(0),
                            yearlyTotal: 0
                        },
                        availableYears: []
                    },
                });
            }
        }
        // 构建基础查询条件
        const whereCondition = {
            status: 'NORMAL', // 只统计正常状态的发票
        };
        // 添加公司权限过滤
        if (allowedCompanyIds.length > 0) {
            whereCondition.companyId = { in: allowedCompanyIds };
        }
        // 处理指定公司ID过滤
        if (companyIds) {
            const companiesArray = Array.isArray(companyIds) ? companyIds : [companyIds];
            if (companiesArray.length > 0) {
                const requestedCompanyIds = companiesArray.map(id => String(id));
                // 如果用户有权限限制，需要取交集
                if (allowedCompanyIds.length > 0) {
                    const filteredIds = requestedCompanyIds.filter(id => allowedCompanyIds.includes(id));
                    whereCondition.companyId = { in: filteredIds };
                }
                else {
                    // 管理员用户，直接使用请求的公司ID
                    whereCondition.companyId = { in: requestedCompanyIds };
                }
            }
        }
        // 添加年度过滤条件
        if (targetYears.length > 0) {
            const minYear = Math.min(...targetYears);
            const maxYear = Math.max(...targetYears);
            whereCondition.invoiceDate = {
                gte: new Date(`${minYear}-01-01`),
                lte: new Date(`${maxYear}-12-31`),
            };
        }
        // 查询发票数据 - 只查询激活公司的发票
        const invoices = await prisma.invoice.findMany({
            where: {
                ...whereCondition,
                company: {
                    isActive: true, // 只查询激活公司的发票
                },
            },
            include: {
                company: {
                    select: {
                        id: true,
                        name: true,
                        taxId: true,
                        isActive: true,
                    },
                },
            },
            orderBy: [
                { company: { name: 'asc' } },
                { invoiceDate: 'asc' }
            ]
        });
        // 获取所有可用年度（从发票数据中提取）
        const availableYears = Array.from(new Set(invoices.map(invoice => new Date(invoice.invoiceDate).getFullYear()))).sort((a, b) => b - a); // 从大到小排序
        // 按公司和年度分组，统计每月发票张数
        const companyYearGroups = new Map();
        invoices.forEach(invoice => {
            const companyId = invoice.company?.id || invoice.companyId;
            const companyName = invoice.company?.name || '未知公司';
            const invoiceDate = new Date(invoice.invoiceDate);
            const year = invoiceDate.getFullYear();
            const month = invoiceDate.getMonth() + 1; // 1-12
            const companyYearKey = `${companyId}-${year}`;
            // 初始化公司年度分组
            if (!companyYearGroups.has(companyYearKey)) {
                companyYearGroups.set(companyYearKey, {
                    companyId: companyId,
                    companyName: companyName,
                    year: year,
                    monthly: {
                        jan: 0, feb: 0, mar: 0, apr: 0, may: 0, jun: 0,
                        jul: 0, aug: 0, sep: 0, oct: 0, nov: 0, dec: 0,
                    },
                    quarterly: {
                        Q1: 0, Q2: 0, Q3: 0, Q4: 0,
                    },
                    yearlyTotal: 0,
                });
            }
            const group = companyYearGroups.get(companyYearKey);
            const monthNames = ['jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'];
            const monthKey = monthNames[month - 1];
            // 统计发票张数（每张发票计数为1）
            group.monthly[monthKey] += 1;
            group.yearlyTotal += 1;
            // 计算季度统计
            if (month >= 1 && month <= 3) {
                group.quarterly.Q1 += 1;
            }
            else if (month >= 4 && month <= 6) {
                group.quarterly.Q2 += 1;
            }
            else if (month >= 7 && month <= 9) {
                group.quarterly.Q3 += 1;
            }
            else if (month >= 10 && month <= 12) {
                group.quarterly.Q4 += 1;
            }
        });
        // 转换为数组格式
        const companyStats = Array.from(companyYearGroups.values()).map((company) => ({
            id: `${company.companyId}-${company.year}`, // 使用复合ID确保唯一性
            companyId: company.companyId,
            name: company.companyName,
            year: company.year,
            monthly: company.monthly,
            quarterly: company.quarterly,
            yearlyTotal: company.yearlyTotal,
        }));
        // 按公司名称和年度排序
        companyStats.sort((a, b) => {
            if (a.name !== b.name) {
                return a.name.localeCompare(b.name);
            }
            return b.year - a.year; // 年度降序
        });
        // 计算汇总统计
        const summary = {
            totalInvoices: invoices.length,
            quarterlyTotals: { Q1: 0, Q2: 0, Q3: 0, Q4: 0 },
            monthlyTotals: Array(12).fill(0),
            yearlyTotal: invoices.length,
        };
        // 计算月度和季度汇总
        companyStats.forEach(company => {
            const monthNames = ['jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'];
            monthNames.forEach((month, index) => {
                summary.monthlyTotals[index] += company.monthly[month] || 0;
            });
            summary.quarterlyTotals.Q1 += company.quarterly.Q1 || 0;
            summary.quarterlyTotals.Q2 += company.quarterly.Q2 || 0;
            summary.quarterlyTotals.Q3 += company.quarterly.Q3 || 0;
            summary.quarterlyTotals.Q4 += company.quarterly.Q4 || 0;
        });
        res.json({
            success: true,
            data: {
                years: targetYears,
                companies: companyStats,
                summary: summary,
                availableYears: availableYears,
            },
        });
    }
    catch (error) {
        console.error('获取发票张数汇总失败:', error);
        res.status(500).json({
            success: false,
            message: '获取发票张数汇总失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
}));
exports.default = router;
//# sourceMappingURL=invoices.js.map