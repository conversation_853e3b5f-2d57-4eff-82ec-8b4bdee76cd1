{"version": 3, "file": "operationLogger.js", "sourceRoot": "", "sources": ["../../src/middleware/operationLogger.ts"], "names": [], "mappings": ";;;AACA,2CAA8C;AAE9C,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;AAYlC,SAAS;AACT,MAAM,gBAAgB,GAAG,CAAC,MAAc,EAAE,IAAY,EAAU,EAAE;IAChE,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IAErD,cAAc;IACd,IAAI,YAAY,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE,CAAC;QAC9B,YAAY,CAAC,KAAK,EAAE,CAAC;IACvB,CAAC;IAED,MAAM,QAAQ,GAAG,YAAY,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC;IAC9C,MAAM,MAAM,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;IAE/B,QAAQ,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;QAC7B,KAAK,KAAK;YACR,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC;gBAAE,OAAO,OAAO,CAAC;YAChD,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,MAAM,KAAK,QAAQ;gBAAE,OAAO,QAAQ,CAAC;YACxE,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC;gBAAE,OAAO,QAAQ,CAAC;YACjD,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;gBAAE,OAAO,QAAQ,CAAC;YAChD,IAAI,IAAI,CAAC,QAAQ,CAAC,oBAAoB,CAAC;gBAAE,OAAO,UAAU,CAAC;YAC3D,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAAE,OAAO,QAAQ,CAAC;YAC7C,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAAE,OAAO,QAAQ,CAAC;YAC7C,IAAI,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC;gBAAE,OAAO,QAAQ,CAAC;YACtD,OAAO,KAAK,QAAQ,EAAE,CAAC;QAEzB,KAAK,MAAM;YACT,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC;gBAAE,OAAO,MAAM,CAAC;YAChD,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC;gBAAE,OAAO,MAAM,CAAC;YACjD,IAAI,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC;gBAAE,OAAO,QAAQ,CAAC;YACxD,IAAI,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC;gBAAE,OAAO,QAAQ,CAAC;YACvD,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC;gBAAE,OAAO,MAAM,CAAC;YAC/C,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;gBAAE,OAAO,MAAM,CAAC;YAC9C,IAAI,IAAI,CAAC,QAAQ,CAAC,oBAAoB,CAAC;gBAAE,OAAO,QAAQ,CAAC;YACzD,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAAE,OAAO,MAAM,CAAC;YAC3C,OAAO,KAAK,QAAQ,EAAE,CAAC;QAEzB,KAAK,KAAK;YACR,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC;gBAAE,OAAO,QAAQ,CAAC;YACjD,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;gBAAE,OAAO,QAAQ,CAAC;YAChD,IAAI,IAAI,CAAC,QAAQ,CAAC,oBAAoB,CAAC;gBAAE,OAAO,UAAU,CAAC;YAC3D,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;gBAAE,OAAO,QAAQ,CAAC;YAC3E,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC;gBAAE,OAAO,UAAU,CAAC;YACrF,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC;gBAAE,OAAO,UAAU,CAAC;YAC9E,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAAE,OAAO,QAAQ,CAAC;YAC7C,OAAO,KAAK,QAAQ,EAAE,CAAC;QAEzB,KAAK,QAAQ;YACX,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC;gBAAE,OAAO,MAAM,CAAC;YAC/C,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;gBAAE,OAAO,MAAM,CAAC;YAC9C,IAAI,IAAI,CAAC,QAAQ,CAAC,oBAAoB,CAAC;gBAAE,OAAO,QAAQ,CAAC;YACzD,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAAE,OAAO,MAAM,CAAC;YAC3C,OAAO,KAAK,QAAQ,EAAE,CAAC;QAEzB;YACE,OAAO,GAAG,MAAM,IAAI,QAAQ,EAAE,CAAC;IACnC,CAAC;AACH,CAAC,CAAC;AAEF,SAAS;AACT,MAAM,YAAY,GAAG,CAAC,IAAS,EAAO,EAAE;IACtC,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;QACtC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,eAAe,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;IAChF,MAAM,SAAS,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;IAE9B,KAAK,MAAM,KAAK,IAAI,eAAe,EAAE,CAAC;QACpC,IAAI,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;YACrB,SAAS,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;QAC3B,CAAC;IACH,CAAC;IAED,WAAW;IACX,KAAK,MAAM,GAAG,IAAI,SAAS,EAAE,CAAC;QAC5B,IAAI,OAAO,SAAS,CAAC,GAAG,CAAC,KAAK,QAAQ,IAAI,SAAS,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC;YAClE,SAAS,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AAEF,aAAa;AACb,MAAM,SAAS,GAAG,CAAC,IAAY,EAAW,EAAE;IAC1C,SAAS;IACT,MAAM,YAAY,GAAG;QACnB,qBAAqB,EAAE,eAAe;QACtC,SAAS;QACT,cAAc;QACd,SAAS;QACT,SAAS;KACV,CAAC;IAEF,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC;AACzE,CAAC,CAAC;AAEK,MAAM,eAAe,GAAG,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAC9F,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;QACzB,OAAO,IAAI,EAAE,CAAC;IAChB,CAAC;IAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC7B,MAAM,YAAY,GAAG,GAAG,CAAC,IAAI,CAAC;IAC9B,IAAI,YAAiB,CAAC;IAEtB,SAAS;IACT,GAAG,CAAC,IAAI,GAAG,UAAS,IAAS;QAC3B,YAAY,GAAG,IAAI,CAAC;QACpB,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACvC,CAAC,CAAC;IAEF,aAAa;IACb,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;QAC1B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACxC,MAAM,SAAS,GAAG,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC;YAEvC,SAAS;YACT,IAAI,kBAAkB,GAAG,IAAI,CAAC;YAC9B,IAAI,CAAC;gBACH,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;oBACrC,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;gBAChD,CAAC;qBAAM,CAAC;oBACN,kBAAkB,GAAG,YAAY,CAAC;gBACpC,CAAC;YACH,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,gBAAgB;gBAChB,kBAAkB,GAAG,YAAY,CAAC;YACpC,CAAC;YAED,UAAU;YACV,MAAM,SAAS,GAAG,GAAG,CAAC,EAAE;gBACP,GAAG,CAAC,UAAU,EAAE,aAAa;gBAC7B,GAAG,CAAC,MAAM,EAAE,aAAa;gBACxB,GAAG,CAAC,UAAkB,EAAE,MAAM,EAAE,aAAa;gBAC9C,SAAS,CAAC;YAE3B,wBAAwB;YACxB;;;;;;;;;;;;;;;;;;cAkBE;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,oBAAoB;YACpB,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AApEW,QAAA,eAAe,mBAoE1B"}