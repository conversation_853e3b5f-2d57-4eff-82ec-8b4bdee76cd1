"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.asyncHandler = exports.errorHandler = exports.AppError = void 0;
const index_1 = require("../index");
class AppError extends Error {
    constructor(message, statusCode = 500) {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = true;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.AppError = AppError;
const errorHandler = (err, req, res, next) => {
    let { statusCode = 500, message } = err;
    // Prisma错误处理
    if (err.name === 'PrismaClientKnownRequestError') {
        const prismaError = err;
        switch (prismaError.code) {
            case 'P2002':
                statusCode = 409;
                message = '数据已存在，请检查唯一字段';
                break;
            case 'P2025':
                statusCode = 404;
                message = '记录不存在';
                break;
            case 'P2003':
                statusCode = 400;
                message = '外键约束错误';
                break;
            default:
                statusCode = 500;
                message = '数据库操作失败';
        }
    }
    // JWT错误处理
    if (err.name === 'JsonWebTokenError') {
        statusCode = 401;
        message = '无效的访问令牌';
    }
    if (err.name === 'TokenExpiredError') {
        statusCode = 401;
        message = '访问令牌已过期';
    }
    // 验证错误处理
    if (err.name === 'ValidationError') {
        statusCode = 400;
        message = '数据验证失败';
    }
    // 记录错误日志
    index_1.logger.error('Error occurred:', {
        error: err.message,
        stack: err.stack,
        statusCode,
        url: req.url,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
    });
    // 发送错误响应
    res.status(statusCode).json({
        success: false,
        message,
        ...(process.env.NODE_ENV === 'development' && {
            stack: err.stack,
            error: err
        })
    });
};
exports.errorHandler = errorHandler;
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
exports.asyncHandler = asyncHandler;
//# sourceMappingURL=errorHandler.js.map