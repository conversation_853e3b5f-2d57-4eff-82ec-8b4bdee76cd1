
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.9.0
 * Query Engine version: 81e4af48011447c3cc503a190e86995b66d2a28e
 */
Prisma.prismaVersion = {
  client: "6.9.0",
  engine: "81e4af48011447c3cc503a190e86995b66d2a28e"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  username: 'username',
  email: 'email',
  password: 'password',
  name: 'name',
  role: 'role',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserCompanyScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  companyId: 'companyId',
  createdAt: 'createdAt'
};

exports.Prisma.MenuScalarFieldEnum = {
  id: 'id',
  key: 'key',
  name: 'name',
  path: 'path',
  icon: 'icon',
  parentId: 'parentId',
  sort: 'sort',
  isActive: 'isActive',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserMenuPermissionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  menuId: 'menuId',
  canView: 'canView',
  canEdit: 'canEdit',
  canDelete: 'canDelete',
  canExport: 'canExport',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CompanyScalarFieldEnum = {
  id: 'id',
  name: 'name',
  taxId: 'taxId',
  address: 'address',
  phone: 'phone',
  email: 'email',
  contact: 'contact',
  organization: 'organization',
  remarks: 'remarks',
  registrationDate: 'registrationDate',
  lastInvoiceDate: 'lastInvoiceDate',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.InvoiceScalarFieldEnum = {
  id: 'id',
  invoiceNumber: 'invoiceNumber',
  invoiceCode: 'invoiceCode',
  invoiceDate: 'invoiceDate',
  amount: 'amount',
  taxAmount: 'taxAmount',
  totalAmount: 'totalAmount',
  buyerName: 'buyerName',
  buyerTaxId: 'buyerTaxId',
  buyerAddress: 'buyerAddress',
  buyerPhone: 'buyerPhone',
  buyerBank: 'buyerBank',
  sellerName: 'sellerName',
  sellerTaxId: 'sellerTaxId',
  sellerAddress: 'sellerAddress',
  sellerPhone: 'sellerPhone',
  sellerBank: 'sellerBank',
  invoiceType: 'invoiceType',
  status: 'status',
  verificationStatus: 'verificationStatus',
  drawer: 'drawer',
  remarks: 'remarks',
  remark: 'remark',
  projectName: 'projectName',
  department: 'department',
  costCenter: 'costCenter',
  isDuplicate: 'isDuplicate',
  isArchived: 'isArchived',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  companyId: 'companyId'
};

exports.Prisma.ReceivedInvoiceScalarFieldEnum = {
  id: 'id',
  invoiceNumber: 'invoiceNumber',
  invoiceCode: 'invoiceCode',
  invoiceDate: 'invoiceDate',
  amount: 'amount',
  taxAmount: 'taxAmount',
  totalAmount: 'totalAmount',
  buyerName: 'buyerName',
  buyerTaxId: 'buyerTaxId',
  buyerAddress: 'buyerAddress',
  buyerPhone: 'buyerPhone',
  buyerBank: 'buyerBank',
  sellerName: 'sellerName',
  sellerTaxId: 'sellerTaxId',
  sellerAddress: 'sellerAddress',
  sellerPhone: 'sellerPhone',
  sellerBank: 'sellerBank',
  invoiceType: 'invoiceType',
  status: 'status',
  verificationStatus: 'verificationStatus',
  drawer: 'drawer',
  remarks: 'remarks',
  remark: 'remark',
  projectName: 'projectName',
  department: 'department',
  costCenter: 'costCenter',
  isDuplicate: 'isDuplicate',
  isArchived: 'isArchived',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  companyId: 'companyId'
};

exports.Prisma.InvoiceItemScalarFieldEnum = {
  id: 'id',
  itemName: 'itemName',
  specification: 'specification',
  unit: 'unit',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  amount: 'amount',
  taxRate: 'taxRate',
  taxAmount: 'taxAmount',
  totalAmount: 'totalAmount',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  invoiceId: 'invoiceId'
};

exports.Prisma.ReceivedInvoiceItemScalarFieldEnum = {
  id: 'id',
  itemName: 'itemName',
  specification: 'specification',
  unit: 'unit',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  amount: 'amount',
  taxRate: 'taxRate',
  taxAmount: 'taxAmount',
  totalAmount: 'totalAmount',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  receivedInvoiceId: 'receivedInvoiceId'
};

exports.Prisma.InvoiceAttachmentScalarFieldEnum = {
  id: 'id',
  fileName: 'fileName',
  filePath: 'filePath',
  fileSize: 'fileSize',
  mimeType: 'mimeType',
  fileType: 'fileType',
  isOriginal: 'isOriginal',
  createdAt: 'createdAt',
  invoiceId: 'invoiceId'
};

exports.Prisma.ReceivedInvoiceAttachmentScalarFieldEnum = {
  id: 'id',
  fileName: 'fileName',
  filePath: 'filePath',
  fileSize: 'fileSize',
  mimeType: 'mimeType',
  fileType: 'fileType',
  isOriginal: 'isOriginal',
  createdAt: 'createdAt',
  receivedInvoiceId: 'receivedInvoiceId'
};

exports.Prisma.ReceivedInvoiceAuditLogScalarFieldEnum = {
  id: 'id',
  action: 'action',
  tableName: 'tableName',
  recordId: 'recordId',
  oldValues: 'oldValues',
  newValues: 'newValues',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  createdAt: 'createdAt',
  userId: 'userId',
  receivedInvoiceId: 'receivedInvoiceId'
};

exports.Prisma.AuditLogScalarFieldEnum = {
  id: 'id',
  action: 'action',
  tableName: 'tableName',
  recordId: 'recordId',
  oldValues: 'oldValues',
  newValues: 'newValues',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  createdAt: 'createdAt',
  userId: 'userId',
  invoiceId: 'invoiceId'
};

exports.Prisma.OperationLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  username: 'username',
  operationName: 'operationName',
  method: 'method',
  path: 'path',
  userAgent: 'userAgent',
  ipAddress: 'ipAddress',
  requestParams: 'requestParams',
  responseData: 'responseData',
  statusCode: 'statusCode',
  isSuccess: 'isSuccess',
  errorMessage: 'errorMessage',
  duration: 'duration',
  createdAt: 'createdAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.UserOrderByRelevanceFieldEnum = {
  id: 'id',
  username: 'username',
  email: 'email',
  password: 'password',
  name: 'name'
};

exports.Prisma.UserCompanyOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  companyId: 'companyId'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.MenuOrderByRelevanceFieldEnum = {
  id: 'id',
  key: 'key',
  name: 'name',
  path: 'path',
  icon: 'icon',
  parentId: 'parentId',
  description: 'description'
};

exports.Prisma.UserMenuPermissionOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  menuId: 'menuId'
};

exports.Prisma.CompanyOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  taxId: 'taxId',
  address: 'address',
  phone: 'phone',
  email: 'email',
  contact: 'contact',
  organization: 'organization',
  remarks: 'remarks'
};

exports.Prisma.InvoiceOrderByRelevanceFieldEnum = {
  id: 'id',
  invoiceNumber: 'invoiceNumber',
  invoiceCode: 'invoiceCode',
  buyerName: 'buyerName',
  buyerTaxId: 'buyerTaxId',
  buyerAddress: 'buyerAddress',
  buyerPhone: 'buyerPhone',
  buyerBank: 'buyerBank',
  sellerName: 'sellerName',
  sellerTaxId: 'sellerTaxId',
  sellerAddress: 'sellerAddress',
  sellerPhone: 'sellerPhone',
  sellerBank: 'sellerBank',
  drawer: 'drawer',
  remarks: 'remarks',
  remark: 'remark',
  projectName: 'projectName',
  department: 'department',
  costCenter: 'costCenter',
  companyId: 'companyId'
};

exports.Prisma.ReceivedInvoiceOrderByRelevanceFieldEnum = {
  id: 'id',
  invoiceNumber: 'invoiceNumber',
  invoiceCode: 'invoiceCode',
  buyerName: 'buyerName',
  buyerTaxId: 'buyerTaxId',
  buyerAddress: 'buyerAddress',
  buyerPhone: 'buyerPhone',
  buyerBank: 'buyerBank',
  sellerName: 'sellerName',
  sellerTaxId: 'sellerTaxId',
  sellerAddress: 'sellerAddress',
  sellerPhone: 'sellerPhone',
  sellerBank: 'sellerBank',
  drawer: 'drawer',
  remarks: 'remarks',
  remark: 'remark',
  projectName: 'projectName',
  department: 'department',
  costCenter: 'costCenter',
  companyId: 'companyId'
};

exports.Prisma.InvoiceItemOrderByRelevanceFieldEnum = {
  id: 'id',
  itemName: 'itemName',
  specification: 'specification',
  unit: 'unit',
  invoiceId: 'invoiceId'
};

exports.Prisma.ReceivedInvoiceItemOrderByRelevanceFieldEnum = {
  id: 'id',
  itemName: 'itemName',
  specification: 'specification',
  unit: 'unit',
  receivedInvoiceId: 'receivedInvoiceId'
};

exports.Prisma.InvoiceAttachmentOrderByRelevanceFieldEnum = {
  id: 'id',
  fileName: 'fileName',
  filePath: 'filePath',
  mimeType: 'mimeType',
  invoiceId: 'invoiceId'
};

exports.Prisma.ReceivedInvoiceAttachmentOrderByRelevanceFieldEnum = {
  id: 'id',
  fileName: 'fileName',
  filePath: 'filePath',
  mimeType: 'mimeType',
  receivedInvoiceId: 'receivedInvoiceId'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.ReceivedInvoiceAuditLogOrderByRelevanceFieldEnum = {
  id: 'id',
  action: 'action',
  tableName: 'tableName',
  recordId: 'recordId',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  userId: 'userId',
  receivedInvoiceId: 'receivedInvoiceId'
};

exports.Prisma.AuditLogOrderByRelevanceFieldEnum = {
  id: 'id',
  action: 'action',
  tableName: 'tableName',
  recordId: 'recordId',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  userId: 'userId',
  invoiceId: 'invoiceId'
};

exports.Prisma.OperationLogOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  username: 'username',
  operationName: 'operationName',
  method: 'method',
  path: 'path',
  userAgent: 'userAgent',
  ipAddress: 'ipAddress',
  errorMessage: 'errorMessage'
};
exports.UserRole = exports.$Enums.UserRole = {
  ADMIN: 'ADMIN',
  FINANCE: 'FINANCE',
  BUSINESS: 'BUSINESS',
  AUDITOR: 'AUDITOR',
  USER: 'USER'
};

exports.UserStatus = exports.$Enums.UserStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE'
};

exports.InvoiceType = exports.$Enums.InvoiceType = {
  SPECIAL_VAT: 'SPECIAL_VAT',
  ORDINARY_VAT: 'ORDINARY_VAT',
  ELECTRONIC: 'ELECTRONIC',
  RECEIPT: 'RECEIPT',
  OTHER: 'OTHER'
};

exports.InvoiceStatus = exports.$Enums.InvoiceStatus = {
  NORMAL: 'NORMAL',
  CANCELLED: 'CANCELLED'
};

exports.VerificationStatus = exports.$Enums.VerificationStatus = {
  UNVERIFIED: 'UNVERIFIED',
  VERIFIED: 'VERIFIED',
  FAILED: 'FAILED',
  DUPLICATE: 'DUPLICATE'
};

exports.FileType = exports.$Enums.FileType = {
  PDF: 'PDF',
  IMAGE: 'IMAGE',
  EXCEL: 'EXCEL',
  OTHER: 'OTHER'
};

exports.Prisma.ModelName = {
  User: 'User',
  UserCompany: 'UserCompany',
  Menu: 'Menu',
  UserMenuPermission: 'UserMenuPermission',
  Company: 'Company',
  Invoice: 'Invoice',
  ReceivedInvoice: 'ReceivedInvoice',
  InvoiceItem: 'InvoiceItem',
  ReceivedInvoiceItem: 'ReceivedInvoiceItem',
  InvoiceAttachment: 'InvoiceAttachment',
  ReceivedInvoiceAttachment: 'ReceivedInvoiceAttachment',
  ReceivedInvoiceAuditLog: 'ReceivedInvoiceAuditLog',
  AuditLog: 'AuditLog',
  OperationLog: 'OperationLog'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
