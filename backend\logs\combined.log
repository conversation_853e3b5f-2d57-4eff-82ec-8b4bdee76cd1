{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T07:17:40.107Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T07:17:40.115Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T07:17:40.117Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T07:17:40.117Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T07:17:40.118Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T07:18:26.925Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T07:18:26.928Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T07:18:26.928Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T07:18:26.929Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T07:18:26.929Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T07:19:04.598Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T07:19:04.602Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T07:19:04.602Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T07:19:04.603Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T07:19:04.603Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T07:36:00.344Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T07:36:00.348Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T07:36:00.349Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T07:36:00.349Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T07:36:00.350Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T07:47:10.762Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T07:47:10.765Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T07:47:10.766Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T07:47:10.766Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T07:47:10.767Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T07:48:28.454Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T07:48:28.457Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T07:48:28.458Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T07:48:28.458Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T07:48:28.459Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T07:50:12.993Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T07:50:12.997Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T07:50:12.997Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T07:50:12.998Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T07:50:12.998Z"}
{"error":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\menus.ts:82:34\n\n  79 const targetUserId = userId === 'current' ? req.user!.id : userId;\n  80 \n  81 // 首先检查用户角色\n→ 82 const user = await prisma.user.findUnique(\nCan't reach database server at `nslemons.com:43306`\n\nPlease make sure your database server is running at `nslemons.com:43306`.","level":"error","message":"Error occurred:","method":"GET","service":"invoice-management","stack":"PrismaClientInitializationError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\menus.ts:82:34\n\n  79 const targetUserId = userId === 'current' ? req.user!.id : userId;\n  80 \n  81 // 首先检查用户角色\n→ 82 const user = await prisma.user.findUnique(\nCan't reach database server at `nslemons.com:43306`\n\nPlease make sure your database server is running at `nslemons.com:43306`.\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7759)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\menus.ts:82:16","statusCode":500,"timestamp":"2025-06-08T07:50:43.546Z","url":"/api/menus/user/current/accessible","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\menus.ts:82:34\n\n  79 const targetUserId = userId === 'current' ? req.user!.id : userId;\n  80 \n  81 // 首先检查用户角色\n→ 82 const user = await prisma.user.findUnique(\nCan't reach database server at `nslemons.com:43306`\n\nPlease make sure your database server is running at `nslemons.com:43306`.","level":"error","message":"Error occurred:","method":"GET","service":"invoice-management","stack":"PrismaClientInitializationError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\menus.ts:82:34\n\n  79 const targetUserId = userId === 'current' ? req.user!.id : userId;\n  80 \n  81 // 首先检查用户角色\n→ 82 const user = await prisma.user.findUnique(\nCan't reach database server at `nslemons.com:43306`\n\nPlease make sure your database server is running at `nslemons.com:43306`.\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7759)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\menus.ts:82:16","statusCode":500,"timestamp":"2025-06-08T07:50:43.549Z","url":"/api/menus/user/current/accessible","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\menus.ts:82:34\n\n  79 const targetUserId = userId === 'current' ? req.user!.id : userId;\n  80 \n  81 // 首先检查用户角色\n→ 82 const user = await prisma.user.findUnique(\nCan't reach database server at `nslemons.com:43306`\n\nPlease make sure your database server is running at `nslemons.com:43306`.","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"invoice-management","stack":"PrismaClientInitializationError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\menus.ts:82:34\n\n  79 const targetUserId = userId === 'current' ? req.user!.id : userId;\n  80 \n  81 // 首先检查用户角色\n→ 82 const user = await prisma.user.findUnique(\nCan't reach database server at `nslemons.com:43306`\n\nPlease make sure your database server is running at `nslemons.com:43306`.\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7759)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\menus.ts:82:16","statusCode":500,"timestamp":"2025-06-08T07:50:43.553Z","url":"/api/menus/user/current/accessible","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\menus.ts:82:34\n\n  79 const targetUserId = userId === 'current' ? req.user!.id : userId;\n  80 \n  81 // 首先检查用户角色\n→ 82 const user = await prisma.user.findUnique(\nCan't reach database server at `nslemons.com:43306`\n\nPlease make sure your database server is running at `nslemons.com:43306`.","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"invoice-management","stack":"PrismaClientInitializationError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\menus.ts:82:34\n\n  79 const targetUserId = userId === 'current' ? req.user!.id : userId;\n  80 \n  81 // 首先检查用户角色\n→ 82 const user = await prisma.user.findUnique(\nCan't reach database server at `nslemons.com:43306`\n\nPlease make sure your database server is running at `nslemons.com:43306`.\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7759)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\menus.ts:82:16","statusCode":500,"timestamp":"2025-06-08T07:50:43.559Z","url":"/api/menus/user/current/accessible","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"Error: Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\invoices.ts:203:13\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\errorHandler.ts:93:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at done (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:59:7)\n    at indicateDone (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:63:68)\n    at Multipart.<anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:182:7)\n    at Multipart.emit (node:events:518:28)\n    at Multipart.emit (node:domain:489:12)\n    at emitCloseNT (node:internal/streams/destroy:148:10)","statusCode":400,"timestamp":"2025-06-08T07:51:24.008Z","url":"/api/invoices/import","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"Error: Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\invoices.ts:203:13\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\errorHandler.ts:93:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at done (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:59:7)\n    at indicateDone (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:63:68)\n    at Multipart.<anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:182:7)\n    at Multipart.emit (node:events:518:28)\n    at Multipart.emit (node:domain:489:12)\n    at emitCloseNT (node:internal/streams/destroy:148:10)","statusCode":400,"timestamp":"2025-06-08T07:51:33.570Z","url":"/api/invoices/import","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"Error: Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\invoices.ts:203:13\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\errorHandler.ts:93:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at done (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:59:7)\n    at indicateDone (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:63:68)\n    at Multipart.<anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:182:7)\n    at Multipart.emit (node:events:518:28)\n    at Multipart.emit (node:domain:489:12)\n    at emitCloseNT (node:internal/streams/destroy:148:10)","statusCode":400,"timestamp":"2025-06-08T07:51:33.619Z","url":"/api/invoices/import","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"Error: Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\invoices.ts:203:13\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\errorHandler.ts:93:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at done (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:59:7)\n    at indicateDone (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:63:68)\n    at Multipart.<anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:182:7)\n    at Multipart.emit (node:events:518:28)\n    at Multipart.emit (node:domain:489:12)\n    at emitCloseNT (node:internal/streams/destroy:148:10)","statusCode":400,"timestamp":"2025-06-08T07:51:33.671Z","url":"/api/invoices/import","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"Error: Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\invoices.ts:203:13\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\errorHandler.ts:93:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at done (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:59:7)\n    at indicateDone (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:63:68)\n    at Multipart.<anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:182:7)\n    at Multipart.emit (node:events:518:28)\n    at Multipart.emit (node:domain:489:12)\n    at emitCloseNT (node:internal/streams/destroy:148:10)","statusCode":400,"timestamp":"2025-06-08T07:51:33.718Z","url":"/api/invoices/import","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"Error: Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\invoices.ts:203:13\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\errorHandler.ts:93:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at done (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:59:7)\n    at indicateDone (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:63:68)\n    at Multipart.<anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:182:7)\n    at Multipart.emit (node:events:518:28)\n    at Multipart.emit (node:domain:489:12)\n    at emitCloseNT (node:internal/streams/destroy:148:10)","statusCode":400,"timestamp":"2025-06-08T07:51:53.744Z","url":"/api/invoices/import","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T07:52:48.842Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T07:52:48.844Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T07:52:48.844Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T07:52:48.845Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T07:52:48.845Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T07:53:01.134Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T07:53:01.136Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T07:53:01.137Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T07:53:01.137Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T07:53:01.137Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T07:53:31.762Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T07:53:31.772Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T07:53:31.773Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T07:53:31.777Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T07:53:31.779Z"}
{"error":"Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"Error: Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\invoices.ts:203:13\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\errorHandler.ts:93:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at done (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:59:7)\n    at indicateDone (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:63:68)\n    at Multipart.<anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:182:7)\n    at Multipart.emit (node:events:518:28)\n    at Multipart.emit (node:domain:489:12)\n    at emitCloseNT (node:internal/streams/destroy:148:10)","statusCode":400,"timestamp":"2025-06-08T07:53:32.181Z","url":"/api/invoices/import","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息","ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"Error: Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\invoices.ts:203:13\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\errorHandler.ts:93:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at done (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:59:7)\n    at indicateDone (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:63:68)\n    at Multipart.<anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:182:7)\n    at Multipart.emit (node:events:518:28)\n    at Multipart.emit (node:domain:489:12)\n    at emitCloseNT (node:internal/streams/destroy:148:10)","statusCode":400,"timestamp":"2025-06-08T07:53:53.127Z","url":"/api/invoices/import","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T07:54:44.168Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T07:54:44.172Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T07:54:44.173Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T07:54:44.173Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T07:54:44.173Z"}
{"error":"Excel文件名称必须要包含\"取得\",请选择正确的取得发票文件信息","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"Error: Excel文件名称必须要包含\"取得\",请选择正确的取得发票文件信息\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\receivedInvoices.ts:144:11\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\errorHandler.ts:93:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at done (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:59:7)\n    at indicateDone (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:63:68)\n    at Multipart.<anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:182:7)\n    at Multipart.emit (node:events:518:28)\n    at Multipart.emit (node:domain:489:12)\n    at emitCloseNT (node:internal/streams/destroy:148:10)","statusCode":400,"timestamp":"2025-06-08T07:55:21.229Z","url":"/api/received-invoices/import","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"Error: Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\invoices.ts:203:13\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\errorHandler.ts:93:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at done (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:59:7)\n    at indicateDone (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:63:68)\n    at Multipart.<anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:182:7)\n    at Multipart.emit (node:events:518:28)\n    at Multipart.emit (node:domain:489:12)\n    at emitCloseNT (node:internal/streams/destroy:148:10)","statusCode":400,"timestamp":"2025-06-08T07:55:33.125Z","url":"/api/invoices/import","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"Error: Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\invoices.ts:203:13\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\errorHandler.ts:93:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at done (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:59:7)\n    at indicateDone (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:63:68)\n    at Multipart.<anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:182:7)\n    at Multipart.emit (node:events:518:28)\n    at Multipart.emit (node:domain:489:12)\n    at emitCloseNT (node:internal/streams/destroy:148:10)","statusCode":400,"timestamp":"2025-06-08T07:55:33.164Z","url":"/api/invoices/import","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"Error: Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\invoices.ts:203:13\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\errorHandler.ts:93:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at done (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:59:7)\n    at indicateDone (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:63:68)\n    at Multipart.<anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:182:7)\n    at Multipart.emit (node:events:518:28)\n    at Multipart.emit (node:domain:489:12)\n    at emitCloseNT (node:internal/streams/destroy:148:10)","statusCode":400,"timestamp":"2025-06-08T07:55:33.200Z","url":"/api/invoices/import","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"Error: Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\invoices.ts:203:13\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\errorHandler.ts:93:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at done (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:59:7)\n    at indicateDone (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:63:68)\n    at Multipart.<anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:182:7)\n    at Multipart.emit (node:events:518:28)\n    at Multipart.emit (node:domain:489:12)\n    at emitCloseNT (node:internal/streams/destroy:148:10)","statusCode":400,"timestamp":"2025-06-08T07:56:07.006Z","url":"/api/invoices/import","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"Error: Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\invoices.ts:203:13\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\errorHandler.ts:93:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at done (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:59:7)\n    at indicateDone (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:63:68)\n    at Multipart.<anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:182:7)\n    at Multipart.emit (node:events:518:28)\n    at Multipart.emit (node:domain:489:12)\n    at emitCloseNT (node:internal/streams/destroy:148:10)","statusCode":400,"timestamp":"2025-06-08T07:56:07.041Z","url":"/api/invoices/import","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"Error: Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\invoices.ts:203:13\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\errorHandler.ts:93:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at done (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:59:7)\n    at indicateDone (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:63:68)\n    at Multipart.<anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:182:7)\n    at Multipart.emit (node:events:518:28)\n    at Multipart.emit (node:domain:489:12)\n    at emitCloseNT (node:internal/streams/destroy:148:10)","statusCode":400,"timestamp":"2025-06-08T07:56:07.077Z","url":"/api/invoices/import","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"Error: Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\invoices.ts:203:13\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\errorHandler.ts:93:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at done (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:59:7)\n    at indicateDone (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:63:68)\n    at Multipart.<anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:182:7)\n    at Multipart.emit (node:events:518:28)\n    at Multipart.emit (node:domain:489:12)\n    at emitCloseNT (node:internal/streams/destroy:148:10)","statusCode":400,"timestamp":"2025-06-08T07:56:07.114Z","url":"/api/invoices/import","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"Error: Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\invoices.ts:203:13\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\errorHandler.ts:93:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at done (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:59:7)\n    at indicateDone (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:63:68)\n    at Multipart.<anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:182:7)\n    at Multipart.emit (node:events:518:28)\n    at Multipart.emit (node:domain:489:12)\n    at emitCloseNT (node:internal/streams/destroy:148:10)","statusCode":400,"timestamp":"2025-06-08T07:56:07.169Z","url":"/api/invoices/import","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"Error: Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\invoices.ts:203:13\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\errorHandler.ts:93:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at done (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:59:7)\n    at indicateDone (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:63:68)\n    at Multipart.<anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:182:7)\n    at Multipart.emit (node:events:518:28)\n    at Multipart.emit (node:domain:489:12)\n    at emitCloseNT (node:internal/streams/destroy:148:10)","statusCode":400,"timestamp":"2025-06-08T07:56:07.218Z","url":"/api/invoices/import","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T07:56:38.614Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T07:56:38.617Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T07:56:38.618Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T07:56:38.618Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T07:56:38.618Z"}
{"error":"Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"Error: Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\invoices.ts:203:13\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\errorHandler.ts:93:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at done (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:59:7)\n    at indicateDone (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:63:68)\n    at Multipart.<anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:182:7)\n    at Multipart.emit (node:events:518:28)\n    at Multipart.emit (node:domain:489:12)\n    at emitCloseNT (node:internal/streams/destroy:148:10)","statusCode":400,"timestamp":"2025-06-08T07:56:44.749Z","url":"/api/invoices/import","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T07:57:20.287Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T07:57:20.290Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T07:57:20.291Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T07:57:20.291Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T07:57:20.292Z"}
{"error":"Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"Error: Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\invoices.ts:204:13\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\errorHandler.ts:93:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at done (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:59:7)\n    at indicateDone (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:63:68)\n    at Multipart.<anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:182:7)\n    at Multipart.emit (node:events:518:28)\n    at Multipart.emit (node:domain:489:12)\n    at emitCloseNT (node:internal/streams/destroy:148:10)","statusCode":400,"timestamp":"2025-06-08T07:57:31.711Z","url":"/api/invoices/import","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T07:59:20.005Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T07:59:20.009Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T07:59:20.010Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T07:59:20.010Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T07:59:20.010Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T07:59:47.202Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T07:59:47.205Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T07:59:47.207Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T07:59:47.208Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T07:59:47.208Z"}
{"error":"Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"Error: Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\invoices.ts:216:13\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\errorHandler.ts:93:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at done (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:59:7)\n    at indicateDone (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:63:68)\n    at Multipart.<anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:182:7)\n    at Multipart.emit (node:events:518:28)\n    at Multipart.emit (node:domain:489:12)\n    at emitCloseNT (node:internal/streams/destroy:148:10)","statusCode":400,"timestamp":"2025-06-08T07:59:54.987Z","url":"/api/invoices/import","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T08:00:57.456Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T08:00:57.459Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T08:00:57.459Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T08:00:57.460Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T08:00:57.460Z"}
{"error":"Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"Error: Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\invoices.ts:205:13\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\errorHandler.ts:93:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at done (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:59:7)\n    at indicateDone (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:63:68)\n    at Multipart.<anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:182:7)\n    at Multipart.emit (node:events:518:28)\n    at Multipart.emit (node:domain:489:12)\n    at emitCloseNT (node:internal/streams/destroy:148:10)","statusCode":400,"timestamp":"2025-06-08T08:01:12.607Z","url":"/api/invoices/import","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T08:01:53.030Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T08:01:53.033Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T08:01:53.034Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T08:01:53.034Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T08:01:53.034Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T08:02:48.552Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T08:02:48.555Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T08:02:48.556Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T08:02:48.556Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T08:02:48.557Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T08:03:35.526Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T08:03:35.532Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T08:03:35.533Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T08:03:35.533Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T08:03:35.534Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T08:05:23.673Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T08:05:23.676Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T08:05:23.680Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T08:05:23.680Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T08:05:23.680Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T08:06:29.214Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T08:06:29.218Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T08:06:29.218Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T08:06:29.218Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T08:06:29.219Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T08:06:50.298Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T08:06:50.302Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T08:06:50.303Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T08:06:50.303Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T08:06:50.303Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T08:07:03.668Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T08:07:03.670Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T08:07:03.671Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T08:07:03.671Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T08:07:03.672Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T08:07:18.911Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T08:07:18.913Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T08:07:18.914Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T08:07:18.914Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T08:07:18.914Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T08:08:28.280Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T08:08:28.288Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T08:08:28.289Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T08:08:28.289Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T08:08:28.290Z"}
{"error":"Excel文件名称必须要包含\"取得\",请选择正确的取得发票文件信息","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"Error: Excel文件名称必须要包含\"取得\",请选择正确的取得发票文件信息\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\receivedInvoices.ts:149:11\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\errorHandler.ts:93:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at done (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:59:7)\n    at indicateDone (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:63:68)\n    at Multipart.<anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:182:7)\n    at Multipart.emit (node:events:518:28)\n    at Multipart.emit (node:domain:489:12)\n    at emitCloseNT (node:internal/streams/destroy:148:10)","statusCode":400,"timestamp":"2025-06-08T08:10:17.259Z","url":"/api/received-invoices/import","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Excel文件名称必须要包含\"取得\",请选择正确的取得发票文件信息","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"Error: Excel文件名称必须要包含\"取得\",请选择正确的取得发票文件信息\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\receivedInvoices.ts:149:11\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\errorHandler.ts:93:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at done (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:59:7)\n    at indicateDone (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:63:68)\n    at Multipart.<anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:182:7)\n    at Multipart.emit (node:events:518:28)\n    at Multipart.emit (node:domain:489:12)\n    at emitCloseNT (node:internal/streams/destroy:148:10)","statusCode":400,"timestamp":"2025-06-08T08:10:17.296Z","url":"/api/received-invoices/import","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"Error: Excel文件名称必须要包含“开具”,请选择正确的开具发票文件信息\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\invoices.ts:240:13\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\errorHandler.ts:93:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at done (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:59:7)\n    at indicateDone (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:63:68)\n    at Multipart.<anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\multer\\lib\\make-middleware.js:182:7)\n    at Multipart.emit (node:events:518:28)\n    at Multipart.emit (node:domain:489:12)\n    at emitCloseNT (node:internal/streams/destroy:148:10)","statusCode":400,"timestamp":"2025-06-08T08:15:24.279Z","url":"/api/invoices/import","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.user.count()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\users.ts:79:35\n\n  76 if (status) where.status = status;\n  77 \n  78 // 获取总数\n→ 79 const total = await prisma.user.count(\nCan't reach database server at `nslemons.com:43306`\n\nPlease make sure your database server is running at `nslemons.com:43306`.","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"invoice-management","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.count()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\users.ts:79:35\n\n  76 if (status) where.status = status;\n  77 \n  78 // 获取总数\n→ 79 const total = await prisma.user.count(\nCan't reach database server at `nslemons.com:43306`\n\nPlease make sure your database server is running at `nslemons.com:43306`.\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7459)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\users.ts:79:17","statusCode":500,"timestamp":"2025-06-08T08:28:03.960Z","url":"/api/users?page=1&pageSize=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T08:29:43.758Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T08:29:43.761Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T08:29:43.762Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T08:29:43.762Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T08:29:43.762Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T08:30:11.969Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T08:30:11.972Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T08:30:11.973Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T08:30:11.973Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T08:30:11.973Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T08:30:34.248Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T08:30:34.258Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T08:30:34.281Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T08:30:34.283Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T08:30:34.287Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T08:31:05.496Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T08:31:05.499Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T08:31:05.499Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T08:31:05.500Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T08:31:05.501Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T08:32:44.696Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T08:32:44.698Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T08:32:44.699Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T08:32:44.700Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T08:32:44.700Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T08:35:24.560Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T08:35:24.563Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T08:35:24.564Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T08:35:24.564Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T08:35:24.564Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T08:39:51.735Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T08:39:51.738Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T08:39:51.738Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T08:39:51.739Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T08:39:51.739Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T08:43:33.194Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T08:43:33.197Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T08:43:33.198Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T08:43:33.199Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T08:43:33.199Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T08:44:27.230Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T08:44:27.232Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T08:44:27.233Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T08:44:27.233Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T08:44:27.233Z"}
{"error":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:36\n\n  69 \n  70 const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;\n  71 \n→ 72 const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"invoice-management","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:36\n\n  69 \n  70 const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;\n  71 \n→ 72 const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7459)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async authenticate (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:18)","statusCode":500,"timestamp":"2025-06-08T08:51:39.499Z","url":"/api/invoices?page=1&pageSize=20&sortBy=createdAt&sortOrder=desc","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:36\n\n  69 \n  70 const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;\n  71 \n→ 72 const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"invoice-management","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:36\n\n  69 \n  70 const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;\n  71 \n→ 72 const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7459)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async authenticate (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:18)","statusCode":500,"timestamp":"2025-06-08T08:51:39.505Z","url":"/api/invoices/stats/summary?year=2025","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:36\n\n  69 \n  70 const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;\n  71 \n→ 72 const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"invoice-management","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:36\n\n  69 \n  70 const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;\n  71 \n→ 72 const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7459)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async authenticate (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:18)","statusCode":500,"timestamp":"2025-06-08T08:51:39.512Z","url":"/api/invoices/stats/quarterly?year=2025","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:36\n\n  69 \n  70 const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;\n  71 \n→ 72 const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"invoice-management","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:36\n\n  69 \n  70 const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;\n  71 \n→ 72 const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7459)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async authenticate (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:18)","statusCode":500,"timestamp":"2025-06-08T08:51:39.525Z","url":"/api/invoices/stats/summary?year=2025","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:36\n\n  69 \n  70 const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;\n  71 \n→ 72 const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"invoice-management","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:36\n\n  69 \n  70 const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;\n  71 \n→ 72 const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7459)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async authenticate (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:18)","statusCode":500,"timestamp":"2025-06-08T08:51:39.533Z","url":"/api/invoices/stats/company-summary?years=2025","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.company.count()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:120:38\n\n  117 }\n  118 \n  119 // 获取总数\n→ 120 const total = await prisma.company.count(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"invoice-management","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.company.count()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:120:38\n\n  117 }\n  118 \n  119 // 获取总数\n→ 120 const total = await prisma.company.count(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7459)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:120:17","statusCode":500,"timestamp":"2025-06-08T08:51:40.249Z","url":"/api/companies?page=1&pageSize=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T08:51:50.198Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T08:51:50.201Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T08:51:50.201Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T08:51:50.202Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T08:51:50.202Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T08:52:32.111Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T08:52:32.116Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T08:52:32.117Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T08:52:32.118Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T08:52:32.118Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T08:53:02.120Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T08:53:02.125Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T08:53:02.126Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T08:53:02.126Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T08:53:02.127Z"}
{"error":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:36\n\n  69 \n  70 const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;\n  71 \n→ 72 const user = await prisma.user.findUnique(\nCan't reach database server at `nslemons.com:43306`\n\nPlease make sure your database server is running at `nslemons.com:43306`.","ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred:","method":"GET","service":"invoice-management","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:36\n\n  69 \n  70 const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;\n  71 \n→ 72 const user = await prisma.user.findUnique(\nCan't reach database server at `nslemons.com:43306`\n\nPlease make sure your database server is running at `nslemons.com:43306`.\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7459)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async authenticate (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:18)","statusCode":500,"timestamp":"2025-06-08T08:53:07.392Z","url":"/api/invoices/stats/company-summary?years=2025","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T08:54:41.732Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T08:54:41.736Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T08:54:41.737Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T08:54:41.737Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T08:54:41.738Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T08:55:03.809Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T08:55:03.813Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T08:55:03.814Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T08:55:03.814Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T08:55:03.815Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T09:03:16.146Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T09:03:16.150Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T09:03:16.151Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T09:03:16.152Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T09:03:16.152Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T09:04:52.306Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T09:04:52.308Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T09:04:52.309Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T09:04:52.309Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T09:04:52.310Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T12:27:15.634Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T12:27:15.636Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T12:27:15.637Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T12:27:15.637Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T12:27:15.640Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T12:28:24.726Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T12:28:24.730Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T12:28:24.730Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T12:28:24.731Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T12:28:24.731Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T12:30:11.056Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T12:30:11.058Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T12:30:11.058Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T12:30:11.059Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T12:30:11.059Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T12:30:46.214Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T12:30:46.216Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T12:30:46.217Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T12:30:46.217Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T12:30:46.218Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T12:31:20.009Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T12:31:20.011Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T12:31:20.011Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T12:31:20.012Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T12:31:20.012Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T12:52:01.329Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T12:52:01.333Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T12:52:01.334Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T12:52:01.334Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T12:52:01.335Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T12:52:53.373Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T12:52:53.376Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T12:52:53.376Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T12:52:53.376Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T12:52:53.377Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T12:53:30.808Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T12:53:30.811Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T12:53:30.812Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T12:53:30.813Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T12:53:30.813Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T12:53:55.386Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T12:53:55.389Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T12:53:55.390Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T12:53:55.390Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T12:53:55.391Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T12:54:48.805Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T12:54:48.807Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T12:54:48.808Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T12:54:48.808Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T12:54:48.809Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T13:32:33.883Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T13:32:33.886Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T13:32:33.887Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T13:32:33.887Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T13:32:33.888Z"}
{"clientVersion":"6.9.0","level":"error","message":"Failed to create default admin user: \nInvalid `prisma.user.findFirst()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\index.ts:638:43\n\n  635 // 创建默认管理员用户\n  636 async function createDefaultAdmin() {\n  637   try {\n→ 638     const adminExists = await prisma.user.findFirst(\nCan't reach database server at `nslemons.com:43306`\n\nPlease make sure your database server is running at `nslemons.com:43306`.","name":"PrismaClientInitializationError","service":"invoice-management","stack":"PrismaClientInitializationError: \nInvalid `prisma.user.findFirst()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\index.ts:638:43\n\n  635 // 创建默认管理员用户\n  636 async function createDefaultAdmin() {\n  637   try {\n→ 638     const adminExists = await prisma.user.findFirst(\nCan't reach database server at `nslemons.com:43306`\n\nPlease make sure your database server is running at `nslemons.com:43306`.\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7759)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async createDefaultAdmin (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\index.ts:638:25)","timestamp":"2025-06-08T13:32:39.020Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T13:33:57.456Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T13:33:57.464Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T13:33:57.464Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T13:33:57.465Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T13:33:57.466Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T13:34:26.437Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T13:34:26.440Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T13:34:26.440Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T13:34:26.440Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T13:34:26.441Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T13:35:00.448Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T13:35:00.451Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T13:35:00.452Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T13:35:00.452Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T13:35:00.453Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T13:35:41.686Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T13:35:41.690Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T13:35:41.690Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T13:35:41.691Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T13:35:41.691Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T13:36:24.955Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T13:36:24.957Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T13:36:24.957Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T13:36:24.958Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T13:36:24.958Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T13:47:03.236Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T13:47:03.239Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T13:47:03.240Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T13:47:03.240Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T13:47:03.240Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T13:49:55.225Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T13:49:55.229Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T13:49:55.230Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T13:49:55.230Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T13:49:55.231Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T13:50:46.490Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T13:50:46.493Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T13:50:46.494Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T13:50:46.495Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T13:50:46.495Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T14:15:50.214Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T14:15:50.218Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T14:15:50.218Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T14:15:50.219Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T14:15:50.219Z"}
{"error":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:36\n\n  69 \n  70 const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;\n  71 \n→ 72 const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"invoice-management","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:36\n\n  69 \n  70 const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;\n  71 \n→ 72 const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7459)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async authenticate (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:18)","statusCode":500,"timestamp":"2025-06-08T14:17:05.760Z","url":"/api/menus/user/current/accessible","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T14:18:54.668Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T14:18:54.672Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T14:18:54.673Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T14:18:54.673Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T14:18:54.674Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T14:19:36.407Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T14:19:36.409Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T14:19:36.410Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T14:19:36.410Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T14:19:36.410Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T14:41:03.263Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T14:41:03.265Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T14:41:03.266Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T14:41:03.266Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T14:41:03.266Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T14:41:34.703Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T14:41:34.705Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T14:41:34.706Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T14:41:34.706Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T14:41:34.706Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T14:42:05.735Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T14:42:05.737Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T14:42:05.737Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T14:42:05.738Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T14:42:05.738Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T14:43:34.851Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T14:43:34.854Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T14:43:34.854Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T14:43:34.855Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T14:43:34.855Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T14:44:51.221Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T14:44:51.226Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T14:44:51.227Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T14:44:51.227Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T14:44:51.227Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T14:45:59.586Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T14:45:59.591Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T14:45:59.591Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T14:45:59.592Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T14:45:59.592Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T14:46:43.257Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T14:46:43.259Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T14:46:43.260Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T14:46:43.260Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T14:46:43.261Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T14:49:01.229Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T14:49:01.232Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T14:49:01.233Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T14:49:01.233Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T14:49:01.234Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T14:49:24.061Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T14:49:24.064Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T14:49:24.066Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T14:49:24.067Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T14:49:24.068Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T14:58:48.121Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T14:58:48.124Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T14:58:48.125Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T14:58:48.125Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T14:58:48.125Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T14:59:24.858Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T14:59:24.860Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T14:59:24.861Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T14:59:24.861Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T14:59:24.861Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T14:59:51.420Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T14:59:51.423Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T14:59:51.423Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T14:59:51.424Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T14:59:51.424Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T15:00:54.415Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T15:00:54.417Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T15:00:54.418Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T15:00:54.418Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T15:00:54.419Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T15:01:22.118Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T15:01:22.122Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T15:01:22.122Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T15:01:22.123Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T15:01:22.123Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T15:02:14.020Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T15:02:14.022Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T15:02:14.023Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T15:02:14.023Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T15:02:14.023Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T15:02:56.773Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T15:02:56.783Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T15:02:56.784Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T15:02:56.784Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T15:02:56.786Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T15:04:43.099Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T15:04:43.102Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T15:04:43.102Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T15:04:43.102Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T15:04:43.103Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T15:05:55.402Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T15:05:55.404Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T15:05:55.405Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T15:05:55.405Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T15:05:55.405Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T15:06:19.888Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T15:06:19.892Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T15:06:19.893Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T15:06:19.893Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T15:06:19.894Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T15:07:05.386Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T15:07:05.388Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T15:07:05.389Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T15:07:05.389Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T15:07:05.390Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T15:07:29.435Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T15:07:29.438Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T15:07:29.439Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T15:07:29.439Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T15:07:29.440Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T15:07:52.652Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T15:07:52.659Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T15:07:52.661Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T15:07:52.662Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T15:07:52.663Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T15:08:22.808Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T15:08:22.810Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T15:08:22.811Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T15:08:22.811Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T15:08:22.812Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T15:09:09.557Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T15:09:09.559Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T15:09:09.560Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T15:09:09.560Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T15:09:09.561Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T15:09:32.023Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T15:09:32.027Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T15:09:32.028Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T15:09:32.029Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T15:09:32.030Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T15:10:11.958Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T15:10:11.961Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T15:10:11.962Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T15:10:11.963Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T15:10:11.963Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T15:10:33.906Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T15:10:33.912Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T15:10:33.913Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T15:10:33.913Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T15:10:33.914Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-08T15:11:18.617Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-08T15:11:18.621Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-08T15:11:18.622Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-08T15:11:18.622Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-08T15:11:18.623Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T05:50:23.551Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T05:50:23.554Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T05:50:23.555Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T05:50:23.555Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T05:50:23.555Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T05:51:30.453Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T05:51:30.458Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T05:51:30.459Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T05:51:30.460Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T05:51:30.460Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T05:52:15.985Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T05:52:15.988Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T05:52:15.988Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T05:52:15.988Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T05:52:15.989Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T05:54:02.057Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T05:54:02.060Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T05:54:02.061Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T05:54:02.061Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T05:54:02.065Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T05:54:46.154Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T05:54:46.158Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T05:54:46.159Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T05:54:46.160Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T05:54:46.160Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T05:56:02.449Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T05:56:02.452Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T05:56:02.452Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T05:56:02.453Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T05:56:02.453Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T05:58:08.972Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T05:58:08.975Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T05:58:08.976Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T05:58:08.976Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T05:58:08.978Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T05:59:42.838Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T05:59:42.840Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T05:59:42.840Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T05:59:42.841Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T05:59:42.842Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T06:01:15.125Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T06:01:15.128Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T06:01:15.128Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T06:01:15.129Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T06:01:15.129Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T06:04:08.314Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T06:04:08.316Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T06:04:08.317Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T06:04:08.317Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T06:04:08.317Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T06:04:47.337Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T06:04:47.346Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T06:04:47.347Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T06:04:47.348Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T06:04:47.348Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T06:05:39.182Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T06:05:39.185Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T06:05:39.186Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T06:05:39.186Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T06:05:39.187Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T06:10:45.469Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T06:10:45.471Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T06:10:45.471Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T06:10:45.472Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T06:10:45.472Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T06:12:32.038Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T06:12:32.041Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T06:12:32.042Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T06:12:32.042Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T06:12:32.042Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T07:09:44.556Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T07:09:44.559Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T07:09:44.559Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T07:09:44.559Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T07:09:44.560Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T07:10:09.302Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T07:10:09.305Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T07:10:09.305Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T07:10:09.306Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T07:10:09.306Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T07:11:38.560Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T07:11:38.563Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T07:11:38.564Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T07:11:38.564Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T07:11:38.564Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T07:31:13.071Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T07:31:13.076Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T07:31:13.077Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T07:31:13.077Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T07:31:13.078Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T07:35:51.225Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T07:35:51.228Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T07:35:51.229Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T07:35:51.229Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T07:35:51.230Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T07:36:23.008Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T07:36:23.011Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T07:36:23.012Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T07:36:23.012Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T07:36:23.013Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T07:36:51.079Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T07:36:51.082Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T07:36:51.083Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T07:36:51.084Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T07:36:51.084Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T07:39:06.917Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T07:39:06.920Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T07:39:06.921Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T07:39:06.921Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T07:39:06.922Z"}
{"level":"info","message":"🛑 Shutting down server...","service":"invoice-management","timestamp":"2025-06-09T07:47:36.754Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T07:48:20.600Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T07:48:20.602Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T07:48:20.603Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T07:48:20.603Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T07:48:20.603Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T07:54:11.178Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T07:54:11.181Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T07:54:11.182Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T07:54:11.182Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T07:54:11.183Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T07:54:34.186Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T07:54:34.189Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T07:54:34.190Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T07:54:34.190Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T07:54:34.190Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T07:55:00.089Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T07:55:00.097Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T07:55:00.098Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T07:55:00.099Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T07:55:00.099Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T07:55:26.693Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T07:55:26.695Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T07:55:26.696Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T07:55:26.696Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T07:55:26.696Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T07:55:49.974Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T07:55:49.979Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T07:55:49.980Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T07:55:49.980Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T07:55:49.981Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T07:56:12.114Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T07:56:12.117Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T07:56:12.118Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T07:56:12.118Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T07:56:12.118Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T07:56:35.858Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T07:56:35.861Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T07:56:35.862Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T07:56:35.863Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T07:56:35.863Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T07:58:33.089Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T07:58:33.092Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T07:58:33.093Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T07:58:33.093Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T07:58:33.093Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T08:00:30.604Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T08:00:30.607Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T08:00:30.607Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T08:00:30.607Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T08:00:30.608Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T08:01:36.845Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T08:01:36.849Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T08:01:36.849Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T08:01:36.850Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T08:01:36.850Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T08:03:13.244Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T08:03:13.247Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T08:03:13.248Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T08:03:13.248Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T08:03:13.248Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T08:07:31.817Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T08:07:31.859Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T08:07:31.861Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T08:07:31.861Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T08:07:31.862Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T08:08:47.750Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T08:08:47.753Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T08:08:47.753Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T08:08:47.753Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T08:08:47.754Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T08:14:49.036Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T08:14:49.040Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T08:14:49.040Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T08:14:49.041Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T08:14:49.042Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T08:25:32.836Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T08:25:32.841Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T08:25:32.841Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T08:25:32.842Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T08:25:32.842Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T08:25:59.616Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T08:25:59.621Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T08:25:59.622Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T08:25:59.622Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T08:25:59.623Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T08:26:29.738Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T08:26:29.741Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T08:26:29.741Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T08:26:29.742Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T08:26:29.742Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T08:28:02.325Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T08:28:02.328Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T08:28:02.329Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T08:28:02.329Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T08:28:02.330Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T08:28:31.145Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T08:28:31.151Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T08:28:31.152Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T08:28:31.152Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T08:28:31.153Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T08:29:26.184Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T08:29:26.187Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T08:29:26.188Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T08:29:26.189Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T08:29:26.189Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T08:29:52.085Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T08:29:52.088Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T08:29:52.089Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T08:29:52.090Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T08:29:52.090Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T08:30:20.436Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T08:30:20.440Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T08:30:20.441Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T08:30:20.441Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T08:30:20.442Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T08:31:58.014Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T08:31:58.018Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T08:31:58.019Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T08:31:58.020Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T08:31:58.020Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T08:32:19.934Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T08:32:19.938Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T08:32:19.939Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T08:32:19.939Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T08:32:19.940Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T08:33:02.623Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T08:33:02.626Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T08:33:02.627Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T08:33:02.627Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T08:33:02.628Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T08:35:15.783Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T08:35:15.786Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T08:35:15.786Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T08:35:15.787Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T08:35:15.787Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T08:55:26.780Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T08:55:26.783Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T08:55:26.783Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T08:55:26.784Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T08:55:26.784Z"}
{"error":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\menus.ts:82:34\n\n  79 const targetUserId = userId === 'current' ? req.user!.id : userId;\n  80 \n  81 // 首先检查用户角色\n→ 82 const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"invoice-management","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\menus.ts:82:34\n\n  79 const targetUserId = userId === 'current' ? req.user!.id : userId;\n  80 \n  81 // 首先检查用户角色\n→ 82 const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7459)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\menus.ts:82:16","statusCode":500,"timestamp":"2025-06-09T09:37:58.059Z","url":"/api/menus/user/current/accessible","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T09:38:37.521Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T09:38:37.524Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T09:38:37.524Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T09:38:37.524Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T09:38:37.525Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T09:43:14.569Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T09:43:14.571Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T09:43:14.572Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T09:43:14.572Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T09:43:14.572Z"}
{"error":"访问令牌缺失","ip":"::1","level":"error","message":"Error occurred:","method":"HEAD","service":"invoice-management","stack":"Error: 访问令牌缺失\n    at authenticate (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:67:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\index.js:342:13)","statusCode":401,"timestamp":"2025-06-09T10:48:38.474Z","url":"/api/invoices/template","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.22621.4249"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T11:01:05.669Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T11:01:05.673Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T11:01:05.673Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T11:01:05.673Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T11:01:05.674Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T11:05:12.364Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T11:05:12.374Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T11:05:12.367Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T11:05:12.367Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T11:05:12.367Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T11:05:12.368Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T11:05:12.377Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T11:05:12.377Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T11:05:12.377Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T11:05:12.378Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T11:09:07.520Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T11:09:07.523Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T11:09:07.523Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T11:09:07.524Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T11:09:07.524Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T11:09:07.923Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T11:09:07.925Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T11:09:07.925Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T11:09:07.926Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T11:09:07.926Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T11:30:10.675Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T11:30:10.679Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T11:30:10.680Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T11:30:10.682Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T11:30:10.682Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T11:30:10.878Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T11:30:10.882Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T11:30:10.883Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T11:30:10.884Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T11:30:10.884Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T11:30:58.248Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T11:30:58.252Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T11:30:58.252Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T11:30:58.252Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T11:30:58.253Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T11:30:58.464Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T11:30:58.467Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T11:30:58.468Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T11:30:58.468Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T11:30:58.469Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T11:31:29.155Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T11:31:29.158Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T11:31:29.159Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T11:31:29.159Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T11:31:29.159Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T11:31:29.174Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T11:31:29.179Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T11:31:29.180Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T11:31:29.181Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T11:31:29.181Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T11:34:35.604Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T11:34:35.607Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T11:34:35.607Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T11:34:35.607Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T11:34:35.608Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T11:59:01.862Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T11:59:01.865Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T11:59:01.866Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T11:59:01.867Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T11:59:01.867Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T11:59:02.041Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T11:59:02.045Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T11:59:02.046Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T11:59:02.046Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T11:59:02.046Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T12:05:29.337Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T12:05:29.341Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T12:05:29.342Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T12:05:29.343Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T12:05:29.343Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T12:05:29.472Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T12:05:29.476Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T12:05:29.476Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T12:05:29.477Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T12:05:29.477Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T12:09:36.396Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T12:09:36.398Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T12:09:36.398Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T12:09:36.399Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T12:09:36.399Z"}
{"error":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:36\n\n  69 \n  70 const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;\n  71 \n→ 72 const user = await prisma.user.findUnique(\nCan't reach database server at `nslemons.com:43306`\n\nPlease make sure your database server is running at `nslemons.com:43306`.","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"invoice-management","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:36\n\n  69 \n  70 const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;\n  71 \n→ 72 const user = await prisma.user.findUnique(\nCan't reach database server at `nslemons.com:43306`\n\nPlease make sure your database server is running at `nslemons.com:43306`.\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7459)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async authenticate (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:18)","statusCode":500,"timestamp":"2025-06-09T12:10:05.303Z","url":"/api/menus/user/current/accessible","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T12:47:26.963Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T12:47:26.968Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T12:47:26.969Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T12:47:26.969Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T12:47:26.970Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T12:47:27.330Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T12:47:27.334Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T12:47:27.335Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T12:47:27.335Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T12:47:27.336Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T12:48:21.540Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T12:48:21.544Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T12:48:21.545Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T12:48:21.545Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T12:48:21.545Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T12:48:21.702Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T12:48:21.706Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T12:48:21.707Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T12:48:21.707Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T12:48:21.708Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T12:58:04.441Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T12:58:04.444Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T12:58:04.444Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T12:58:04.444Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T12:58:04.445Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T13:46:45.777Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T13:46:45.781Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T13:46:45.782Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T13:46:45.782Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T13:46:45.782Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T13:46:45.877Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T13:46:45.881Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T13:46:45.881Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T13:46:45.882Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T13:46:45.882Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T13:47:25.857Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T13:47:25.860Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T13:47:25.860Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T13:47:25.860Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T13:47:25.861Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T14:26:56.049Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T14:26:56.052Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T14:26:56.055Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T14:26:56.055Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T14:26:56.056Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T16:45:59.927Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T16:45:59.935Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T16:45:59.935Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T16:45:59.936Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T16:45:59.936Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T16:45:59.998Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T16:46:00.003Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T16:46:00.003Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T16:46:00.004Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T16:46:00.004Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T16:46:00.176Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T16:46:00.179Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T16:46:00.181Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T16:46:00.181Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T16:46:00.182Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T16:46:57.491Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T16:46:57.495Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T16:46:57.495Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T16:46:57.496Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T16:46:57.496Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T16:51:18.944Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T16:51:18.958Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T16:51:18.960Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T16:51:18.999Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T16:51:19.004Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T16:51:19.290Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T16:51:19.294Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T16:51:19.296Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T16:51:19.296Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T16:51:19.297Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T16:51:19.365Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T16:51:19.371Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T16:51:19.372Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T16:51:19.373Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T16:51:19.374Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T16:52:23.243Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T16:52:23.252Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T16:52:23.253Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T16:52:23.253Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T16:52:23.254Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T16:52:23.256Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T16:52:23.260Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T16:52:23.261Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T16:52:23.264Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T16:52:23.266Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T16:52:23.461Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T16:52:23.475Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T16:52:23.476Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T16:52:23.476Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T16:52:23.477Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T17:54:16.508Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T17:54:16.510Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T17:54:16.511Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T17:54:16.512Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T17:54:16.512Z"}
{"error":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:36\n\n  69 \n  70 const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;\n  71 \n→ 72 const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"invoice-management","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:36\n\n  69 \n  70 const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;\n  71 \n→ 72 const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7459)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async authenticate (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:18)","statusCode":500,"timestamp":"2025-06-09T17:56:23.736Z","url":"/api/invoices/stats/summary?year=2025","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T18:43:03.831Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T18:43:03.833Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T18:43:03.834Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T18:43:03.834Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T18:43:03.834Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T18:55:58.977Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T18:55:58.979Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T18:55:58.980Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T18:55:58.980Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T18:55:58.980Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T19:07:10.515Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T19:07:10.517Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T19:07:10.518Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T19:07:10.518Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T19:07:10.519Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T19:42:06.248Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T19:42:06.251Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T19:42:06.252Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T19:42:06.252Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T19:42:06.253Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-09T19:50:48.185Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-09T19:50:48.187Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-09T19:50:48.188Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-09T19:50:48.188Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-09T19:50:48.189Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-10T09:30:28.820Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-10T09:30:28.823Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-10T09:30:28.823Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-10T09:30:28.824Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-10T09:30:28.824Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-10T09:30:50.055Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-10T09:30:50.058Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-10T09:30:50.059Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-10T09:30:50.059Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-10T09:30:50.060Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-10T18:35:54.951Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-10T18:35:54.954Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-10T18:35:54.954Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-10T18:35:54.954Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-10T18:35:54.955Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-10T18:36:22.824Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-10T18:36:22.826Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-10T18:36:22.826Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-10T18:36:22.827Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-10T18:36:22.827Z"}
{"error":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:36\n\n  69 \n  70 const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;\n  71 \n→ 72 const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)","level":"error","message":"Error occurred:","method":"GET","service":"invoice-management","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:36\n\n  69 \n  70 const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;\n  71 \n→ 72 const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7459)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async authenticate (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:18)","statusCode":500,"timestamp":"2025-06-10T18:41:18.187Z","url":"/api/menus/user/current/accessible","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:36\n\n  69 \n  70 const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;\n  71 \n→ 72 const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)","level":"error","message":"Error occurred:","method":"GET","service":"invoice-management","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:36\n\n  69 \n  70 const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;\n  71 \n→ 72 const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7459)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async authenticate (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:18)","statusCode":500,"timestamp":"2025-06-10T18:41:18.190Z","url":"/api/auth/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\auth.ts:140:34\n\n  137 \n  138 // 获取当前用户信息\n  139 router.get('/me', authenticate, asyncHandler(async (req: AuthenticatedRequest, res: express.Response) => {\n→ 140   const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"invoice-management","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\auth.ts:140:34\n\n  137 \n  138 // 获取当前用户信息\n  139 router.get('/me', authenticate, asyncHandler(async (req: AuthenticatedRequest, res: express.Response) => {\n→ 140   const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7459)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\auth.ts:140:16","statusCode":500,"timestamp":"2025-06-10T18:41:27.774Z","url":"/api/auth/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\menus.ts:82:34\n\n  79 const targetUserId = userId === 'current' ? req.user!.id : userId;\n  80 \n  81 // 首先检查用户角色\n→ 82 const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"invoice-management","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\menus.ts:82:34\n\n  79 const targetUserId = userId === 'current' ? req.user!.id : userId;\n  80 \n  81 // 首先检查用户角色\n→ 82 const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7459)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\menus.ts:82:16","statusCode":500,"timestamp":"2025-06-10T18:41:27.782Z","url":"/api/menus/user/current/accessible","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-10T18:45:00.896Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-10T18:45:00.898Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-10T18:45:00.898Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-10T18:45:00.899Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-10T18:45:00.899Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-10T18:45:26.377Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-10T18:45:26.379Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-10T18:45:26.380Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-10T18:45:26.380Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-10T18:45:26.381Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-10T18:45:49.042Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-10T18:45:49.044Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-10T18:45:49.045Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-10T18:45:49.045Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-10T18:45:49.046Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-10T18:46:14.100Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-10T18:46:14.102Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-10T18:46:14.103Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-10T18:46:14.103Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-10T18:46:14.103Z"}
{"error":"取得发票不存在","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"invoice-management","stack":"Error: 取得发票不存在\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\receivedInvoices.ts:725:11","statusCode":404,"timestamp":"2025-06-10T19:00:55.211Z","url":"/api/received-invoices/export?startDate=2025-04-01&endDate=2025-06-10","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-10T19:01:34.940Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-10T19:01:34.943Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-10T19:01:34.944Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-10T19:01:34.944Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-10T19:01:34.944Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-10T19:01:50.426Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-10T19:01:50.428Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-10T19:01:50.429Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-10T19:01:50.429Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-10T19:01:50.429Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-10T19:02:08.726Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-10T19:02:08.728Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-10T19:02:08.729Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-10T19:02:08.729Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-10T19:02:08.730Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-10T19:08:19.214Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-10T19:08:19.217Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-10T19:08:19.217Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-10T19:08:19.218Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-10T19:08:19.218Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-10T19:08:42.474Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-10T19:08:42.476Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-10T19:08:42.476Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-10T19:08:42.477Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-10T19:08:42.477Z"}
{"error":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:36\n\n  69 \n  70 const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;\n  71 \n→ 72 const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"invoice-management","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:36\n\n  69 \n  70 const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;\n  71 \n→ 72 const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7459)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async authenticate (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:18)","statusCode":500,"timestamp":"2025-06-10T21:32:19.050Z","url":"/api/invoices?page=1&pageSize=20&sortBy=createdAt&sortOrder=desc","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:36\n\n  69 \n  70 const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;\n  71 \n→ 72 const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"invoice-management","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:36\n\n  69 \n  70 const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;\n  71 \n→ 72 const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7459)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async authenticate (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:18)","statusCode":500,"timestamp":"2025-06-11T01:53:18.902Z","url":"/api/invoices/stats/company-summary?years=2025","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:36\n\n  69 \n  70 const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;\n  71 \n→ 72 const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"invoice-management","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:36\n\n  69 \n  70 const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;\n  71 \n→ 72 const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7459)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async authenticate (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:18)","statusCode":500,"timestamp":"2025-06-11T02:01:18.902Z","url":"/api/invoices/stats/quarterly?year=2025","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"取得发票不存在","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"invoice-management","stack":"Error: 取得发票不存在\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\receivedInvoices.ts:742:11","statusCode":404,"timestamp":"2025-06-11T02:25:01.028Z","url":"/api/received-invoices/export?startDate=2025-04-01&endDate=2025-06-11","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"取得发票不存在","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"invoice-management","stack":"Error: 取得发票不存在\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\receivedInvoices.ts:742:11","statusCode":404,"timestamp":"2025-06-11T02:25:06.746Z","url":"/api/received-invoices/export?startDate=2025-04-01&endDate=2025-06-11&onlyShowPartnerCompanies=true","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T03:00:58.246Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T03:00:58.250Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T03:00:58.251Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T03:00:58.252Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T03:00:58.252Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T03:00:58.279Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T03:00:58.283Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T03:00:58.285Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T03:00:58.286Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T03:00:58.289Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T03:01:46.555Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T03:01:46.559Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T03:01:46.559Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T03:01:46.560Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T03:01:46.560Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T03:01:46.668Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T03:01:46.671Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T03:01:46.672Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T03:01:46.672Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T03:01:46.673Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T03:02:08.356Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T03:02:08.358Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T03:02:08.359Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T03:02:08.359Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T03:02:08.359Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T03:02:08.435Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T03:02:08.437Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T03:02:08.438Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T03:02:08.438Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T03:02:08.438Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T03:05:40.991Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T03:05:40.996Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T03:05:40.997Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T03:05:40.997Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T03:05:40.998Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T03:05:41.032Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T03:05:41.038Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T03:05:41.040Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T03:05:41.040Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T03:05:41.041Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T03:05:41.098Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T03:05:41.104Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T03:05:41.105Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T03:05:41.105Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T03:05:41.109Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T03:17:31.249Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T03:17:31.251Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T03:17:31.252Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T03:17:31.252Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T03:17:31.252Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T03:21:01.324Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T03:21:01.328Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T03:21:01.328Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T03:21:01.328Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T03:21:01.329Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T03:34:37.872Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T03:34:37.875Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T03:34:37.876Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T03:34:37.877Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T03:34:37.877Z"}
{"error":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:36\n\n  69 \n  70 const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;\n  71 \n→ 72 const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"invoice-management","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:36\n\n  69 \n  70 const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;\n  71 \n→ 72 const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7459)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async authenticate (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:72:18)","statusCode":500,"timestamp":"2025-06-11T04:04:07.031Z","url":"/api/menus/user/current/accessible","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.company.create()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:308:40\n\n  305   value.organization = req.user.name;\n  306 }\n  307 \n→ 308 const company = await prisma.company.create(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.company.create()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:308:40\n\n  305   value.organization = req.user.name;\n  306 }\n  307 \n→ 308 const company = await prisma.company.create(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7649)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:308:19","statusCode":500,"timestamp":"2025-06-11T04:13:33.220Z","url":"/api/companies","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.company.create()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:308:40\n\n  305   value.organization = req.user.name;\n  306 }\n  307 \n→ 308 const company = await prisma.company.create(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.company.create()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:308:40\n\n  305   value.organization = req.user.name;\n  306 }\n  307 \n→ 308 const company = await prisma.company.create(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7649)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:308:19","statusCode":500,"timestamp":"2025-06-11T04:13:35.398Z","url":"/api/companies","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.company.create()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:308:40\n\n  305   value.organization = req.user.name;\n  306 }\n  307 \n→ 308 const company = await prisma.company.create(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.company.create()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:308:40\n\n  305   value.organization = req.user.name;\n  306 }\n  307 \n→ 308 const company = await prisma.company.create(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7649)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:308:19","statusCode":500,"timestamp":"2025-06-11T04:14:03.557Z","url":"/api/companies","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.company.create()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:308:40\n\n  305   value.organization = req.user.name;\n  306 }\n  307 \n→ 308 const company = await prisma.company.create(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.company.create()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:308:40\n\n  305   value.organization = req.user.name;\n  306 }\n  307 \n→ 308 const company = await prisma.company.create(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7649)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:308:19","statusCode":500,"timestamp":"2025-06-11T04:14:04.763Z","url":"/api/companies","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.company.create()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:308:40\n\n  305   value.organization = req.user.name;\n  306 }\n  307 \n→ 308 const company = await prisma.company.create(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.company.create()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:308:40\n\n  305   value.organization = req.user.name;\n  306 }\n  307 \n→ 308 const company = await prisma.company.create(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7649)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:308:19","statusCode":500,"timestamp":"2025-06-11T04:14:08.053Z","url":"/api/companies","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.company.create()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:308:40\n\n  305   value.organization = req.user.name;\n  306 }\n  307 \n→ 308 const company = await prisma.company.create(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.company.create()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:308:40\n\n  305   value.organization = req.user.name;\n  306 }\n  307 \n→ 308 const company = await prisma.company.create(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7649)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:308:19","statusCode":500,"timestamp":"2025-06-11T04:14:08.058Z","url":"/api/companies","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.company.create()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:308:40\n\n  305   value.organization = req.user.name;\n  306 }\n  307 \n→ 308 const company = await prisma.company.create(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.company.create()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:308:40\n\n  305   value.organization = req.user.name;\n  306 }\n  307 \n→ 308 const company = await prisma.company.create(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7649)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:308:19","statusCode":500,"timestamp":"2025-06-11T04:14:25.055Z","url":"/api/companies","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.company.create()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:308:40\n\n  305   value.organization = req.user.name;\n  306 }\n  307 \n→ 308 const company = await prisma.company.create(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.company.create()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:308:40\n\n  305   value.organization = req.user.name;\n  306 }\n  307 \n→ 308 const company = await prisma.company.create(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7649)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:308:19","statusCode":500,"timestamp":"2025-06-11T04:14:27.058Z","url":"/api/companies","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.company.create()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:308:40\n\n  305   value.organization = req.user.name;\n  306 }\n  307 \n→ 308 const company = await prisma.company.create(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.company.create()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:308:40\n\n  305   value.organization = req.user.name;\n  306 }\n  307 \n→ 308 const company = await prisma.company.create(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7649)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:308:19","statusCode":500,"timestamp":"2025-06-11T04:14:55.061Z","url":"/api/companies","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.company.create()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:308:40\n\n  305   value.organization = req.user.name;\n  306 }\n  307 \n→ 308 const company = await prisma.company.create(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"invoice-management","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.company.create()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:308:40\n\n  305   value.organization = req.user.name;\n  306 }\n  307 \n→ 308 const company = await prisma.company.create(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7649)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:308:19","statusCode":500,"timestamp":"2025-06-11T04:14:56.060Z","url":"/api/companies","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T04:17:33.806Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T04:17:33.809Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T04:17:33.810Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T04:17:33.810Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T04:17:33.810Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T04:41:25.795Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T04:41:25.798Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T04:41:25.798Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T04:41:25.799Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T04:41:25.799Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T04:41:50.738Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T04:41:50.740Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T04:41:50.741Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T04:41:50.741Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T04:41:50.742Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T04:42:34.474Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T04:42:34.477Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T04:42:34.477Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T04:42:34.478Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T04:42:34.478Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T04:42:52.375Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T04:42:52.380Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T04:42:52.381Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T04:42:52.381Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T04:42:52.382Z"}
{"error":"\nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })","ip":"::1","level":"error","message":"Error occurred:","method":"PUT","service":"invoice-management","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7649)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:19","statusCode":500,"timestamp":"2025-06-11T04:51:15.279Z","url":"/api/companies/cmbq2rhj200g2w0bpk8aqzqfg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })","ip":"::1","level":"error","message":"Error occurred:","method":"PUT","service":"invoice-management","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7649)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:19","statusCode":500,"timestamp":"2025-06-11T04:51:17.272Z","url":"/api/companies/cmbq2rhj200g2w0bpk8aqzqfg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })","ip":"::1","level":"error","message":"Error occurred:","method":"PUT","service":"invoice-management","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7649)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:19","statusCode":500,"timestamp":"2025-06-11T04:51:28.271Z","url":"/api/companies/cmbq2rhj200g2w0bpk8aqzqfg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })","ip":"::1","level":"error","message":"Error occurred:","method":"PUT","service":"invoice-management","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7649)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:19","statusCode":500,"timestamp":"2025-06-11T04:51:29.272Z","url":"/api/companies/cmbq2rhj200g2w0bpk8aqzqfg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.company.findFirst()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:334:48\n\n  331 }\n  332 \n  333 // 检查公司是否存在\n→ 334 const existingCompany = await prisma.company.findFirst(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)","ip":"::1","level":"error","message":"Error occurred:","method":"PUT","service":"invoice-management","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.company.findFirst()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:334:48\n\n  331 }\n  332 \n  333 // 检查公司是否存在\n→ 334 const existingCompany = await prisma.company.findFirst(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 9)\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7459)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:334:27","statusCode":500,"timestamp":"2025-06-11T04:53:02.315Z","url":"/api/companies/cmbq2rhj200g2w0bpk8aqzqfg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })","ip":"::1","level":"error","message":"Error occurred:","method":"PUT","service":"invoice-management","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7649)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:19","statusCode":500,"timestamp":"2025-06-11T04:53:03.020Z","url":"/api/companies/cmbpx0rav00imw0h45zitdx6a","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"\nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })","ip":"::1","level":"error","message":"Error occurred:","method":"PUT","service":"invoice-management","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7649)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:19","statusCode":500,"timestamp":"2025-06-11T04:53:07.020Z","url":"/api/companies/cmbpx0rav00imw0h45zitdx6a","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"\nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })","ip":"::1","level":"error","message":"Error occurred:","method":"PUT","service":"invoice-management","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7649)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:19","statusCode":500,"timestamp":"2025-06-11T04:53:13.020Z","url":"/api/companies/cmbq2rhj200g2w0bpk8aqzqfg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })","ip":"::1","level":"error","message":"Error occurred:","method":"PUT","service":"invoice-management","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7649)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:19","statusCode":500,"timestamp":"2025-06-11T04:53:17.020Z","url":"/api/companies/cmbq2rhj200g2w0bpk8aqzqfg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })","ip":"::1","level":"error","message":"Error occurred:","method":"PUT","service":"invoice-management","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7649)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:19","statusCode":500,"timestamp":"2025-06-11T04:53:21.021Z","url":"/api/companies/cmbpx0rav00imw0h45zitdx6a","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"\nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })","ip":"::1","level":"error","message":"Error occurred:","method":"PUT","service":"invoice-management","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7649)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:19","statusCode":500,"timestamp":"2025-06-11T04:53:25.377Z","url":"/api/companies/cmbq2rhj200g2w0bpk8aqzqfg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })","ip":"::1","level":"error","message":"Error occurred:","method":"PUT","service":"invoice-management","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7649)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:19","statusCode":500,"timestamp":"2025-06-11T04:53:38.377Z","url":"/api/companies/cmbq2rhj200g2w0bpk8aqzqfg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })","ip":"::1","level":"error","message":"Error occurred:","method":"PUT","service":"invoice-management","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7649)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:19","statusCode":500,"timestamp":"2025-06-11T04:53:39.374Z","url":"/api/companies/cmbq2rhj200g2w0bpk8aqzqfg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })","ip":"::1","level":"error","message":"Error occurred:","method":"PUT","service":"invoice-management","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7649)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:19","statusCode":500,"timestamp":"2025-06-11T04:54:16.371Z","url":"/api/companies/cmbq2rhj200g2w0bpk8aqzqfg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T04:55:02.674Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T04:55:02.677Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T04:55:02.677Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T04:55:02.678Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T04:55:02.678Z"}
{"error":"\nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })","ip":"::1","level":"error","message":"Error occurred:","method":"PUT","service":"invoice-management","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7649)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:19","statusCode":500,"timestamp":"2025-06-11T04:57:08.353Z","url":"/api/companies/cmbq2rhj200g2w0bpk8aqzqfg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })","ip":"::1","level":"error","message":"Error occurred:","method":"PUT","service":"invoice-management","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7649)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:19","statusCode":500,"timestamp":"2025-06-11T04:57:15.346Z","url":"/api/companies/cmbq2rhj200g2w0bpk8aqzqfg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })","ip":"::1","level":"error","message":"Error occurred:","method":"PUT","service":"invoice-management","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7649)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:19","statusCode":500,"timestamp":"2025-06-11T04:57:26.338Z","url":"/api/companies/cmbq2rhj200g2w0bpk8aqzqfg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })","ip":"::1","level":"error","message":"Error occurred:","method":"PUT","service":"invoice-management","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7649)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:19","statusCode":500,"timestamp":"2025-06-11T04:57:32.345Z","url":"/api/companies/cmbq2rhj200g2w0bpk8aqzqfg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })","ip":"::1","level":"error","message":"Error occurred:","method":"PUT","service":"invoice-management","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7649)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:19","statusCode":500,"timestamp":"2025-06-11T05:07:57.927Z","url":"/api/companies/cmbq2rhj200g2w0bpk8aqzqfg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })","ip":"::1","level":"error","message":"Error occurred:","method":"PUT","service":"invoice-management","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7649)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:19","statusCode":500,"timestamp":"2025-06-11T05:08:10.924Z","url":"/api/companies/cmbpx0rav00imw0h45zitdx6a","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"\nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })","ip":"::1","level":"error","message":"Error occurred:","method":"PUT","service":"invoice-management","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.company.update()` invocation in\nC:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:40\n\n  363   }\n  364 }\n  365 \n→ 366 const company = await prisma.company.update(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(Server(MysqlError { code: 1205, message: \"Lock wait timeout exceeded; try restarting transaction\", state: \"HY000\" })), transient: false })\n    at Zn.handleRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7649)\n    at Zn.handleAndLogRequestError (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6784)\n    at Zn.request (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6491)\n    at async l (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9778)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:366:19","statusCode":500,"timestamp":"2025-06-11T05:08:30.931Z","url":"/api/companies/cmbpx0rav00imw0h45zitdx6a","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T05:10:45.930Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T05:10:45.933Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T05:10:45.933Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T05:10:45.933Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T05:10:45.934Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T05:12:04.467Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T05:12:04.469Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T05:12:04.470Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T05:12:04.470Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T05:12:04.471Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T05:12:44.262Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T05:12:44.265Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T05:12:44.265Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T05:12:44.265Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T05:12:44.266Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T05:13:07.077Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T05:13:07.080Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T05:13:07.080Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T05:13:07.081Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T05:13:07.081Z"}
{"error":"系统繁忙，请稍后再试","ip":"::1","level":"error","message":"Error occurred:","method":"PUT","service":"invoice-management","stack":"Error: 系统繁忙，请稍后再试\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:421:17","statusCode":503,"timestamp":"2025-06-11T05:17:02.748Z","url":"/api/companies/cmbq2rhj200g2w0bpk8aqzqfg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"系统繁忙，请稍后再试","ip":"::1","level":"error","message":"Error occurred:","method":"PUT","service":"invoice-management","stack":"Error: 系统繁忙，请稍后再试\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:421:17","statusCode":503,"timestamp":"2025-06-11T05:17:06.754Z","url":"/api/companies/cmbq2rhj200g2w0bpk8aqzqfg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"系统繁忙，请稍后再试","ip":"::1","level":"error","message":"Error occurred:","method":"PUT","service":"invoice-management","stack":"Error: 系统繁忙，请稍后再试\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:421:17","statusCode":503,"timestamp":"2025-06-11T05:17:08.753Z","url":"/api/companies/cmbq2rhj200g2w0bpk8aqzqfg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"系统繁忙，请稍后再试","ip":"::1","level":"error","message":"Error occurred:","method":"PUT","service":"invoice-management","stack":"Error: 系统繁忙，请稍后再试\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:421:17","statusCode":503,"timestamp":"2025-06-11T05:17:10.749Z","url":"/api/companies/cmbq2rhj200g2w0bpk8aqzqfg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"系统繁忙，请稍后再试","ip":"::1","level":"error","message":"Error occurred:","method":"PUT","service":"invoice-management","stack":"Error: 系统繁忙，请稍后再试\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:421:17","statusCode":503,"timestamp":"2025-06-11T05:17:10.753Z","url":"/api/companies/cmbq2rhj200g2w0bpk8aqzqfg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"系统繁忙，请稍后再试","ip":"::1","level":"error","message":"Error occurred:","method":"PUT","service":"invoice-management","stack":"Error: 系统繁忙，请稍后再试\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:421:17","statusCode":503,"timestamp":"2025-06-11T05:30:44.511Z","url":"/api/companies/cmbq2rhj200g2w0bpk8aqzqfg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"系统繁忙，请稍后再试","level":"error","message":"Error occurred:","method":"PUT","service":"invoice-management","stack":"Error: 系统繁忙，请稍后再试\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:421:17","statusCode":503,"timestamp":"2025-06-11T05:43:18.402Z","url":"/api/companies/cmbq2rhj200g2w0bpk8aqzqfg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"系统繁忙，请稍后再试","ip":"::1","level":"error","message":"Error occurred:","method":"PUT","service":"invoice-management","stack":"Error: 系统繁忙，请稍后再试\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:421:17","statusCode":503,"timestamp":"2025-06-11T05:43:23.404Z","url":"/api/companies/cmbq2rhj200g2w0bpk8aqzqfg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"系统繁忙，请稍后再试","ip":"::1","level":"error","message":"Error occurred:","method":"PUT","service":"invoice-management","stack":"Error: 系统繁忙，请稍后再试\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\companies.ts:421:17","statusCode":503,"timestamp":"2025-06-11T05:43:42.409Z","url":"/api/companies/cmbq2rhj200g2w0bpk8aqzqfg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T05:45:58.068Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T05:45:58.071Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T05:45:58.071Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T05:45:58.071Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T05:45:58.072Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T05:46:14.671Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T05:46:14.673Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T05:46:14.674Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T05:46:14.674Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T05:46:14.674Z"}
{"error":"发票不存在","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"invoice-management","stack":"Error: 发票不存在\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\invoices.ts:855:11","statusCode":404,"timestamp":"2025-06-11T06:41:15.801Z","url":"/api/invoices/export?startDate=2025-04-01&endDate=2025-06-11&onlyShowPartnerCompanies=true","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"取得发票不存在","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"invoice-management","stack":"Error: 取得发票不存在\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\routes\\receivedInvoices.ts:754:11","statusCode":404,"timestamp":"2025-06-11T06:41:19.506Z","url":"/api/received-invoices/export?startDate=2025-04-01&endDate=2025-06-11&onlyShowPartnerCompanies=true","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T06:43:53.337Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T06:43:53.340Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T06:43:53.341Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T06:43:53.342Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T06:43:53.342Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T06:44:46.144Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T06:44:46.147Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T06:44:46.147Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T06:44:46.147Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T06:44:46.148Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T06:45:22.595Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T06:45:22.604Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T06:45:22.605Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T06:45:22.605Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T06:45:22.606Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T06:46:17.149Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T06:46:17.152Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T06:46:17.153Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T06:46:17.153Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T06:46:17.153Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T06:46:54.982Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T06:46:54.985Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T06:46:54.986Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T06:46:54.986Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T06:46:54.986Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T06:52:14.387Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T06:52:14.390Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T06:52:14.390Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T06:52:14.391Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T06:52:14.391Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T06:53:15.018Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T06:53:15.021Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T06:53:15.022Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T06:53:15.022Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T06:53:15.023Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T07:00:19.686Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T07:00:19.689Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T07:00:19.690Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T07:00:19.690Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T07:00:19.690Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T07:00:41.131Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T07:00:41.134Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T07:00:41.135Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T07:00:41.136Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T07:00:41.136Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T07:01:13.868Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T07:01:13.871Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T07:01:13.871Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T07:01:13.871Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T07:01:13.872Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T07:01:42.732Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T07:01:42.735Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T07:01:42.736Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T07:01:42.736Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T07:01:42.737Z"}
{"error":"访问令牌缺失","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"invoice-management","stack":"Error: 访问令牌缺失\n    at authenticate (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\src\\middleware\\auth.ts:67:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\router\\index.js:342:13)","statusCode":401,"timestamp":"2025-06-11T07:03:09.884Z","url":"/api/invoices/export","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T07:03:53.960Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T07:03:53.965Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T07:03:53.966Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T07:03:53.966Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T07:03:53.966Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T07:04:28.003Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T07:04:28.007Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T07:04:28.007Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T07:04:28.007Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T07:04:28.008Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T07:12:03.300Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T07:12:03.302Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T07:12:03.303Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T07:12:03.303Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T07:12:03.303Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T07:17:28.085Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T07:17:28.088Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T07:17:28.089Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T07:17:28.089Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T07:17:28.089Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T07:18:08.454Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T07:18:08.456Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T07:18:08.457Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T07:18:08.457Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T07:18:08.458Z"}
{"level":"info","message":"🚀 Server is running on http://localhost:3001","service":"invoice-management","timestamp":"2025-06-11T11:51:47.958Z"}
{"level":"info","message":"📊 Environment: development","service":"invoice-management","timestamp":"2025-06-11T11:51:47.960Z"}
{"level":"info","message":"🔗 Frontend URL: http://localhost:5173","service":"invoice-management","timestamp":"2025-06-11T11:51:47.960Z"}
{"level":"info","message":"📁 Upload directory: uploads","service":"invoice-management","timestamp":"2025-06-11T11:51:47.961Z"}
{"level":"info","message":"⚠️ Database initialization skipped for testing","service":"invoice-management","timestamp":"2025-06-11T11:51:47.961Z"}
