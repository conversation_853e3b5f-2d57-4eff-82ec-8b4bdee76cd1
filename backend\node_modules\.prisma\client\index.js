
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  PrismaClientKnownRequestError,
  PrismaClientUnknownRequestError,
  PrismaClientRustPanicError,
  PrismaClientInitializationError,
  PrismaClientValidationError,
  getPrismaClient,
  sqltag,
  empty,
  join,
  raw,
  skip,
  Decimal,
  Debug,
  objectEnumValues,
  makeStrictEnum,
  Extensions,
  warnOnce,
  defineDmmfProperty,
  Public,
  getRuntime,
  createParam,
} = require('@prisma/client/runtime/library.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.9.0
 * Query Engine version: 81e4af48011447c3cc503a190e86995b66d2a28e
 */
Prisma.prismaVersion = {
  client: "6.9.0",
  engine: "81e4af48011447c3cc503a190e86995b66d2a28e"
}

Prisma.PrismaClientKnownRequestError = PrismaClientKnownRequestError;
Prisma.PrismaClientUnknownRequestError = PrismaClientUnknownRequestError
Prisma.PrismaClientRustPanicError = PrismaClientRustPanicError
Prisma.PrismaClientInitializationError = PrismaClientInitializationError
Prisma.PrismaClientValidationError = PrismaClientValidationError
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = sqltag
Prisma.empty = empty
Prisma.join = join
Prisma.raw = raw
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = Extensions.getExtensionContext
Prisma.defineExtension = Extensions.defineExtension

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}




  const path = require('path')

/**
 * Enums
 */
exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  username: 'username',
  email: 'email',
  password: 'password',
  name: 'name',
  role: 'role',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserCompanyScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  companyId: 'companyId',
  createdAt: 'createdAt'
};

exports.Prisma.MenuScalarFieldEnum = {
  id: 'id',
  key: 'key',
  name: 'name',
  path: 'path',
  icon: 'icon',
  parentId: 'parentId',
  sort: 'sort',
  isActive: 'isActive',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserMenuPermissionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  menuId: 'menuId',
  canView: 'canView',
  canEdit: 'canEdit',
  canDelete: 'canDelete',
  canExport: 'canExport',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CompanyScalarFieldEnum = {
  id: 'id',
  name: 'name',
  taxId: 'taxId',
  address: 'address',
  phone: 'phone',
  email: 'email',
  contact: 'contact',
  organization: 'organization',
  remarks: 'remarks',
  registrationDate: 'registrationDate',
  lastInvoiceDate: 'lastInvoiceDate',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.InvoiceScalarFieldEnum = {
  id: 'id',
  invoiceNumber: 'invoiceNumber',
  invoiceCode: 'invoiceCode',
  invoiceDate: 'invoiceDate',
  amount: 'amount',
  taxAmount: 'taxAmount',
  totalAmount: 'totalAmount',
  buyerName: 'buyerName',
  buyerTaxId: 'buyerTaxId',
  buyerAddress: 'buyerAddress',
  buyerPhone: 'buyerPhone',
  buyerBank: 'buyerBank',
  sellerName: 'sellerName',
  sellerTaxId: 'sellerTaxId',
  sellerAddress: 'sellerAddress',
  sellerPhone: 'sellerPhone',
  sellerBank: 'sellerBank',
  invoiceType: 'invoiceType',
  status: 'status',
  verificationStatus: 'verificationStatus',
  drawer: 'drawer',
  remarks: 'remarks',
  remark: 'remark',
  projectName: 'projectName',
  department: 'department',
  costCenter: 'costCenter',
  isDuplicate: 'isDuplicate',
  isArchived: 'isArchived',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  companyId: 'companyId'
};

exports.Prisma.ReceivedInvoiceScalarFieldEnum = {
  id: 'id',
  invoiceNumber: 'invoiceNumber',
  invoiceCode: 'invoiceCode',
  invoiceDate: 'invoiceDate',
  amount: 'amount',
  taxAmount: 'taxAmount',
  totalAmount: 'totalAmount',
  buyerName: 'buyerName',
  buyerTaxId: 'buyerTaxId',
  buyerAddress: 'buyerAddress',
  buyerPhone: 'buyerPhone',
  buyerBank: 'buyerBank',
  sellerName: 'sellerName',
  sellerTaxId: 'sellerTaxId',
  sellerAddress: 'sellerAddress',
  sellerPhone: 'sellerPhone',
  sellerBank: 'sellerBank',
  invoiceType: 'invoiceType',
  status: 'status',
  verificationStatus: 'verificationStatus',
  drawer: 'drawer',
  remarks: 'remarks',
  remark: 'remark',
  projectName: 'projectName',
  department: 'department',
  costCenter: 'costCenter',
  isDuplicate: 'isDuplicate',
  isArchived: 'isArchived',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  companyId: 'companyId'
};

exports.Prisma.InvoiceItemScalarFieldEnum = {
  id: 'id',
  itemName: 'itemName',
  specification: 'specification',
  unit: 'unit',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  amount: 'amount',
  taxRate: 'taxRate',
  taxAmount: 'taxAmount',
  totalAmount: 'totalAmount',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  invoiceId: 'invoiceId'
};

exports.Prisma.ReceivedInvoiceItemScalarFieldEnum = {
  id: 'id',
  itemName: 'itemName',
  specification: 'specification',
  unit: 'unit',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  amount: 'amount',
  taxRate: 'taxRate',
  taxAmount: 'taxAmount',
  totalAmount: 'totalAmount',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  receivedInvoiceId: 'receivedInvoiceId'
};

exports.Prisma.InvoiceAttachmentScalarFieldEnum = {
  id: 'id',
  fileName: 'fileName',
  filePath: 'filePath',
  fileSize: 'fileSize',
  mimeType: 'mimeType',
  fileType: 'fileType',
  isOriginal: 'isOriginal',
  createdAt: 'createdAt',
  invoiceId: 'invoiceId'
};

exports.Prisma.ReceivedInvoiceAttachmentScalarFieldEnum = {
  id: 'id',
  fileName: 'fileName',
  filePath: 'filePath',
  fileSize: 'fileSize',
  mimeType: 'mimeType',
  fileType: 'fileType',
  isOriginal: 'isOriginal',
  createdAt: 'createdAt',
  receivedInvoiceId: 'receivedInvoiceId'
};

exports.Prisma.ReceivedInvoiceAuditLogScalarFieldEnum = {
  id: 'id',
  action: 'action',
  tableName: 'tableName',
  recordId: 'recordId',
  oldValues: 'oldValues',
  newValues: 'newValues',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  createdAt: 'createdAt',
  userId: 'userId',
  receivedInvoiceId: 'receivedInvoiceId'
};

exports.Prisma.AuditLogScalarFieldEnum = {
  id: 'id',
  action: 'action',
  tableName: 'tableName',
  recordId: 'recordId',
  oldValues: 'oldValues',
  newValues: 'newValues',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  createdAt: 'createdAt',
  userId: 'userId',
  invoiceId: 'invoiceId'
};

exports.Prisma.OperationLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  username: 'username',
  operationName: 'operationName',
  method: 'method',
  path: 'path',
  userAgent: 'userAgent',
  ipAddress: 'ipAddress',
  requestParams: 'requestParams',
  responseData: 'responseData',
  statusCode: 'statusCode',
  isSuccess: 'isSuccess',
  errorMessage: 'errorMessage',
  duration: 'duration',
  createdAt: 'createdAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.UserOrderByRelevanceFieldEnum = {
  id: 'id',
  username: 'username',
  email: 'email',
  password: 'password',
  name: 'name'
};

exports.Prisma.UserCompanyOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  companyId: 'companyId'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.MenuOrderByRelevanceFieldEnum = {
  id: 'id',
  key: 'key',
  name: 'name',
  path: 'path',
  icon: 'icon',
  parentId: 'parentId',
  description: 'description'
};

exports.Prisma.UserMenuPermissionOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  menuId: 'menuId'
};

exports.Prisma.CompanyOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  taxId: 'taxId',
  address: 'address',
  phone: 'phone',
  email: 'email',
  contact: 'contact',
  organization: 'organization',
  remarks: 'remarks'
};

exports.Prisma.InvoiceOrderByRelevanceFieldEnum = {
  id: 'id',
  invoiceNumber: 'invoiceNumber',
  invoiceCode: 'invoiceCode',
  buyerName: 'buyerName',
  buyerTaxId: 'buyerTaxId',
  buyerAddress: 'buyerAddress',
  buyerPhone: 'buyerPhone',
  buyerBank: 'buyerBank',
  sellerName: 'sellerName',
  sellerTaxId: 'sellerTaxId',
  sellerAddress: 'sellerAddress',
  sellerPhone: 'sellerPhone',
  sellerBank: 'sellerBank',
  drawer: 'drawer',
  remarks: 'remarks',
  remark: 'remark',
  projectName: 'projectName',
  department: 'department',
  costCenter: 'costCenter',
  companyId: 'companyId'
};

exports.Prisma.ReceivedInvoiceOrderByRelevanceFieldEnum = {
  id: 'id',
  invoiceNumber: 'invoiceNumber',
  invoiceCode: 'invoiceCode',
  buyerName: 'buyerName',
  buyerTaxId: 'buyerTaxId',
  buyerAddress: 'buyerAddress',
  buyerPhone: 'buyerPhone',
  buyerBank: 'buyerBank',
  sellerName: 'sellerName',
  sellerTaxId: 'sellerTaxId',
  sellerAddress: 'sellerAddress',
  sellerPhone: 'sellerPhone',
  sellerBank: 'sellerBank',
  drawer: 'drawer',
  remarks: 'remarks',
  remark: 'remark',
  projectName: 'projectName',
  department: 'department',
  costCenter: 'costCenter',
  companyId: 'companyId'
};

exports.Prisma.InvoiceItemOrderByRelevanceFieldEnum = {
  id: 'id',
  itemName: 'itemName',
  specification: 'specification',
  unit: 'unit',
  invoiceId: 'invoiceId'
};

exports.Prisma.ReceivedInvoiceItemOrderByRelevanceFieldEnum = {
  id: 'id',
  itemName: 'itemName',
  specification: 'specification',
  unit: 'unit',
  receivedInvoiceId: 'receivedInvoiceId'
};

exports.Prisma.InvoiceAttachmentOrderByRelevanceFieldEnum = {
  id: 'id',
  fileName: 'fileName',
  filePath: 'filePath',
  mimeType: 'mimeType',
  invoiceId: 'invoiceId'
};

exports.Prisma.ReceivedInvoiceAttachmentOrderByRelevanceFieldEnum = {
  id: 'id',
  fileName: 'fileName',
  filePath: 'filePath',
  mimeType: 'mimeType',
  receivedInvoiceId: 'receivedInvoiceId'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.ReceivedInvoiceAuditLogOrderByRelevanceFieldEnum = {
  id: 'id',
  action: 'action',
  tableName: 'tableName',
  recordId: 'recordId',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  userId: 'userId',
  receivedInvoiceId: 'receivedInvoiceId'
};

exports.Prisma.AuditLogOrderByRelevanceFieldEnum = {
  id: 'id',
  action: 'action',
  tableName: 'tableName',
  recordId: 'recordId',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  userId: 'userId',
  invoiceId: 'invoiceId'
};

exports.Prisma.OperationLogOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  username: 'username',
  operationName: 'operationName',
  method: 'method',
  path: 'path',
  userAgent: 'userAgent',
  ipAddress: 'ipAddress',
  errorMessage: 'errorMessage'
};
exports.UserRole = exports.$Enums.UserRole = {
  ADMIN: 'ADMIN',
  FINANCE: 'FINANCE',
  BUSINESS: 'BUSINESS',
  AUDITOR: 'AUDITOR',
  USER: 'USER'
};

exports.UserStatus = exports.$Enums.UserStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE'
};

exports.InvoiceType = exports.$Enums.InvoiceType = {
  SPECIAL_VAT: 'SPECIAL_VAT',
  ORDINARY_VAT: 'ORDINARY_VAT',
  ELECTRONIC: 'ELECTRONIC',
  RECEIPT: 'RECEIPT',
  OTHER: 'OTHER'
};

exports.InvoiceStatus = exports.$Enums.InvoiceStatus = {
  NORMAL: 'NORMAL',
  CANCELLED: 'CANCELLED'
};

exports.VerificationStatus = exports.$Enums.VerificationStatus = {
  UNVERIFIED: 'UNVERIFIED',
  VERIFIED: 'VERIFIED',
  FAILED: 'FAILED',
  DUPLICATE: 'DUPLICATE'
};

exports.FileType = exports.$Enums.FileType = {
  PDF: 'PDF',
  IMAGE: 'IMAGE',
  EXCEL: 'EXCEL',
  OTHER: 'OTHER'
};

exports.Prisma.ModelName = {
  User: 'User',
  UserCompany: 'UserCompany',
  Menu: 'Menu',
  UserMenuPermission: 'UserMenuPermission',
  Company: 'Company',
  Invoice: 'Invoice',
  ReceivedInvoice: 'ReceivedInvoice',
  InvoiceItem: 'InvoiceItem',
  ReceivedInvoiceItem: 'ReceivedInvoiceItem',
  InvoiceAttachment: 'InvoiceAttachment',
  ReceivedInvoiceAttachment: 'ReceivedInvoiceAttachment',
  ReceivedInvoiceAuditLog: 'ReceivedInvoiceAuditLog',
  AuditLog: 'AuditLog',
  OperationLog: 'OperationLog'
};
/**
 * Create the Client
 */
const config = {
  "generator": {
    "name": "client",
    "provider": {
      "fromEnvVar": null,
      "value": "prisma-client-js"
    },
    "output": {
      "value": "C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\node_modules\\@prisma\\client",
      "fromEnvVar": null
    },
    "config": {
      "engineType": "library"
    },
    "binaryTargets": [
      {
        "fromEnvVar": null,
        "value": "windows",
        "native": true
      }
    ],
    "previewFeatures": [],
    "sourceFilePath": "C:\\Users\\<USER>\\Documents\\augment-projects\\invoice-management-pro\\backend\\prisma\\schema.prisma"
  },
  "relativeEnvPaths": {
    "rootEnvPath": null,
    "schemaEnvPath": "../../../.env"
  },
  "relativePath": "../../../prisma",
  "clientVersion": "6.9.0",
  "engineVersion": "81e4af48011447c3cc503a190e86995b66d2a28e",
  "datasourceNames": [
    "db"
  ],
  "activeProvider": "mysql",
  "postinstall": false,
  "inlineDatasources": {
    "db": {
      "url": {
        "fromEnvVar": "DATABASE_URL",
        "value": null
      }
    }
  },
  "inlineSchema": "// This is your Prisma schema file,\n// learn more about it in the docs: https://pris.ly/d/prisma-schema\n// Updated to remove bankAccount field\n\ngenerator client {\n  provider = \"prisma-client-js\"\n}\n\ndatasource db {\n  provider = \"mysql\"\n  url      = env(\"DATABASE_URL\")\n}\n\n/// 用户表 - 支持RBAC权限模型\nmodel User {\n  /// 用户唯一标识符\n  id        String     @id @default(cuid())\n  /// 用户名（登录用）\n  username  String     @unique\n  /// 邮箱地址（登录用）\n  email     String     @unique\n  /// 加密后的密码\n  password  String\n  /// 用户真实姓名\n  name      String\n  /// 用户角色（管理员、财务、业务等）\n  role      UserRole   @default(USER)\n  /// 用户状态（激活、禁用）\n  status    UserStatus @default(ACTIVE)\n  /// 创建时间\n  createdAt DateTime   @default(now())\n  /// 更新时间\n  updatedAt DateTime   @updatedAt\n\n  // 关联用户-公司权限\n  userCompanies UserCompany[]\n\n  // 关联操作日志\n  auditLogs AuditLog[]\n\n  // 关联取得发票操作日志\n  receivedInvoiceAuditLogs ReceivedInvoiceAuditLog[]\n\n  // 关联用户菜单权限\n  userMenuPermissions UserMenuPermission[]\n\n  // 关联操作日志\n  operationLogs OperationLog[]\n\n  @@map(\"users\")\n}\n\n/// 用户-公司关联表（权限控制）\nmodel UserCompany {\n  /// 关联记录唯一标识符\n  id        String   @id @default(cuid())\n  /// 用户ID\n  userId    String\n  /// 公司ID\n  companyId String\n  /// 创建时间\n  createdAt DateTime @default(now())\n\n  /// 关联用户\n  user User @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  /// 关联公司\n  company Company @relation(fields: [companyId], references: [id], onDelete: Cascade)\n\n  @@unique([userId, companyId])\n  @@map(\"user_companies\")\n}\n\n/// 菜单表\nmodel Menu {\n  /// 菜单唯一标识符\n  id          String   @id @default(cuid())\n  /// 菜单唯一标识键\n  key         String   @unique\n  /// 菜单显示名称\n  name        String\n  /// 菜单路由路径\n  path        String?\n  /// 菜单图标\n  icon        String?\n  /// 父菜单ID（用于构建菜单树）\n  parentId    String?\n  /// 菜单排序号\n  sort        Int      @default(0)\n  /// 是否启用\n  isActive    Boolean  @default(true)\n  /// 菜单描述\n  description String?\n  /// 创建时间\n  createdAt   DateTime @default(now())\n  /// 更新时间\n  updatedAt   DateTime @updatedAt\n\n  // 自关联：父菜单\n  parent   Menu?  @relation(\"MenuHierarchy\", fields: [parentId], references: [id])\n  children Menu[] @relation(\"MenuHierarchy\")\n\n  // 关联用户菜单权限\n  userMenuPermissions UserMenuPermission[]\n\n  @@map(\"menus\")\n}\n\n/// 用户菜单权限表\nmodel UserMenuPermission {\n  /// 权限记录唯一标识符\n  id        String   @id @default(cuid())\n  /// 用户ID\n  userId    String\n  /// 菜单ID\n  menuId    String\n  /// 是否可查看\n  canView   Boolean  @default(true)\n  /// 是否可编辑\n  canEdit   Boolean  @default(false)\n  /// 是否可删除\n  canDelete Boolean  @default(false)\n  /// 是否可导出\n  canExport Boolean  @default(false)\n  /// 创建时间\n  createdAt DateTime @default(now())\n  /// 更新时间\n  updatedAt DateTime @updatedAt\n\n  // 关联用户\n  user User @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  // 关联菜单\n  menu Menu @relation(fields: [menuId], references: [id], onDelete: Cascade)\n\n  @@unique([userId, menuId])\n  @@map(\"user_menu_permissions\")\n}\n\n/// 公司表\nmodel Company {\n  /// 公司唯一标识符\n  id               String    @id @default(cuid())\n  /// 公司名称\n  name             String\n  /// 纳税人识别号（税号）\n  taxId            String    @unique\n  /// 公司地址\n  address          String?\n  /// 联系电话\n  phone            String?\n  /// 邮箱地址\n  email            String?\n  /// 联系人姓名\n  contact          String?\n  /// 所属组织\n  organization     String?\n  /// 备注信息\n  remarks          String?\n  /// 公司注册日期\n  registrationDate DateTime?\n  /// 最新发票日期\n  lastInvoiceDate  DateTime?\n  /// 是否激活\n  isActive         Boolean   @default(true)\n  /// 创建时间\n  createdAt        DateTime  @default(now())\n  /// 更新时间\n  updatedAt        DateTime  @updatedAt\n\n  // 关联用户-公司权限\n  userCompanies UserCompany[]\n\n  // 关联发票\n  invoices Invoice[]\n\n  // 关联取得发票\n  receivedInvoices ReceivedInvoice[]\n\n  @@map(\"companies\")\n}\n\n/// 发票主表（发票头信息）\nmodel Invoice {\n  /// 发票唯一标识符\n  id                 String             @id @default(cuid())\n  /// 发票号码\n  invoiceNumber      String             @unique\n  /// 发票代码\n  invoiceCode        String\n  /// 开票日期\n  invoiceDate        DateTime\n  /// 不含税金额\n  amount             Decimal            @db.Decimal(15, 2)\n  /// 税额\n  taxAmount          Decimal            @db.Decimal(15, 2)\n  /// 价税合计金额\n  totalAmount        Decimal            @db.Decimal(15, 2)\n  /// 购买方名称\n  buyerName          String\n  /// 购买方纳税人识别号\n  buyerTaxId         String\n  /// 购买方地址\n  buyerAddress       String?\n  /// 购买方电话\n  buyerPhone         String?\n  /// 购买方开户行及账号\n  buyerBank          String?\n  /// 销售方名称\n  sellerName         String\n  /// 销售方纳税人识别号\n  sellerTaxId        String\n  /// 销售方地址\n  sellerAddress      String?\n  /// 销售方电话\n  sellerPhone        String?\n  /// 销售方开户行及账号\n  sellerBank         String?\n  /// 发票类型\n  invoiceType        InvoiceType\n  /// 发票状态\n  status             InvoiceStatus      @default(NORMAL)\n  /// 查验状态\n  verificationStatus VerificationStatus @default(UNVERIFIED)\n  /// 开票人\n  drawer             String?\n  /// 备注信息\n  remarks            String?\n  /// 备注（保留兼容性）\n  remark             String?\n  /// 项目名称\n  projectName        String?\n  /// 部门\n  department         String?\n  /// 成本中心\n  costCenter         String?\n  /// 是否重复发票\n  isDuplicate        Boolean            @default(false)\n  /// 是否已归档\n  isArchived         Boolean            @default(false)\n  /// 创建时间\n  createdAt          DateTime           @default(now())\n  /// 更新时间\n  updatedAt          DateTime           @updatedAt\n\n  /// 关联公司ID\n  companyId String\n  /// 关联公司\n  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)\n\n  /// 关联发票明细\n  invoiceItems InvoiceItem[]\n\n  /// 关联附件\n  attachments InvoiceAttachment[]\n\n  /// 关联审计日志\n  auditLogs AuditLog[]\n\n  @@index([invoiceNumber, invoiceCode])\n  @@index([companyId])\n  @@index([invoiceDate])\n  @@index([status])\n  @@map(\"invoices\")\n}\n\n/// 取得发票主表（取得发票头信息）\nmodel ReceivedInvoice {\n  /// 取得发票唯一标识符\n  id                 String             @id @default(cuid())\n  /// 发票号码\n  invoiceNumber      String             @unique\n  /// 发票代码\n  invoiceCode        String\n  /// 开票日期\n  invoiceDate        DateTime\n  /// 不含税金额\n  amount             Decimal            @db.Decimal(15, 2)\n  /// 税额\n  taxAmount          Decimal            @db.Decimal(15, 2)\n  /// 价税合计金额\n  totalAmount        Decimal            @db.Decimal(15, 2)\n  /// 购买方名称\n  buyerName          String\n  /// 购买方纳税人识别号\n  buyerTaxId         String\n  /// 购买方地址\n  buyerAddress       String?\n  /// 购买方电话\n  buyerPhone         String?\n  /// 购买方开户行及账号\n  buyerBank          String?\n  /// 销售方名称\n  sellerName         String\n  /// 销售方纳税人识别号\n  sellerTaxId        String\n  /// 销售方地址\n  sellerAddress      String?\n  /// 销售方电话\n  sellerPhone        String?\n  /// 销售方开户行及账号\n  sellerBank         String?\n  /// 发票类型\n  invoiceType        InvoiceType\n  /// 发票状态\n  status             InvoiceStatus      @default(NORMAL)\n  /// 查验状态\n  verificationStatus VerificationStatus @default(UNVERIFIED)\n  /// 开票人\n  drawer             String?\n  /// 备注信息\n  remarks            String?\n  /// 备注（保留兼容性）\n  remark             String?\n  /// 项目名称\n  projectName        String?\n  /// 部门\n  department         String?\n  /// 成本中心\n  costCenter         String?\n  /// 是否重复发票\n  isDuplicate        Boolean            @default(false)\n  /// 是否已归档\n  isArchived         Boolean            @default(false)\n  /// 创建时间\n  createdAt          DateTime           @default(now())\n  /// 更新时间\n  updatedAt          DateTime           @updatedAt\n\n  /// 关联公司ID\n  companyId String\n  /// 关联公司\n  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)\n\n  /// 关联取得发票明细\n  receivedInvoiceItems ReceivedInvoiceItem[]\n\n  /// 关联附件\n  attachments ReceivedInvoiceAttachment[]\n\n  /// 关联审计日志\n  auditLogs ReceivedInvoiceAuditLog[]\n\n  @@index([invoiceNumber, invoiceCode])\n  @@index([companyId])\n  @@index([invoiceDate])\n  @@index([status])\n  @@map(\"received_invoices\")\n}\n\n/// 发票明细表（商品/服务条目）\nmodel InvoiceItem {\n  /// 发票明细唯一标识符\n  id            String   @id @default(cuid())\n  /// 商品/服务名称\n  itemName      String\n  /// 规格型号\n  specification String?\n  /// 计量单位\n  unit          String?\n  /// 数量\n  quantity      Decimal  @db.Decimal(10, 4)\n  /// 单价\n  unitPrice     Decimal  @db.Decimal(15, 4)\n  /// 不含税金额\n  amount        Decimal  @db.Decimal(15, 2)\n  /// 税率\n  taxRate       Decimal  @db.Decimal(5, 4)\n  /// 税额\n  taxAmount     Decimal  @db.Decimal(15, 2)\n  /// 价税合计金额\n  totalAmount   Decimal  @db.Decimal(15, 2)\n  /// 创建时间\n  createdAt     DateTime @default(now())\n  /// 更新时间\n  updatedAt     DateTime @updatedAt\n\n  /// 关联发票ID\n  invoiceId String\n  /// 关联发票\n  invoice   Invoice @relation(fields: [invoiceId], references: [id], onDelete: Cascade)\n\n  @@map(\"invoice_items\")\n}\n\n/// 取得发票明细表（商品/服务条目）\nmodel ReceivedInvoiceItem {\n  /// 取得发票明细唯一标识符\n  id            String   @id @default(cuid())\n  /// 商品/服务名称\n  itemName      String\n  /// 规格型号\n  specification String?\n  /// 计量单位\n  unit          String?\n  /// 数量\n  quantity      Decimal  @db.Decimal(10, 4)\n  /// 单价\n  unitPrice     Decimal  @db.Decimal(15, 4)\n  /// 不含税金额\n  amount        Decimal  @db.Decimal(15, 2)\n  /// 税率\n  taxRate       Decimal  @db.Decimal(5, 4)\n  /// 税额\n  taxAmount     Decimal  @db.Decimal(15, 2)\n  /// 价税合计金额\n  totalAmount   Decimal  @db.Decimal(15, 2)\n  /// 创建时间\n  createdAt     DateTime @default(now())\n  /// 更新时间\n  updatedAt     DateTime @updatedAt\n\n  /// 关联取得发票ID\n  receivedInvoiceId String\n  /// 关联取得发票\n  receivedInvoice   ReceivedInvoice @relation(fields: [receivedInvoiceId], references: [id], onDelete: Cascade)\n\n  @@map(\"received_invoice_items\")\n}\n\n/// 发票附件表（存储扫描件/电子原件）\nmodel InvoiceAttachment {\n  /// 附件唯一标识符\n  id         String   @id @default(cuid())\n  /// 系统生成的文件名\n  fileName   String\n  /// 原始文件名（用户上传时的文件名）\n  // originalName String? // 暂时注释掉，数据库中没有这个字段\n  /// 文件存储路径\n  filePath   String\n  /// 文件大小（字节）\n  fileSize   Int\n  /// 文件MIME类型\n  mimeType   String\n  /// 文件分类\n  fileType   FileType\n  /// 是否为原件\n  isOriginal Boolean  @default(false)\n  /// 创建时间\n  createdAt  DateTime @default(now())\n\n  /// 关联发票ID\n  invoiceId String\n  /// 关联发票\n  invoice   Invoice @relation(fields: [invoiceId], references: [id], onDelete: Cascade)\n\n  @@map(\"invoice_attachments\")\n}\n\n/// 取得发票附件表（存储扫描件/电子原件）\nmodel ReceivedInvoiceAttachment {\n  /// 取得发票附件唯一标识符\n  id         String   @id @default(cuid())\n  /// 系统生成的文件名\n  fileName   String\n  /// 原始文件名（用户上传时的文件名）\n  // originalName String? // 暂时注释掉，数据库中没有这个字段\n  /// 文件存储路径\n  filePath   String\n  /// 文件大小（字节）\n  fileSize   Int\n  /// 文件MIME类型\n  mimeType   String\n  /// 文件分类\n  fileType   FileType\n  /// 是否为原件\n  isOriginal Boolean  @default(false)\n  /// 创建时间\n  createdAt  DateTime @default(now())\n\n  /// 关联取得发票ID\n  receivedInvoiceId String\n  /// 关联取得发票\n  receivedInvoice   ReceivedInvoice @relation(fields: [receivedInvoiceId], references: [id], onDelete: Cascade)\n\n  @@map(\"received_invoice_attachments\")\n}\n\n/// 取得发票操作审计日志表\nmodel ReceivedInvoiceAuditLog {\n  /// 审计日志唯一标识符\n  id        String   @id @default(cuid())\n  /// 操作类型（CREATE, UPDATE, DELETE, VIEW）\n  action    String\n  /// 操作的表名\n  tableName String\n  /// 操作的记录ID\n  recordId  String\n  /// 修改前的值（JSON格式）\n  oldValues Json?\n  /// 修改后的值（JSON格式）\n  newValues Json?\n  /// 操作者IP地址\n  ipAddress String?\n  /// 用户代理信息\n  userAgent String?\n  /// 创建时间\n  createdAt DateTime @default(now())\n\n  // 关联用户\n  userId String?\n  user   User?   @relation(fields: [userId], references: [id])\n\n  // 关联取得发票\n  receivedInvoiceId String?\n  receivedInvoice   ReceivedInvoice? @relation(fields: [receivedInvoiceId], references: [id])\n\n  @@index([userId])\n  @@index([tableName, recordId])\n  @@index([createdAt])\n  @@map(\"received_invoice_audit_logs\")\n}\n\n/// 操作审计日志表\nmodel AuditLog {\n  /// 审计日志唯一标识符\n  id        String   @id @default(cuid())\n  /// 操作类型（CREATE, UPDATE, DELETE, VIEW）\n  action    String\n  /// 操作的表名\n  tableName String\n  /// 操作的记录ID\n  recordId  String\n  /// 修改前的值（JSON格式）\n  oldValues Json?\n  /// 修改后的值（JSON格式）\n  newValues Json?\n  /// 操作者IP地址\n  ipAddress String?\n  /// 用户代理信息\n  userAgent String?\n  /// 创建时间\n  createdAt DateTime @default(now())\n\n  // 关联用户\n  userId String?\n  user   User?   @relation(fields: [userId], references: [id])\n\n  // 关联发票（可选）\n  invoiceId String?\n  invoice   Invoice? @relation(fields: [invoiceId], references: [id])\n\n  @@index([userId])\n  @@index([tableName, recordId])\n  @@index([createdAt])\n  @@map(\"audit_logs\")\n}\n\n/// 操作日志表 - 记录所有API调用\nmodel OperationLog {\n  /// 操作日志唯一标识符\n  id            String   @id @default(cuid())\n  /// 操作用户ID（可为空，未登录操作）\n  userId        String?  @map(\"userId\")\n  /// 操作用户名\n  username      String?  @map(\"username\")\n  /// 操作名称（如：登录、创建公司、删除发票等）\n  operationName String   @map(\"operationName\")\n  /// HTTP请求方法\n  method        String   @map(\"method\")\n  /// 请求路径\n  path          String   @map(\"path\")\n  /// 浏览器信息\n  userAgent     String?  @map(\"userAgent\")\n  /// 客户端IP地址\n  ipAddress     String?  @map(\"ipAddress\")\n  /// 请求参数（JSON格式）\n  requestParams Json?    @map(\"requestParams\")\n  /// 返回数据（JSON格式，敏感信息已脱敏）\n  responseData  Json?    @map(\"responseData\")\n  /// HTTP状态码\n  statusCode    Int      @map(\"statusCode\")\n  /// 操作是否成功\n  isSuccess     Boolean  @map(\"isSuccess\")\n  /// 错误信息（失败时记录）\n  errorMessage  String?  @map(\"errorMessage\")\n  /// 请求耗时（毫秒）\n  duration      Int?     @map(\"duration\")\n  /// 创建时间\n  createdAt     DateTime @default(now()) @map(\"createdAt\")\n\n  /// 关联用户\n  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)\n\n  @@index([userId])\n  @@index([operationName])\n  @@index([path])\n  @@index([createdAt])\n  @@index([isSuccess])\n  @@map(\"operation_logs\")\n}\n\n// 枚举定义\nenum UserRole {\n  ADMIN // 管理员（全权限）\n  FINANCE // 财务人员（编辑+查看）\n  BUSINESS // 业务人员（提交+查看）\n  AUDITOR // 审计人员（仅查看）\n  USER // 普通用户（仅提交）\n}\n\nenum UserStatus {\n  ACTIVE // 激活\n  INACTIVE // 禁用\n}\n\nenum InvoiceType {\n  SPECIAL_VAT // 增值税专用发票\n  ORDINARY_VAT // 增值税普通发票\n  ELECTRONIC // 电子发票\n  RECEIPT // 收据\n  OTHER // 其他\n}\n\nenum InvoiceStatus {\n  NORMAL // 正常\n  CANCELLED // 作废\n}\n\nenum VerificationStatus {\n  UNVERIFIED // 未查验\n  VERIFIED // 已查验通过\n  FAILED // 查验失败\n  DUPLICATE // 重复发票\n}\n\nenum FileType {\n  PDF // PDF文件\n  IMAGE // 图片文件\n  EXCEL // Excel文件\n  OTHER // 其他文件\n}\n",
  "inlineSchemaHash": "fdc4f0b63e258cb71a7e61bbe7f19c77bdf1c79f03c92564379a7ac91bb910fe",
  "copyEngine": true
}

const fs = require('fs')

config.dirname = __dirname
if (!fs.existsSync(path.join(__dirname, 'schema.prisma'))) {
  const alternativePaths = [
    "node_modules/.prisma/client",
    ".prisma/client",
  ]
  
  const alternativePath = alternativePaths.find((altPath) => {
    return fs.existsSync(path.join(process.cwd(), altPath, 'schema.prisma'))
  }) ?? alternativePaths[0]

  config.dirname = path.join(process.cwd(), alternativePath)
  config.isBundled = true
}

config.runtimeDataModel = JSON.parse("{\"models\":{\"User\":{\"dbName\":\"users\",\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"用户唯一标识符\"},{\"name\":\"username\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"用户名（登录用）\"},{\"name\":\"email\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"邮箱地址（登录用）\"},{\"name\":\"password\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"加密后的密码\"},{\"name\":\"name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"用户真实姓名\"},{\"name\":\"role\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"UserRole\",\"nativeType\":null,\"default\":\"USER\",\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"用户角色（管理员、财务、业务等）\"},{\"name\":\"status\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"UserStatus\",\"nativeType\":null,\"default\":\"ACTIVE\",\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"用户状态（激活、禁用）\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"创建时间\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true,\"documentation\":\"更新时间\"},{\"name\":\"userCompanies\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserCompany\",\"nativeType\":null,\"relationName\":\"UserToUserCompany\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"auditLogs\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"AuditLog\",\"nativeType\":null,\"relationName\":\"AuditLogToUser\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"receivedInvoiceAuditLogs\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ReceivedInvoiceAuditLog\",\"nativeType\":null,\"relationName\":\"ReceivedInvoiceAuditLogToUser\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userMenuPermissions\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserMenuPermission\",\"nativeType\":null,\"relationName\":\"UserToUserMenuPermission\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"operationLogs\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"OperationLog\",\"nativeType\":null,\"relationName\":\"OperationLogToUser\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false,\"documentation\":\"用户表 - 支持RBAC权限模型\"},\"UserCompany\":{\"dbName\":\"user_companies\",\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"关联记录唯一标识符\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"用户ID\"},{\"name\":\"companyId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"公司ID\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"创建时间\"},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"UserToUserCompany\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"关联用户\"},{\"name\":\"company\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Company\",\"nativeType\":null,\"relationName\":\"CompanyToUserCompany\",\"relationFromFields\":[\"companyId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"关联公司\"}],\"primaryKey\":null,\"uniqueFields\":[[\"userId\",\"companyId\"]],\"uniqueIndexes\":[{\"name\":null,\"fields\":[\"userId\",\"companyId\"]}],\"isGenerated\":false,\"documentation\":\"用户-公司关联表（权限控制）\"},\"Menu\":{\"dbName\":\"menus\",\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"菜单唯一标识符\"},{\"name\":\"key\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"菜单唯一标识键\"},{\"name\":\"name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"菜单显示名称\"},{\"name\":\"path\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"菜单路由路径\"},{\"name\":\"icon\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"菜单图标\"},{\"name\":\"parentId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"父菜单ID（用于构建菜单树）\"},{\"name\":\"sort\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"菜单排序号\"},{\"name\":\"isActive\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"是否启用\"},{\"name\":\"description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"菜单描述\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"创建时间\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true,\"documentation\":\"更新时间\"},{\"name\":\"parent\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Menu\",\"nativeType\":null,\"relationName\":\"MenuHierarchy\",\"relationFromFields\":[\"parentId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"children\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Menu\",\"nativeType\":null,\"relationName\":\"MenuHierarchy\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userMenuPermissions\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserMenuPermission\",\"nativeType\":null,\"relationName\":\"MenuToUserMenuPermission\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false,\"documentation\":\"菜单表\"},\"UserMenuPermission\":{\"dbName\":\"user_menu_permissions\",\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"权限记录唯一标识符\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"用户ID\"},{\"name\":\"menuId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"菜单ID\"},{\"name\":\"canView\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"是否可查看\"},{\"name\":\"canEdit\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"是否可编辑\"},{\"name\":\"canDelete\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"是否可删除\"},{\"name\":\"canExport\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"是否可导出\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"创建时间\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true,\"documentation\":\"更新时间\"},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"UserToUserMenuPermission\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"menu\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Menu\",\"nativeType\":null,\"relationName\":\"MenuToUserMenuPermission\",\"relationFromFields\":[\"menuId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[[\"userId\",\"menuId\"]],\"uniqueIndexes\":[{\"name\":null,\"fields\":[\"userId\",\"menuId\"]}],\"isGenerated\":false,\"documentation\":\"用户菜单权限表\"},\"Company\":{\"dbName\":\"companies\",\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"公司唯一标识符\"},{\"name\":\"name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"公司名称\"},{\"name\":\"taxId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"纳税人识别号（税号）\"},{\"name\":\"address\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"公司地址\"},{\"name\":\"phone\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"联系电话\"},{\"name\":\"email\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"邮箱地址\"},{\"name\":\"contact\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"联系人姓名\"},{\"name\":\"organization\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"所属组织\"},{\"name\":\"remarks\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"备注信息\"},{\"name\":\"registrationDate\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"公司注册日期\"},{\"name\":\"lastInvoiceDate\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"最新发票日期\"},{\"name\":\"isActive\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"是否激活\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"创建时间\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true,\"documentation\":\"更新时间\"},{\"name\":\"userCompanies\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserCompany\",\"nativeType\":null,\"relationName\":\"CompanyToUserCompany\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"invoices\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Invoice\",\"nativeType\":null,\"relationName\":\"CompanyToInvoice\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"receivedInvoices\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ReceivedInvoice\",\"nativeType\":null,\"relationName\":\"CompanyToReceivedInvoice\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false,\"documentation\":\"公司表\"},\"Invoice\":{\"dbName\":\"invoices\",\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"发票唯一标识符\"},{\"name\":\"invoiceNumber\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"发票号码\"},{\"name\":\"invoiceCode\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"发票代码\"},{\"name\":\"invoiceDate\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"开票日期\"},{\"name\":\"amount\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"15\",\"2\"]],\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"不含税金额\"},{\"name\":\"taxAmount\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"15\",\"2\"]],\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"税额\"},{\"name\":\"totalAmount\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"15\",\"2\"]],\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"价税合计金额\"},{\"name\":\"buyerName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"购买方名称\"},{\"name\":\"buyerTaxId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"购买方纳税人识别号\"},{\"name\":\"buyerAddress\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"购买方地址\"},{\"name\":\"buyerPhone\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"购买方电话\"},{\"name\":\"buyerBank\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"购买方开户行及账号\"},{\"name\":\"sellerName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"销售方名称\"},{\"name\":\"sellerTaxId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"销售方纳税人识别号\"},{\"name\":\"sellerAddress\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"销售方地址\"},{\"name\":\"sellerPhone\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"销售方电话\"},{\"name\":\"sellerBank\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"销售方开户行及账号\"},{\"name\":\"invoiceType\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"InvoiceType\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"发票类型\"},{\"name\":\"status\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"InvoiceStatus\",\"nativeType\":null,\"default\":\"NORMAL\",\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"发票状态\"},{\"name\":\"verificationStatus\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"VerificationStatus\",\"nativeType\":null,\"default\":\"UNVERIFIED\",\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"查验状态\"},{\"name\":\"drawer\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"开票人\"},{\"name\":\"remarks\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"备注信息\"},{\"name\":\"remark\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"备注（保留兼容性）\"},{\"name\":\"projectName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"项目名称\"},{\"name\":\"department\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"部门\"},{\"name\":\"costCenter\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"成本中心\"},{\"name\":\"isDuplicate\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"是否重复发票\"},{\"name\":\"isArchived\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"是否已归档\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"创建时间\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true,\"documentation\":\"更新时间\"},{\"name\":\"companyId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"关联公司ID\"},{\"name\":\"company\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Company\",\"nativeType\":null,\"relationName\":\"CompanyToInvoice\",\"relationFromFields\":[\"companyId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"关联公司\"},{\"name\":\"invoiceItems\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"InvoiceItem\",\"nativeType\":null,\"relationName\":\"InvoiceToInvoiceItem\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"关联发票明细\"},{\"name\":\"attachments\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"InvoiceAttachment\",\"nativeType\":null,\"relationName\":\"InvoiceToInvoiceAttachment\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"关联附件\"},{\"name\":\"auditLogs\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"AuditLog\",\"nativeType\":null,\"relationName\":\"AuditLogToInvoice\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"关联审计日志\"}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false,\"documentation\":\"发票主表（发票头信息）\"},\"ReceivedInvoice\":{\"dbName\":\"received_invoices\",\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"取得发票唯一标识符\"},{\"name\":\"invoiceNumber\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"发票号码\"},{\"name\":\"invoiceCode\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"发票代码\"},{\"name\":\"invoiceDate\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"开票日期\"},{\"name\":\"amount\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"15\",\"2\"]],\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"不含税金额\"},{\"name\":\"taxAmount\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"15\",\"2\"]],\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"税额\"},{\"name\":\"totalAmount\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"15\",\"2\"]],\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"价税合计金额\"},{\"name\":\"buyerName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"购买方名称\"},{\"name\":\"buyerTaxId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"购买方纳税人识别号\"},{\"name\":\"buyerAddress\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"购买方地址\"},{\"name\":\"buyerPhone\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"购买方电话\"},{\"name\":\"buyerBank\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"购买方开户行及账号\"},{\"name\":\"sellerName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"销售方名称\"},{\"name\":\"sellerTaxId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"销售方纳税人识别号\"},{\"name\":\"sellerAddress\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"销售方地址\"},{\"name\":\"sellerPhone\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"销售方电话\"},{\"name\":\"sellerBank\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"销售方开户行及账号\"},{\"name\":\"invoiceType\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"InvoiceType\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"发票类型\"},{\"name\":\"status\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"InvoiceStatus\",\"nativeType\":null,\"default\":\"NORMAL\",\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"发票状态\"},{\"name\":\"verificationStatus\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"VerificationStatus\",\"nativeType\":null,\"default\":\"UNVERIFIED\",\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"查验状态\"},{\"name\":\"drawer\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"开票人\"},{\"name\":\"remarks\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"备注信息\"},{\"name\":\"remark\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"备注（保留兼容性）\"},{\"name\":\"projectName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"项目名称\"},{\"name\":\"department\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"部门\"},{\"name\":\"costCenter\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"成本中心\"},{\"name\":\"isDuplicate\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"是否重复发票\"},{\"name\":\"isArchived\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"是否已归档\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"创建时间\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true,\"documentation\":\"更新时间\"},{\"name\":\"companyId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"关联公司ID\"},{\"name\":\"company\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Company\",\"nativeType\":null,\"relationName\":\"CompanyToReceivedInvoice\",\"relationFromFields\":[\"companyId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"关联公司\"},{\"name\":\"receivedInvoiceItems\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ReceivedInvoiceItem\",\"nativeType\":null,\"relationName\":\"ReceivedInvoiceToReceivedInvoiceItem\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"关联取得发票明细\"},{\"name\":\"attachments\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ReceivedInvoiceAttachment\",\"nativeType\":null,\"relationName\":\"ReceivedInvoiceToReceivedInvoiceAttachment\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"关联附件\"},{\"name\":\"auditLogs\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ReceivedInvoiceAuditLog\",\"nativeType\":null,\"relationName\":\"ReceivedInvoiceToReceivedInvoiceAuditLog\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"关联审计日志\"}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false,\"documentation\":\"取得发票主表（取得发票头信息）\"},\"InvoiceItem\":{\"dbName\":\"invoice_items\",\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"发票明细唯一标识符\"},{\"name\":\"itemName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"商品/服务名称\"},{\"name\":\"specification\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"规格型号\"},{\"name\":\"unit\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"计量单位\"},{\"name\":\"quantity\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"10\",\"4\"]],\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"数量\"},{\"name\":\"unitPrice\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"15\",\"4\"]],\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"单价\"},{\"name\":\"amount\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"15\",\"2\"]],\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"不含税金额\"},{\"name\":\"taxRate\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"5\",\"4\"]],\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"税率\"},{\"name\":\"taxAmount\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"15\",\"2\"]],\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"税额\"},{\"name\":\"totalAmount\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"15\",\"2\"]],\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"价税合计金额\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"创建时间\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true,\"documentation\":\"更新时间\"},{\"name\":\"invoiceId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"关联发票ID\"},{\"name\":\"invoice\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Invoice\",\"nativeType\":null,\"relationName\":\"InvoiceToInvoiceItem\",\"relationFromFields\":[\"invoiceId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"关联发票\"}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false,\"documentation\":\"发票明细表（商品/服务条目）\"},\"ReceivedInvoiceItem\":{\"dbName\":\"received_invoice_items\",\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"取得发票明细唯一标识符\"},{\"name\":\"itemName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"商品/服务名称\"},{\"name\":\"specification\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"规格型号\"},{\"name\":\"unit\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"计量单位\"},{\"name\":\"quantity\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"10\",\"4\"]],\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"数量\"},{\"name\":\"unitPrice\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"15\",\"4\"]],\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"单价\"},{\"name\":\"amount\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"15\",\"2\"]],\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"不含税金额\"},{\"name\":\"taxRate\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"5\",\"4\"]],\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"税率\"},{\"name\":\"taxAmount\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"15\",\"2\"]],\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"税额\"},{\"name\":\"totalAmount\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"15\",\"2\"]],\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"价税合计金额\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"创建时间\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true,\"documentation\":\"更新时间\"},{\"name\":\"receivedInvoiceId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"关联取得发票ID\"},{\"name\":\"receivedInvoice\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ReceivedInvoice\",\"nativeType\":null,\"relationName\":\"ReceivedInvoiceToReceivedInvoiceItem\",\"relationFromFields\":[\"receivedInvoiceId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"关联取得发票\"}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false,\"documentation\":\"取得发票明细表（商品/服务条目）\"},\"InvoiceAttachment\":{\"dbName\":\"invoice_attachments\",\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"附件唯一标识符\"},{\"name\":\"fileName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"系统生成的文件名\"},{\"name\":\"filePath\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"原始文件名（用户上传时的文件名）\\\\n文件存储路径\"},{\"name\":\"fileSize\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"文件大小（字节）\"},{\"name\":\"mimeType\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"文件MIME类型\"},{\"name\":\"fileType\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"FileType\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"文件分类\"},{\"name\":\"isOriginal\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"是否为原件\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"创建时间\"},{\"name\":\"invoiceId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"关联发票ID\"},{\"name\":\"invoice\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Invoice\",\"nativeType\":null,\"relationName\":\"InvoiceToInvoiceAttachment\",\"relationFromFields\":[\"invoiceId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"关联发票\"}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false,\"documentation\":\"发票附件表（存储扫描件/电子原件）\"},\"ReceivedInvoiceAttachment\":{\"dbName\":\"received_invoice_attachments\",\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"取得发票附件唯一标识符\"},{\"name\":\"fileName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"系统生成的文件名\"},{\"name\":\"filePath\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"原始文件名（用户上传时的文件名）\\\\n文件存储路径\"},{\"name\":\"fileSize\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"文件大小（字节）\"},{\"name\":\"mimeType\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"文件MIME类型\"},{\"name\":\"fileType\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"FileType\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"文件分类\"},{\"name\":\"isOriginal\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"是否为原件\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"创建时间\"},{\"name\":\"receivedInvoiceId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"关联取得发票ID\"},{\"name\":\"receivedInvoice\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ReceivedInvoice\",\"nativeType\":null,\"relationName\":\"ReceivedInvoiceToReceivedInvoiceAttachment\",\"relationFromFields\":[\"receivedInvoiceId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"关联取得发票\"}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false,\"documentation\":\"取得发票附件表（存储扫描件/电子原件）\"},\"ReceivedInvoiceAuditLog\":{\"dbName\":\"received_invoice_audit_logs\",\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"审计日志唯一标识符\"},{\"name\":\"action\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"操作类型（CREATE, UPDATE, DELETE, VIEW）\"},{\"name\":\"tableName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"操作的表名\"},{\"name\":\"recordId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"操作的记录ID\"},{\"name\":\"oldValues\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"修改前的值（JSON格式）\"},{\"name\":\"newValues\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"修改后的值（JSON格式）\"},{\"name\":\"ipAddress\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"操作者IP地址\"},{\"name\":\"userAgent\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"用户代理信息\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"创建时间\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"ReceivedInvoiceAuditLogToUser\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"receivedInvoiceId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"receivedInvoice\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ReceivedInvoice\",\"nativeType\":null,\"relationName\":\"ReceivedInvoiceToReceivedInvoiceAuditLog\",\"relationFromFields\":[\"receivedInvoiceId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false,\"documentation\":\"取得发票操作审计日志表\"},\"AuditLog\":{\"dbName\":\"audit_logs\",\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"审计日志唯一标识符\"},{\"name\":\"action\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"操作类型（CREATE, UPDATE, DELETE, VIEW）\"},{\"name\":\"tableName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"操作的表名\"},{\"name\":\"recordId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"操作的记录ID\"},{\"name\":\"oldValues\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"修改前的值（JSON格式）\"},{\"name\":\"newValues\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"修改后的值（JSON格式）\"},{\"name\":\"ipAddress\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"操作者IP地址\"},{\"name\":\"userAgent\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"用户代理信息\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"创建时间\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"AuditLogToUser\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"invoiceId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"invoice\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Invoice\",\"nativeType\":null,\"relationName\":\"AuditLogToInvoice\",\"relationFromFields\":[\"invoiceId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false,\"documentation\":\"操作审计日志表\"},\"OperationLog\":{\"dbName\":\"operation_logs\",\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"操作日志唯一标识符\"},{\"name\":\"userId\",\"dbName\":\"userId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"操作用户ID（可为空，未登录操作）\"},{\"name\":\"username\",\"dbName\":\"username\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"操作用户名\"},{\"name\":\"operationName\",\"dbName\":\"operationName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"操作名称（如：登录、创建公司、删除发票等）\"},{\"name\":\"method\",\"dbName\":\"method\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"HTTP请求方法\"},{\"name\":\"path\",\"dbName\":\"path\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"请求路径\"},{\"name\":\"userAgent\",\"dbName\":\"userAgent\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"浏览器信息\"},{\"name\":\"ipAddress\",\"dbName\":\"ipAddress\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"客户端IP地址\"},{\"name\":\"requestParams\",\"dbName\":\"requestParams\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"请求参数（JSON格式）\"},{\"name\":\"responseData\",\"dbName\":\"responseData\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"返回数据（JSON格式，敏感信息已脱敏）\"},{\"name\":\"statusCode\",\"dbName\":\"statusCode\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"HTTP状态码\"},{\"name\":\"isSuccess\",\"dbName\":\"isSuccess\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"操作是否成功\"},{\"name\":\"errorMessage\",\"dbName\":\"errorMessage\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"错误信息（失败时记录）\"},{\"name\":\"duration\",\"dbName\":\"duration\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"请求耗时（毫秒）\"},{\"name\":\"createdAt\",\"dbName\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"创建时间\"},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"OperationLogToUser\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"SetNull\",\"isGenerated\":false,\"isUpdatedAt\":false,\"documentation\":\"关联用户\"}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false,\"documentation\":\"操作日志表 - 记录所有API调用\"}},\"enums\":{\"UserRole\":{\"values\":[{\"name\":\"ADMIN\",\"dbName\":null},{\"name\":\"FINANCE\",\"dbName\":null},{\"name\":\"BUSINESS\",\"dbName\":null},{\"name\":\"AUDITOR\",\"dbName\":null},{\"name\":\"USER\",\"dbName\":null}],\"dbName\":null},\"UserStatus\":{\"values\":[{\"name\":\"ACTIVE\",\"dbName\":null},{\"name\":\"INACTIVE\",\"dbName\":null}],\"dbName\":null},\"InvoiceType\":{\"values\":[{\"name\":\"SPECIAL_VAT\",\"dbName\":null},{\"name\":\"ORDINARY_VAT\",\"dbName\":null},{\"name\":\"ELECTRONIC\",\"dbName\":null},{\"name\":\"RECEIPT\",\"dbName\":null},{\"name\":\"OTHER\",\"dbName\":null}],\"dbName\":null},\"InvoiceStatus\":{\"values\":[{\"name\":\"NORMAL\",\"dbName\":null},{\"name\":\"CANCELLED\",\"dbName\":null}],\"dbName\":null},\"VerificationStatus\":{\"values\":[{\"name\":\"UNVERIFIED\",\"dbName\":null},{\"name\":\"VERIFIED\",\"dbName\":null},{\"name\":\"FAILED\",\"dbName\":null},{\"name\":\"DUPLICATE\",\"dbName\":null}],\"dbName\":null},\"FileType\":{\"values\":[{\"name\":\"PDF\",\"dbName\":null},{\"name\":\"IMAGE\",\"dbName\":null},{\"name\":\"EXCEL\",\"dbName\":null},{\"name\":\"OTHER\",\"dbName\":null}],\"dbName\":null}},\"types\":{}}")
defineDmmfProperty(exports.Prisma, config.runtimeDataModel)
config.engineWasm = undefined
config.compilerWasm = undefined


const { warnEnvConflicts } = require('@prisma/client/runtime/library.js')

warnEnvConflicts({
    rootEnvPath: config.relativeEnvPaths.rootEnvPath && path.resolve(config.dirname, config.relativeEnvPaths.rootEnvPath),
    schemaEnvPath: config.relativeEnvPaths.schemaEnvPath && path.resolve(config.dirname, config.relativeEnvPaths.schemaEnvPath)
})

const PrismaClient = getPrismaClient(config)
exports.PrismaClient = PrismaClient
Object.assign(exports, Prisma)

// file annotations for bundling tools to include these files
path.join(__dirname, "query_engine-windows.dll.node");
path.join(process.cwd(), "node_modules/.prisma/client/query_engine-windows.dll.node")
// file annotations for bundling tools to include these files
path.join(__dirname, "schema.prisma");
path.join(process.cwd(), "node_modules/.prisma/client/schema.prisma")
