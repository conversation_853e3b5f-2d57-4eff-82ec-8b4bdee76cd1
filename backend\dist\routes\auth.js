"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const client_1 = require("@prisma/client");
const joi_1 = __importDefault(require("joi"));
const errorHandler_1 = require("../middleware/errorHandler");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
const prisma = new client_1.PrismaClient();
// 验证schemas
const loginSchema = joi_1.default.object({
    emailOrUsername: joi_1.default.string().required().messages({
        'any.required': '用户名或邮箱是必填项',
    }),
    password: joi_1.default.string().min(6).required().messages({
        'string.min': '密码至少6位字符',
        'any.required': '密码是必填项',
    }),
});
const registerSchema = joi_1.default.object({
    email: joi_1.default.string().email().required(),
    password: joi_1.default.string().min(6).required(),
    name: joi_1.default.string().min(2).max(50).required(),
    role: joi_1.default.string().valid('ADMIN', 'FINANCE', 'BUSINESS', 'AUDITOR', 'USER').default('USER'),
});
// 生成JWT令牌
const generateToken = (userId) => {
    return jsonwebtoken_1.default.sign({ userId }, process.env.JWT_SECRET || 'invoice-management-super-secret-key-2024');
};
// 用户登录
router.post('/login', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { error, value } = loginSchema.validate(req.body);
    if (error) {
        throw new errorHandler_1.AppError(error.details[0].message, 400);
    }
    const { emailOrUsername, password } = value;
    // 查找用户（支持邮箱或用户名登录）
    const user = await prisma.user.findFirst({
        where: {
            OR: [
                { email: emailOrUsername },
                { username: emailOrUsername }
            ]
        },
    });
    if (!user || user.status !== 'ACTIVE') {
        throw new errorHandler_1.AppError('用户名/邮箱或密码错误', 401);
    }
    // 验证密码
    const isPasswordValid = await bcryptjs_1.default.compare(password, user.password);
    if (!isPasswordValid) {
        throw new errorHandler_1.AppError('用户名/邮箱或密码错误', 401);
    }
    // 生成令牌
    const token = generateToken(user.id);
    res.json({
        success: true,
        data: {
            user: {
                id: user.id,
                email: user.email,
                name: user.name,
                role: user.role,
                createdAt: user.createdAt,
            },
            token,
        },
        message: '登录成功',
    });
}));
// 用户注册
router.post('/register', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { error, value } = registerSchema.validate(req.body);
    if (error) {
        throw new errorHandler_1.AppError(error.details[0].message, 400);
    }
    const { email, password, name, role } = value;
    // 检查邮箱是否已存在
    const existingUser = await prisma.user.findUnique({
        where: { email },
    });
    if (existingUser) {
        throw new errorHandler_1.AppError('邮箱已被注册', 409);
    }
    // 加密密码
    const hashedPassword = await bcryptjs_1.default.hash(password, 12);
    // 创建用户
    const user = await prisma.user.create({
        data: {
            username: email, // 使用邮箱作为用户名
            email,
            password: hashedPassword,
            name,
            role,
            status: 'ACTIVE'
        },
    });
    // 生成令牌
    const token = generateToken(user.id);
    res.status(201).json({
        success: true,
        data: {
            user: {
                id: user.id,
                email: user.email,
                name: user.name,
                role: user.role,
                createdAt: user.createdAt,
            },
            token,
        },
        message: '注册成功',
    });
}));
// 获取当前用户信息
router.get('/me', auth_1.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const user = await prisma.user.findUnique({
        where: { id: req.user.id },
        select: {
            id: true,
            username: true,
            email: true,
            name: true,
            role: true,
            status: true,
            createdAt: true,
            updatedAt: true,
        },
    });
    res.json({
        success: true,
        data: user,
    });
}));
// 用户登出
router.post('/logout', auth_1.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    // 在实际应用中，可以将令牌加入黑名单
    // 这里简单返回成功消息
    res.json({
        success: true,
        message: '登出成功',
    });
}));
// 修改密码
router.put('/change-password', auth_1.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const schema = joi_1.default.object({
        currentPassword: joi_1.default.string().required(),
        newPassword: joi_1.default.string().min(6).required(),
    });
    const { error, value } = schema.validate(req.body);
    if (error) {
        throw new errorHandler_1.AppError(error.details[0].message, 400);
    }
    const { currentPassword, newPassword } = value;
    // 获取用户当前密码
    const user = await prisma.user.findUnique({
        where: { id: req.user.id },
    });
    if (!user) {
        throw new errorHandler_1.AppError('用户不存在', 404);
    }
    // 验证当前密码
    const isCurrentPasswordValid = await bcryptjs_1.default.compare(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
        throw new errorHandler_1.AppError('当前密码错误', 400);
    }
    // 加密新密码
    const hashedNewPassword = await bcryptjs_1.default.hash(newPassword, 12);
    // 更新密码
    await prisma.user.update({
        where: { id: req.user.id },
        data: { password: hashedNewPassword },
    });
    res.json({
        success: true,
        message: '密码修改成功',
    });
}));
exports.default = router;
//# sourceMappingURL=auth.js.map