"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const client_1 = require("@prisma/client");
const auth_1 = require("../middleware/auth");
const errorHandler_1 = require("../middleware/errorHandler");
const errors_1 = require("../utils/errors");
const joi_1 = __importDefault(require("joi"));
const router = express_1.default.Router();
const prisma = new client_1.PrismaClient();
// 查询参数验证
const querySchema = joi_1.default.object({
    page: joi_1.default.number().integer().min(1).default(1),
    pageSize: joi_1.default.number().integer().min(1).max(100).default(20),
    username: joi_1.default.string().optional(),
    operationName: joi_1.default.string().optional(),
    method: joi_1.default.string().valid('GET', 'POST', 'PUT', 'DELETE').optional(),
    path: joi_1.default.string().optional(),
    isSuccess: joi_1.default.boolean().optional(),
    startDate: joi_1.default.date().optional(),
    endDate: joi_1.default.date().optional(),
    sortBy: joi_1.default.string().valid('createdAt', 'operationName', 'username', 'duration').default('createdAt'),
    sortOrder: joi_1.default.string().valid('asc', 'desc').default('desc'),
});
// 获取操作日志列表
router.get('/', auth_1.authenticate, (0, auth_1.authorize)('ADMIN'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { error, value } = querySchema.validate(req.query);
    if (error) {
        throw new errors_1.AppError(error.details[0].message, 400);
    }
    const { page, pageSize, username, operationName, method, path, isSuccess, startDate, endDate, sortBy, sortOrder } = value;
    // 构建查询条件
    const where = {};
    if (username) {
        where.username = {
            contains: username
        };
    }
    if (operationName) {
        where.operationName = {
            contains: operationName
        };
    }
    if (method) {
        where.method = method;
    }
    if (path) {
        where.path = {
            contains: path
        };
    }
    if (isSuccess !== undefined) {
        where.isSuccess = isSuccess;
    }
    if (startDate || endDate) {
        where.createdAt = {};
        if (startDate) {
            where.createdAt.gte = new Date(startDate);
        }
        if (endDate) {
            where.createdAt.lte = new Date(endDate);
        }
    }
    // 计算分页
    const skip = (page - 1) * pageSize;
    // 获取总数
    const total = await prisma.operationLog.count({ where });
    // 获取数据
    const logs = await prisma.operationLog.findMany({
        where,
        skip,
        take: pageSize,
        orderBy: { [sortBy]: sortOrder },
        include: {
            user: {
                select: {
                    id: true,
                    username: true,
                    name: true,
                    role: true
                }
            }
        }
    });
    res.json({
        success: true,
        data: {
            data: logs,
            total,
            page,
            pageSize,
            totalPages: Math.ceil(total / pageSize),
        },
    });
}));
// 获取操作日志详情
router.get('/:id', auth_1.authenticate, (0, auth_1.authorize)('ADMIN'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const log = await prisma.operationLog.findUnique({
        where: { id },
        include: {
            user: {
                select: {
                    id: true,
                    username: true,
                    name: true,
                    role: true,
                    email: true
                }
            }
        }
    });
    if (!log) {
        throw new errors_1.AppError('操作日志不存在', 404);
    }
    res.json({
        success: true,
        data: log,
    });
}));
// 获取操作统计
router.get('/stats/summary', auth_1.authenticate, (0, auth_1.authorize)('ADMIN'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    // 今日统计
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const todayStats = await prisma.operationLog.groupBy({
        by: ['isSuccess'],
        where: {
            createdAt: {
                gte: today,
                lt: tomorrow
            }
        },
        _count: {
            id: true
        }
    });
    // 本周统计
    const weekStart = new Date();
    weekStart.setDate(weekStart.getDate() - weekStart.getDay());
    weekStart.setHours(0, 0, 0, 0);
    const weekStats = await prisma.operationLog.groupBy({
        by: ['isSuccess'],
        where: {
            createdAt: {
                gte: weekStart
            }
        },
        _count: {
            id: true
        }
    });
    // 操作类型统计
    const operationStats = await prisma.operationLog.groupBy({
        by: ['operationName'],
        where: {
            createdAt: {
                gte: weekStart
            }
        },
        _count: {
            id: true
        },
        orderBy: {
            _count: {
                id: 'desc'
            }
        },
        take: 10
    });
    // 用户活跃度统计
    const userStats = await prisma.operationLog.groupBy({
        by: ['userId', 'username'],
        where: {
            createdAt: {
                gte: weekStart
            },
            userId: {
                not: null
            }
        },
        _count: {
            id: true
        },
        orderBy: {
            _count: {
                id: 'desc'
            }
        },
        take: 10
    });
    res.json({
        success: true,
        data: {
            today: {
                success: todayStats.find(s => s.isSuccess)?._count.id || 0,
                failed: todayStats.find(s => !s.isSuccess)?._count.id || 0,
                total: todayStats.reduce((sum, s) => sum + s._count.id, 0)
            },
            week: {
                success: weekStats.find(s => s.isSuccess)?._count.id || 0,
                failed: weekStats.find(s => !s.isSuccess)?._count.id || 0,
                total: weekStats.reduce((sum, s) => sum + s._count.id, 0)
            },
            topOperations: operationStats,
            activeUsers: userStats
        },
    });
}));
// 清理旧日志（管理员功能）
router.delete('/cleanup/:days', auth_1.authenticate, (0, auth_1.authorize)('ADMIN'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const days = parseInt(req.params.days);
    if (isNaN(days) || days < 1) {
        throw new errors_1.AppError('天数必须是大于0的整数', 400);
    }
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);
    const result = await prisma.operationLog.deleteMany({
        where: {
            createdAt: {
                lt: cutoffDate
            }
        }
    });
    res.json({
        success: true,
        message: `已清理 ${result.count} 条 ${days} 天前的操作日志`,
        data: {
            deletedCount: result.count,
            cutoffDate: cutoffDate
        }
    });
}));
exports.default = router;
//# sourceMappingURL=operationLogs.js.map