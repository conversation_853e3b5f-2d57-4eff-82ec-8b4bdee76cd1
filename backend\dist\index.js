"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = exports.prisma = void 0;
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const dotenv_1 = __importDefault(require("dotenv"));
const client_1 = require("@prisma/client");
const winston_1 = __importDefault(require("winston"));
const path_1 = __importDefault(require("path"));
BigInt.prototype.toJSON = function () {
    return Number(this);
};
// 路由导入
const auth_1 = __importDefault(require("./routes/auth"));
const companies_1 = __importDefault(require("./routes/companies"));
const invoices_1 = __importDefault(require("./routes/invoices"));
const receivedInvoices_1 = __importDefault(require("./routes/receivedInvoices"));
const upload_1 = __importDefault(require("./routes/upload"));
const dashboard_1 = __importDefault(require("./routes/dashboard"));
const users_1 = __importDefault(require("./routes/users"));
const menus_1 = __importDefault(require("./routes/menus"));
const invoiceRelations_1 = __importDefault(require("./routes/invoiceRelations"));
const operationLogs_1 = __importDefault(require("./routes/operationLogs"));
// 中间件导入
const errorHandler_1 = require("./middleware/errorHandler");
const auditLogger_1 = require("./middleware/auditLogger");
const operationLogger_1 = require("./middleware/operationLogger");
// 加载环境变量 - 根据NODE_ENV加载对应的配置文件
const nodeEnv = process.env.NODE_ENV || 'development';
console.log(`🔧 Loading environment: ${nodeEnv}`);
if (nodeEnv === 'production') {
    // 生产环境：优先加载.env.production，然后加载.env作为备用
    const productionResult = dotenv_1.default.config({ path: '.env.production' });
    if (productionResult.error) {
        console.warn('⚠️ .env.production not found, falling back to .env');
        dotenv_1.default.config();
    }
    else {
        console.log('✅ Loaded .env.production');
    }
}
else {
    // 开发环境：加载.env
    dotenv_1.default.config();
    console.log('✅ Loaded .env (development)');
}
const app = (0, express_1.default)();
const prisma = new client_1.PrismaClient();
exports.prisma = prisma;
const PORT = process.env.PORT || 3001;
// 配置日志
const logger = winston_1.default.createLogger({
    level: 'info',
    format: winston_1.default.format.combine(winston_1.default.format.timestamp(), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json()),
    defaultMeta: { service: 'invoice-management' },
    transports: [
        new winston_1.default.transports.File({ filename: 'logs/error.log', level: 'error' }),
        new winston_1.default.transports.File({ filename: 'logs/combined.log' }),
    ],
});
exports.logger = logger;
if (process.env.NODE_ENV !== 'production') {
    logger.add(new winston_1.default.transports.Console({
        format: winston_1.default.format.simple()
    }));
}
// 中间件配置
app.use((0, cors_1.default)({
    origin: process.env.FRONTEND_URL || 'http://localhost:5173',
    credentials: true
}));
app.use(express_1.default.json({ limit: '50mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '50mb' }));
// 处理URL参数中的数组和中文字符
app.use((req, res, next) => {
    // 特殊处理sellerCompanies参数
    if (req.query.sellerCompanies && typeof req.query.sellerCompanies === 'string') {
        try {
            // URL参数已经被Express自动解码，但我们需要确保它被正确处理
            const decoded = req.query.sellerCompanies;
            // 如果包含逗号，分割成数组
            if (decoded.includes(',')) {
                req.query.sellerCompanies = decoded.split(',').map(s => s.trim());
            }
        }
        catch (error) {
            console.warn('URL参数处理失败:', error);
        }
    }
    next();
});
// 静态文件服务
app.use('/uploads', express_1.default.static(path_1.default.join(__dirname, '../uploads')));
// 操作日志中间件（在路由之前）
app.use(operationLogger_1.operationLogger);
// 审计日志中间件
app.use(auditLogger_1.auditLogger);
// 基本路由
app.get('/', (req, res) => {
    res.json({
        message: '发票管理系统 API',
        version: '2.0.0',
        status: 'running',
        features: [
            '发票全生命周期管理',
            'RBAC权限控制',
            '文件上传与OCR识别',
            '税务合规校验',
            '多维度报表分析',
            '操作审计日志'
        ]
    });
});
// 健康检查
app.get('/health', async (req, res) => {
    try {
        // 测试数据库连接
        await prisma.$queryRaw `SELECT 1`;
        res.json({
            status: 'healthy',
            database: 'connected',
            timestamp: new Date().toISOString(),
            uptime: process.uptime()
        });
    }
    catch (error) {
        logger.error('Health check failed:', error);
        res.status(500).json({
            status: 'unhealthy',
            database: 'disconnected',
            error: 'Database connection failed',
            timestamp: new Date().toISOString()
        });
    }
});
// 附件下载路由（不需要认证中间件，在其他路由之前）
app.get('/api/upload/attachment/:attachmentId', async (req, res) => {
    const { attachmentId } = req.params;
    const { preview, type, token } = req.query;
    try {
        // 如果提供了token参数，验证token
        if (token) {
            try {
                const jwt = require('jsonwebtoken');
                const decoded = jwt.verify(token, process.env.JWT_SECRET || 'invoice-management-super-secret-key-2024');
                console.log('Token验证成功:', decoded);
            }
            catch (error) {
                console.error('Token验证失败:', error);
                res.status(401).json({
                    success: false,
                    message: '无效的访问令牌',
                    error: error.message
                });
                return;
            }
        }
        else {
            // 如果没有token参数，检查Authorization头
            const authHeader = req.headers.authorization;
            if (!authHeader || !authHeader.startsWith('Bearer ')) {
                res.status(401).json({
                    success: false,
                    message: '缺少访问令牌'
                });
                return;
            }
            try {
                const jwt = require('jsonwebtoken');
                const token = authHeader.substring(7);
                const decoded = jwt.verify(token, process.env.JWT_SECRET || 'invoice-management-super-secret-key-2024');
                console.log('Authorization头验证成功:', decoded);
            }
            catch (error) {
                res.status(401).json({
                    success: false,
                    message: '无效的访问令牌'
                });
                return;
            }
        }
        let attachment = null;
        // 根据类型查找附件
        if (type === 'received') {
            attachment = await prisma.receivedInvoiceAttachment.findUnique({
                where: { id: attachmentId },
            });
        }
        else {
            attachment = await prisma.invoiceAttachment.findUnique({
                where: { id: attachmentId },
            });
        }
        if (!attachment) {
            res.status(404).json({
                success: false,
                message: '附件不存在',
                error: `Attachment not found: ${attachmentId}, type: ${type}`
            });
            return;
        }
        const fs = require('fs');
        const path = require('path');
        const filePath = attachment.filePath;
        if (!fs.existsSync(filePath)) {
            res.status(404).json({
                success: false,
                message: '文件不存在',
                error: `File not found: ${filePath}`
            });
            return;
        }
        // 获取文件信息
        const stats = fs.statSync(filePath);
        const ext = path.extname(filePath).toLowerCase();
        // 使用原始文件名作为下载文件名
        const originalFileName = attachment.fileName;
        // 设置响应头
        if (preview === 'true') {
            // 预览模式
            if (ext === '.pdf') {
                res.setHeader('Content-Type', 'application/pdf');
                res.setHeader('Content-Disposition', 'inline; filename*=UTF-8\'\'' + encodeURIComponent(originalFileName));
                // 设置CORS头以支持iframe预览 - 不设置X-Frame-Options以允许iframe显示
                // 移除X-Frame-Options和CSP限制，允许在iframe中显示
            }
            else if (['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'].includes(ext)) {
                const mimeTypes = {
                    '.jpg': 'image/jpeg',
                    '.jpeg': 'image/jpeg',
                    '.png': 'image/png',
                    '.gif': 'image/gif',
                    '.webp': 'image/webp',
                    '.bmp': 'image/bmp',
                };
                res.setHeader('Content-Type', mimeTypes[ext] || 'application/octet-stream');
                res.setHeader('Content-Disposition', 'inline; filename*=UTF-8\'\'' + encodeURIComponent(originalFileName));
            }
            else {
                res.setHeader('Content-Type', 'application/octet-stream');
                res.setHeader('Content-Disposition', 'attachment; filename*=UTF-8\'\'' + encodeURIComponent(originalFileName));
            }
        }
        else {
            // 下载模式
            res.setHeader('Content-Type', 'application/octet-stream');
            res.setHeader('Content-Disposition', 'attachment; filename*=UTF-8\'\'' + encodeURIComponent(originalFileName));
        }
        res.setHeader('Content-Length', stats.size);
        res.setHeader('Cache-Control', 'public, max-age=31536000'); // 缓存一年
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
        // 发送文件
        const fileStream = fs.createReadStream(filePath);
        fileStream.pipe(res);
    }
    catch (error) {
        console.error('下载附件失败:', error);
        res.status(500).json({
            success: false,
            message: '下载附件失败',
            error: error.message
        });
    }
});
// API路由
app.use('/api/auth', auth_1.default);
app.use('/api/companies', companies_1.default);
app.use('/api/invoices', invoices_1.default);
app.use('/api/received-invoices', receivedInvoices_1.default);
app.use('/api/upload', upload_1.default);
app.use('/api/dashboard', dashboard_1.default);
app.use('/api/users', users_1.default);
app.use('/api/menus', menus_1.default);
app.use('/api/invoice-relations', invoiceRelations_1.default);
app.use('/api/operation-logs', operationLogs_1.default);
// 错误处理中间件
app.use(errorHandler_1.errorHandler);
// 404处理
app.use((req, res) => {
    res.status(404).json({
        success: false,
        message: 'Route not found',
        path: req.originalUrl
    });
});
// 初始化用户公司权限表
async function initializeUserCompanyTable() {
    try {
        // 检查user_companies表是否存在
        const userCompanyTableExists = await prisma.$queryRaw `
      SELECT COUNT(*) as count FROM information_schema.tables
      WHERE table_schema = DATABASE() AND table_name = 'user_companies'
    `;
        if (userCompanyTableExists[0].count === 0) {
            logger.info('🔧 Creating user_companies table...');
            await prisma.$executeRaw `
        CREATE TABLE user_companies (
          id VARCHAR(191) NOT NULL PRIMARY KEY,
          userId VARCHAR(191) NOT NULL,
          companyId VARCHAR(191) NOT NULL,
          createdAt DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
          updatedAt DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
          UNIQUE KEY user_companies_userId_companyId_key (userId, companyId)
        )
      `;
            logger.info('✅ user_companies table created successfully');
        }
    }
    catch (error) {
        logger.error('❌ User companies table initialization failed:', error);
    }
}
// 菜单表初始化函数
async function initializeMenuTables() {
    try {
        // 强制创建菜单表（如果不存在）
        logger.info('🔧 Creating menus table if not exists...');
        await prisma.$executeRaw `
      CREATE TABLE IF NOT EXISTS menus (
        id VARCHAR(191) NOT NULL PRIMARY KEY,
        \`key\` VARCHAR(191) NOT NULL UNIQUE,
        name VARCHAR(191) NOT NULL,
        path VARCHAR(191) NULL,
        icon VARCHAR(191) NULL,
        parentId VARCHAR(191) NULL,
        sort INT NOT NULL DEFAULT 0,
        isActive BOOLEAN NOT NULL DEFAULT true,
        description TEXT NULL,
        createdAt DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
        updatedAt DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3)
      )
    `;
        logger.info('✅ menus table created/verified successfully');
        // 强制创建用户菜单权限表（如果不存在）
        logger.info('🔧 Creating user_menu_permissions table if not exists...');
        await prisma.$executeRaw `
      CREATE TABLE IF NOT EXISTS user_menu_permissions (
        id VARCHAR(191) NOT NULL PRIMARY KEY,
        userId VARCHAR(191) NOT NULL,
        menuId VARCHAR(191) NOT NULL,
        canView BOOLEAN NOT NULL DEFAULT true,
        canEdit BOOLEAN NOT NULL DEFAULT false,
        canDelete BOOLEAN NOT NULL DEFAULT false,
        canExport BOOLEAN NOT NULL DEFAULT false,
        createdAt DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
        updatedAt DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
        UNIQUE KEY user_menu_permissions_userId_menuId_key (userId, menuId)
      )
    `;
        logger.info('✅ user_menu_permissions table created/verified successfully');
        // 创建操作日志表
        logger.info('🔧 Creating operation_logs table if not exists...');
        await prisma.$executeRaw `
      CREATE TABLE IF NOT EXISTS operation_logs (
        id VARCHAR(191) NOT NULL PRIMARY KEY,
        userId VARCHAR(191) NULL,
        username VARCHAR(191) NULL,
        operationName VARCHAR(191) NOT NULL,
        method VARCHAR(191) NOT NULL,
        path VARCHAR(191) NOT NULL,
        userAgent TEXT NULL,
        ipAddress VARCHAR(191) NULL,
        requestParams JSON NULL,
        responseData JSON NULL,
        statusCode INT NOT NULL,
        isSuccess BOOLEAN NOT NULL,
        errorMessage TEXT NULL,
        duration INT NULL,
        createdAt DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
        INDEX operation_logs_userId_idx (userId),
        INDEX operation_logs_operationName_idx (operationName),
        INDEX operation_logs_path_idx (path),
        INDEX operation_logs_createdAt_idx (createdAt),
        INDEX operation_logs_isSuccess_idx (isSuccess)
      )
    `;
        logger.info('✅ operation_logs table created/verified successfully');
        // 初始化默认菜单
        await initializeDefaultMenus();
    }
    catch (error) {
        logger.error('❌ Menu tables initialization failed:', error);
    }
}
// 初始化默认菜单
async function initializeDefaultMenus() {
    try {
        // 首先检查menus表是否存在
        const menuTableExists = await prisma.$queryRaw `
      SELECT COUNT(*) as count
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_NAME = 'menus' AND TABLE_SCHEMA = 'InvicesAI'
    `;
        if (menuTableExists[0].count === 0) {
            logger.warn('⚠️ Menus table does not exist, skipping default menu initialization');
            return;
        }
        // 强制重新初始化菜单数据
        logger.info('🔧 Reinitializing default menus...');
        // 先清空现有菜单数据
        await prisma.$executeRaw `DELETE FROM user_menu_permissions`;
        await prisma.$executeRaw `DELETE FROM menus`;
        const defaultMenus = [
            // 一级菜单
            { id: 'dashboard', key: 'dashboard', name: '仪表板', path: '/dashboard', icon: 'DashboardOutlined', parentId: null, sort: 1 },
            { id: 'companies', key: 'companies', name: '公司管理', path: '/companies', icon: 'BankOutlined', parentId: null, sort: 2 },
            { id: 'invoices', key: 'invoices', name: '开具发票', path: '/invoices', icon: 'FileTextOutlined', parentId: null, sort: 3 },
            { id: 'received-invoices', key: 'received-invoices', name: '取得发票', path: '/received-invoices', icon: 'FileTextOutlined', parentId: null, sort: 4 },
            { id: 'import', key: 'import', name: 'Excel导入', path: '/import', icon: 'ImportOutlined', parentId: null, sort: 5 },
            { id: 'users', key: 'users', name: '用户管理', path: '/users', icon: 'UserOutlined', parentId: null, sort: 6 },
            { id: 'operation-logs', key: 'operation-logs', name: '操作日志', path: '/operation-logs', icon: 'HistoryOutlined', parentId: null, sort: 7 },
            { id: 'reports', key: 'reports', name: '报表', path: null, icon: 'BarChartOutlined', parentId: null, sort: 8 },
            // 报表子菜单
            { id: 'quarterly-summary', key: 'quarterly-summary', name: '季度开票汇总', path: '/reports/quarterly-summary', icon: 'BarChartOutlined', parentId: 'reports', sort: 1 },
            { id: 'company-summary', key: 'company-summary', name: '公司开票汇总', path: '/reports/company-summary', icon: 'BarChartOutlined', parentId: 'reports', sort: 2 },
            { id: 'relations', key: 'relations', name: '开票关系穿透图', path: '/reports/relations', icon: 'NodeIndexOutlined', parentId: 'reports', sort: 3 },
        ];
        for (const menu of defaultMenus) {
            await prisma.$executeRaw `
        INSERT INTO menus (id, \`key\`, name, path, icon, parentId, sort, isActive, createdAt, updatedAt)
        VALUES (${menu.id}, ${menu.key}, ${menu.name}, ${menu.path}, ${menu.icon}, ${menu.parentId}, ${menu.sort}, true, NOW(), NOW())
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        path = VALUES(path),
        icon = VALUES(icon),
        parentId = VALUES(parentId),
        sort = VALUES(sort),
        updatedAt = NOW()
      `;
        }
        logger.info('✅ Default menus initialized successfully');
        // 为管理员用户创建默认菜单权限
        await createDefaultAdminMenuPermissions();
        // 为石磊用户创建基本菜单权限
        await createLeiShiMenuPermissions();
    }
    catch (error) {
        logger.error('❌ Default menus initialization failed:', error);
    }
}
// 为管理员创建默认菜单权限
async function createDefaultAdminMenuPermissions() {
    try {
        // 获取所有管理员用户
        const adminUsers = await prisma.$queryRaw `
      SELECT id FROM users WHERE role = 'ADMIN'
    `;
        // 获取所有菜单
        const allMenus = await prisma.$queryRaw `
      SELECT id FROM menus WHERE isActive = true
    `;
        // 为每个管理员用户创建所有菜单的完整权限
        for (const admin of adminUsers) {
            for (const menu of allMenus) {
                const permissionId = `perm_${admin.id}_${menu.id}`;
                await prisma.$executeRaw `
          INSERT INTO user_menu_permissions (id, userId, menuId, canView, canEdit, canDelete, canExport, createdAt, updatedAt)
          VALUES (${permissionId}, ${admin.id}, ${menu.id}, true, true, true, true, NOW(), NOW())
          ON DUPLICATE KEY UPDATE
          canView = true,
          canEdit = true,
          canDelete = true,
          canExport = true,
          updatedAt = NOW()
        `;
            }
        }
        logger.info('✅ Default admin menu permissions created successfully');
    }
    catch (error) {
        logger.error('❌ Failed to create default admin menu permissions:', error);
    }
}
// 为石磊用户创建基本菜单权限（除了用户管理）
async function createLeiShiMenuPermissions() {
    try {
        const leiShiUserId = 'cmb70ud6d0000v0z8atel4lt1';
        // 检查石磊用户是否存在
        const leiShiUser = await prisma.$queryRaw `
      SELECT id FROM users WHERE id = ${leiShiUserId}
    `;
        if (leiShiUser.length === 0) {
            logger.warn('⚠️ Lei Shi user not found, skipping menu permissions creation');
            return;
        }
        // 获取除了用户管理和操作日志之外的所有菜单
        const allowedMenus = await prisma.$queryRaw `
      SELECT id FROM menus WHERE isActive = true AND \`key\` NOT IN ('users', 'operation-logs')
    `;
        // 先删除石磊用户现有的菜单权限
        await prisma.$executeRaw `
      DELETE FROM user_menu_permissions WHERE userId = ${leiShiUserId}
    `;
        // 为石磊用户创建基本菜单权限（只有查看权限）
        for (const menu of allowedMenus) {
            const permissionId = `perm_${leiShiUserId}_${menu.id}`;
            await prisma.$executeRaw `
        INSERT INTO user_menu_permissions (id, userId, menuId, canView, canEdit, canDelete, canExport, createdAt, updatedAt)
        VALUES (${permissionId}, ${leiShiUserId}, ${menu.id}, true, false, false, false, NOW(), NOW())
      `;
        }
        logger.info('✅ Lei Shi user menu permissions created successfully');
    }
    catch (error) {
        logger.error('❌ Failed to create Lei Shi user menu permissions:', error);
    }
}
// 数据库初始化函数
async function initializeDatabase() {
    try {
        // 检查是否需要添加注册日期字段
        const result = await prisma.$queryRaw `
      SELECT COLUMN_NAME
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_NAME = 'companies'
      AND COLUMN_NAME IN ('registrationDate', 'lastInvoiceDate')
    `;
        const existingColumns = result.map((row) => row.COLUMN_NAME);
        if (!existingColumns.includes('registrationDate')) {
            logger.info('🔧 Adding registrationDate column to companies table...');
            await prisma.$executeRaw `
        ALTER TABLE companies
        ADD COLUMN registrationDate DATETIME NULL
      `;
            logger.info('✅ registrationDate column added successfully');
        }
        if (!existingColumns.includes('lastInvoiceDate')) {
            logger.info('🔧 Adding lastInvoiceDate column to companies table...');
            await prisma.$executeRaw `
        ALTER TABLE companies
        ADD COLUMN lastInvoiceDate DATETIME NULL
      `;
            logger.info('✅ lastInvoiceDate column added successfully');
        }
        // 检查并创建用户公司权限表
        await initializeUserCompanyTable();
        // 检查并创建菜单表
        await initializeMenuTables();
        if (existingColumns.includes('registrationDate') && existingColumns.includes('lastInvoiceDate')) {
            logger.info('✅ Database schema is up to date');
        }
    }
    catch (error) {
        logger.error('❌ Database initialization failed:', error);
        // 不要因为数据库初始化失败而停止服务器
    }
}
// 启动服务器
app.listen(PORT, async () => {
    logger.info(`🚀 Server is running on http://localhost:${PORT}`);
    logger.info(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
    logger.info(`🔗 Frontend URL: ${process.env.FRONTEND_URL || 'http://localhost:5173'}`);
    logger.info(`📁 Upload directory: ${process.env.UPLOAD_DIR || 'uploads'}`);
    // 暂时跳过数据库初始化以便测试
    // await initializeDatabase();
    logger.info('⚠️ Database initialization skipped for testing');
});
// 优雅关闭
process.on('SIGINT', async () => {
    logger.info('🛑 Shutting down server...');
    await prisma.$disconnect();
    process.exit(0);
});
process.on('SIGTERM', async () => {
    logger.info('🛑 Shutting down server...');
    await prisma.$disconnect();
    process.exit(0);
});
// 创建默认管理员用户
async function createDefaultAdmin() {
    try {
        const adminExists = await prisma.user.findFirst({
            where: { role: 'ADMIN' },
        });
        if (!adminExists) {
            const bcrypt = require('bcryptjs');
            const hashedPassword = await bcrypt.hash('admin123', 12);
            await prisma.user.create({
                data: {
                    username: 'admin',
                    email: '<EMAIL>',
                    password: hashedPassword,
                    name: '系统管理员',
                    role: 'ADMIN',
                    status: 'ACTIVE'
                },
            });
            logger.info('Default admin user created: <EMAIL> / admin123');
        }
    }
    catch (error) {
        logger.error('Failed to create default admin user:', error);
    }
}
// 启动时创建默认用户
createDefaultAdmin();
//# sourceMappingURL=index.js.map