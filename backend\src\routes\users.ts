import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';
import Joi from 'joi';
import { AppError } from '../middleware/errorHandler';
import { authenticate, authorize, AuthenticatedRequest } from '../middleware/auth';

const router = Router();
const prisma = new PrismaClient();

// 应用认证中间件到所有路由
router.use(authenticate);

// 验证schemas
const createUserSchema = Joi.object({
  username: Joi.string().min(2).max(50).required().messages({
    'string.min': '用户名至少2个字符',
    'string.max': '用户名不能超过50个字符',
    'any.required': '用户名是必填项',
  }),
  email: Joi.string().email().required().messages({
    'string.email': '请输入有效的邮箱地址',
    'any.required': '邮箱是必填项',
  }),
  name: Joi.string().min(2).max(50).required().messages({
    'string.min': '姓名至少2个字符',
    'string.max': '姓名不能超过50个字符',
    'any.required': '姓名是必填项',
  }),
  password: Joi.string().min(6).required().messages({
    'string.min': '密码至少6个字符',
    'any.required': '密码是必填项',
  }),
  role: Joi.string().valid('ADMIN', 'FINANCE', 'BUSINESS', 'AUDITOR', 'USER').default('USER'),
  status: Joi.string().valid('ACTIVE', 'INACTIVE').default('ACTIVE'),
  companyIds: Joi.array().items(Joi.string()).optional(),
});

const updateUserSchema = createUserSchema.fork(['password'], (schema) => schema.optional());

const querySchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  pageSize: Joi.number().integer().min(1).max(100).default(20),
  search: Joi.string().optional(),
  role: Joi.string().valid('ADMIN', 'FINANCE', 'BUSINESS', 'AUDITOR', 'USER').optional(),
  status: Joi.string().valid('ACTIVE', 'INACTIVE').optional(),
});

// 异步处理器包装函数
const asyncHandler = (fn: Function) => (req: AuthenticatedRequest, res: any, next: any) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// 获取用户列表
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res: any) => {
  const { error, value } = querySchema.validate(req.query);
  if (error) {
    throw new AppError(error.details[0].message, 400);
  }

  const { page, pageSize, search, role, status } = value;
  const skip = (page - 1) * pageSize;

  // 构建查询条件
  const where: any = {};

  if (search) {
    where.OR = [
      { username: { contains: search } },
      { email: { contains: search } },
      { name: { contains: search } },
    ];
  }

  if (role) where.role = role;
  if (status) where.status = status;

  // 获取总数
  const total = await prisma.user.count({ where });

  // 获取数据
  const users = await prisma.user.findMany({
    where,
    skip,
    take: pageSize,
    orderBy: { createdAt: 'desc' },
  });

  // 手动获取用户公司关联信息
  const usersWithCompanies = await Promise.all(
    users.map(async (user) => {
      try {
        const userCompanies = await prisma.$queryRaw`
          SELECT uc.companyId, c.name as companyName
          FROM user_companies uc
          LEFT JOIN companies c ON uc.companyId = c.id
          WHERE uc.userId = ${user.id}
        ` as any[];

        return {
          ...user,
          userCompanies: userCompanies.map((uc: any) => ({
            companyId: uc.companyId,
            company: {
              id: uc.companyId,
              name: uc.companyName,
            }
          }))
        };
      } catch (error) {
        console.error(`获取用户 ${user.id} 的公司关联失败:`, error);
        return {
          ...user,
          userCompanies: []
        };
      }
    })
  );

  res.json({
    success: true,
    data: {
      data: usersWithCompanies,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    },
  });
}));

// 获取单个用户
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res: any) => {
  const { id } = req.params;

  const user = await prisma.user.findUnique({
    where: { id },
    include: {
      userCompanies: {
        include: {
          company: {
            select: {
              id: true,
              name: true,
            }
          }
        }
      }
    }
  });

  if (!user) {
    throw new AppError('用户不存在', 404);
  }

  res.json({
    success: true,
    data: user
  });
}));

// 创建用户
router.post('/', authorize('ADMIN'), asyncHandler(async (req: AuthenticatedRequest, res: any) => {
  const { error, value } = createUserSchema.validate(req.body);
  if (error) {
    throw new AppError(error.details[0].message, 400);
  }

  const { username, email, name, password, role, status, companyIds } = value;

  // 检查用户名和邮箱是否已存在
  const existingUser = await prisma.user.findFirst({
    where: {
      OR: [
        { username },
        { email }
      ]
    }
  });

  if (existingUser) {
    throw new AppError('用户名或邮箱已存在', 400);
  }

  // 加密密码
  const hashedPassword = await bcrypt.hash(password, 10);

  // 创建用户
  const user = await prisma.user.create({
    data: {
      username,
      email,
      name,
      password: hashedPassword,
      role: role || 'USER',
      status: status || 'ACTIVE'
    }
  });

  // 如果指定了公司权限，创建用户-公司关联
  if (companyIds && Array.isArray(companyIds) && companyIds.length > 0) {
    await prisma.userCompany.createMany({
      data: companyIds.map((companyId: string) => ({
        userId: user.id,
        companyId
      }))
    });
  }

  // 获取完整的用户信息
  const fullUser = await prisma.user.findUnique({
    where: { id: user.id },
    include: {
      userCompanies: {
        include: {
          company: {
            select: {
              id: true,
              name: true,
            }
          }
        }
      }
    }
  });

  res.status(201).json({
    success: true,
    data: fullUser,
    message: '用户创建成功'
  });
}));

// 更新用户
router.put('/:id', authorize('ADMIN'), asyncHandler(async (req: AuthenticatedRequest, res: any) => {
  const { id } = req.params;
  const { error, value } = updateUserSchema.validate(req.body);
  if (error) {
    throw new AppError(error.details[0].message, 400);
  }

  const { username, email, name, role, status, companyIds } = value;

  // 检查用户是否存在
  const existingUser = await prisma.user.findUnique({
    where: { id }
  });

  if (!existingUser) {
    throw new AppError('用户不存在', 404);
  }

  // 检查用户名和邮箱是否被其他用户使用
  const duplicateUser = await prisma.user.findFirst({
    where: {
      AND: [
        { id: { not: id } },
        {
          OR: [
            { username },
            { email }
          ]
        }
      ]
    }
  });

  if (duplicateUser) {
    throw new AppError('用户名或邮箱已被其他用户使用', 400);
  }

  // 更新用户基本信息
  const user = await prisma.user.update({
    where: { id },
    data: {
      username,
      email,
      name,
      role,
      status
    }
  });

  // 更新用户-公司关联 - 使用原始SQL避免字段不匹配问题
  try {
    // 先删除现有关联
    await prisma.$executeRaw`DELETE FROM user_companies WHERE userId = ${id}`;

    // 如果指定了新的公司权限，创建新关联
    if (companyIds && Array.isArray(companyIds) && companyIds.length > 0) {
      for (const companyId of companyIds) {
        const relationId = `uc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        await prisma.$executeRaw`
          INSERT INTO user_companies (id, userId, companyId, createdAt)
          VALUES (${relationId}, ${id}, ${companyId}, NOW())
        `;
      }
    }
  } catch (error) {
    console.error('更新用户公司关联失败:', error);
    // 如果表不存在，先创建表
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS user_companies (
        id VARCHAR(191) NOT NULL PRIMARY KEY,
        userId VARCHAR(191) NOT NULL,
        companyId VARCHAR(191) NOT NULL,
        createdAt DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
        UNIQUE KEY user_companies_userId_companyId_key (userId, companyId)
      )
    `;

    // 重新尝试插入
    if (companyIds && Array.isArray(companyIds) && companyIds.length > 0) {
      for (const companyId of companyIds) {
        const relationId = `uc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        await prisma.$executeRaw`
          INSERT INTO user_companies (id, userId, companyId, createdAt)
          VALUES (${relationId}, ${id}, ${companyId}, NOW())
        `;
      }
    }
  }

  // 获取完整的用户信息 - 手动查询避免关联问题
  const fullUser = await prisma.user.findUnique({
    where: { id }
  });

  // 手动获取用户公司关联信息
  let userCompanies: any[] = [];
  try {
    const userCompanyData = await prisma.$queryRaw`
      SELECT uc.companyId, c.name as companyName
      FROM user_companies uc
      LEFT JOIN companies c ON uc.companyId = c.id
      WHERE uc.userId = ${id}
    ` as any[];

    userCompanies = userCompanyData.map((uc: any) => ({
      companyId: uc.companyId,
      company: {
        id: uc.companyId,
        name: uc.companyName,
      }
    }));
  } catch (error) {
    console.error('获取用户公司关联失败:', error);
    userCompanies = [];
  }

  const fullUserWithCompanies = {
    ...fullUser,
    userCompanies
  };

  res.json({
    success: true,
    data: fullUserWithCompanies,
    message: '用户更新成功'
  });
}));

// 删除用户
router.delete('/:id', authorize('ADMIN'), asyncHandler(async (req: AuthenticatedRequest, res: any) => {
  const { id } = req.params;

  // 检查用户是否存在
  const existingUser = await prisma.user.findUnique({
    where: { id }
  });

  if (!existingUser) {
    throw new AppError('用户不存在', 404);
  }

  // 不能删除自己
  if (req.user?.id === id) {
    throw new AppError('不能删除自己的账户', 400);
  }

  // 删除用户-公司关联
  await prisma.userCompany.deleteMany({
    where: { userId: id }
  });

  // 删除用户
  await prisma.user.delete({
    where: { id }
  });

  res.json({
    success: true,
    message: '用户删除成功'
  });
}));

// 获取用户菜单权限
router.get('/:id/menu-permissions', authenticate, asyncHandler(async (req: AuthenticatedRequest, res: any) => {
  const { id } = req.params;

  try {
    // 从数据库获取所有菜单
    const systemMenus = await prisma.$queryRaw`
      SELECT id, \`key\`, name, parentId, sort
      FROM menus
      WHERE isActive = true
      ORDER BY parentId ASC, sort ASC
    ` as any[];

    // 构建层级排序的菜单列表
    const sortedMenus = sortMenusHierarchically(systemMenus);

    // 获取用户的菜单权限
    const userPermissions = await prisma.$queryRaw`
      SELECT
        menuId,
        canView,
        canEdit,
        canDelete,
        canExport
      FROM user_menu_permissions
      WHERE userId = ${id}
    ` as any[];

    // 构建权限映射
    const permissionMap = new Map();
    userPermissions.forEach((perm: any) => {
      permissionMap.set(perm.menuId, {
        canView: !!perm.canView,
        canEdit: !!perm.canEdit,
        canDelete: !!perm.canDelete,
        canExport: !!perm.canExport,
      });
    });

    // 合并系统菜单和用户权限
    const userMenus = sortedMenus.map((menu: any) => ({
      id: menu.id,
      key: menu.key,
      name: menu.name,
      parentId: menu.parentId,
      sort: menu.sort,
      canView: permissionMap.get(menu.id)?.canView || false,
      canEdit: permissionMap.get(menu.id)?.canEdit || false,
      canDelete: permissionMap.get(menu.id)?.canDelete || false,
      canExport: permissionMap.get(menu.id)?.canExport || false,
    }));

    res.json({
      success: true,
      data: userMenus,
    });
  } catch (error) {


    // 如果数据库查询失败，返回默认菜单结构
    const defaultMenus = [
      { id: 'dashboard', key: 'dashboard', name: '仪表板', parentId: null, sort: 1, canView: false, canEdit: false, canDelete: false, canExport: false },
      { id: 'companies', key: 'companies', name: '公司管理', parentId: null, sort: 2, canView: false, canEdit: false, canDelete: false, canExport: false },
      { id: 'invoices', key: 'invoices', name: '开具发票', parentId: null, sort: 3, canView: false, canEdit: false, canDelete: false, canExport: false },
      { id: 'received-invoices', key: 'received-invoices', name: '取得发票', parentId: null, sort: 4, canView: false, canEdit: false, canDelete: false, canExport: false },
      { id: 'import', key: 'import', name: 'Excel导入', parentId: null, sort: 5, canView: false, canEdit: false, canDelete: false, canExport: false },
      { id: 'users', key: 'users', name: '用户管理', parentId: null, sort: 6, canView: false, canEdit: false, canDelete: false, canExport: false },
      { id: 'reports', key: 'reports', name: '报表', parentId: null, sort: 7, canView: false, canEdit: false, canDelete: false, canExport: false },
      { id: 'quarterly-summary', key: 'quarterly-summary', name: '季度开票汇总', parentId: 'reports', sort: 1, canView: false, canEdit: false, canDelete: false, canExport: false },
      { id: 'relations', key: 'relations', name: '开票关系穿透图', parentId: 'reports', sort: 2, canView: false, canEdit: false, canDelete: false, canExport: false },
    ];

    res.json({
      success: true,
      data: defaultMenus,
    });
  }
}));

// 更新用户菜单权限
router.put('/:id/menu-permissions', authenticate, asyncHandler(async (req: AuthenticatedRequest, res: any) => {
  const { id } = req.params;
  const { permissions } = req.body;

  if (!Array.isArray(permissions)) {
    throw new AppError('权限数据格式错误', 400);
  }

  try {
    // 首先检查表是否存在，如果不存在则创建
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS user_menu_permissions (
        id VARCHAR(191) NOT NULL PRIMARY KEY,
        userId VARCHAR(191) NOT NULL,
        menuId VARCHAR(191) NOT NULL,
        canView BOOLEAN NOT NULL DEFAULT true,
        canEdit BOOLEAN NOT NULL DEFAULT false,
        canDelete BOOLEAN NOT NULL DEFAULT false,
        canExport BOOLEAN NOT NULL DEFAULT false,
        createdAt DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
        updatedAt DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
        UNIQUE KEY user_menu_permissions_userId_menuId_key (userId, menuId)
      )
    `;

    // 开始事务
    await prisma.$transaction(async (tx) => {
      // 删除用户现有权限
      await tx.$executeRaw`DELETE FROM user_menu_permissions WHERE userId = ${id}`;

      // 插入新权限（只插入有权限的菜单）
      for (const permission of permissions) {
        if (permission.canView || permission.canEdit || permission.canDelete || permission.canExport) {
          const permissionId = `perm_${id}_${permission.menuId || permission.id}`;
          const menuId = permission.menuId || permission.id; // 兼容两种字段名

          await tx.$executeRaw`
            INSERT INTO user_menu_permissions (id, userId, menuId, canView, canEdit, canDelete, canExport, createdAt, updatedAt)
            VALUES (${permissionId}, ${id}, ${menuId}, ${permission.canView || false}, ${permission.canEdit || false}, ${permission.canDelete || false}, ${permission.canExport || false}, NOW(), NOW())
          `;
        }
      }
    });

    res.json({
      success: true,
      message: '用户菜单权限更新成功',
    });
  } catch (error) {

    throw new AppError('更新用户菜单权限失败', 500);
  }
}));

// 修改用户密码
router.put('/:id/password', authenticate, asyncHandler(async (req: AuthenticatedRequest, res: any) => {
  const { id } = req.params;
  const { newPassword } = req.body;

  if (!newPassword || newPassword.length < 6) {
    throw new AppError('新密码至少6个字符', 400);
  }

  // 检查用户是否存在
  const existingUser = await prisma.user.findUnique({
    where: { id }
  });

  if (!existingUser) {
    throw new AppError('用户不存在', 404);
  }

  // 检查权限：只有管理员或用户本人可以修改密码
  if (req.user?.role !== 'ADMIN' && req.user?.id !== id) {
    throw new AppError('没有权限修改此用户密码', 403);
  }

  // 加密新密码
  const hashedPassword = await bcrypt.hash(newPassword, 10);

  // 更新密码
  await prisma.user.update({
    where: { id },
    data: {
      password: hashedPassword,
      updatedAt: new Date()
    }
  });

  res.json({
    success: true,
    message: '密码修改成功',
  });
}));

// 获取用户公司权限
router.get('/:id/companies', authenticate, authorize('ADMIN'), asyncHandler(async (req: AuthenticatedRequest, res: any) => {
  const { id } = req.params;

  try {
    const userCompanies = await prisma.$queryRaw`
      SELECT uc.companyId, c.name as companyName
      FROM user_companies uc
      LEFT JOIN companies c ON uc.companyId = c.id
      WHERE uc.userId = ${id}
    ` as any[];

    res.json({
      success: true,
      data: userCompanies,
    });
  } catch (error) {

    res.json({
      success: true,
      data: [],
    });
  }
}));

// 设置用户公司权限
router.put('/:id/companies', authenticate, authorize('ADMIN'), asyncHandler(async (req: AuthenticatedRequest, res: any) => {
  const { id } = req.params;
  const { companyIds } = req.body;

  if (!Array.isArray(companyIds)) {
    throw new AppError('公司ID列表格式错误', 400);
  }

  try {
    // 首先检查表是否存在，如果不存在则创建
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS user_companies (
        id VARCHAR(191) NOT NULL PRIMARY KEY,
        userId VARCHAR(191) NOT NULL,
        companyId VARCHAR(191) NOT NULL,
        createdAt DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
        updatedAt DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
        UNIQUE KEY user_companies_userId_companyId_key (userId, companyId)
      )
    `;

    await prisma.$transaction(async (tx) => {
      // 删除现有权限
      await tx.$executeRaw`DELETE FROM user_companies WHERE userId = ${id}`;

      // 添加新权限
      for (const companyId of companyIds) {
        const permissionId = `uc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        await tx.$executeRaw`
          INSERT INTO user_companies (id, userId, companyId, createdAt, updatedAt)
          VALUES (${permissionId}, ${id}, ${companyId}, NOW(), NOW())
        `;
      }
    });

    res.json({
      success: true,
      message: '用户公司权限更新成功',
    });
  } catch (error) {

    throw new AppError('更新用户公司权限失败', 500);
  }
}));

// 辅助函数：按层级排序菜单
function sortMenusHierarchically(menus: any[]): any[] {
  const result: any[] = [];
  const menuMap = new Map();

  // 创建菜单映射
  menus.forEach(menu => {
    menuMap.set(menu.id, menu);
  });

  // 首先添加所有父菜单（parentId为null的菜单）
  const parentMenus = menus.filter(menu => !menu.parentId).sort((a, b) => a.sort - b.sort);

  parentMenus.forEach(parentMenu => {
    result.push(parentMenu);

    // 然后添加该父菜单的所有子菜单
    const childMenus = menus
      .filter(menu => menu.parentId === parentMenu.id)
      .sort((a, b) => a.sort - b.sort);

    childMenus.forEach(childMenu => {
      result.push(childMenu);
    });
  });

  return result;
}

export default router;
