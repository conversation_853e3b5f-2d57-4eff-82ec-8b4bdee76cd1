// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema
// Updated to remove bankAccount field

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

/// 用户表 - 支持RBAC权限模型
model User {
  /// 用户唯一标识符
  id        String     @id @default(cuid())
  /// 用户名（登录用）
  username  String     @unique
  /// 邮箱地址（登录用）
  email     String     @unique
  /// 加密后的密码
  password  String
  /// 用户真实姓名
  name      String
  /// 用户角色（管理员、财务、业务等）
  role      UserRole   @default(USER)
  /// 用户状态（激活、禁用）
  status    UserStatus @default(ACTIVE)
  /// 创建时间
  createdAt DateTime   @default(now())
  /// 更新时间
  updatedAt DateTime   @updatedAt

  // 关联用户-公司权限
  userCompanies UserCompany[]

  // 关联操作日志
  auditLogs AuditLog[]

  // 关联取得发票操作日志
  receivedInvoiceAuditLogs ReceivedInvoiceAuditLog[]

  // 关联用户菜单权限
  userMenuPermissions UserMenuPermission[]

  // 关联操作日志
  operationLogs OperationLog[]

  @@map("users")
}

/// 用户-公司关联表（权限控制）
model UserCompany {
  /// 关联记录唯一标识符
  id        String   @id @default(cuid())
  /// 用户ID
  userId    String
  /// 公司ID
  companyId String
  /// 创建时间
  createdAt DateTime @default(now())

  /// 关联用户
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  /// 关联公司
  company   Company  @relation(fields: [companyId], references: [id], onDelete: Cascade)

  @@unique([userId, companyId])
  @@map("user_companies")
}

/// 菜单表
model Menu {
  /// 菜单唯一标识符
  id          String   @id @default(cuid())
  /// 菜单唯一标识键
  key         String   @unique
  /// 菜单显示名称
  name        String
  /// 菜单路由路径
  path        String?
  /// 菜单图标
  icon        String?
  /// 父菜单ID（用于构建菜单树）
  parentId    String?
  /// 菜单排序号
  sort        Int      @default(0)
  /// 是否启用
  isActive    Boolean  @default(true)
  /// 菜单描述
  description String?
  /// 创建时间
  createdAt   DateTime @default(now())
  /// 更新时间
  updatedAt   DateTime @updatedAt

  // 自关联：父菜单
  parent      Menu?    @relation("MenuHierarchy", fields: [parentId], references: [id])
  children    Menu[]   @relation("MenuHierarchy")

  // 关联用户菜单权限
  userMenuPermissions UserMenuPermission[]

  @@map("menus")
}

/// 用户菜单权限表
model UserMenuPermission {
  /// 权限记录唯一标识符
  id        String   @id @default(cuid())
  /// 用户ID
  userId    String
  /// 菜单ID
  menuId    String
  /// 是否可查看
  canView   Boolean  @default(true)
  /// 是否可编辑
  canEdit   Boolean  @default(false)
  /// 是否可删除
  canDelete Boolean  @default(false)
  /// 是否可导出
  canExport Boolean  @default(false)
  /// 创建时间
  createdAt DateTime @default(now())
  /// 更新时间
  updatedAt DateTime @updatedAt

  // 关联用户
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  // 关联菜单
  menu      Menu     @relation(fields: [menuId], references: [id], onDelete: Cascade)

  @@unique([userId, menuId])
  @@map("user_menu_permissions")
}

/// 公司表
model Company {
  /// 公司唯一标识符
  id          String    @id @default(cuid())
  /// 公司名称
  name        String
  /// 纳税人识别号（税号）
  taxId       String    @unique
  /// 公司地址
  address     String?
  /// 联系电话
  phone       String?
  /// 邮箱地址
  email       String?
  /// 联系人姓名
  contact     String?
  /// 所属组织
  organization String?
  /// 备注信息
  remarks     String?
  /// 公司注册日期
  registrationDate DateTime?
  /// 最新发票日期
  lastInvoiceDate  DateTime?
  /// 是否激活
  isActive    Boolean   @default(true)
  /// 创建时间
  createdAt   DateTime  @default(now())
  /// 更新时间
  updatedAt   DateTime  @updatedAt

  // 关联用户-公司权限
  userCompanies UserCompany[]

  // 关联发票
  invoices    Invoice[]

  // 关联取得发票
  receivedInvoices ReceivedInvoice[]

  @@map("companies")
}

/// 发票主表（发票头信息）
model Invoice {
  /// 发票唯一标识符
  id              String        @id @default(cuid())
  /// 发票号码
  invoiceNumber   String        @unique
  /// 发票代码
  invoiceCode     String
  /// 开票日期
  invoiceDate     DateTime
  /// 不含税金额
  amount          Decimal       @db.Decimal(15, 2)
  /// 税额
  taxAmount       Decimal       @db.Decimal(15, 2)
  /// 价税合计金额
  totalAmount     Decimal       @db.Decimal(15, 2)
  /// 购买方名称
  buyerName       String
  /// 购买方纳税人识别号
  buyerTaxId      String
  /// 购买方地址
  buyerAddress    String?
  /// 购买方电话
  buyerPhone      String?
  /// 购买方开户行及账号
  buyerBank       String?
  /// 销售方名称
  sellerName      String
  /// 销售方纳税人识别号
  sellerTaxId     String
  /// 销售方地址
  sellerAddress   String?
  /// 销售方电话
  sellerPhone     String?
  /// 销售方开户行及账号
  sellerBank      String?
  /// 发票类型
  invoiceType     InvoiceType
  /// 发票状态
  status          InvoiceStatus @default(NORMAL)
  /// 查验状态
  verificationStatus VerificationStatus @default(UNVERIFIED)
  /// 开票人
  drawer          String?
  /// 备注信息
  remarks         String?
  /// 备注（保留兼容性）
  remark          String?
  /// 项目名称
  projectName     String?
  /// 部门
  department      String?
  /// 成本中心
  costCenter      String?
  /// 是否重复发票
  isDuplicate     Boolean       @default(false)
  /// 是否已归档
  isArchived      Boolean       @default(false)
  /// 创建时间
  createdAt       DateTime      @default(now())
  /// 更新时间
  updatedAt       DateTime      @updatedAt

  /// 关联公司ID
  companyId       String
  /// 关联公司
  company         Company       @relation(fields: [companyId], references: [id], onDelete: Cascade)

  /// 关联发票明细
  invoiceItems    InvoiceItem[]

  /// 关联附件
  attachments     InvoiceAttachment[]

  /// 关联审计日志
  auditLogs       AuditLog[]

  @@index([invoiceNumber, invoiceCode])
  @@index([companyId])
  @@index([invoiceDate])
  @@index([status])
  @@map("invoices")
}

/// 取得发票主表（取得发票头信息）
model ReceivedInvoice {
  /// 取得发票唯一标识符
  id              String        @id @default(cuid())
  /// 发票号码
  invoiceNumber   String        @unique
  /// 发票代码
  invoiceCode     String
  /// 开票日期
  invoiceDate     DateTime
  /// 不含税金额
  amount          Decimal       @db.Decimal(15, 2)
  /// 税额
  taxAmount       Decimal       @db.Decimal(15, 2)
  /// 价税合计金额
  totalAmount     Decimal       @db.Decimal(15, 2)
  /// 购买方名称
  buyerName       String
  /// 购买方纳税人识别号
  buyerTaxId      String
  /// 购买方地址
  buyerAddress    String?
  /// 购买方电话
  buyerPhone      String?
  /// 购买方开户行及账号
  buyerBank       String?
  /// 销售方名称
  sellerName      String
  /// 销售方纳税人识别号
  sellerTaxId     String
  /// 销售方地址
  sellerAddress   String?
  /// 销售方电话
  sellerPhone     String?
  /// 销售方开户行及账号
  sellerBank      String?
  /// 发票类型
  invoiceType     InvoiceType
  /// 发票状态
  status          InvoiceStatus @default(NORMAL)
  /// 查验状态
  verificationStatus VerificationStatus @default(UNVERIFIED)
  /// 开票人
  drawer          String?
  /// 备注信息
  remarks         String?
  /// 备注（保留兼容性）
  remark          String?
  /// 项目名称
  projectName     String?
  /// 部门
  department      String?
  /// 成本中心
  costCenter      String?
  /// 是否重复发票
  isDuplicate     Boolean       @default(false)
  /// 是否已归档
  isArchived      Boolean       @default(false)
  /// 创建时间
  createdAt       DateTime      @default(now())
  /// 更新时间
  updatedAt       DateTime      @updatedAt

  /// 关联公司ID
  companyId       String
  /// 关联公司
  company         Company       @relation(fields: [companyId], references: [id], onDelete: Cascade)

  /// 关联取得发票明细
  receivedInvoiceItems    ReceivedInvoiceItem[]

  /// 关联附件
  attachments     ReceivedInvoiceAttachment[]

  /// 关联审计日志
  auditLogs       ReceivedInvoiceAuditLog[]

  @@index([invoiceNumber, invoiceCode])
  @@index([companyId])
  @@index([invoiceDate])
  @@index([status])
  @@map("received_invoices")
}

/// 发票明细表（商品/服务条目）
model InvoiceItem {
  /// 发票明细唯一标识符
  id          String  @id @default(cuid())
  /// 商品/服务名称
  itemName    String
  /// 规格型号
  specification String?
  /// 计量单位
  unit        String?
  /// 数量
  quantity    Decimal @db.Decimal(10, 4)
  /// 单价
  unitPrice   Decimal @db.Decimal(15, 4)
  /// 不含税金额
  amount      Decimal @db.Decimal(15, 2)
  /// 税率
  taxRate     Decimal @db.Decimal(5, 4)
  /// 税额
  taxAmount   Decimal @db.Decimal(15, 2)
  /// 价税合计金额
  totalAmount Decimal @db.Decimal(15, 2)
  /// 创建时间
  createdAt   DateTime @default(now())
  /// 更新时间
  updatedAt   DateTime @updatedAt

  /// 关联发票ID
  invoiceId   String
  /// 关联发票
  invoice     Invoice @relation(fields: [invoiceId], references: [id], onDelete: Cascade)

  @@map("invoice_items")
}

/// 取得发票明细表（商品/服务条目）
model ReceivedInvoiceItem {
  /// 取得发票明细唯一标识符
  id          String  @id @default(cuid())
  /// 商品/服务名称
  itemName    String
  /// 规格型号
  specification String?
  /// 计量单位
  unit        String?
  /// 数量
  quantity    Decimal @db.Decimal(10, 4)
  /// 单价
  unitPrice   Decimal @db.Decimal(15, 4)
  /// 不含税金额
  amount      Decimal @db.Decimal(15, 2)
  /// 税率
  taxRate     Decimal @db.Decimal(5, 4)
  /// 税额
  taxAmount   Decimal @db.Decimal(15, 2)
  /// 价税合计金额
  totalAmount Decimal @db.Decimal(15, 2)
  /// 创建时间
  createdAt   DateTime @default(now())
  /// 更新时间
  updatedAt   DateTime @updatedAt

  /// 关联取得发票ID
  receivedInvoiceId   String
  /// 关联取得发票
  receivedInvoice     ReceivedInvoice @relation(fields: [receivedInvoiceId], references: [id], onDelete: Cascade)

  @@map("received_invoice_items")
}

/// 发票附件表（存储扫描件/电子原件）
model InvoiceAttachment {
  /// 附件唯一标识符
  id          String      @id @default(cuid())
  /// 系统生成的文件名
  fileName    String
  /// 原始文件名（用户上传时的文件名）
  // originalName String? // 暂时注释掉，数据库中没有这个字段
  /// 文件存储路径
  filePath    String
  /// 文件大小（字节）
  fileSize    Int
  /// 文件MIME类型
  mimeType    String
  /// 文件分类
  fileType    FileType
  /// 是否为原件
  isOriginal  Boolean     @default(false)
  /// 创建时间
  createdAt   DateTime    @default(now())

  /// 关联发票ID
  invoiceId   String
  /// 关联发票
  invoice     Invoice     @relation(fields: [invoiceId], references: [id], onDelete: Cascade)

  @@map("invoice_attachments")
}

/// 取得发票附件表（存储扫描件/电子原件）
model ReceivedInvoiceAttachment {
  /// 取得发票附件唯一标识符
  id          String      @id @default(cuid())
  /// 系统生成的文件名
  fileName    String
  /// 原始文件名（用户上传时的文件名）
  // originalName String? // 暂时注释掉，数据库中没有这个字段
  /// 文件存储路径
  filePath    String
  /// 文件大小（字节）
  fileSize    Int
  /// 文件MIME类型
  mimeType    String
  /// 文件分类
  fileType    FileType
  /// 是否为原件
  isOriginal  Boolean     @default(false)
  /// 创建时间
  createdAt   DateTime    @default(now())

  /// 关联取得发票ID
  receivedInvoiceId   String
  /// 关联取得发票
  receivedInvoice     ReceivedInvoice     @relation(fields: [receivedInvoiceId], references: [id], onDelete: Cascade)

  @@map("received_invoice_attachments")
}

/// 取得发票操作审计日志表
model ReceivedInvoiceAuditLog {
  /// 审计日志唯一标识符
  id          String      @id @default(cuid())
  /// 操作类型（CREATE, UPDATE, DELETE, VIEW）
  action      String
  /// 操作的表名
  tableName   String
  /// 操作的记录ID
  recordId    String
  /// 修改前的值（JSON格式）
  oldValues   Json?
  /// 修改后的值（JSON格式）
  newValues   Json?
  /// 操作者IP地址
  ipAddress   String?
  /// 用户代理信息
  userAgent   String?
  /// 创建时间
  createdAt   DateTime    @default(now())

  // 关联用户
  userId      String?
  user        User?       @relation(fields: [userId], references: [id])

  // 关联取得发票
  receivedInvoiceId   String?
  receivedInvoice     ReceivedInvoice?    @relation(fields: [receivedInvoiceId], references: [id])

  @@index([userId])
  @@index([tableName, recordId])
  @@index([createdAt])
  @@map("received_invoice_audit_logs")
}

/// 操作审计日志表
model AuditLog {
  /// 审计日志唯一标识符
  id          String      @id @default(cuid())
  /// 操作类型（CREATE, UPDATE, DELETE, VIEW）
  action      String
  /// 操作的表名
  tableName   String
  /// 操作的记录ID
  recordId    String
  /// 修改前的值（JSON格式）
  oldValues   Json?
  /// 修改后的值（JSON格式）
  newValues   Json?
  /// 操作者IP地址
  ipAddress   String?
  /// 用户代理信息
  userAgent   String?
  /// 创建时间
  createdAt   DateTime    @default(now())

  // 关联用户
  userId      String?
  user        User?       @relation(fields: [userId], references: [id])

  // 关联发票（可选）
  invoiceId   String?
  invoice     Invoice?    @relation(fields: [invoiceId], references: [id])

  @@index([userId])
  @@index([tableName, recordId])
  @@index([createdAt])
  @@map("audit_logs")
}

/// 操作日志表 - 记录所有API调用
model OperationLog {
  /// 操作日志唯一标识符
  id            String    @id @default(cuid())
  /// 操作用户ID（可为空，未登录操作）
  userId        String?   @map("userId")
  /// 操作用户名
  username      String?   @map("username")
  /// 操作名称（如：登录、创建公司、删除发票等）
  operationName String    @map("operationName")
  /// HTTP请求方法
  method        String    @map("method")
  /// 请求路径
  path          String    @map("path")
  /// 浏览器信息
  userAgent     String?   @map("userAgent")
  /// 客户端IP地址
  ipAddress     String?   @map("ipAddress")
  /// 请求参数（JSON格式）
  requestParams Json?     @map("requestParams")
  /// 返回数据（JSON格式，敏感信息已脱敏）
  responseData  Json?     @map("responseData")
  /// HTTP状态码
  statusCode    Int       @map("statusCode")
  /// 操作是否成功
  isSuccess     Boolean   @map("isSuccess")
  /// 错误信息（失败时记录）
  errorMessage  String?   @map("errorMessage")
  /// 请求耗时（毫秒）
  duration      Int?      @map("duration")
  /// 创建时间
  createdAt     DateTime  @default(now()) @map("createdAt")

  /// 关联用户
  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@index([userId])
  @@index([operationName])
  @@index([path])
  @@index([createdAt])
  @@index([isSuccess])
  @@map("operation_logs")
}

// 枚举定义
enum UserRole {
  ADMIN       // 管理员（全权限）
  FINANCE     // 财务人员（编辑+查看）
  BUSINESS    // 业务人员（提交+查看）
  AUDITOR     // 审计人员（仅查看）
  USER        // 普通用户（仅提交）
}

enum UserStatus {
  ACTIVE      // 激活
  INACTIVE    // 禁用
}

enum InvoiceType {
  SPECIAL_VAT     // 增值税专用发票
  ORDINARY_VAT    // 增值税普通发票
  ELECTRONIC      // 电子发票
  RECEIPT         // 收据
  OTHER           // 其他
}

enum InvoiceStatus {
  NORMAL          // 正常
  CANCELLED       // 作废
}

enum VerificationStatus {
  UNVERIFIED      // 未查验
  VERIFIED        // 已查验通过
  FAILED          // 查验失败
  DUPLICATE       // 重复发票
}

enum FileType {
  PDF             // PDF文件
  IMAGE           // 图片文件
  EXCEL           // Excel文件
  OTHER           // 其他文件
}
