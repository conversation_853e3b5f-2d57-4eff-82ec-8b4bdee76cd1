import express from 'express';
import { PrismaClient } from '@prisma/client';
import Joi from 'joi';
import { AppError, asyncHandler } from '../middleware/errorHandler';
import { authenticate, AuthenticatedRequest, getUserCompanyIds } from '../middleware/auth';

const router = express.Router();
const prisma = new PrismaClient();

// 类型定义
interface InvoiceRelation {
  target: string;
  amount: number;
  date: Date;
  invoiceNumber: string;
}

interface CompanyRelations {
  outgoing: InvoiceRelation[];
  incoming: InvoiceRelation[];
}

interface ChainDetail {
  from: string;
  to: string;
  relations: InvoiceRelation[];
  totalAmount: number;
  count: number;
  invoices: any[]; // 添加发票详情
}

interface PenetrationChain {
  id: string;
  companies: string[];
  depth: number;
  totalAmount: number;
  isCircular: boolean;
  details: ChainDetail[];
}

interface GraphNode {
  id: string;
  name: string;
  type: string;
  chainCount: number;
  totalAmount: number;
  size?: number;
  level?: string;
}

interface GraphEdge {
  source: string;
  target: string;
  amount: number;
  count: number;
  type: string;
  width?: number;
  strength?: string;
}

// 验证schemas
const relationQuerySchema = Joi.object({
  companyName: Joi.string().allow(''),
  startDate: Joi.string().allow(''),
  endDate: Joi.string().allow(''),
  maxDepth: Joi.number().integer().min(1).max(10).default(5), // 最大穿透深度
});

// 获取公司开票关系穿透图
router.get('/penetration', authenticate, asyncHandler(async (req: AuthenticatedRequest, res: any) => {
  const { error, value } = relationQuerySchema.validate(req.query);
  if (error) {
    throw new AppError(error.details[0].message, 400);
  }

  const { companyName, startDate, endDate, maxDepth } = value;

  try {
    // 构建查询条件
    const whereCondition: any = {
      status: 'NORMAL', // 只查询正常发票
    };

    // 如果是普通用户，只能查看有权限的公司数据
    if (req.user?.role !== 'ADMIN') {
      const userCompanyIds = await getUserCompanyIds(req.user!.id);
      if (userCompanyIds.length === 0) {
        return res.json({
          success: true,
          data: {
            chains: [],
            nodes: [],
            edges: [],
            stats: {
              totalChains: 0,
              totalNodes: 0,
              totalEdges: 0,
              totalAmount: 0,
            }
          },
        });
      }
      whereCondition.companyId = { in: userCompanyIds };
    }

    // 日期过滤
    if (startDate && endDate) {
      whereCondition.invoiceDate = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      };
    }



    // 获取开具发票数据（限制数量避免内存溢出） - 只查询激活公司的发票
    const issuedInvoices = await prisma.invoice.findMany({
      where: {
        ...whereCondition,
        company: {
          isActive: true, // 只查询激活公司的发票
        },
      },
      select: {
        id: true,
        sellerName: true,
        sellerTaxId: true,
        buyerName: true,
        buyerTaxId: true,
        totalAmount: true,
        invoiceDate: true,
        invoiceNumber: true,
        status: true,
      },
      take: 10000, // 限制最多查询10000条记录
      orderBy: {
        invoiceDate: 'desc',
      },
    });

    // 构建取得发票的查询条件
    const receivedWhereCondition: any = {
      status: 'NORMAL', // 只查询正常发票
    };

    // 日期过滤
    if (startDate && endDate) {
      receivedWhereCondition.invoiceDate = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      };
    }



    // 如果是普通用户，只能查看有权限的公司数据
    if (req.user?.role !== 'ADMIN') {
      const userCompanyIds = await getUserCompanyIds(req.user!.id);
      if (userCompanyIds.length > 0) {
        receivedWhereCondition.companyId = { in: userCompanyIds };
      }
    }

    // 获取取得发票数据（限制数量避免内存溢出） - 只查询激活公司的发票
    const receivedInvoices = await prisma.receivedInvoice.findMany({
      where: {
        ...receivedWhereCondition,
        company: {
          isActive: true, // 只查询激活公司的发票
        },
      },
      select: {
        id: true,
        sellerName: true,
        sellerTaxId: true,
        buyerName: true,
        buyerTaxId: true,
        totalAmount: true,
        invoiceDate: true,
        invoiceNumber: true,
        status: true,
      },
      take: 10000, // 限制最多查询10000条记录
      orderBy: {
        invoiceDate: 'desc',
      },
    });

    // 合并开具发票和取得发票数据
    const allInvoices = [
      ...issuedInvoices.map(inv => ({ ...inv, source: 'issued' })),
      ...receivedInvoices.map(inv => ({ ...inv, source: 'received' }))
    ];

    // 构建穿透关系链
    const penetrationData = buildPenetrationChains(allInvoices, companyName, maxDepth);

    res.json({
      success: true,
      data: penetrationData,
    });

  } catch (error) {
    console.error('获取开票关系穿透图失败:', error);
    throw new AppError('获取开票关系穿透图失败', 500);
  }
}));

// 获取公司详细关系信息
router.get('/company/:companyName', authenticate, asyncHandler(async (req: AuthenticatedRequest, res: any) => {
  const { companyName } = req.params;
  const { startDate, endDate } = req.query;

  try {
    // 构建查询条件
    const whereCondition: any = {
      status: 'NORMAL',
      OR: [
        { sellerName: companyName },
        { buyerName: companyName },
      ],
    };

    // 如果是普通用户，只能查看有权限的公司数据
    if (req.user?.role !== 'ADMIN') {
      const userCompanyIds = await getUserCompanyIds(req.user!.id);
      if (userCompanyIds.length === 0) {
        return res.json({
          success: true,
          data: {
            asSellerStats: { count: 0, totalAmount: 0, buyers: [] },
            asBuyerStats: { count: 0, totalAmount: 0, sellers: [] },
          },
        });
      }
      whereCondition.companyId = { in: userCompanyIds };
    }

    // 日期过滤
    if (startDate && endDate) {
      whereCondition.invoiceDate = {
        gte: new Date(startDate as string),
        lte: new Date(endDate as string),
      };
    }

    // 作为销售方的开具发票统计
    const asSellerIssuedInvoices = await prisma.invoice.findMany({
      where: {
        ...whereCondition,
        sellerName: companyName,
      },
      select: {
        buyerName: true,
        totalAmount: true,
        invoiceDate: true,
      },
    });

    // 作为购买方的开具发票统计
    const asBuyerIssuedInvoices = await prisma.invoice.findMany({
      where: {
        ...whereCondition,
        buyerName: companyName,
      },
      select: {
        sellerName: true,
        totalAmount: true,
        invoiceDate: true,
      },
    });

    // 作为销售方的取得发票统计
    const asSellerReceivedInvoices = await prisma.receivedInvoice.findMany({
      where: {
        ...whereCondition,
        sellerName: companyName,
      },
      select: {
        buyerName: true,
        totalAmount: true,
        invoiceDate: true,
      },
    });

    // 作为购买方的取得发票统计
    const asBuyerReceivedInvoices = await prisma.receivedInvoice.findMany({
      where: {
        ...whereCondition,
        buyerName: companyName,
      },
      select: {
        sellerName: true,
        totalAmount: true,
        invoiceDate: true,
      },
    });

    // 统计数据
    const asSellerStats = {
      issuedInvoices: {
        count: asSellerIssuedInvoices.length,
        totalAmount: asSellerIssuedInvoices.reduce((sum, inv) => sum + (parseFloat(inv.totalAmount?.toString() || '0') || 0), 0),
      },
      receivedInvoices: {
        count: asSellerReceivedInvoices.length,
        totalAmount: asSellerReceivedInvoices.reduce((sum, inv) => sum + (parseFloat(inv.totalAmount?.toString() || '0') || 0), 0),
      },
      buyers: [],
    };

    const asBuyerStats = {
      issuedInvoices: {
        count: asBuyerIssuedInvoices.length,
        totalAmount: asBuyerIssuedInvoices.reduce((sum, inv) => sum + (parseFloat(inv.totalAmount?.toString() || '0') || 0), 0),
      },
      receivedInvoices: {
        count: asBuyerReceivedInvoices.length,
        totalAmount: asBuyerReceivedInvoices.reduce((sum, inv) => sum + (parseFloat(inv.totalAmount?.toString() || '0') || 0), 0),
      },
      sellers: [],
    };

    res.json({
      success: true,
      data: {
        companyName,
        asSellerStats,
        asBuyerStats,
      },
    });

  } catch (error) {
    console.error('获取公司关系详情失败:', error);
    throw new AppError('获取公司关系详情失败', 500);
  }
}));

// 构建穿透关系链的核心函数
function buildPenetrationChains(invoices: any[], targetCompany: string, maxDepth: number) {
  // 构建公司关系图
  const companyGraph = new Map<string, CompanyRelations>();
  const allCompanies = new Set<string>();

  // 建立公司间的直接关系
  const invoiceMap = new Map<string, any[]>(); // 保存发票详情

  invoices.forEach((invoice: any) => {
    const { sellerName, buyerName, totalAmount, invoiceDate, invoiceNumber } = invoice;

    allCompanies.add(sellerName);
    allCompanies.add(buyerName);

    if (!companyGraph.has(sellerName)) {
      companyGraph.set(sellerName, { outgoing: [], incoming: [] });
    }
    if (!companyGraph.has(buyerName)) {
      companyGraph.set(buyerName, { outgoing: [], incoming: [] });
    }

    // 保存发票详情
    const relationKey = `${sellerName}->${buyerName}`;
    if (!invoiceMap.has(relationKey)) {
      invoiceMap.set(relationKey, []);
    }
    invoiceMap.get(relationKey)!.push(invoice);

    // 销售方的出账关系
    companyGraph.get(sellerName)!.outgoing.push({
      target: buyerName,
      amount: parseFloat(totalAmount?.toString() || '0') || 0,
      date: invoiceDate,
      invoiceNumber,
    });

    // 购买方的进账关系
    companyGraph.get(buyerName)!.incoming.push({
      target: sellerName, // 注意：incoming关系中target是来源公司
      amount: parseFloat(totalAmount?.toString() || '0') || 0,
      date: invoiceDate,
      invoiceNumber,
    });
  });

  // 查找穿透关系链
  const chains: PenetrationChain[] = [];
  const visited = new Set<string>();

  // 如果指定了目标公司，从该公司开始查找
  if (targetCompany && companyGraph.has(targetCompany)) {
    findChainsFromCompany(targetCompany, companyGraph, chains, [], visited, maxDepth, 0, invoiceMap);
  } else {
    // 否则从所有公司开始查找
    for (const company of allCompanies) {
      if (!visited.has(company)) {
        findChainsFromCompany(company, companyGraph, chains, [], visited, maxDepth, 0, invoiceMap);
      }
    }
  }

  // 过滤和排序链条，去重相同的链条
  const uniqueChains = new Map<string, PenetrationChain>();

  chains
    .filter(chain => chain.companies.length >= 2) // 至少包含2个公司
    .forEach(chain => {
      // 创建链条的唯一标识（排序后的公司列表）
      const sortedCompanies = [...chain.companies].sort();
      const chainKey = sortedCompanies.join('->');

      // 如果已存在相同的链条，保留金额更大的
      if (!uniqueChains.has(chainKey) || uniqueChains.get(chainKey)!.totalAmount < chain.totalAmount) {
        uniqueChains.set(chainKey, chain);
      }
    });

  const filteredChains = Array.from(uniqueChains.values())
    .sort((a, b) => b.totalAmount - a.totalAmount) // 按总金额排序
    .slice(0, 100); // 限制返回数量

  // 构建节点和边数据用于图形显示
  const { nodes, edges } = buildGraphData(filteredChains, allCompanies);

  return {
    chains: filteredChains,
    nodes,
    edges,
    stats: {
      totalChains: filteredChains.length,
      totalNodes: nodes.length,
      totalEdges: edges.length,
      totalAmount: filteredChains.reduce((sum, chain) => sum + chain.totalAmount, 0),
    },
    pagination: {
      totalChains: filteredChains.length,
      totalNodes: nodes.length,
      totalEdges: edges.length,
      totalAmount: filteredChains.reduce((sum, chain) => sum + chain.totalAmount, 0),
    }
  };
}

// 递归查找穿透关系链
function findChainsFromCompany(
  currentCompany: string,
  companyGraph: Map<string, CompanyRelations>,
  chains: PenetrationChain[],
  currentChain: { company: string; depth: number }[],
  visited: Set<string>,
  maxDepth: number,
  currentDepth: number,
  invoiceMap: Map<string, any[]>
) {
  if (currentDepth >= maxDepth) {
    return;
  }

  // 检查是否已经在当前链条中（形成循环）
  const existingIndex = currentChain.findIndex(item => item.company === currentCompany);
  if (existingIndex !== -1) {
    // 找到了循环关系，创建穿透链条
    const circularChain = currentChain.slice(existingIndex);
    circularChain.push({ company: currentCompany, depth: currentDepth });

    const totalAmount = calculateChainAmount(circularChain, companyGraph, invoiceMap);
    chains.push({
      id: `chain_${chains.length}`,
      companies: circularChain.map(c => c.company),
      depth: circularChain.length,
      totalAmount,
      isCircular: true,
      details: buildChainDetails(circularChain, companyGraph, invoiceMap),
    });
    return;
  }

  // 如果已经访问过这个公司（但不在当前链条中），跳过
  if (visited.has(currentCompany)) {
    return;
  }

  visited.add(currentCompany);
  currentChain.push({
    company: currentCompany,
    depth: currentDepth,
  });

  const companyData = companyGraph.get(currentCompany);
  if (!companyData) {
    visited.delete(currentCompany);
    currentChain.pop();
    return;
  }

  // 继续向下探索
  companyData.outgoing.forEach((relation: InvoiceRelation) => {
    findChainsFromCompany(
      relation.target,
      companyGraph,
      chains,
      currentChain,
      visited,
      maxDepth,
      currentDepth + 1,
      invoiceMap
    );
  });

  // 回溯
  visited.delete(currentCompany);
  currentChain.pop();
}

// 计算链条总金额 - 使用链条详情中的实际金额
function calculateChainAmount(chain: { company: string; depth: number }[], companyGraph: Map<string, CompanyRelations>, invoiceMap: Map<string, any[]>): number {
  let totalAmount = 0;

  for (let i = 0; i < chain.length - 1; i++) {
    const currentCompany = chain[i].company;
    const nextCompany = chain[i + 1].company;
    const relationKey = `${currentCompany}->${nextCompany}`;

    // 获取该关系的所有发票
    const allInvoices = invoiceMap.get(relationKey) || [];

    // 对发票进行去重，相同发票号的只计算一次
    const uniqueInvoices = [];
    const seenInvoiceNumbers = new Set<string>();

    for (const invoice of allInvoices) {
      const invoiceNumber = invoice.invoiceNumber;
      if (invoiceNumber && !seenInvoiceNumbers.has(invoiceNumber)) {
        seenInvoiceNumbers.add(invoiceNumber);
        uniqueInvoices.push(invoice);
      } else if (!invoiceNumber) {
        // 如果没有发票号，则保留（可能是特殊情况）
        uniqueInvoices.push(invoice);
      }
    }

    // 计算去重后发票的总金额
    const relationAmount = uniqueInvoices.reduce((sum: number, invoice: any) => {
      return sum + (parseFloat(invoice.totalAmount?.toString() || '0') || 0);
    }, 0);

    totalAmount += relationAmount;
  }

  return totalAmount;
}

// 构建链条详细信息
function buildChainDetails(chain: { company: string; depth: number }[], companyGraph: Map<string, CompanyRelations>, invoiceMap: Map<string, any[]>): ChainDetail[] {
  const details: ChainDetail[] = [];

  for (let i = 0; i < chain.length - 1; i++) {
    const currentCompany = chain[i].company;
    const nextCompany = chain[i + 1].company;

    const companyData = companyGraph.get(currentCompany);
    if (companyData) {
      const relations = companyData.outgoing.filter((r: InvoiceRelation) => r.target === nextCompany);
      const relationKey = `${currentCompany}->${nextCompany}`;
      const allInvoices = invoiceMap.get(relationKey) || [];

      // 对发票进行去重，相同发票号的只保留一张
      const uniqueInvoices = [];
      const seenInvoiceNumbers = new Set<string>();

      for (const invoice of allInvoices) {
        const invoiceNumber = invoice.invoiceNumber;
        if (invoiceNumber && !seenInvoiceNumbers.has(invoiceNumber)) {
          seenInvoiceNumbers.add(invoiceNumber);
          uniqueInvoices.push(invoice);
        } else if (!invoiceNumber) {
          // 如果没有发票号，则保留（可能是特殊情况）
          uniqueInvoices.push(invoice);
        }
      }

      // 计算去重后发票的实际总金额
      const actualTotalAmount = uniqueInvoices.reduce((sum: number, invoice: any) => {
        return sum + (parseFloat(invoice.totalAmount?.toString() || '0') || 0);
      }, 0);

      details.push({
        from: currentCompany,
        to: nextCompany,
        relations: relations,
        totalAmount: actualTotalAmount, // 使用去重后发票的实际金额
        count: uniqueInvoices.length, // 使用去重后的数量
        invoices: uniqueInvoices, // 使用去重后的发票列表
      });
    }
  }

  return details;
}

// 构建图形数据
function buildGraphData(chains: PenetrationChain[], allCompanies: Set<string>): { nodes: GraphNode[]; edges: GraphEdge[] } {
  const nodes: GraphNode[] = [];
  const edges: GraphEdge[] = [];
  const nodeMap = new Map<string, GraphNode>();
  const edgeMap = new Map<string, GraphEdge>();

  // 创建节点
  allCompanies.forEach((company: string) => {
    if (!nodeMap.has(company)) {
      nodeMap.set(company, {
        id: company,
        name: company,
        type: 'company',
        chainCount: 0,
        totalAmount: 0,
      });
    }
  });

  // 处理链条数据
  chains.forEach((chain: PenetrationChain) => {
    chain.companies.forEach((company: string) => {
      const node = nodeMap.get(company);
      if (node) {
        node.chainCount += 1;
        node.totalAmount += chain.totalAmount / chain.companies.length;
      }
    });

    // 创建边
    chain.details.forEach((detail: ChainDetail) => {
      const edgeKey = `${detail.from}->${detail.to}`;
      if (!edgeMap.has(edgeKey)) {
        edgeMap.set(edgeKey, {
          source: detail.from,
          target: detail.to,
          amount: 0,
          count: 0,
          type: 'penetration',
        });
      }

      const edge = edgeMap.get(edgeKey)!;
      edge.amount += detail.totalAmount;
      edge.count += detail.count;
    });
  });

  // 计算节点大小和颜色
  const maxAmount = Math.max(...Array.from(nodeMap.values()).map((n: GraphNode) => n.totalAmount));
  nodeMap.forEach((node: GraphNode) => {
    node.size = Math.max(20, Math.min(80, (node.totalAmount / maxAmount) * 60 + 20));
    node.level = node.chainCount > 5 ? 'high' : node.chainCount > 2 ? 'medium' : 'low';
    nodes.push(node);
  });

  // 计算边的粗细
  const maxEdgeAmount = Math.max(...Array.from(edgeMap.values()).map((e: GraphEdge) => e.amount));
  edgeMap.forEach((edge: GraphEdge) => {
    edge.width = Math.max(1, Math.min(8, (edge.amount / maxEdgeAmount) * 6 + 1));
    edge.strength = edge.amount > maxEdgeAmount * 0.5 ? 'strong' : edge.amount > maxEdgeAmount * 0.2 ? 'medium' : 'weak';
    edges.push(edge);
  });

  return { nodes, edges };
}

export default router;
