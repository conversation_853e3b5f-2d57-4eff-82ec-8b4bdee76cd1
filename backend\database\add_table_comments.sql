-- 发票管理系统数据库表和字段注释
-- 执行此脚本为所有表和字段添加中文描述

-- 用户表注释
ALTER TABLE `users` COMMENT = '用户表 - 支持RBAC权限模型，存储系统用户信息';
ALTER TABLE `users` MODIFY COLUMN `id` VARCHAR(191) NOT NULL COMMENT '用户唯一标识符，使用CUID格式';
ALTER TABLE `users` MODIFY COLUMN `email` VARCHAR(191) NOT NULL COMMENT '用户邮箱地址，用于登录认证，必须唯一';
ALTER TABLE `users` MODIFY COLUMN `password` VARCHAR(191) NOT NULL COMMENT '用户密码，经过bcrypt加密存储';
ALTER TABLE `users` MODIFY COLUMN `name` VARCHAR(191) NOT NULL COMMENT '用户真实姓名或显示名称';
ALTER TABLE `users` MODIFY COLUMN `role` ENUM('ADMIN','FINANCE','BUSINESS','AUDITOR','USER') NOT NULL DEFAULT 'USER' COMMENT '用户角色：ADMIN-管理员，FINANCE-财务，BUSINESS-业务，AUDITOR-审计，USER-普通用户';
ALTER TABLE `users` MODIFY COLUMN `isActive` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '用户状态：true-激活，false-禁用';
ALTER TABLE `users` MODIFY COLUMN `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '用户创建时间';
ALTER TABLE `users` MODIFY COLUMN `updatedAt` DATETIME(3) NOT NULL COMMENT '用户信息最后更新时间';

-- 公司表注释
ALTER TABLE `companies` COMMENT = '公司表 - 存储开票公司和收票公司的基本信息';
ALTER TABLE `companies` MODIFY COLUMN `id` VARCHAR(191) NOT NULL COMMENT '公司唯一标识符，使用CUID格式';
ALTER TABLE `companies` MODIFY COLUMN `name` VARCHAR(191) NOT NULL COMMENT '公司名称，用于发票开具和识别';
ALTER TABLE `companies` MODIFY COLUMN `taxId` VARCHAR(191) NOT NULL COMMENT '纳税人识别号，税务登记的唯一标识，必须唯一';
ALTER TABLE `companies` MODIFY COLUMN `address` VARCHAR(191) NULL COMMENT '公司注册地址或经营地址';
ALTER TABLE `companies` MODIFY COLUMN `phone` VARCHAR(191) NULL COMMENT '公司联系电话';
ALTER TABLE `companies` MODIFY COLUMN `email` VARCHAR(191) NULL COMMENT '公司邮箱地址';
ALTER TABLE `companies` MODIFY COLUMN `contact` VARCHAR(191) NULL COMMENT '公司联系人姓名';
ALTER TABLE `companies` MODIFY COLUMN `organization` VARCHAR(191) NULL COMMENT '所属组织或集团，如总部、分公司、子公司等';
ALTER TABLE `companies` MODIFY COLUMN `bankAccount` VARCHAR(191) NULL COMMENT '公司银行账号信息，敏感信息需加密存储';
ALTER TABLE `companies` MODIFY COLUMN `remarks` TEXT NULL COMMENT '公司备注信息，可记录特殊说明或注意事项';
ALTER TABLE `companies` MODIFY COLUMN `isActive` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '公司状态：true-正常使用，false-已停用';
ALTER TABLE `companies` MODIFY COLUMN `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '公司信息创建时间';
ALTER TABLE `companies` MODIFY COLUMN `updatedAt` DATETIME(3) NOT NULL COMMENT '公司信息最后更新时间';

-- 开具发票主表注释
ALTER TABLE `invoices` COMMENT = '开具发票主表 - 存储企业开具的发票头信息';
ALTER TABLE `invoices` MODIFY COLUMN `id` VARCHAR(191) NOT NULL COMMENT '发票唯一标识符，使用CUID格式';
ALTER TABLE `invoices` MODIFY COLUMN `invoiceNumber` VARCHAR(191) NOT NULL COMMENT '发票号码，8位数字，发票的唯一标识';
ALTER TABLE `invoices` MODIFY COLUMN `invoiceCode` VARCHAR(191) NOT NULL COMMENT '发票代码，12位数字，标识发票的类型和来源';
ALTER TABLE `invoices` MODIFY COLUMN `invoiceDate` DATETIME(3) NOT NULL COMMENT '开票日期，发票开具的日期';
ALTER TABLE `invoices` MODIFY COLUMN `amount` DECIMAL(15,2) NOT NULL COMMENT '不含税金额，发票商品或服务的总价值';
ALTER TABLE `invoices` MODIFY COLUMN `taxAmount` DECIMAL(15,2) NOT NULL COMMENT '税额，根据税率计算的增值税金额';
ALTER TABLE `invoices` MODIFY COLUMN `totalAmount` DECIMAL(15,2) NOT NULL COMMENT '价税合计，含税总金额（金额+税额）';
ALTER TABLE `invoices` MODIFY COLUMN `buyerName` VARCHAR(191) NOT NULL COMMENT '购买方名称，购买商品或服务的企业或个人名称';
ALTER TABLE `invoices` MODIFY COLUMN `buyerTaxId` VARCHAR(191) NOT NULL COMMENT '购买方纳税人识别号，购买方的税务登记号';
ALTER TABLE `invoices` MODIFY COLUMN `buyerAddress` VARCHAR(191) NULL COMMENT '购买方地址和电话，购买方的联系信息';
ALTER TABLE `invoices` MODIFY COLUMN `buyerPhone` VARCHAR(191) NULL COMMENT '购买方电话号码';
ALTER TABLE `invoices` MODIFY COLUMN `buyerBank` VARCHAR(191) NULL COMMENT '购买方开户行及账号信息';
ALTER TABLE `invoices` MODIFY COLUMN `sellerName` VARCHAR(191) NOT NULL COMMENT '销售方名称，开具发票的企业名称';
ALTER TABLE `invoices` MODIFY COLUMN `sellerTaxId` VARCHAR(191) NOT NULL COMMENT '销售方纳税人识别号，开票方的税务登记号';
ALTER TABLE `invoices` MODIFY COLUMN `sellerAddress` VARCHAR(191) NULL COMMENT '销售方地址和电话，开票方的联系信息';
ALTER TABLE `invoices` MODIFY COLUMN `sellerPhone` VARCHAR(191) NULL COMMENT '销售方电话号码';
ALTER TABLE `invoices` MODIFY COLUMN `sellerBank` VARCHAR(191) NULL COMMENT '销售方开户行及账号信息';
ALTER TABLE `invoices` MODIFY COLUMN `invoiceType` ENUM('SPECIAL_VAT','ORDINARY_VAT','ELECTRONIC','RECEIPT','OTHER') NOT NULL COMMENT '发票类型：专用发票、普通发票、电子发票、收据、其他';
ALTER TABLE `invoices` MODIFY COLUMN `status` ENUM('NORMAL','CANCELLED') NOT NULL DEFAULT 'NORMAL' COMMENT '发票状态：NORMAL-正常，CANCELLED-作废';
ALTER TABLE `invoices` MODIFY COLUMN `verificationStatus` ENUM('UNVERIFIED','VERIFIED','FAILED','DUPLICATE') NOT NULL DEFAULT 'UNVERIFIED' COMMENT '查验状态：未查验、已查验通过、查验失败、重复发票';
ALTER TABLE `invoices` MODIFY COLUMN `drawer` VARCHAR(191) NULL COMMENT '开票人姓名，实际开具发票的操作员';
ALTER TABLE `invoices` MODIFY COLUMN `remarks` TEXT NULL COMMENT '发票备注信息，可记录特殊说明';
ALTER TABLE `invoices` MODIFY COLUMN `remark` TEXT NULL COMMENT '发票备注（兼容性字段）';
ALTER TABLE `invoices` MODIFY COLUMN `projectName` VARCHAR(191) NULL COMMENT '项目名称，发票关联的项目或业务';
ALTER TABLE `invoices` MODIFY COLUMN `department` VARCHAR(191) NULL COMMENT '部门名称，开票的业务部门';
ALTER TABLE `invoices` MODIFY COLUMN `costCenter` VARCHAR(191) NULL COMMENT '成本中心，用于财务核算和成本分摊';
ALTER TABLE `invoices` MODIFY COLUMN `isDuplicate` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否重复发票：true-重复，false-正常';
ALTER TABLE `invoices` MODIFY COLUMN `isArchived` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已归档：true-已归档，false-未归档';
ALTER TABLE `invoices` MODIFY COLUMN `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '发票记录创建时间';
ALTER TABLE `invoices` MODIFY COLUMN `updatedAt` DATETIME(3) NOT NULL COMMENT '发票记录最后更新时间';
ALTER TABLE `invoices` MODIFY COLUMN `companyId` VARCHAR(191) NOT NULL COMMENT '关联公司ID，指向companies表的外键';

-- 取得发票主表注释
ALTER TABLE `received_invoices` COMMENT = '取得发票主表 - 存储企业收到的发票头信息';
ALTER TABLE `received_invoices` MODIFY COLUMN `id` VARCHAR(191) NOT NULL COMMENT '取得发票唯一标识符，使用CUID格式';
ALTER TABLE `received_invoices` MODIFY COLUMN `invoiceNumber` VARCHAR(191) NOT NULL COMMENT '发票号码，8位数字，发票的唯一标识';
ALTER TABLE `received_invoices` MODIFY COLUMN `invoiceCode` VARCHAR(191) NOT NULL COMMENT '发票代码，12位数字，标识发票的类型和来源';
ALTER TABLE `received_invoices` MODIFY COLUMN `invoiceDate` DATETIME(3) NOT NULL COMMENT '开票日期，发票开具的日期';
ALTER TABLE `received_invoices` MODIFY COLUMN `amount` DECIMAL(15,2) NOT NULL COMMENT '不含税金额，发票商品或服务的总价值';
ALTER TABLE `received_invoices` MODIFY COLUMN `taxAmount` DECIMAL(15,2) NOT NULL COMMENT '税额，根据税率计算的增值税金额';
ALTER TABLE `received_invoices` MODIFY COLUMN `totalAmount` DECIMAL(15,2) NOT NULL COMMENT '价税合计，含税总金额（金额+税额）';
ALTER TABLE `received_invoices` MODIFY COLUMN `buyerName` VARCHAR(191) NOT NULL COMMENT '购买方名称，购买商品或服务的企业或个人名称（通常是我方）';
ALTER TABLE `received_invoices` MODIFY COLUMN `buyerTaxId` VARCHAR(191) NOT NULL COMMENT '购买方纳税人识别号，购买方的税务登记号（通常是我方税号）';
ALTER TABLE `received_invoices` MODIFY COLUMN `buyerAddress` VARCHAR(191) NULL COMMENT '购买方地址和电话，购买方的联系信息';
ALTER TABLE `received_invoices` MODIFY COLUMN `buyerPhone` VARCHAR(191) NULL COMMENT '购买方电话号码';
ALTER TABLE `received_invoices` MODIFY COLUMN `buyerBank` VARCHAR(191) NULL COMMENT '购买方开户行及账号信息';
ALTER TABLE `received_invoices` MODIFY COLUMN `sellerName` VARCHAR(191) NOT NULL COMMENT '销售方名称，开具发票的企业名称（供应商）';
ALTER TABLE `received_invoices` MODIFY COLUMN `sellerTaxId` VARCHAR(191) NOT NULL COMMENT '销售方纳税人识别号，开票方的税务登记号';
ALTER TABLE `received_invoices` MODIFY COLUMN `sellerAddress` VARCHAR(191) NULL COMMENT '销售方地址和电话，开票方的联系信息';
ALTER TABLE `received_invoices` MODIFY COLUMN `sellerPhone` VARCHAR(191) NULL COMMENT '销售方电话号码';
ALTER TABLE `received_invoices` MODIFY COLUMN `sellerBank` VARCHAR(191) NULL COMMENT '销售方开户行及账号信息';
ALTER TABLE `received_invoices` MODIFY COLUMN `invoiceType` ENUM('SPECIAL_VAT','ORDINARY_VAT','ELECTRONIC','RECEIPT','OTHER') NOT NULL COMMENT '发票类型：专用发票、普通发票、电子发票、收据、其他';
ALTER TABLE `received_invoices` MODIFY COLUMN `status` ENUM('NORMAL','CANCELLED') NOT NULL DEFAULT 'NORMAL' COMMENT '发票状态：NORMAL-正常，CANCELLED-作废';
ALTER TABLE `received_invoices` MODIFY COLUMN `verificationStatus` ENUM('UNVERIFIED','VERIFIED','FAILED','DUPLICATE') NOT NULL DEFAULT 'UNVERIFIED' COMMENT '查验状态：未查验、已查验通过、查验失败、重复发票';
ALTER TABLE `received_invoices` MODIFY COLUMN `drawer` VARCHAR(191) NULL COMMENT '开票人姓名，实际开具发票的操作员';
ALTER TABLE `received_invoices` MODIFY COLUMN `remarks` TEXT NULL COMMENT '发票备注信息，可记录特殊说明';
ALTER TABLE `received_invoices` MODIFY COLUMN `remark` TEXT NULL COMMENT '发票备注（兼容性字段）';
ALTER TABLE `received_invoices` MODIFY COLUMN `projectName` VARCHAR(191) NULL COMMENT '项目名称，发票关联的项目或业务';
ALTER TABLE `received_invoices` MODIFY COLUMN `department` VARCHAR(191) NULL COMMENT '部门名称，使用发票的业务部门';
ALTER TABLE `received_invoices` MODIFY COLUMN `costCenter` VARCHAR(191) NULL COMMENT '成本中心，用于财务核算和成本分摊';
ALTER TABLE `received_invoices` MODIFY COLUMN `isDuplicate` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否重复发票：true-重复，false-正常';
ALTER TABLE `received_invoices` MODIFY COLUMN `isArchived` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已归档：true-已归档，false-未归档';
ALTER TABLE `received_invoices` MODIFY COLUMN `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '取得发票记录创建时间';
ALTER TABLE `received_invoices` MODIFY COLUMN `updatedAt` DATETIME(3) NOT NULL COMMENT '取得发票记录最后更新时间';
ALTER TABLE `received_invoices` MODIFY COLUMN `companyId` VARCHAR(191) NOT NULL COMMENT '关联公司ID，指向companies表的外键';

-- 开具发票明细表注释
ALTER TABLE `invoice_items` COMMENT = '开具发票明细表 - 存储发票中的商品或服务条目详细信息';
ALTER TABLE `invoice_items` MODIFY COLUMN `id` VARCHAR(191) NOT NULL COMMENT '发票明细唯一标识符，使用CUID格式';
ALTER TABLE `invoice_items` MODIFY COLUMN `itemName` VARCHAR(191) NOT NULL COMMENT '商品或服务名称，发票明细的主要内容';
ALTER TABLE `invoice_items` MODIFY COLUMN `specification` VARCHAR(191) NULL COMMENT '规格型号，商品的具体规格或型号信息';
ALTER TABLE `invoice_items` MODIFY COLUMN `unit` VARCHAR(191) NULL COMMENT '计量单位，如个、台、公斤、米等';
ALTER TABLE `invoice_items` MODIFY COLUMN `quantity` DECIMAL(10,4) NOT NULL COMMENT '数量，商品或服务的数量，支持小数';
ALTER TABLE `invoice_items` MODIFY COLUMN `unitPrice` DECIMAL(15,4) NOT NULL COMMENT '单价，商品或服务的不含税单价';
ALTER TABLE `invoice_items` MODIFY COLUMN `amount` DECIMAL(15,2) NOT NULL COMMENT '金额，不含税金额（数量×单价）';
ALTER TABLE `invoice_items` MODIFY COLUMN `taxRate` DECIMAL(5,4) NOT NULL COMMENT '税率，增值税税率，如0.13表示13%';
ALTER TABLE `invoice_items` MODIFY COLUMN `taxAmount` DECIMAL(15,2) NOT NULL COMMENT '税额，该明细项的增值税金额';
ALTER TABLE `invoice_items` MODIFY COLUMN `totalAmount` DECIMAL(15,2) NOT NULL COMMENT '价税合计，该明细项的含税总金额';
ALTER TABLE `invoice_items` MODIFY COLUMN `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '明细记录创建时间';
ALTER TABLE `invoice_items` MODIFY COLUMN `updatedAt` DATETIME(3) NOT NULL COMMENT '明细记录最后更新时间';
ALTER TABLE `invoice_items` MODIFY COLUMN `invoiceId` VARCHAR(191) NOT NULL COMMENT '关联发票ID，指向invoices表的外键';

-- 取得发票明细表注释
ALTER TABLE `received_invoice_items` COMMENT = '取得发票明细表 - 存储取得发票中的商品或服务条目详细信息';
ALTER TABLE `received_invoice_items` MODIFY COLUMN `id` VARCHAR(191) NOT NULL COMMENT '取得发票明细唯一标识符，使用CUID格式';
ALTER TABLE `received_invoice_items` MODIFY COLUMN `itemName` VARCHAR(191) NOT NULL COMMENT '商品或服务名称，发票明细的主要内容';
ALTER TABLE `received_invoice_items` MODIFY COLUMN `specification` VARCHAR(191) NULL COMMENT '规格型号，商品的具体规格或型号信息';
ALTER TABLE `received_invoice_items` MODIFY COLUMN `unit` VARCHAR(191) NULL COMMENT '计量单位，如个、台、公斤、米等';
ALTER TABLE `received_invoice_items` MODIFY COLUMN `quantity` DECIMAL(10,4) NOT NULL COMMENT '数量，商品或服务的数量，支持小数';
ALTER TABLE `received_invoice_items` MODIFY COLUMN `unitPrice` DECIMAL(15,4) NOT NULL COMMENT '单价，商品或服务的不含税单价';
ALTER TABLE `received_invoice_items` MODIFY COLUMN `amount` DECIMAL(15,2) NOT NULL COMMENT '金额，不含税金额（数量×单价）';
ALTER TABLE `received_invoice_items` MODIFY COLUMN `taxRate` DECIMAL(5,4) NOT NULL COMMENT '税率，增值税税率，如0.13表示13%';
ALTER TABLE `received_invoice_items` MODIFY COLUMN `taxAmount` DECIMAL(15,2) NOT NULL COMMENT '税额，该明细项的增值税金额';
ALTER TABLE `received_invoice_items` MODIFY COLUMN `totalAmount` DECIMAL(15,2) NOT NULL COMMENT '价税合计，该明细项的含税总金额';
ALTER TABLE `received_invoice_items` MODIFY COLUMN `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '明细记录创建时间';
ALTER TABLE `received_invoice_items` MODIFY COLUMN `updatedAt` DATETIME(3) NOT NULL COMMENT '明细记录最后更新时间';
ALTER TABLE `received_invoice_items` MODIFY COLUMN `receivedInvoiceId` VARCHAR(191) NOT NULL COMMENT '关联取得发票ID，指向received_invoices表的外键';

-- 开具发票附件表注释
ALTER TABLE `invoice_attachments` COMMENT = '开具发票附件表 - 存储发票相关的文件附件，如扫描件、电子原件等';
ALTER TABLE `invoice_attachments` MODIFY COLUMN `id` VARCHAR(191) NOT NULL COMMENT '附件唯一标识符，使用CUID格式';
ALTER TABLE `invoice_attachments` MODIFY COLUMN `fileName` VARCHAR(191) NOT NULL COMMENT '原始文件名，用户上传时的文件名称';
ALTER TABLE `invoice_attachments` MODIFY COLUMN `filePath` VARCHAR(191) NOT NULL COMMENT '文件存储路径，服务器上的实际存储位置';
ALTER TABLE `invoice_attachments` MODIFY COLUMN `fileSize` INT NOT NULL COMMENT '文件大小，以字节为单位';
ALTER TABLE `invoice_attachments` MODIFY COLUMN `mimeType` VARCHAR(191) NOT NULL COMMENT '文件MIME类型，如image/jpeg、application/pdf等';
ALTER TABLE `invoice_attachments` MODIFY COLUMN `fileType` ENUM('PDF','IMAGE','EXCEL','OTHER') NOT NULL COMMENT '文件分类：PDF文件、图片文件、Excel文件、其他文件';
ALTER TABLE `invoice_attachments` MODIFY COLUMN `isOriginal` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否为原件：true-原件，false-副本或扫描件';
ALTER TABLE `invoice_attachments` MODIFY COLUMN `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '附件上传时间';
ALTER TABLE `invoice_attachments` MODIFY COLUMN `invoiceId` VARCHAR(191) NOT NULL COMMENT '关联发票ID，指向invoices表的外键';

-- 取得发票附件表注释
ALTER TABLE `received_invoice_attachments` COMMENT = '取得发票附件表 - 存储取得发票相关的文件附件，如扫描件、电子原件等';
ALTER TABLE `received_invoice_attachments` MODIFY COLUMN `id` VARCHAR(191) NOT NULL COMMENT '附件唯一标识符，使用CUID格式';
ALTER TABLE `received_invoice_attachments` MODIFY COLUMN `fileName` VARCHAR(191) NOT NULL COMMENT '原始文件名，用户上传时的文件名称';
ALTER TABLE `received_invoice_attachments` MODIFY COLUMN `filePath` VARCHAR(191) NOT NULL COMMENT '文件存储路径，服务器上的实际存储位置';
ALTER TABLE `received_invoice_attachments` MODIFY COLUMN `fileSize` INT NOT NULL COMMENT '文件大小，以字节为单位';
ALTER TABLE `received_invoice_attachments` MODIFY COLUMN `mimeType` VARCHAR(191) NOT NULL COMMENT '文件MIME类型，如image/jpeg、application/pdf等';
ALTER TABLE `received_invoice_attachments` MODIFY COLUMN `fileType` ENUM('PDF','IMAGE','EXCEL','OTHER') NOT NULL COMMENT '文件分类：PDF文件、图片文件、Excel文件、其他文件';
ALTER TABLE `received_invoice_attachments` MODIFY COLUMN `isOriginal` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否为原件：true-原件，false-副本或扫描件';
ALTER TABLE `received_invoice_attachments` MODIFY COLUMN `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '附件上传时间';
ALTER TABLE `received_invoice_attachments` MODIFY COLUMN `receivedInvoiceId` VARCHAR(191) NOT NULL COMMENT '关联取得发票ID，指向received_invoices表的外键';

-- 开具发票审计日志表注释
ALTER TABLE `audit_logs` COMMENT = '开具发票审计日志表 - 记录发票相关的所有操作历史，用于审计和追踪';
ALTER TABLE `audit_logs` MODIFY COLUMN `id` VARCHAR(191) NOT NULL COMMENT '审计日志唯一标识符，使用CUID格式';
ALTER TABLE `audit_logs` MODIFY COLUMN `action` VARCHAR(191) NOT NULL COMMENT '操作类型：CREATE-创建，UPDATE-更新，DELETE-删除，VIEW-查看';
ALTER TABLE `audit_logs` MODIFY COLUMN `tableName` VARCHAR(191) NOT NULL COMMENT '操作的表名，如invoices、invoice_items等';
ALTER TABLE `audit_logs` MODIFY COLUMN `recordId` VARCHAR(191) NOT NULL COMMENT '操作的记录ID，被操作记录的主键值';
ALTER TABLE `audit_logs` MODIFY COLUMN `oldValues` JSON NULL COMMENT '修改前的值，JSON格式存储字段的原始值';
ALTER TABLE `audit_logs` MODIFY COLUMN `newValues` JSON NULL COMMENT '修改后的值，JSON格式存储字段的新值';
ALTER TABLE `audit_logs` MODIFY COLUMN `ipAddress` VARCHAR(191) NULL COMMENT '操作者IP地址，用于安全审计';
ALTER TABLE `audit_logs` MODIFY COLUMN `userAgent` VARCHAR(191) NULL COMMENT '用户代理信息，浏览器或客户端信息';
ALTER TABLE `audit_logs` MODIFY COLUMN `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '操作发生时间';
ALTER TABLE `audit_logs` MODIFY COLUMN `userId` VARCHAR(191) NULL COMMENT '操作用户ID，指向users表的外键';
ALTER TABLE `audit_logs` MODIFY COLUMN `invoiceId` VARCHAR(191) NULL COMMENT '关联发票ID，指向invoices表的外键（可选）';

-- 取得发票审计日志表注释
ALTER TABLE `received_invoice_audit_logs` COMMENT = '取得发票审计日志表 - 记录取得发票相关的所有操作历史，用于审计和追踪';
ALTER TABLE `received_invoice_audit_logs` MODIFY COLUMN `id` VARCHAR(191) NOT NULL COMMENT '审计日志唯一标识符，使用CUID格式';
ALTER TABLE `received_invoice_audit_logs` MODIFY COLUMN `action` VARCHAR(191) NOT NULL COMMENT '操作类型：CREATE-创建，UPDATE-更新，DELETE-删除，VIEW-查看';
ALTER TABLE `received_invoice_audit_logs` MODIFY COLUMN `tableName` VARCHAR(191) NOT NULL COMMENT '操作的表名，如received_invoices、received_invoice_items等';
ALTER TABLE `received_invoice_audit_logs` MODIFY COLUMN `recordId` VARCHAR(191) NOT NULL COMMENT '操作的记录ID，被操作记录的主键值';
ALTER TABLE `received_invoice_audit_logs` MODIFY COLUMN `oldValues` JSON NULL COMMENT '修改前的值，JSON格式存储字段的原始值';
ALTER TABLE `received_invoice_audit_logs` MODIFY COLUMN `newValues` JSON NULL COMMENT '修改后的值，JSON格式存储字段的新值';
ALTER TABLE `received_invoice_audit_logs` MODIFY COLUMN `ipAddress` VARCHAR(191) NULL COMMENT '操作者IP地址，用于安全审计';
ALTER TABLE `received_invoice_audit_logs` MODIFY COLUMN `userAgent` VARCHAR(191) NULL COMMENT '用户代理信息，浏览器或客户端信息';
ALTER TABLE `received_invoice_audit_logs` MODIFY COLUMN `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '操作发生时间';
ALTER TABLE `received_invoice_audit_logs` MODIFY COLUMN `userId` VARCHAR(191) NULL COMMENT '操作用户ID，指向users表的外键';
ALTER TABLE `received_invoice_audit_logs` MODIFY COLUMN `receivedInvoiceId` VARCHAR(191) NULL COMMENT '关联取得发票ID，指向received_invoices表的外键（可选）';

-- 执行完成提示
SELECT '数据库表和字段注释添加完成！' AS message;
