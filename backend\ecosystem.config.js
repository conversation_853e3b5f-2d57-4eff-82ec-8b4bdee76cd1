module.exports = {
  apps: [
    {
      // 开发环境配置
      name: 'invoice-backend-dev',
      script: 'dist/index.js',
      cwd: __dirname,
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'development',
        PORT: 3001,
        FRONTEND_URL: 'http://localhost:5173'
      },
      error_file: './logs/pm2-error.log',
      out_file: './logs/pm2-out.log',
      log_file: './logs/pm2-combined.log',
      time: true
    },
    {
      // 生产环境配置
      name: 'invoice-backend-prod',
      script: 'dist/index.js',
      cwd: __dirname,
      instances: 'max', // 使用所有CPU核心
      exec_mode: 'cluster',
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        PORT: 3001,
        FRONTEND_URL: 'http://fpm.nslemons.com:5173',
        CORS_ORIGIN: 'http://fpm.nslemons.com',
        DATABASE_URL: 'mysql://InvicesAI:<EMAIL>:43306/invicesai',
        JWT_SECRET: 'invoice-management-super-secret-key-2024',
        JWT_EXPIRES_IN: '7d',
        UPLOAD_DIR: 'uploads',
        UPLOAD_MAX_SIZE: '10485760',
        ALLOWED_FILE_TYPES: 'pdf,jpg,jpeg,png,xlsx',
        LOG_LEVEL: 'info',
        LOG_DIR: 'logs',
        TIMEZONE: 'Asia/Shanghai',
        DEFAULT_LANGUAGE: 'zh-CN'
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3001,
        FRONTEND_URL: 'http://fpm.nslemons.com',
        CORS_ORIGIN: 'http://fpm.nslemons.com'
      },
      error_file: './logs/pm2-error.log',
      out_file: './logs/pm2-out.log',
      log_file: './logs/pm2-combined.log',
      time: true,
      // 生产环境优化
      node_args: '--max-old-space-size=1024',
      // 健康检查
      health_check_url: 'http://localhost:3001/health',
      health_check_grace_period: 3000
    }
  ],

  // 部署配置
  deploy: {
    production: {
      user: 'root',
      host: 'fpm.nslemons.com',
      ref: 'origin/main',
      repo: '**************:your-repo/invoice-management.git',
      path: '/var/www/invoice-backend',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
};
