{"version": 3, "file": "auditLogger.js", "sourceRoot": "", "sources": ["../../src/middleware/auditLogger.ts"], "names": [], "mappings": ";;;AACA,2CAA8C;AAE9C,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;AAU3B,MAAM,WAAW,GAAG,KAAK,EAC9B,GAAyB,EACzB,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,cAAc;IACd,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;QAClC,OAAO,IAAI,EAAE,CAAC;IAChB,CAAC;IAED,cAAc;IACd,IAAI,GAAG,CAAC,IAAI,KAAK,aAAa,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,EAAE,CAAC;QACzE,OAAO,IAAI,EAAE,CAAC;IAChB,CAAC;IAED,MAAM,YAAY,GAAG,GAAG,CAAC,IAAI,CAAC;IAC9B,IAAI,YAAiB,CAAC;IAEtB,OAAO;IACP,GAAG,CAAC,IAAI,GAAG,UAAU,IAAS;QAC5B,YAAY,GAAG,IAAI,CAAC;QACpB,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACvC,CAAC,CAAC;IAEF,eAAe;IACf,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;QAC1B,IAAI,CAAC;YACH,SAAS;YACT,IAAI,MAAM,GAAG,SAAS,CAAC;YACvB,QAAQ,GAAG,CAAC,MAAM,EAAE,CAAC;gBACnB,KAAK,KAAK;oBACR,MAAM,GAAG,MAAM,CAAC;oBAChB,MAAM;gBACR,KAAK,MAAM;oBACT,MAAM,GAAG,QAAQ,CAAC;oBAClB,MAAM;gBACR,KAAK,KAAK,CAAC;gBACX,KAAK,OAAO;oBACV,MAAM,GAAG,QAAQ,CAAC;oBAClB,MAAM;gBACR,KAAK,QAAQ;oBACX,MAAM,GAAG,QAAQ,CAAC;oBAClB,MAAM;YACV,CAAC;YAED,YAAY;YACZ,MAAM,SAAS,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACtC,IAAI,SAAS,GAAG,SAAS,CAAC;YAC1B,IAAI,QAAQ,GAAG,IAAI,CAAC;YAEpB,IAAI,SAAS,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;gBAC1B,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,8BAA8B;gBACxD,IAAI,SAAS,CAAC,MAAM,IAAI,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC1C,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,4BAA4B;gBACvD,CAAC;YACH,CAAC;YAED,qCAAqC;YACrC;;;;;;;;;;;;;;cAcE;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACpD,YAAY;QACd,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAhFW,QAAA,WAAW,eAgFtB"}